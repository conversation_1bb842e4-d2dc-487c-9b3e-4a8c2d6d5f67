#data
<foo bar=qux/>
#errors
(1,14): expected-doctype-but-got-start-tag
(1,14): expected-closing-tag-but-got-eof
#document
| <html>
|   <head>
|   <body>
|     <foo>
|       bar="qux/"

#data
<p id="status"><noscript><strong>A</strong></noscript><span>B</span></p>
#errors
(1,15): expected-doctype-but-got-start-tag
#script-on
#document
| <html>
|   <head>
|   <body>
|     <p>
|       id="status"
|       <noscript>
|         "<strong>A</strong>"
|       <span>
|         "B"

#data
<p id="status"><noscript><strong>A</strong></noscript><span>B</span></p>
#errors
(1,15): expected-doctype-but-got-start-tag
#script-off
#document
| <html>
|   <head>
|   <body>
|     <p>
|       id="status"
|       <noscript>
|         <strong>
|           "A"
|       <span>
|         "B"

#data
<div><sarcasm><div></div></sarcasm></div>
#errors
(1,5): expected-doctype-but-got-start-tag
#document
| <html>
|   <head>
|   <body>
|     <div>
|       <sarcasm>
|         <div>

#data
<html><body><img src="" border="0" alt="><div>A</div></body></html>
#errors
(1,6): expected-doctype-but-got-start-tag
(1,67): eof-in-attribute-value-double-quote
#new-errors
(1:68) eof-in-tag
#document
| <html>
|   <head>
|   <body>

#data
<table><td></tbody>A
#errors
(1,7): expected-doctype-but-got-start-tag
(1,11): unexpected-cell-in-table-body
(1,20): foster-parenting-character
(1,20): eof-in-table
#document
| <html>
|   <head>
|   <body>
|     "A"
|     <table>
|       <tbody>
|         <tr>
|           <td>

#data
<table><td></thead>A
#errors
(1,7): expected-doctype-but-got-start-tag
(1,11): unexpected-cell-in-table-body
(1,19): XXX-undefined-error
(1,20): expected-closing-tag-but-got-eof
#document
| <html>
|   <head>
|   <body>
|     <table>
|       <tbody>
|         <tr>
|           <td>
|             "A"

#data
<table><td></tfoot>A
#errors
(1,7): expected-doctype-but-got-start-tag
(1,11): unexpected-cell-in-table-body
(1,19): XXX-undefined-error
(1,20): expected-closing-tag-but-got-eof
#document
| <html>
|   <head>
|   <body>
|     <table>
|       <tbody>
|         <tr>
|           <td>
|             "A"

#data
<table><thead><td></tbody>A
#errors
(1,7): expected-doctype-but-got-start-tag
(1,18): unexpected-cell-in-table-body
(1,26): XXX-undefined-error
(1,27): expected-closing-tag-but-got-eof
#document
| <html>
|   <head>
|   <body>
|     <table>
|       <thead>
|         <tr>
|           <td>
|             "A"

#data
<legend>test</legend>
#errors
#document
| <html>
|   <head>
|   <body>
|     <legend>
|       "test"

#data
<table><input>
#errors
#document
| <html>
|   <head>
|   <body>
|     <input>
|     <table>

#data
<b><em><foo><foo><aside></b>
#errors
#document
| <html>
|   <head>
|   <body>
|     <b>
|       <em>
|         <foo>
|           <foo>
|     <em>
|       <aside>
|         <b>

#data
<b><em><foo><foo><aside></b></em>
#errors
#document
| <html>
|   <head>
|   <body>
|     <b>
|       <em>
|         <foo>
|           <foo>
|     <em>
|     <aside>
|       <em>
|         <b>

#data
<b><em><foo><foo><foo><aside></b>
#errors
#document
| <html>
|   <head>
|   <body>
|     <b>
|       <em>
|         <foo>
|           <foo>
|             <foo>
|     <aside>
|       <b>

#data
<b><em><foo><foo><foo><aside></b></em>
#errors
#document
| <html>
|   <head>
|   <body>
|     <b>
|       <em>
|         <foo>
|           <foo>
|             <foo>
|     <aside>
|       <b>

#data
<b><em><foo><foo><foo><foo><foo><foo><foo><foo><foo><foo><aside></b></em>
#errors
#document-fragment
div
#document
| <b>
|   <em>
|     <foo>
|       <foo>
|         <foo>
|           <foo>
|             <foo>
|               <foo>
|                 <foo>
|                   <foo>
|                     <foo>
|                       <foo>
| <aside>
|   <b>

#data
<b><em><foo><foob><foob><foob><foob><fooc><fooc><fooc><fooc><food><aside></b></em>
#errors
#document-fragment
div
#document
| <b>
|   <em>
|     <foo>
|       <foob>
|         <foob>
|           <foob>
|             <foob>
|               <fooc>
|                 <fooc>
|                   <fooc>
|                     <fooc>
|                       <food>
| <aside>
|   <b>

#data
<option><XH<optgroup></optgroup>
#errors
#document-fragment
select
#document
| <option>

#data
<svg><foreignObject><div>foo</div><plaintext></foreignObject></svg><div>bar</div>
#errors
#document
| <html>
|   <head>
|   <body>
|     <svg svg>
|       <svg foreignObject>
|         <div>
|           "foo"
|         <plaintext>
|           "</foreignObject></svg><div>bar</div>"

#data
<svg><foreignObject></foreignObject><title></svg>foo
#errors
#document
| <html>
|   <head>
|   <body>
|     <svg svg>
|       <svg foreignObject>
|       <svg title>
|     "foo"

#data
</foreignObject><plaintext><div>foo</div>
#errors
#document
| <html>
|   <head>
|   <body>
|     <plaintext>
|       "<div>foo</div>"
