package pusher

import (
	"net/http"
	"net/http/httptest"
	"testing"
	"time"
)

func TestNewBlackboxPusher(t *testing.T) {
	pusher := NewBlackboxPusher(
		"http://localhost:8080/metrics",
		"http://localhost:9091",
		"test_job",
		"test_instance",
	)

	if pusher == nil {
		t.Fatal("Expected pusher to be created, got nil")
	}

	config := pusher.GetConfig()
	if config["blackbox_url"] != "http://localhost:8080/metrics" {
		t.<PERSON><PERSON><PERSON>("Expected blackbox_url to be 'http://localhost:8080/metrics', got %s", config["blackbox_url"])
	}

	if config["push_gateway_url"] != "http://localhost:9091" {
		t.<PERSON>rrorf("Expected push_gateway_url to be 'http://localhost:9091', got %s", config["push_gateway_url"])
	}

	if config["job_name"] != "test_job" {
		t.<PERSON>rrorf("Expected job_name to be 'test_job', got %s", config["job_name"])
	}

	if config["instance_label"] != "test_instance" {
		t.Errorf("Expected instance_label to be 'test_instance', got %s", config["instance_label"])
	}
}

func TestSetTimeout(t *testing.T) {
	pusher := NewBlackboxPusher(
		"http://localhost:8080/metrics",
		"http://localhost:9091",
		"test_job",
		"test_instance",
	)

	newTimeout := 30 * time.Second
	pusher.SetTimeout(newTimeout)

	config := pusher.GetConfig()
	if config["timeout"] != newTimeout.String() {
		t.Errorf("Expected timeout to be %s, got %s", newTimeout.String(), config["timeout"])
	}
}

func TestPushOnceWithMockServers(t *testing.T) {
	// 创建模拟的blackbox exporter服务器
	blackboxServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path == "/metrics" {
			w.Header().Set("Content-Type", "text/plain")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte("# HELP test_metric A test metric\n# TYPE test_metric counter\ntest_metric 1\n"))
		} else {
			w.WriteHeader(http.StatusNotFound)
		}
	}))
	defer blackboxServer.Close()

	// 创建模拟的push gateway服务器
	pushGatewayServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Method == "POST" && r.URL.Path == "/metrics/job/test_job/instance/test_instance" {
			w.WriteHeader(http.StatusOK)
		} else {
			w.WriteHeader(http.StatusBadRequest)
		}
	}))
	defer pushGatewayServer.Close()

	// 创建pusher实例
	pusher := NewBlackboxPusher(
		blackboxServer.URL+"/metrics",
		pushGatewayServer.URL,
		"test_job",
		"test_instance",
	)

	// 测试推送
	err := pusher.PushOnce()
	if err != nil {
		t.Errorf("Expected PushOnce to succeed, got error: %v", err)
	}
}

func TestPushOnceWithBlackboxError(t *testing.T) {
	// 创建返回错误的blackbox exporter服务器
	blackboxServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusInternalServerError)
	}))
	defer blackboxServer.Close()

	// 创建正常的push gateway服务器
	pushGatewayServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}))
	defer pushGatewayServer.Close()

	pusher := NewBlackboxPusher(
		blackboxServer.URL+"/metrics",
		pushGatewayServer.URL,
		"test_job",
		"test_instance",
	)

	err := pusher.PushOnce()
	if err == nil {
		t.Error("Expected PushOnce to fail when blackbox exporter returns error, but it succeeded")
	}
}

func TestDeleteMetrics(t *testing.T) {
	// 创建模拟的push gateway服务器
	pushGatewayServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Method == "DELETE" && r.URL.Path == "/metrics/job/test_job/instance/test_instance" {
			w.WriteHeader(http.StatusOK)
		} else {
			w.WriteHeader(http.StatusBadRequest)
		}
	}))
	defer pushGatewayServer.Close()

	pusher := NewBlackboxPusher(
		"http://localhost:8080/metrics",
		pushGatewayServer.URL,
		"test_job",
		"test_instance",
	)

	err := pusher.DeleteMetrics()
	if err != nil {
		t.Errorf("Expected DeleteMetrics to succeed, got error: %v", err)
	}
}
