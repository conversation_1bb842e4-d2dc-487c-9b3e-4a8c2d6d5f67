package pusher

import (
	"bytes"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"
)

// BlackboxPusher 结构体用于处理blackbox exporter指标推送
type BlackboxPusher struct {
	blackboxURL    string
	pushGatewayURL string
	jobName        string
	instanceLabel  string
	httpClient     *http.Client
}

// NewBlackboxPusher 创建新的BlackboxPusher实例
func NewBlackboxPusher(blackboxURL, pushGatewayURL, jobName, instanceLabel string) *BlackboxPusher {
	return &BlackboxPusher{
		blackboxURL:    blackboxURL,
		pushGatewayURL: pushGatewayURL,
		jobName:        jobName,
		instanceLabel:  instanceLabel,
		httpClient: &http.Client{
			Timeout: 10 * time.Second,
		},
	}
}

// fetchAndPushMetrics 获取blackbox_exporter指标并推送到push gateway
func (bp *BlackboxPusher) fetchAndPushMetrics() error {
	start := time.Now()

	log.Printf("Fetching metrics from %s", bp.blackboxURL)

	// 1. 从blackbox_exporter获取指标
	resp, err := bp.httpClient.Get(bp.blackboxURL)
	if err != nil {
		return fmt.Errorf("failed to fetch metrics from blackbox exporter: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("blackbox exporter returned status %d", resp.StatusCode)
	}

	metricsData, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read metrics data: %w", err)
	}

	log.Printf("Successfully fetched %d bytes of metrics data", len(metricsData))

	// 2. 构建push gateway URL
	pushURL := fmt.Sprintf("%s/metrics/job/%s/instance/%s",
		bp.pushGatewayURL, bp.jobName, bp.instanceLabel)

	log.Printf("Pushing metrics to %s", pushURL)

	// 3. 推送指标到push gateway
	req, err := http.NewRequest("POST", pushURL, bytes.NewReader(metricsData))
	if err != nil {
		return fmt.Errorf("failed to create push request: %w", err)
	}

	req.Header.Set("Content-Type", "text/plain; version=0.0.4; charset=utf-8")

	pushResp, err := bp.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to push metrics to gateway: %w", err)
	}
	defer pushResp.Body.Close()

	if pushResp.StatusCode < 200 || pushResp.StatusCode >= 300 {
		body, _ := io.ReadAll(pushResp.Body)
		return fmt.Errorf("push gateway returned status %d: %s", pushResp.StatusCode, string(body))
	}

	log.Printf("Successfully pushed metrics to push gateway in %v", time.Since(start))
	return nil
}

// PushOnce 执行一次推送操作
func (bp *BlackboxPusher) PushOnce() error {
	return bp.fetchAndPushMetrics()
}

// StartPeriodicPush 开始周期性推送
func (bp *BlackboxPusher) StartPeriodicPush(interval time.Duration) {
	log.Printf("Starting periodic push every %v", interval)

	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	// 立即执行一次
	if err := bp.PushOnce(); err != nil {
		log.Printf("Initial push failed: %v", err)
	}

	// 周期性执行
	for range ticker.C {
		if err := bp.PushOnce(); err != nil {
			log.Printf("Periodic push failed: %v", err)
		}
	}
}

// DeleteMetrics 从push gateway删除指标
func (bp *BlackboxPusher) DeleteMetrics() error {
	deleteURL := fmt.Sprintf("%s/metrics/job/%s/instance/%s",
		bp.pushGatewayURL, bp.jobName, bp.instanceLabel)

	log.Printf("Deleting metrics from %s", deleteURL)

	req, err := http.NewRequest("DELETE", deleteURL, nil)
	if err != nil {
		return fmt.Errorf("failed to create delete request: %w", err)
	}

	resp, err := bp.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to delete metrics: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("delete request returned status %d: %s", resp.StatusCode, string(body))
	}

	log.Printf("Successfully deleted metrics from push gateway")
	return nil
}

// SetTimeout 设置HTTP客户端超时时间
func (bp *BlackboxPusher) SetTimeout(timeout time.Duration) {
	bp.httpClient.Timeout = timeout
}

// GetConfig 获取当前配置信息
func (bp *BlackboxPusher) GetConfig() map[string]string {
	return map[string]string{
		"blackbox_url":     bp.blackboxURL,
		"push_gateway_url": bp.pushGatewayURL,
		"job_name":         bp.jobName,
		"instance_label":   bp.instanceLabel,
		"timeout":          bp.httpClient.Timeout.String(),
	}
}
