package main

import (
	"flag"
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/push"
	dto "github.com/prometheus/client_model/go"
	"github.com/prometheus/common/expfmt"
)

var (
	blackboxURL     = flag.String("blackbox.url", "http://127.0.0.1:8080/metrics", "Blackbox exporter metrics URL")
	pushGatewayURL  = flag.String("pushgateway.url", "http://localhost:9091", "Push Gateway URL")
	jobName         = flag.String("job", "blackbox_exporter", "Job name for push gateway")
	pushInterval    = flag.Duration("interval", 30*time.Second, "Push interval")
	instanceLabel   = flag.String("instance", "127.0.0.1:8080", "Instance label for metrics")
	enableOnce      = flag.Bool("once", false, "Push once and exit")
)

type BlackboxPusher struct {
	blackboxURL    string
	pushGatewayURL string
	jobName        string
	instanceLabel  string
	httpClient     *http.Client
}

func NewBlackboxPusher(blackboxURL, pushGatewayURL, jobName, instanceLabel string) *BlackboxPusher {
	return &BlackboxPusher{
		blackboxURL:    blackboxURL,
		pushGatewayURL: pushGatewayURL,
		jobName:        jobName,
		instanceLabel:  instanceLabel,
		httpClient: &http.Client{
			Timeout: 10 * time.Second,
		},
	}
}

// fetchMetrics 从blackbox_exporter获取指标
func (bp *BlackboxPusher) fetchMetrics() (map[string]*dto.MetricFamily, error) {
	log.Printf("Fetching metrics from %s", bp.blackboxURL)
	
	resp, err := bp.httpClient.Get(bp.blackboxURL)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch metrics: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("blackbox exporter returned status %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// 解析Prometheus格式的指标
	parser := expfmt.TextParser{}
	metricFamilies, err := parser.TextToMetricFamilies(strings.NewReader(string(body)))
	if err != nil {
		return nil, fmt.Errorf("failed to parse metrics: %w", err)
	}

	log.Printf("Successfully fetched %d metric families", len(metricFamilies))
	return metricFamilies, nil
}

// pushMetrics 将指标推送到push gateway
func (bp *BlackboxPusher) pushMetrics(metricFamilies map[string]*dto.MetricFamily) error {
	log.Printf("Pushing metrics to %s", bp.pushGatewayURL)
	
	// 创建一个临时的registry来存储指标
	registry := prometheus.NewRegistry()
	
	// 将获取的指标转换为prometheus.Collector
	for name, mf := range metricFamilies {
		collector := newMetricFamilyCollector(name, mf)
		registry.MustRegister(collector)
	}

	// 创建pusher并推送指标
	pusher := push.New(bp.pushGatewayURL, bp.jobName).
		Gatherer(registry).
		Grouping("instance", bp.instanceLabel)

	if err := pusher.Push(); err != nil {
		return fmt.Errorf("failed to push metrics: %w", err)
	}

	log.Printf("Successfully pushed metrics to push gateway")
	return nil
}

// pushOnce 执行一次获取和推送操作
func (bp *BlackboxPusher) pushOnce() error {
	start := time.Now()
	
	// 获取指标
	metricFamilies, err := bp.fetchMetrics()
	if err != nil {
		return fmt.Errorf("failed to fetch metrics: %w", err)
	}

	// 推送指标
	if err := bp.pushMetrics(metricFamilies); err != nil {
		return fmt.Errorf("failed to push metrics: %w", err)
	}

	log.Printf("Push operation completed in %v", time.Since(start))
	return nil
}

// startPeriodicPush 开始周期性推送
func (bp *BlackboxPusher) startPeriodicPush(interval time.Duration) {
	log.Printf("Starting periodic push every %v", interval)
	
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	// 立即执行一次
	if err := bp.pushOnce(); err != nil {
		log.Printf("Initial push failed: %v", err)
	}

	// 周期性执行
	for range ticker.C {
		if err := bp.pushOnce(); err != nil {
			log.Printf("Periodic push failed: %v", err)
		}
	}
}

// metricFamilyCollector 实现prometheus.Collector接口
type metricFamilyCollector struct {
	name         string
	metricFamily *dto.MetricFamily
}

func newMetricFamilyCollector(name string, mf *dto.MetricFamily) *metricFamilyCollector {
	return &metricFamilyCollector{
		name:         name,
		metricFamily: mf,
	}
}

func (mfc *metricFamilyCollector) Describe(ch chan<- *prometheus.Desc) {
	// 为每个指标创建描述
	help := ""
	if mfc.metricFamily.Help != nil {
		help = *mfc.metricFamily.Help
	}
	
	// 获取标签名称
	var labelNames []string
	if len(mfc.metricFamily.Metric) > 0 {
		for _, label := range mfc.metricFamily.Metric[0].Label {
			if label.Name != nil {
				labelNames = append(labelNames, *label.Name)
			}
		}
	}
	
	desc := prometheus.NewDesc(mfc.name, help, labelNames, nil)
	ch <- desc
}

func (mfc *metricFamilyCollector) Collect(ch chan<- prometheus.Metric) {
	// 将dto.MetricFamily转换为prometheus.Metric
	for _, metric := range mfc.metricFamily.Metric {
		// 提取标签
		labels := make(prometheus.Labels)
		var labelNames []string
		var labelValues []string
		
		for _, label := range metric.Label {
			if label.Name != nil && label.Value != nil {
				labels[*label.Name] = *label.Value
				labelNames = append(labelNames, *label.Name)
				labelValues = append(labelValues, *label.Value)
			}
		}

		// 创建描述
		help := ""
		if mfc.metricFamily.Help != nil {
			help = *mfc.metricFamily.Help
		}
		desc := prometheus.NewDesc(mfc.name, help, labelNames, nil)

		// 根据指标类型创建相应的prometheus.Metric
		switch mfc.metricFamily.GetType() {
		case dto.MetricType_COUNTER:
			if metric.Counter != nil && metric.Counter.Value != nil {
				ch <- prometheus.MustNewConstMetric(desc, prometheus.CounterValue, *metric.Counter.Value, labelValues...)
			}
		case dto.MetricType_GAUGE:
			if metric.Gauge != nil && metric.Gauge.Value != nil {
				ch <- prometheus.MustNewConstMetric(desc, prometheus.GaugeValue, *metric.Gauge.Value, labelValues...)
			}
		case dto.MetricType_HISTOGRAM:
			if metric.Histogram != nil {
				// 处理直方图指标
				buckets := make(map[float64]uint64)
				for _, bucket := range metric.Histogram.Bucket {
					if bucket.UpperBound != nil && bucket.CumulativeCount != nil {
						buckets[*bucket.UpperBound] = *bucket.CumulativeCount
					}
				}
				
				count := uint64(0)
				sum := 0.0
				if metric.Histogram.SampleCount != nil {
					count = *metric.Histogram.SampleCount
				}
				if metric.Histogram.SampleSum != nil {
					sum = *metric.Histogram.SampleSum
				}
				
				ch <- prometheus.MustNewConstHistogram(desc, count, sum, buckets, labelValues...)
			}
		case dto.MetricType_SUMMARY:
			if metric.Summary != nil {
				quantiles := make(map[float64]float64)
				for _, quantile := range metric.Summary.Quantile {
					if quantile.Quantile != nil && quantile.Value != nil {
						quantiles[*quantile.Quantile] = *quantile.Value
					}
				}
				
				count := uint64(0)
				sum := 0.0
				if metric.Summary.SampleCount != nil {
					count = *metric.Summary.SampleCount
				}
				if metric.Summary.SampleSum != nil {
					sum = *metric.Summary.SampleSum
				}
				
				ch <- prometheus.MustNewConstSummary(desc, count, sum, quantiles, labelValues...)
			}
		}
	}
}

func main() {
	flag.Parse()

	log.Printf("Starting Blackbox Exporter Pusher")
	log.Printf("Blackbox URL: %s", *blackboxURL)
	log.Printf("Push Gateway URL: %s", *pushGatewayURL)
	log.Printf("Job Name: %s", *jobName)
	log.Printf("Instance Label: %s", *instanceLabel)

	pusher := NewBlackboxPusher(*blackboxURL, *pushGatewayURL, *jobName, *instanceLabel)

	if *enableOnce {
		log.Printf("Running in one-time mode")
		if err := pusher.pushOnce(); err != nil {
			log.Fatalf("Push failed: %v", err)
		}
		log.Printf("One-time push completed successfully")
		return
	}

	log.Printf("Running in periodic mode with interval: %v", *pushInterval)
	pusher.startPeriodicPush(*pushInterval)
}
