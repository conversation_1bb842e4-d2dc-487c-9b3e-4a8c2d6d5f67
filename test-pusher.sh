#!/bin/bash

echo "Testing Blackbox Exporter Pusher..."

# 检查是否有Go环境
if ! command -v go &> /dev/null; then
    echo "Go is not installed. Please install Go first."
    exit 1
fi

# 编译simple-pusher
echo "Building simple-pusher..."
cd simple-pusher
go build -o pusher main.go

if [ $? -eq 0 ]; then
    echo "✅ Simple pusher built successfully"
else
    echo "❌ Failed to build simple pusher"
    exit 1
fi

# 显示帮助信息
echo ""
echo "Pusher help:"
./pusher -h

echo ""
echo "✅ Build completed successfully!"
echo ""
echo "To test the pusher, you can run:"
echo "  cd simple-pusher"
echo "  ./pusher --blackbox.url=http://127.0.0.1:8080/metrics --pushgateway.url=http://localhost:9091 --once"
echo ""
echo "Or use Docker Compose to start the full stack:"
echo "  ./start.sh"
