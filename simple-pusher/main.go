package main

import (
	"bytes"
	"flag"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"
)

var (
	blackboxURL     = flag.String("blackbox.url", "http://127.0.0.1:8080/metrics", "Blackbox exporter metrics URL")
	pushGatewayURL  = flag.String("pushgateway.url", "http://localhost:9091", "Push Gateway URL")
	jobName         = flag.String("job", "blackbox_exporter", "Job name for push gateway")
	pushInterval    = flag.Duration("interval", 30*time.Second, "Push interval")
	instanceLabel   = flag.String("instance", "127.0.0.1:8080", "Instance label for metrics")
	enableOnce      = flag.Bool("once", false, "Push once and exit")
)

type SimplePusher struct {
	blackboxURL    string
	pushGatewayURL string
	jobName        string
	instanceLabel  string
	httpClient     *http.Client
}

func NewSimplePusher(blackboxURL, pushGatewayURL, jobName, instanceLabel string) *SimplePusher {
	return &SimplePusher{
		blackboxURL:    blackboxURL,
		pushGatewayURL: pushGatewayURL,
		jobName:        jobName,
		instanceLabel:  instanceLabel,
		httpClient: &http.Client{
			Timeout: 10 * time.Second,
		},
	}
}

// fetchAndPushMetrics 获取blackbox_exporter指标并直接推送到push gateway
func (sp *SimplePusher) fetchAndPushMetrics() error {
	start := time.Now()
	
	log.Printf("Fetching metrics from %s", sp.blackboxURL)
	
	// 1. 从blackbox_exporter获取指标
	resp, err := sp.httpClient.Get(sp.blackboxURL)
	if err != nil {
		return fmt.Errorf("failed to fetch metrics from blackbox exporter: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("blackbox exporter returned status %d", resp.StatusCode)
	}

	metricsData, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read metrics data: %w", err)
	}

	log.Printf("Successfully fetched %d bytes of metrics data", len(metricsData))

	// 2. 构建push gateway URL
	pushURL := fmt.Sprintf("%s/metrics/job/%s/instance/%s", 
		sp.pushGatewayURL, sp.jobName, sp.instanceLabel)
	
	log.Printf("Pushing metrics to %s", pushURL)

	// 3. 推送指标到push gateway
	req, err := http.NewRequest("POST", pushURL, bytes.NewReader(metricsData))
	if err != nil {
		return fmt.Errorf("failed to create push request: %w", err)
	}
	
	req.Header.Set("Content-Type", "text/plain; version=0.0.4; charset=utf-8")

	pushResp, err := sp.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to push metrics to gateway: %w", err)
	}
	defer pushResp.Body.Close()

	if pushResp.StatusCode < 200 || pushResp.StatusCode >= 300 {
		body, _ := io.ReadAll(pushResp.Body)
		return fmt.Errorf("push gateway returned status %d: %s", pushResp.StatusCode, string(body))
	}

	log.Printf("Successfully pushed metrics to push gateway in %v", time.Since(start))
	return nil
}

// pushOnce 执行一次推送操作
func (sp *SimplePusher) pushOnce() error {
	return sp.fetchAndPushMetrics()
}

// startPeriodicPush 开始周期性推送
func (sp *SimplePusher) startPeriodicPush(interval time.Duration) {
	log.Printf("Starting periodic push every %v", interval)
	
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	// 立即执行一次
	if err := sp.pushOnce(); err != nil {
		log.Printf("Initial push failed: %v", err)
	}

	// 周期性执行
	for range ticker.C {
		if err := sp.pushOnce(); err != nil {
			log.Printf("Periodic push failed: %v", err)
		}
	}
}

// deleteMetrics 从push gateway删除指标
func (sp *SimplePusher) deleteMetrics() error {
	deleteURL := fmt.Sprintf("%s/metrics/job/%s/instance/%s", 
		sp.pushGatewayURL, sp.jobName, sp.instanceLabel)
	
	log.Printf("Deleting metrics from %s", deleteURL)

	req, err := http.NewRequest("DELETE", deleteURL, nil)
	if err != nil {
		return fmt.Errorf("failed to create delete request: %w", err)
	}

	resp, err := sp.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to delete metrics: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("delete request returned status %d: %s", resp.StatusCode, string(body))
	}

	log.Printf("Successfully deleted metrics from push gateway")
	return nil
}

func main() {
	flag.Parse()

	log.Printf("Starting Simple Blackbox Exporter Pusher")
	log.Printf("Blackbox URL: %s", *blackboxURL)
	log.Printf("Push Gateway URL: %s", *pushGatewayURL)
	log.Printf("Job Name: %s", *jobName)
	log.Printf("Instance Label: %s", *instanceLabel)

	pusher := NewSimplePusher(*blackboxURL, *pushGatewayURL, *jobName, *instanceLabel)

	if *enableOnce {
		log.Printf("Running in one-time mode")
		if err := pusher.pushOnce(); err != nil {
			log.Fatalf("Push failed: %v", err)
		}
		log.Printf("One-time push completed successfully")
		return
	}

	log.Printf("Running in periodic mode with interval: %v", *pushInterval)
	pusher.startPeriodicPush(*pushInterval)
}
