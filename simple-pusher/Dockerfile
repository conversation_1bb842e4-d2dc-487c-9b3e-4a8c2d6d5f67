FROM golang:1.19-alpine AS builder

WORKDIR /app
COPY . .
RUN go mod tidy
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o pusher main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates tzdata
WORKDIR /root/

COPY --from=builder /app/pusher .

# 设置默认参数
ENV BLACKBOX_URL="http://127.0.0.1:8080/metrics"
ENV PUSHGATEWAY_URL="http://localhost:9091"
ENV JOB_NAME="blackbox_exporter"
ENV INSTANCE_LABEL="127.0.0.1:8080"
ENV PUSH_INTERVAL="30s"

CMD ["sh", "-c", "./pusher --blackbox.url=${BLACKBOX_URL} --pushgateway.url=${PUSHGATEWAY_URL} --job=${JOB_NAME} --instance=${INSTANCE_LABEL} --interval=${PUSH_INTERVAL}"]
