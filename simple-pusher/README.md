# Blackbox Exporter Pusher

这是一个简单的pusher程序，用于从本地的blackbox_exporter获取指标并推送到远端的push-gateway。

## 功能特性

- 从指定的blackbox_exporter URL获取Prometheus格式的指标
- 将指标推送到远端的push-gateway
- 支持周期性推送和一次性推送
- 支持自定义job名称和instance标签
- 包含错误处理和日志记录

## 使用方法

### 编译程序

```bash
cd simple-pusher
go build -o pusher main.go
```

### 运行参数

- `--blackbox.url`: Blackbox exporter的metrics URL (默认: http://127.0.0.1:8080/metrics)
- `--pushgateway.url`: Push Gateway的URL (默认: http://localhost:9091)
- `--job`: Push Gateway中的job名称 (默认: blackbox_exporter)
- `--instance`: 实例标签 (默认: 127.0.0.1:8080)
- `--interval`: 推送间隔 (默认: 30s)
- `--once`: 只推送一次然后退出 (默认: false)

### 使用示例

#### 1. 周期性推送（每30秒推送一次）

```bash
./pusher \
  --blackbox.url=http://127.0.0.1:8080/metrics \
  --pushgateway.url=http://your-pushgateway:9091 \
  --job=blackbox_exporter \
  --instance=127.0.0.1:8080 \
  --interval=30s
```

#### 2. 一次性推送

```bash
./pusher \
  --blackbox.url=http://127.0.0.1:8080/metrics \
  --pushgateway.url=http://your-pushgateway:9091 \
  --job=blackbox_exporter \
  --instance=127.0.0.1:8080 \
  --once
```

#### 3. 自定义推送间隔（每1分钟推送一次）

```bash
./pusher \
  --blackbox.url=http://127.0.0.1:8080/metrics \
  --pushgateway.url=http://your-pushgateway:9091 \
  --job=my_blackbox_job \
  --instance=my-instance \
  --interval=1m
```

## 工作原理

1. **获取指标**: 程序会向blackbox_exporter的`/metrics`端点发送HTTP GET请求
2. **推送指标**: 将获取到的Prometheus格式指标直接POST到push-gateway的API端点
3. **周期执行**: 如果不是一次性模式，程序会按指定间隔重复执行上述操作

## Push Gateway API

程序使用Push Gateway的标准API：
```
POST /metrics/job/<job_name>/instance/<instance_name>
```

## 日志输出

程序会输出详细的日志信息，包括：
- 启动参数
- 指标获取状态
- 推送操作结果
- 错误信息

## 错误处理

- 网络连接错误会被记录但不会终止程序（周期性模式）
- HTTP错误状态码会被详细记录
- 一次性模式下的错误会导致程序退出

## 系统要求

- Go 1.19+
- 网络访问blackbox_exporter和push-gateway

## 注意事项

1. 确保blackbox_exporter在指定地址运行并可访问
2. 确保push-gateway在指定地址运行并可访问
3. 推送的指标会在push-gateway中保留，直到被新的推送覆盖或手动删除
4. 建议在生产环境中使用适当的监控和告警机制
