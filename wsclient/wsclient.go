package wsclient

import (
	"boce-client/metrics"
	"boce-client/pushgateway"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"os"
	"os/signal"
	"path/filepath"
	"strings"
	"time"
)

type FileMessage struct {
	Type      string            `json:"type"`
	Files     []FileData        `json:"files,omitempty"`
	Auth      *AuthData         `json:"auth,omitempty"`
	Metadata  map[string]string `json:"metadata,omitempty"`
	Timestamp int64             `json:"timestamp"`
}

type FileData struct {
	Path    string `json:"path"`
	Content string `json:"content"`
	Hash    string `json:"hash"`
}

type AuthData struct {
	Token    string `json:"token"`
	ClientID string `json:"client_id"`
	Version  string `json:"version"`
}

type WSClient struct {
	conn      *websocket.Conn
	clientID  string
	version   string
	serverURL string
	done      chan struct{}
}

func BoceWsClient(serverURL string, clientID string, wsVersion string) {

	client := &WSClient{
		version:   wsVersion,
		serverURL: serverURL,
		clientID:  clientID,
		done:      make(chan struct{}),
	}

	// 处理中断信号
	interrupt := make(chan os.Signal, 1)
	signal.Notify(interrupt, os.Interrupt)

	// 启动客户端
	go client.run()

	// 等待中断信号
	<-interrupt
	log.Println("收到中断信号，正在关闭...")
	close(client.done)

	if client.conn != nil {
		client.conn.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseNormalClosure, ""))
		client.conn.Close()
	}
}
func (c *WSClient) run() {
	for {
		select {
		case <-c.done:
			return
		default:
			c.connect()
			time.Sleep(5 * time.Second) // 重连间隔
		}
	}
}

func (c *WSClient) connect() {
	u, err := url.Parse(c.serverURL)
	if err != nil {
		log.Printf("解析URL失败: %v", err)
		return
	}

	// 先进行节点注册
	if err := c.autoRegister(); err != nil {
		log.Printf("节点注册失败: %v", err)
		// 注册失败不阻断WebSocket连接，继续尝试连接
	}

	log.Printf("连接到 %s", c.serverURL)
	conn, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		log.Printf("连接失败: %v", err)
		return
	}
	defer conn.Close()

	c.conn = conn

	// 发送认证信息
	if err := c.authenticate(); err != nil {
		log.Printf("认证失败: %v", err)
		return
	}

	// 启动消息处理
	done := make(chan struct{})
	go c.readMessages(done)

	// 心跳
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-done:
			return
		case <-c.done:
			return
		case <-ticker.C:
			if err := conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				log.Printf("发送心跳失败: %v", err)
				return
			}
		}
	}
}

func (c *WSClient) authenticate() error {
	token := c.generateToken()
	authMsg := FileMessage{
		Type: "auth",
		Auth: &AuthData{
			Token:    token,
			ClientID: c.clientID,
			Version:  c.version,
		},
		Timestamp: time.Now().Unix(),
	}

	data, _ := json.Marshal(authMsg)
	return c.conn.WriteMessage(websocket.TextMessage, data)
}

func (c *WSClient) generateToken() string {
	hash := md5.Sum([]byte(c.clientID + "secret_key"))
	return hex.EncodeToString(hash[:])
}

func (c *WSClient) readMessages(done chan struct{}) {
	defer close(done)

	for {
		_, message, err := c.conn.ReadMessage()
		if err != nil {
			log.Printf("读取消息失败: %v", err)
			return
		}

		var msg FileMessage
		if err := json.Unmarshal(message, &msg); err != nil {
			log.Printf("解析消息失败: %v", err)
			continue
		}

		c.handleMessage(msg)
	}
}

func (c *WSClient) handleMessage(msg FileMessage) {
	switch msg.Type {
	case "auth_success":
		log.Printf("认证成功, 版本: %s, 客户端: %s, 客户端IP: %s", c.version, c.clientID, c.conn.LocalAddr().String())
	case "auth_failed":
		log.Printf("认证失败")
	case "file_push":
		log.Printf("收到文件推送，文件数量: %d", len(msg.Files))
		c.handleFilePush(msg.Files, msg.Metadata)
	}
}

func (c *WSClient) handleFilePush(files []FileData, metadata map[string]string) {
	for _, file := range files {
		if err := c.saveFile(file); err != nil {
			log.Printf("保存文件失败 %s: %v", file.Path, err)
		} else {
			log.Printf("文件保存成功: %s (hash: %s)", file.Path, file.Hash)
		}
	}

	if metadata != nil {
		log.Printf("元数据: %+v", metadata)
	}
}

func (c *WSClient) saveFile(file FileData) error {
	// 确保目录存在
	dir := filepath.Dir(file.Path)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建目录失败: %v", err)
	}

	// 验证文件hash
	hash := md5.Sum([]byte(file.Content))
	expectedHash := hex.EncodeToString(hash[:])
	if expectedHash != file.Hash {
		return fmt.Errorf("文件hash不匹配")
	}

	// 写入文件
	return os.WriteFile(file.Path, []byte(file.Content), 0644)
}

// 添加自动注册功能
func (c *WSClient) autoRegister() error {
	log.Println("开始自动注册节点...")

	// 构造注册请求
	registerURL := fmt.Sprintf("http://%s/api/ip/register", c.getServerHost())

	req, err := http.NewRequest("POST", registerURL, nil)
	if err != nil {
		return fmt.Errorf("创建注册请求失败: %v", err)
	}

	// 添加IP参数（让服务端使用指定IP而不是连接IP）
	q := req.URL.Query()
	q.Add("ip", c.clientID)
	req.URL.RawQuery = q.Encode()

	// 发送注册请求
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送注册请求失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("注册失败，状态码: %d", resp.StatusCode)
	}

	var result struct {
		ClientIP string `json:"client_ip"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return fmt.Errorf("解析注册响应失败: %v", err)
	}

	log.Printf("节点注册成功，IP: %s", result.ClientIP)
	return nil
}

// 获取服务器主机地址
func (c *WSClient) getServerHost() string {
	serverURL := c.serverURL
	var serverHost string
	// 从WebSocket URL提取HTTP地址
	if strings.HasPrefix(serverURL, "ws://") {
		serverHost = strings.TrimPrefix(serverURL, "ws://")
	} else if strings.HasPrefix(serverURL, "wss://") {
		serverHost = strings.TrimPrefix(serverURL, "wss://")
	}

	// 移除 /ws 路径后缀
	if strings.HasSuffix(serverHost, "/ws") {
		serverHost = strings.TrimSuffix(serverHost, "/ws")
	}

	return serverHost
}
