version: '3.8'

services:
  # Blackbox Exporter
  blackbox-exporter:
    image: prom/blackbox-exporter:latest
    container_name: blackbox-exporter
    ports:
      - "8080:9115"
    volumes:
      - ./config/blackbox_exporter.yaml:/etc/blackbox_exporter/config.yml
    command:
      - '--config.file=/etc/blackbox_exporter/config.yml'
      - '--web.listen-address=:9115'
    networks:
      - monitoring

  # Push Gateway
  pushgateway:
    image: prom/pushgateway:latest
    container_name: pushgateway
    ports:
      - "9091:9091"
    networks:
      - monitoring

  # Our Pusher Service
  blackbox-pusher:
    build:
      context: ./simple-pusher
      dockerfile: Dockerfile
    container_name: blackbox-pusher
    environment:
      - BLACKBOX_URL=http://blackbox-exporter:9115/metrics
      - PUSHGATEWAY_URL=http://pushgateway:9091
      - JOB_NAME=blackbox_exporter
      - INSTANCE_LABEL=blackbox-exporter:9115
      - PUSH_INTERVAL=30s
    depends_on:
      - blackbox-exporter
      - pushgateway
    networks:
      - monitoring
    restart: unless-stopped

  # Optional: Prometheus to scrape from Push Gateway
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - monitoring
    depends_on:
      - pushgateway

networks:
  monitoring:
    driver: bridge
