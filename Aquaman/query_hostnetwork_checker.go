package main

import (
	"Aquaman/common"
	"context"
	"flag"
	"fmt"
	"time"

	"encoding/json"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/util/homedir"
	"path/filepath"
)

// 定义要忽略的命名空间和项目名称
var (
	ignoreNamespaces   = []string{"kube-system", "arms-prom"}
	ignoreProjectNames = []string{"prometheus-node-exporter", "s2s-agent-helper", "s2s-agent-pub", "s2s-agent-sub",
		"pod-sniff", "udbyy-agentserver-daemonset", "promtail"}
)

type ProjectAllData struct {
	Data []ProjectData `json:"data"`
	Code int           `json:"code"`
	Msg  string        `json:"msg"`
}

type ProjectData struct {
	Owner         string `json:"Owner"`
	ProductName   string `json:"ProductName"`
	ProjectName   string `json:"ProjectName"`
	BussModelName string `json:"BussModelName"`
}

func ReqProjectData(projectName string, productName string) (data ProjectAllData) {
	url := "http://ws.aquaman.sysop.yy.com/ws/aquaman/project/listProject"

	reqParams := map[string]string{
		"projectName": projectName,
		"productName": productName,
	}
	headers := map[string]string{
		"User-Agent":      "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.116 Safari/537.36",
		"operator":        "dw_wuyiwen",
		"Accept":          "*/*",
		"Accept-Encoding": "gzip, deflate, br",
		"Connection":      "keep-alive",
		"Content-Type":    "application/json",
	}
	cookies := map[string]string{}
	var response = common.Get(url, reqParams, headers, cookies)

	err := json.Unmarshal([]byte(response), &data)
	if err != nil {
		fmt.Println(err)
	}
	return
}

func main() {
	// 解析命令行参数
	kubeconfig := flag.String("kubeconfig", "", "absolute path to the kubeconfig file")
	flag.Parse()

	// 如果没有指定kubeconfig，则使用默认路径
	if *kubeconfig == "" {
		if home := homedir.HomeDir(); home != "" {
			*kubeconfig = filepath.Join(home, ".kube", "config")
		}
	}

	// 构建配置
	config, err := clientcmd.BuildConfigFromFlags("", *kubeconfig)
	if err != nil {
		panic(err.Error())
	}

	// 创建客户端集
	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		panic(err.Error())
	}

	// 1. 获取所有 label nat-gateway!=true 的节点
	targetNodes, err := getNodesWithoutNatGateway(clientset)
	if err != nil {
		panic(err.Error())
	}
	if len(targetNodes) == 0 {
		fmt.Println("没有找到 label nat-gateway!=true 的节点。")
		return
	}

	fmt.Printf("找到以下 label nat-gateway!=true 的节点: %v\n", targetNodes)

	// 2. 在这些节点上查询 hostNetwork: true 的 Pod
	hostNetworkProjects, err := getHostNetworkPodsInNodes(clientset, targetNodes)
	if err != nil {
		panic(err.Error())
	}

	if len(hostNetworkProjects) > 0 {
		fmt.Println("找到以下 hostNetwork label nat-gateway!=true 的项目:")
		for _, project := range hostNetworkProjects {
			owner, bussModelName := getPodOwner(project.ProjectName, project.Namespace)
			fmt.Printf("%s,%s,%s,%s,%s\n",
				project.Namespace,
				project.ProjectName,
				project.DeploymentName,
				owner,
				bussModelName)
		}
	} else {
		fmt.Println("未找到 hostNetwork: true 的 Pod。")
	}
}

// NodeInfo 存储节点信息
type NodeInfo struct {
	Name string
}

// HostNetworkProject 存储项目信息
type HostNetworkProject struct {
	Namespace      string
	ProjectName    string
	DeploymentName string
}

// getNodesWithoutNatGateway 获取所有 label nat-gateway!=true 的节点
func getNodesWithoutNatGateway(clientset *kubernetes.Clientset) ([]NodeInfo, error) {
	nodes, err := clientset.CoreV1().Nodes().List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		return nil, err
	}

	var targetNodes []NodeInfo
	for _, node := range nodes.Items {
		labels := node.GetLabels()
		if labels["nat-gateway"] != "true" && labels["type"] != "virtual-kubelet" {
			targetNodes = append(targetNodes, NodeInfo{Name: node.Name})
		}
	}
	return targetNodes, nil
}

// getHostNetworkPodsInNodes 在指定节点上查询 hostNetwork: true 的 Pod
func getHostNetworkPodsInNodes(clientset *kubernetes.Clientset, nodeNames []NodeInfo) ([]HostNetworkProject, error) {
	var hostNetworkProjects []HostNetworkProject

	// 为了避免API调用过于频繁，添加延迟
	time.Sleep(3 * time.Second)

	for _, node := range nodeNames {
		// 使用字段选择器查询指定节点上的Pod
		pods, err := clientset.CoreV1().Pods("").List(context.TODO(), metav1.ListOptions{
			FieldSelector: fmt.Sprintf("spec.nodeName=%s", node.Name),
		})
		if err != nil {
			return nil, err
		}

		for _, pod := range pods.Items {
			// 检查是否在忽略的命名空间中
			if contains(ignoreNamespaces, pod.Namespace) {
				continue
			}

			// 获取项目名称标签
			projectName := pod.Labels["project-name"]
			// 如果项目名称在忽略列表中或不存在，则跳过
			if contains(ignoreProjectNames, projectName) || projectName == "" {
				continue
			}

			// 检查其他必要的标签是否存在
			if pod.Labels["deployment-name"] == "" {
				continue
			}
			if contains(ignoreProjectNames, pod.Labels["app.kubernetes.io/name"]) {
				continue
			}
			if contains(ignoreProjectNames, pod.Labels["k8s-app"]) {
				continue
			}

			// 检查是否为hostNetwork
			if pod.Spec.HostNetwork {
				hostNetworkProjects = append(hostNetworkProjects, HostNetworkProject{
					Namespace:      pod.Namespace,
					ProjectName:    projectName,
					DeploymentName: pod.Labels["deployment-name"],
				})
			}
		}
	}

	// 去重
	hostNetworkProjects = removeDuplicatesHostNetworkProjects(hostNetworkProjects)
	return hostNetworkProjects, nil
}

// removeDuplicatesHostNetworkProjects 去重HostNetworkProject列表
func removeDuplicatesHostNetworkProjects(projects []HostNetworkProject) []HostNetworkProject {
	seen := make(map[string]bool)
	var uniqueProjects []HostNetworkProject

	for _, project := range projects {
		key := fmt.Sprintf("%s-%s-%s", project.Namespace, project.ProjectName, project.DeploymentName)
		if !seen[key] {
			seen[key] = true
			uniqueProjects = append(uniqueProjects, project)
		}
	}
	return uniqueProjects
}

// contains 检查字符串是否在切片中
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// getPodOwner 获取Pod的拥有者和业务模型名称
func getPodOwner(projectName, productName string) (string, string) {
	if projectName == "" || productName == "" {
		return "", ""
	}

	data := ReqProjectData(projectName, productName)
	if len(data.Data) == 0 {
		return "", ""
	}

	return data.Data[0].Owner, data.Data[0].BussModelName
}
