package main

import (
	"context"
	"crypto/tls"
	"fmt"
	"net/http"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
)

// 自定义 RoundTripper，用于设置 Host 头部
type customHostRoundTripper struct {
	underlyingRoundTripper http.RoundTripper
	hostHeader             string
}

func (c *customHostRoundTripper) RoundTrip(req *http.Request) (*http.Response, error) {
	// 克隆请求（避免修改原始请求）
	req = req.Clone(req.Context())
	// 设置 Host 头部
	req.Host = c.hostHeader
	// 调用底层的 RoundTripper
	return c.underlyingRoundTripper.RoundTrip(req)
}

func main() {
	// 1. 加载 Kubernetes 配置（in-cluster 或 kubeconfig）
	config := rest.Config{
		Host: "https://***********:6443", // 设置API Server地址
		// 移除 TLSClientConfig，因为使用自定义 Transport
	}

	// 创建带有TLS配置的自定义Transport
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: true, // 跳过TLS验证
		},
	}

	// 创建自定义 RoundTripper 用于设置 Host 头部
	customRT := &customHostRoundTripper{
		underlyingRoundTripper: transport,
		hostHeader:             "kubeyy.com",
	}

	// 将自定义 RoundTripper 设置到 config 中
	config.Transport = customRT

	// 2. 创建 Kubernetes Clientset
	clientset, err := kubernetes.NewForConfig(&config)
	if err != nil {
		panic(err.Error())
	}

	// 3. 获取所有节点
	svc, err := clientset.CoreV1().Services("default").Get(context.Background(), "kubernetes", metav1.GetOptions{})
	if err != nil {
		panic(err.Error())
	}
	svcIp := svc.Spec.ClusterIP

	fmt.Printf("Selected service : %s\n", svcIp)
}
