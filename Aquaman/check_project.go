package main

import (
	"Ops-cmd/common"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"github.com/urfave/cli"
	"io/ioutil"
	"log"
	"os"
	"strings"
)

// kubectl get po -A -L op-admin,project-name |grep Running |awk '{if ($8 != "") print $1","$8","$7}'|grep -vE "(global-udb|prometheus-monitor)"|sort |uniq
// 请使用以上命令导出旧集群pod列表

var cookieString  string = "hd_newui=0.742998063513238; hiido_ui=0.740189704659443; sysop_privilege_user_id=50048626; sysop_privilege_global_user_id=50048626; Hm_lvt_c493393610cdccbddc1f124d567e36ab=1682327938; sysop_privilege_nick_name=%25E5%2590%25B4%25E6%25AF%2585%25E6%2596%2587; sysop_privilege_yy_no=909047626; sysop_privilege_idcode=C13A76E21A59E4A32703B5CB29FC52677E25ECEB; udb_c=; isFirst=0; oauthCookie=; yyuid=50048626; aquamansessionID=e6032890d0cab2057fd1a1e5774ee87e; _ga=GA1.1.1571447491.1688443456; sysop_privilege_user_name=dw_wuyiwen; username=dw_wuyiwen; ab_sr=1.0.1_N2M1MDQxMmQzZDI5OTc3OThmYmNjYjhhMjJmOTg4MmE5NTUxNjQ5OWJiOWVkMDI0ZGNjNzdjMWQ2MjZhM2FlODE0MGE5Njc2NjhjM2M1ZmNmMTY0MjIxYTNjYTIxN2QxZjc5ODM0ODcwZjgyYmU3NDAzOTYzMGVhN2E5ODRjZGYyNjNjY2E3ZDgxMjNiYTRkNjI1NjFhZTkzY2VkZDZjOA==; password=A3610244345655E5F626A9027F5D7A7634E48A0C; osinfo=3C59F6E2D903935E82E2101B29E5B5B882FE7D0A; udb_oar=3AB50F36CD8EEAD1AD41EBABFF6C15EFD09B06EC3A2DD6E3F06F280A5EA47EF7FCDA1D5CDE68D767884779BFD50965CC8C346BE9AE4FFD14BEE91B5575AE26A43280488ABA89E12D77F9C7F1E54D3C4DC6C9476439321B0B80BF0476E4687144F579E96AAAFE17332EAB8E50EC78B2C3BD83B2175EC34D3650D4D4B0370A1D974D94997ABB593BEC11F9B2812C458B94D909B97DDB610A29904AEC8E67BB6E5BAFF71EFE133D07F8AF8EBAFDF867BAACF88B505D31638038E51F38C5C27B966337BBB9EDAC9DAA30CDD2591922376BCAF6EFDAD6EE57D852B4C99CCAE542D1C1261715D7ED8C98ECA437CC663233145235A7643A53E3C6F5B5A56922BCDEBE8D84E3456474D24ADA3FA697E76B74CB0CC42516AA8394450F21ECB1A9521D2B9C7098D5F2E7FDF72CEE5AC56CD316C4A3249501E107CE47E9BE743AE8E8A00DF8; write_cookie_url=; success_url=; JSESSIONID=1049D312DDF05BD39F0399D5C7D70A3E; _ga_2MFWXK7WYT=GS1.1.1690874647.1.0.1690874647.0.0.0; _ga_DM9497FN4V=GS1.1.1690942465.5.0.1690942465.60.0.0; sysop_udboauthtmptokensec=840B303DAA304B55838F6D923DCFAB25E99C11BB8A067B6F54255F634D4DF263A67BB224B567A336FD7BCAA79BB2A7E2"




type ListDeploymentAllData struct {
	Data []ListDeploymentData `json:"data"`
	Code int `json:"code"`
	Msg string `json:"msg"`
}

type ListDeploymentData struct {
	DeploymentName string `json:"deploymentName"`
	ProductName  string `json:"productName"`
	ClusterName string `json:"clusterName"`
	ProjectName string `json:"projectName"`
	DeployType string `json:"deployType"`
	Status int `json:"status"`
}

type DeploymentAllData struct {
	Data DeploymentData `json:"data"`
	Code int `json:"code"`
	Msg string `json:"msg"`
}

type DeploymentData struct {
	AllCount string `json:"allCount"`
	Replicas string `json:"replicas"`
	Result string `json:"result"`
	Running string `json:"running"`
}


func ReqListDeloyData(projectName string, productName string) (data ListDeploymentAllData)  {
	url := "http://ws.aquaman.sysop.yy.com/ws/aquaman/deployment/listDeployment"
	cookieSlice := strings.Split(cookieString, ";")
	cookies := make(map[string]string)
	for _, v := range cookieSlice{
		ck := strings.Split(v, "=")[0]
		cv := strings.Split(v, "=")[1]
		cookies[ck] = cv
	}
	reqParams := map[string]string{
		"projectName": projectName,
		"productName": productName,
	}
	headers := map[string]string{
		"User-Agent": "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.116 Safari/537.36",
		"operator": "dw_wuyiwen",
		"Accept": "*/*",
		"Accept-Encoding": "gzip, deflate, br",
		"Connection": "keep-alive",
		"Content-Type": "application/json",
	}
	var response = common.Get(url, reqParams, headers, cookies)

	err := json.Unmarshal([]byte(response), &data)
	if err != nil {
		fmt.Println(err)
	}
	return
}


func ReqDeploymentData(projectName string, deploymentName string) (data DeploymentAllData)  {
	url := "http://ws.aquaman.sysop.yy.com/ws/aquaman/deployment/status"
	cookieSlice := strings.Split(cookieString, ";")
	cookies := make(map[string]string)
	for _, v := range cookieSlice{
		ck := strings.Split(v, "=")[0]
		cv := strings.Split(v, "=")[1]
		cookies[ck] = cv
	}
	reqParams := map[string]string{
		"projectName": projectName,
		"deploymentName": deploymentName,
	}
	headers := map[string]string{
		"User-Agent": "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.116 Safari/537.36",
		"operator": "dw_wuyiwen",
		"Accept": "*/*",
		"Accept-Encoding": "gzip, deflate, br",
		"Connection": "keep-alive",
		"Content-Type": "application/json",
	}
	var response = common.Get(url, reqParams, headers, cookies)

	err := json.Unmarshal([]byte(response), &data)
	if err != nil {
		fmt.Println(err)
	}
	return
}

func JugeCluster(ClusterName string, targetClusterName string) (Result bool)  {
	if ClusterName == targetClusterName{
		return true
	}
	return false
}

func ReadCsv(fileName string) [][]string {
	cntb, err := ioutil.ReadFile( fileName)
	if err != nil {
		fmt.Println(err)
	}
	r2 := csv.NewReader(strings.NewReader(string(cntb)))
	ss, _ := r2.ReadAll()
	return ss

}

func CheckProject(srcClusterName string, productName string, projectName string, opAdmin string ) error  {
	targetClusterName := "yylive-" + srcClusterName
	// Project := ReqProjectData(projectName, productName)
	NewClusterDeloy := false
	// fmt.Printf("正在检查目标集群%s是否有部署...\n", targetClusterName)
	ProjectDeloy := ReqListDeloyData(projectName, productName)
	for _,i := range ProjectDeloy.Data{

		if  i.ClusterName == srcClusterName  {
			Deploy := ReqDeploymentData(projectName, i.DeploymentName)
			if Deploy.Data.Running == "0"{
				NewClusterDeloy = true
			}
		}

		if JugeCluster(i.ClusterName, targetClusterName) {
			Deploy := ReqDeploymentData(projectName, i.DeploymentName)
			// fmt.Printf("产品名：%s  服务名：%s 集群：%s %s个Pod处于Running\n", i.ProductName, i.ProjectName, i.ClusterName , Deploy.Data.Replicas)
			if  Deploy.Data.Running != "0" {
				NewClusterDeloy = true
				 //fmt.Printf("产品名：%s  服务名：%s 集群：%s %s个Pod处于Running\n", i.ProductName, i.ProjectName, i.ClusterName , Deploy.Data.Replicas)
			} else {
				 fmt.Printf("产品名：%s  服务名：%s 新集群没有Runnig容器\n", i.ProductName, i.ProjectName )
			}
		}
	}
	// fmt.Println(Project.Data, projectName, productName)
	if NewClusterDeloy == false {
		 fmt.Printf("产品名：%s  服务名：%s 负责人：%s %s新集群没有部署\n", productName, projectName, opAdmin , targetClusterName )
	}
	return nil
}

func main() {
	app := cli.NewApp()
	app.Action = func(c *cli.Context) error {
		fileName := c.Args().Get(0)
		srcClusterName := c.Args().Get(1)
		CheckData := ReadCsv(fileName)
		for _, i := range  CheckData {
			productName := i[0]
			projectName := i[1]
			opAdmin := i[2]
			err := CheckProject(srcClusterName, productName, projectName, opAdmin)
			if err != nil {
				log.Fatal(err)
			}
		}
		return nil
	}
	err := app.Run(os.Args)
	if err != nil {
		log.Fatal(err)
	}
}