package main

import (
	"boce-service/config"
	"boce-service/model"
	"boce-service/router"
	"boce-service/utils/log"
	"fmt"
	"github.com/jinzhu/gorm"
	_ "github.com/jinzhu/gorm/dialects/mysql"
	"os"
)

var DB *gorm.DB

func InitDB() {
	var err error
	dbConfig := config.GetDatabaseConfig()
	DB, err = gorm.Open("mysql", fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8&parseTime=True&loc=Local",
		dbConfig.Username,
		dbConfig.Password,
		dbConfig.Host,
		dbConfig.Port,
		dbConfig.DBName))
	if err != nil {
		panic(err)
	}
	if os.Getenv("AUTO_MIGRATE") == "true" {
		err = DB.AutoMigrate(&model.DNS{}, &model.Isp{}, &model.Node{}).Error
		if err != nil {
			panic(err)
		}
	}
}

var db = make(map[string]string)

func main() {
	// 初始化日志
	logConfig := &log.Config{
		Level:    log.INFO,
		FilePath: "logs/app.log",
	}
	if err := log.Init(logConfig); err != nil {
		panic(err)
	}

	// 加载配置
	if err := config.LoadConfig(); err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 初始化数据库
	InitDB()
	defer DB.Close()

	log.Info("服务启动中...")

	// 初始化路由
	r := router.InitRouter()

	// 启动服务
	port := config.GetServerConfig().Port
	log.Infof("服务运行在 :%d", port)
	if err := r.Run(fmt.Sprintf(":%d", port)); err != nil {
		log.Fatalf("服务启动失败: %v", err)
	}
}
