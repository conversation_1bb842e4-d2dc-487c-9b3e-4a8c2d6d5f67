package service

import (
	"boce-service/model"
	"boce-service/utils/log"
	"bytes"
	"embed"
	"encoding/base64"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"text/template"
	"time"
)

//go:embed template/blackbox_exporter.tmpl
var templateFS embed.FS

// BlackboxService blackbox配置生成服务
type BlackboxService struct {
	templatePath string
	outputPath   string
}

// NewBlackboxService 创建blackbox服务实例
func NewBlackboxService(outputPath string) *BlackboxService {
	return &BlackboxService{
		templatePath: "template/blackbox_exporter.tmpl",
		outputPath:   outputPath,
	}
}

// GetDefaultModules 获取默认的blackbox模块配置
func (bs *BlackboxService) GetDefaultModules() []model.BlackboxModule {
	return []model.BlackboxModule{
		{
			Name:    "http_2xx",
			Prober:  "http",
			Timeout: 5 * time.Second,
			HTTP: &model.HTTPProbe{
				ValidHTTPVersions: []string{"HTTP/1.1", "HTTP/2.0"},
				ValidStatusCodes:  []int{200},
				Method:            "GET",
				NoFollowRedirects: false,
				FailIfSSL:         false,
				FailIfNotSSL:      false,
			},
		},
		{
			Name:    "http_post_2xx",
			Prober:  "http",
			Timeout: 5 * time.Second,
			HTTP: &model.HTTPProbe{
				ValidHTTPVersions: []string{"HTTP/1.1", "HTTP/2.0"},
				ValidStatusCodes:  []int{200},
				Method:            "POST",
				Headers: map[string]string{
					"Content-Type": "application/json",
				},
			},
		},
		{
			Name:    "tcp_connect",
			Prober:  "tcp",
			Timeout: 5 * time.Second,
			TCP:     &model.TCPProbe{},
		},
		{
			Name:    "icmp",
			Prober:  "icmp",
			Timeout: 5 * time.Second,
			ICMP: &model.ICMPProbe{
				Protocol:            "icmp",
				PreferredIPProtocol: "ip4",
			},
		},
		{
			Name:    "dns_udp",
			Prober:  "dns",
			Timeout: 5 * time.Second,
			DNS: &model.DNSProbe{
				QueryName:   "example.com",
				QueryType:   "A",
				ValidRcodes: []string{"NOERROR"},
			},
		},
	}
}

// GenerateFromItemsContent 从Item记录生成blackbox配置并返回原始内容
func (bs *BlackboxService) GenerateFromItemsContent(items []model.Item) (string, error) {
	var modules []model.BlackboxModule

	for _, item := range items {
		if item.Status != model.ITEM_ON_STATUS {
			continue
		}

		module := bs.convertItemToModule(item)
		if module != nil {
			modules = append(modules, *module)
		}
	}

	if len(modules) == 0 {
		log.Warn("没有找到有效的Item记录，使用默认配置")
		modules = bs.GetDefaultModules()
	}

	return bs.GenerateConfigContent(modules)
}

// GenerateConfigContent 生成blackbox_exporter.yaml配置并返回原始内容
func (bs *BlackboxService) GenerateConfigContent(modules []model.BlackboxModule) (string, error) {
	// 创建模板函数映射
	funcMap := template.FuncMap{
		"join": func(slice []string, sep string) string {
			return strings.Join(slice, sep)
		},
		"joinInt": func(slice []int, sep string) string {
			strSlice := make([]string, len(slice))
			for i, v := range slice {
				strSlice[i] = fmt.Sprintf("%d", v)
			}
			return strings.Join(strSlice, sep)
		},
	}

	// 从嵌入的文件系统读取模板
	tmplContent, err := templateFS.ReadFile("template/blackbox_exporter.tmpl")
	if err != nil {
		return "", fmt.Errorf("读取模板文件失败: %v", err)
	}

	// 解析模板
	tmpl, err := template.New("blackbox_exporter").Funcs(funcMap).Parse(string(tmplContent))
	if err != nil {
		return "", fmt.Errorf("解析模板失败: %v", err)
	}

	// 准备模板数据
	data := struct {
		Modules []model.BlackboxModule
	}{
		Modules: modules,
	}

	// 执行模板到内存缓冲区
	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, data); err != nil {
		return "", fmt.Errorf("执行模板失败: %v", err)
	}

	return buf.String(), nil
}

// convertItemToModule 将Item记录转换为BlackboxModule
func (bs *BlackboxService) convertItemToModule(item model.Item) *model.BlackboxModule {
	module := &model.BlackboxModule{
		Name:    item.Name,
		Timeout: time.Duration(item.Interval) * time.Second,
	}

	switch item.Method {
	case model.ITEM_HTTP_GET_Method: // HTTP GET
		module.Prober = "http"
		module.HTTP = &model.HTTPProbe{
			ValidHTTPVersions: []string{"HTTP/1.1", "HTTP/2.0"},
			ValidStatusCodes:  []int{200},
			Method:            "GET",
		}
	case model.ITEM_HTTP_POST_Method: // HTTP POST
		module.Prober = "http"
		module.HTTP = &model.HTTPProbe{
			ValidHTTPVersions: []string{"HTTP/1.1", "HTTP/2.0"},
			ValidStatusCodes:  []int{200},
			Method:            "POST",
		}
	case model.ITEM_TCP_Method: // TCP
		module.Prober = "tcp"
		module.TCP = &model.TCPProbe{}
	case model.ITEM_ICMP_Method: // ICMP
		module.Prober = "icmp"
		module.ICMP = &model.ICMPProbe{
			Protocol:            "icmp",
			PreferredIPProtocol: "ip4",
		}
	case model.ITEM_DNS_Method: // DNS
		module.Prober = "dns"
		module.DNS = &model.DNSProbe{
			QueryName:   item.Data,
			QueryType:   "A",
			ValidRcodes: []string{"NOERROR"},
		}
	default:
		log.Warnf("未知的方法类型: %d, 跳过Item: %s", item.Method, item.Name)
		return nil
	}

	return module
}

// GenerateConfigBase64 生成blackbox_exporter.yaml配置并返回base64字符串
func (bs *BlackboxService) GenerateConfigBase64(modules []model.BlackboxModule) (string, error) {
	// 创建模板函数映射
	funcMap := template.FuncMap{
		"join": func(slice []string, sep string) string {
			return strings.Join(slice, sep)
		},
		"joinInt": func(slice []int, sep string) string {
			strSlice := make([]string, len(slice))
			for i, v := range slice {
				strSlice[i] = fmt.Sprintf("%d", v)
			}
			return strings.Join(strSlice, sep)
		},
	}

	// 从嵌入的文件系统读取模板
	tmplContent, err := templateFS.ReadFile("template/blackbox_exporter.tmpl")
	if err != nil {
		return "", fmt.Errorf("读取模板文件失败: %v", err)
	}

	// 解析模板
	tmpl, err := template.New("blackbox_exporter").Funcs(funcMap).Parse(string(tmplContent))
	if err != nil {
		return "", fmt.Errorf("解析模板失败: %v", err)
	}

	// 准备模板数据
	data := struct {
		Modules []model.BlackboxModule
	}{
		Modules: modules,
	}

	// 执行模板到内存缓冲区
	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, data); err != nil {
		return "", fmt.Errorf("执行模板失败: %v", err)
	}

	if bs.outputPath != "" {
		// 确保输出目录存在
		outputDir := filepath.Dir(bs.outputPath)
		if err := os.MkdirAll(outputDir, 0755); err != nil {
			log.Errorf("创建输出目录失败: %v", err)
		}

		// 创建输出文件
		outputFile, err := os.Create(bs.outputPath)
		if err != nil {
			log.Errorf("创建输出文件失败: %v", err)
		}
		defer func(outputFile *os.File) {
			err := outputFile.Close()
			if err != nil {
				log.Errorf("关闭输出文件失败: %v", err)
			}
		}(outputFile)

		// 执行模板
		if err := tmpl.Execute(outputFile, data); err != nil {
			log.Errorf("执行模板失败: %v", err)
		}

		log.Infof("成功生成blackbox_exporter配置文件: %s", bs.outputPath)
	}

	// 返回base64编码的配置内容
	return base64.StdEncoding.EncodeToString(buf.Bytes()), nil
}

// GenerateFromItemsBase64 从Item记录生成blackbox配置并返回base64字符串
func (bs *BlackboxService) GenerateFromItemsBase64(items []model.Item) (string, error) {
	var modules []model.BlackboxModule

	for _, item := range items {
		if item.Status != model.ITEM_ON_STATUS {
			continue
		}

		module := bs.convertItemToModule(item)
		if module != nil {
			modules = append(modules, *module)
		}
	}

	if len(modules) == 0 {
		log.Warn("没有找到有效的Item记录，使用默认配置")
		modules = bs.GetDefaultModules()
	}

	return bs.GenerateConfigBase64(modules)
}
