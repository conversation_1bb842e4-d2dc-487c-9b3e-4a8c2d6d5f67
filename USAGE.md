# Boce Client 使用说明

这个项目现在支持两个主要功能：
1. **WebSocket客户端** - 原有的WebSocket连接功能
2. **Pusher** - 从blackbox_exporter获取指标并推送到push-gateway

## 编译项目

```bash
go build -o boce-client main.go
```

## 使用方法

### 1. WebSocket客户端模式（默认）

启动WebSocket客户端连接到服务器：

```bash
# 使用默认参数
./boce-client

# 或者显式指定ws子命令
./boce-client ws

# 自定义参数
./boce-client ws \
  --server=ws://your-server:8081 \
  --client=************* \
  --ws.version=2.0.0
```

**WebSocket客户端参数：**
- `--server`: WebSocket服务器地址 (默认: ws://localhost:8081)
- `--client`: 客户端IP (默认: 127.0.0.1)
- `--ws.version`: 客户端版本 (默认: 1.0.0)

### 2. Pusher模式

从blackbox_exporter获取指标并推送到push-gateway：

#### 一次性推送

```bash
./boce-client pusher \
  --blackbox.url=http://127.0.0.1:8080/metrics \
  --pushgateway.url=http://your-pushgateway:9091 \
  --job=blackbox_exporter \
  --instance=127.0.0.1:8080 \
  --once
```

#### 周期性推送

```bash
./boce-client pusher \
  --blackbox.url=http://127.0.0.1:8080/metrics \
  --pushgateway.url=http://your-pushgateway:9091 \
  --job=blackbox_exporter \
  --instance=127.0.0.1:8080 \
  --interval=30s
```

**Pusher参数：**
- `--blackbox.url`: Blackbox exporter的metrics URL (默认: http://127.0.0.1:8080/metrics)
- `--pushgateway.url`: Push Gateway URL (必需)
- `--job`: Push Gateway中的job名称 (默认: blackbox_exporter)
- `--instance`: 实例标签 (默认: 127.0.0.1:8080)
- `--interval`: 推送间隔 (默认: 30s)
- `--once`: 只推送一次然后退出

## 使用示例

### 示例1: 监控Web服务健康状态

1. 启动blackbox_exporter监控你的Web服务
2. 使用pusher将监控结果推送到push-gateway：

```bash
./boce-client pusher \
  --blackbox.url=http://127.0.0.1:8080/metrics \
  --pushgateway.url=http://monitoring.company.com:9091 \
  --job=web_health_check \
  --instance=web-server-01 \
  --interval=1m
```

### 示例2: 批处理任务监控

在批处理脚本中使用一次性推送：

```bash
#!/bin/bash

# 任务开始前推送状态
./boce-client pusher \
  --pushgateway.url=http://pushgateway:9091 \
  --job=batch_job \
  --instance=job-$(date +%Y%m%d-%H%M%S) \
  --once

# 执行实际的批处理任务
echo "Running batch job..."
sleep 10

# 任务完成后再次推送状态
./boce-client pusher \
  --pushgateway.url=http://pushgateway:9091 \
  --job=batch_job \
  --instance=job-$(date +%Y%m%d-%H%M%S) \
  --once
```

### 示例3: 多实例监控

监控多个blackbox_exporter实例：

```bash
# 监控实例1
./boce-client pusher \
  --blackbox.url=http://192.168.1.10:8080/metrics \
  --pushgateway.url=http://pushgateway:9091 \
  --job=blackbox_cluster \
  --instance=node-01 \
  --interval=30s &

# 监控实例2
./boce-client pusher \
  --blackbox.url=http://192.168.1.11:8080/metrics \
  --pushgateway.url=http://pushgateway:9091 \
  --job=blackbox_cluster \
  --instance=node-02 \
  --interval=30s &

# 监控实例3
./boce-client pusher \
  --blackbox.url=http://192.168.1.12:8080/metrics \
  --pushgateway.url=http://pushgateway:9091 \
  --job=blackbox_cluster \
  --instance=node-03 \
  --interval=30s &
```

## 帮助信息

查看完整的帮助信息：

```bash
# 查看主帮助
./boce-client --help

# 查看WebSocket客户端帮助
./boce-client ws --help

# 查看Pusher帮助
./boce-client pusher --help
```

## 日志输出

程序会输出详细的日志信息，包括：

**WebSocket客户端日志：**
- 连接状态
- 认证结果
- 消息收发
- 文件操作
- 错误信息

**Pusher日志：**
- 指标获取状态
- 推送操作结果
- 网络错误
- 时间统计

## 错误处理

### WebSocket客户端
- 连接断开时自动重连
- 认证失败时记录错误但继续尝试
- 文件操作失败时记录错误但不影响其他操作

### Pusher
- 网络错误会记录但不终止程序（周期性模式）
- HTTP错误状态码会详细记录
- 一次性模式下的错误会导致程序退出

## 系统要求

- Go 1.19+
- 网络访问目标服务（WebSocket服务器、blackbox_exporter、push-gateway）

## 注意事项

1. **WebSocket客户端**：
   - 确保WebSocket服务器在指定地址运行
   - 检查网络连通性和防火墙设置
   - 客户端会自动处理重连和心跳

2. **Pusher**：
   - 确保blackbox_exporter和push-gateway都可访问
   - 推送的指标会在push-gateway中保留直到被覆盖
   - 建议在生产环境中设置适当的监控和告警

3. **性能考虑**：
   - 根据需求调整推送间隔
   - 监控网络带宽使用情况
   - 考虑push-gateway的存储容量
