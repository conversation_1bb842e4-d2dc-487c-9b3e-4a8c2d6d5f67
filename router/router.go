package router

import (
	"boce-service/controller"
	"github.com/gin-gonic/gin"
	"net/http"
)

func InitRouter() *gin.Engine {
	r := gin.New()

	// 使用Logger和Recovery中间件
	r.Use(gin.Logger())
	r.Use(gin.Recovery())

	// 定义路由
	r.GET("/ping", func(c *gin.Context) {
		c.String(http.StatusOK, "pong")
	})

	// IP元数据查询接口
	r.GET("/api/ip/aw_meta", controller.GetIpMeta)
	r.GET("/api/dns/reload", controller.ReloadDNS)
	r.POST("/api/ip/register", controller.IpRegister)
	r.GET("/api/ip", controller.GetClientIp)

	return r
}
