package main

import (
	"boce-client/wsclient"
	"github.com/alecthomas/kingpin/v2"
	"github.com/prometheus/common/version"
	"os"
)

//TIP <p>To run your code, right-click the code and select <b>Run</b>.</p> <p>Alternatively, click
// the <icon src="AllIcons.Actions.Execute"/> icon in the gutter and select the <b>Run</b> menu item from here.</p>

var (
	serverURL = kingpin.Flag("server", "WebSocket服务器地址").Default("ws://localhost:8081").String()
	clientID  = kingpin.Flag("client", "客户端IP").Default("127.0.0.1").String()
	wsVersion = kingpin.Flag("ws.sversion", "客户端版本").Default("1.0.0").String()
)

func main() {
	kingpin.CommandLine.UsageWriter(os.Stdout)
	kingpin.Version(version.Print("blackbox_exporter"))
	kingpin.HelpFlag.Short('h')
	kingpin.Parse()

	wsclient.BoceWsClient(*serverURL, *clientID, *wsVersion)

}
