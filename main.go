package main

import (
	"boce-client/wsclient"
	"bytes"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"time"

	"github.com/alecthomas/kingpin/v2"
	"github.com/prometheus/common/version"
)

//TIP <p>To run your code, right-click the code and select <b>Run</b>.</p> <p>Alternatively, click
// the <icon src="AllIcons.Actions.Execute"/> icon in the gutter and select the <b>Run</b> menu item from here.</p>

var (
	// 定义子命令
	app = kingpin.New("boce-client", "Boce client with WebSocket and Push Gateway support")

	// WebSocket客户端子命令
	wsCmd     = app.Command("ws", "Start WebSocket client").Default()
	serverURL = wsCmd.Flag("server", "WebSocket服务器地址").Default("ws://localhost:8081").String()
	clientID  = wsCmd.Flag("client", "客户端IP").Default("127.0.0.1").String()
	wsVersion = wsCmd.Flag("ws.version", "客户端版本").Default("1.0.0").String()

	// Pusher子命令
	pusherCmd      = app.Command("pusher", "Push blackbox_exporter metrics to push gateway")
	blackboxURL    = pusherCmd.Flag("blackbox.url", "Blackbox exporter metrics URL").Default("http://127.0.0.1:8080/metrics").String()
	pushGatewayURL = pusherCmd.Flag("pushgateway.url", "Push Gateway URL").Required().String()
	jobName        = pusherCmd.Flag("job", "Job name for push gateway").Default("blackbox_exporter").String()
	pushInterval   = pusherCmd.Flag("interval", "Push interval (e.g., 30s, 1m)").Default("30s").Duration()
	instanceLabel  = pusherCmd.Flag("instance", "Instance label for metrics").Default("127.0.0.1:8080").String()
	enableOnce     = pusherCmd.Flag("once", "Push once and exit").Bool()
)



func main() {
	app.Version(version.Print("boce-client"))
	app.HelpFlag.Short('h')

	// 解析命令行参数
	command := kingpin.MustParse(app.Parse(os.Args[1:]))

	switch command {
	case wsCmd.FullCommand():
		// 启动WebSocket客户端
		log.Printf("Starting WebSocket client...")
		log.Printf("Server: %s", *serverURL)
		log.Printf("Client ID: %s", *clientID)
		log.Printf("Version: %s", *wsVersion)
		wsclient.BoceWsClient(*serverURL, *clientID, *wsVersion)

	case pusherCmd.FullCommand():
		// 启动Pusher功能
		log.Printf("Starting Blackbox Exporter Pusher...")
		log.Printf("Blackbox URL: %s", *blackboxURL)
		log.Printf("Push Gateway URL: %s", *pushGatewayURL)
		log.Printf("Job Name: %s", *jobName)
		log.Printf("Instance Label: %s", *instanceLabel)

		pusher := NewBlackboxPusher(*blackboxURL, *pushGatewayURL, *jobName, *instanceLabel)

		if *enableOnce {
			log.Printf("Running in one-time mode")
			if err := pusher.pushOnce(); err != nil {
				log.Fatalf("Push failed: %v", err)
			}
			log.Printf("One-time push completed successfully")
			return
		}

		log.Printf("Running in periodic mode with interval: %v", *pushInterval)
		pusher.startPeriodicPush(*pushInterval)

	default:
		// 默认启动WebSocket客户端
		log.Printf("Starting WebSocket client (default)...")
		wsclient.BoceWsClient(*serverURL, *clientID, *wsVersion)
	}
}
