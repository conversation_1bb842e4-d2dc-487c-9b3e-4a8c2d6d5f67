#!/bin/bash

echo "Testing refactored Boce Client..."

# 检查是否有Go环境
if ! command -v go &> /dev/null; then
    echo "Go is not installed. Please install Go first."
    exit 1
fi

# 编译项目
echo "Building boce-client..."
go build -o boce-client main.go

if [ $? -eq 0 ]; then
    echo "✅ Boce client built successfully"
else
    echo "❌ Failed to build boce client"
    exit 1
fi

# 显示帮助信息
echo ""
echo "Main help:"
./boce-client --help

echo ""
echo "WebSocket client help:"
./boce-client ws --help

echo ""
echo "Pusher help:"
./boce-client pusher --help

echo ""
echo "✅ Build and help tests completed successfully!"
echo ""
echo "Usage examples:"
echo "  # Start WebSocket client (default)"
echo "  ./boce-client"
echo "  ./boce-client ws --server=ws://localhost:8081 --client=127.0.0.1"
echo ""
echo "  # Push metrics once"
echo "  ./boce-client pusher --pushgateway.url=http://localhost:9091 --once"
echo ""
echo "  # Push metrics periodically"
echo "  ./boce-client pusher --pushgateway.url=http://localhost:9091 --interval=30s"
