package config

import (
	"io/ioutil"
	"log"
	"os"
	"path/filepath"

	"gopkg.in/yaml.v3"
)

// Config holds all configuration for the application
type Config struct {
	Database DatabaseConfig `yaml:"database"`
	Server   ServerConfig   `yaml:"server"`
}

// DatabaseConfig holds database connection parameters
type DatabaseConfig struct {
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	Username string `yaml:"username"`
	Password string `yaml:"password"`
	DBName   string `yaml:"dbname"`
}

// ServerConfig holds server configuration
type ServerConfig struct {
	Port int `yaml:"port"`
}

var config *Config

// LoadConfig loads configuration from config.yaml file
func LoadConfig() error {
	configPath := getConfigPath()
	data, err := ioutil.ReadFile(configPath)
	if err != nil {
		return err
	}

	config = &Config{}
	err = yaml.Unmarshal(data, config)
	if err != nil {
		return err
	}

	return nil
}

// GetDatabaseConfig returns database configuration
func GetDatabaseConfig() *DatabaseConfig {
	if config == nil {
		if err := LoadConfig(); err != nil {
			log.Printf("Failed to load config: %v, using defaults", err)
			return &DatabaseConfig{
				Host:     "localhost",
				Port:     3306,
				Username: "root",
				Password: "",
				DBName:   "boce",
			}
		}
	}
	return &config.Database
}

// Helper function to get config file path
func getConfigPath() string {
	// First check if config file exists in current directory
	if _, err := os.Stat("config.yaml"); err == nil {
		return "config.yaml"
	}

	// Then check if config file exists in config directory
	configDir := filepath.Join(".", "config")
	configPath := filepath.Join(configDir, "config.yaml")
	if _, err := os.Stat(configPath); err == nil {
		return configPath
	}

	// Default to config.yaml in current directory
	return "config.yaml"
}
