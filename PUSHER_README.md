# Blackbox Exporter Push Gateway 集成

这个项目提供了一个完整的解决方案，用于从blackbox_exporter获取指标并推送到push-gateway。

## 项目结构

```
.
├── simple-pusher/          # 简单的pusher程序
│   ├── main.go            # 主程序
│   ├── go.mod             # Go模块文件
│   ├── Dockerfile         # Docker构建文件
│   └── README.md          # 详细使用说明
├── pusher/                # 完整版pusher程序（使用Prometheus客户端库）
│   ├── main.go
│   └── go.mod
├── config/                # 配置文件
│   ├── blackbox_exporter.yaml
│   └── prometheus.yml
├── docker-compose.yml     # Docker Compose配置
├── start.sh              # 启动脚本
└── PUSHER_README.md      # 项目说明
```

## 快速开始

### 方法1: 使用Docker Compose（推荐）

1. 确保安装了Docker和Docker Compose

2. 启动完整的监控栈：
```bash
chmod +x start.sh
./start.sh
```

这将启动：
- Blackbox Exporter (端口8080)
- Push Gateway (端口9091)
- Pusher服务（自动推送指标）
- Prometheus (端口9090，可选)

3. 访问服务：
- Blackbox Exporter: http://localhost:8080
- Push Gateway: http://localhost:9091
- Prometheus: http://localhost:9090

### 方法2: 手动编译运行

1. 编译pusher程序：
```bash
cd simple-pusher
go build -o pusher main.go
```

2. 运行pusher：
```bash
./pusher \
  --blackbox.url=http://127.0.0.1:8080/metrics \
  --pushgateway.url=http://your-pushgateway:9091 \
  --job=blackbox_exporter \
  --instance=127.0.0.1:8080 \
  --interval=30s
```

## 配置参数

### Pusher程序参数

- `--blackbox.url`: Blackbox exporter的metrics URL
- `--pushgateway.url`: Push Gateway的URL
- `--job`: Push Gateway中的job名称
- `--instance`: 实例标签
- `--interval`: 推送间隔（如30s, 1m, 5m）
- `--once`: 只推送一次然后退出

### 环境变量（Docker版本）

- `BLACKBOX_URL`: Blackbox exporter URL
- `PUSHGATEWAY_URL`: Push Gateway URL
- `JOB_NAME`: Job名称
- `INSTANCE_LABEL`: 实例标签
- `PUSH_INTERVAL`: 推送间隔

## 使用场景

### 1. 定期健康检查

配置blackbox_exporter监控多个HTTP端点，pusher定期将结果推送到push-gateway：

```bash
./pusher \
  --blackbox.url=http://127.0.0.1:8080/metrics \
  --pushgateway.url=http://pushgateway:9091 \
  --job=health_check \
  --instance=web_services \
  --interval=1m
```

### 2. 批处理任务监控

在批处理任务中使用一次性推送：

```bash
# 任务开始前
./pusher --once --job=batch_job --instance=job_001

# 执行批处理任务
run_batch_job.sh

# 任务结束后
./pusher --once --job=batch_job --instance=job_001
```

### 3. 网络连通性监控

监控网络连通性并推送结果：

```bash
./pusher \
  --blackbox.url=http://127.0.0.1:8080/metrics \
  --pushgateway.url=http://monitoring.company.com:9091 \
  --job=network_connectivity \
  --instance=$(hostname) \
  --interval=30s
```

## 监控和告警

### 在Prometheus中查询推送的指标

```promql
# 查看所有从push gateway获取的指标
{__name__=~".+", job="blackbox_exporter"}

# 查看特定的blackbox指标
probe_success{job="blackbox_exporter"}
probe_duration_seconds{job="blackbox_exporter"}
```

### 设置告警规则

```yaml
groups:
- name: blackbox_alerts
  rules:
  - alert: BlackboxProbeFailure
    expr: probe_success{job="blackbox_exporter"} == 0
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "Blackbox probe failure"
      description: "Probe {{ $labels.instance }} has been failing for more than 5 minutes"
```

## 故障排除

### 1. 检查服务状态

```bash
# 查看所有服务状态
docker-compose ps

# 查看pusher日志
docker-compose logs -f blackbox-pusher

# 查看blackbox exporter日志
docker-compose logs -f blackbox-exporter
```

### 2. 手动测试连接

```bash
# 测试blackbox exporter
curl http://localhost:8080/metrics

# 测试push gateway
curl http://localhost:9091/metrics

# 手动推送测试数据
echo "test_metric 1" | curl -X POST --data-binary @- http://localhost:9091/metrics/job/test/instance/test
```

### 3. 常见问题

**问题**: Pusher无法连接到blackbox exporter
**解决**: 检查blackbox exporter是否在指定端口运行，防火墙设置

**问题**: Push gateway拒绝连接
**解决**: 检查push gateway URL是否正确，网络连通性

**问题**: 指标没有出现在Prometheus中
**解决**: 检查Prometheus配置中是否包含push gateway作为抓取目标

## 性能优化

1. **调整推送间隔**: 根据监控需求调整`--interval`参数
2. **批量推送**: 对于大量指标，考虑增加推送间隔减少网络开销
3. **网络超时**: 调整HTTP客户端超时设置适应网络环境

## 安全考虑

1. **网络安全**: 在生产环境中使用HTTPS和认证
2. **访问控制**: 限制对push gateway的访问
3. **数据保护**: 避免在指标中包含敏感信息

## 扩展功能

项目支持以下扩展：
- 自定义指标标签
- 多个blackbox exporter实例
- 指标过滤和转换
- 高可用部署

详细的扩展说明请参考各子目录中的README文件。
