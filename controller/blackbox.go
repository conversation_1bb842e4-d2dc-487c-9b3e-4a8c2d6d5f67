package controller

import (
	"boce-service/model"
	"boce-service/service"
	"boce-service/utils/log"
	"encoding/base64"
	"net/http"
	"path/filepath"
	"time"

	"github.com/gin-gonic/gin"
)

// GenerateBlackboxConfig 生成blackbox_exporter配置并返回base64字符串
func GenerateBlackboxConfig(c *gin.Context) {
	// 创建blackbox服务
	blackboxService := service.NewBlackboxService()

	// 获取所有启用的Item记录
	items := model.GetItemsByStatus(model.ITEM_ON_STATUS)

	log.Infof("找到 %d 条启用的Item记录", len(items))

	// 生成配置base64字符串
	configBase64, err := blackboxService.GenerateFromItemsBase64(items)
	if err != nil {
		log.Error("生成blackbox配置失败:", err)
		c.<PERSON>(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "生成配置失败",
			"error":   err.Error(),
		})
		return
	}

	// 同时返回原始配置内容用于预览
	configContent, _ := blackboxService.GenerateFromItemsContent(items)

	c.JSON(http.StatusOK, gin.H{
		"code":           200,
		"message":        "配置生成成功",
		"config_base64":  configBase64,
		"config_preview": configContent,
		"count":          len(items),
	})
}

// GenerateDefaultBlackboxConfig 生成默认的blackbox配置并返回base64字符串
func GenerateDefaultBlackboxConfig(c *gin.Context) {
	// 创建blackbox服务
	blackboxService := service.NewBlackboxService("")

	// 获取默认模块配置
	modules := blackboxService.GetDefaultModules()

	// 生成配置base64字符串
	configBase64, err := blackboxService.GenerateConfigBase64(modules)
	if err != nil {
		log.Error("生成默认blackbox配置失败:", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "生成默认配置失败",
			"error":   err.Error(),
		})
		return
	}

	// 同时返回原始配置内容用于预览
	configContent, _ := blackboxService.GenerateConfigContent(modules)

	c.JSON(http.StatusOK, gin.H{
		"code":           200,
		"message":        "默认配置生成成功",
		"config_base64":  configBase64,
		"config_preview": configContent,
		"modules":        len(modules),
	})
}

// PreviewBlackboxConfig 预览blackbox配置内容
func PreviewBlackboxConfig(c *gin.Context) {
	// 创建blackbox服务
	blackboxService := service.NewBlackboxService()

	// 获取所有启用的Item记录
	items := model.GetItemsByStatus(model.ITEM_ON_STATUS)

	var modules []model.BlackboxModule
	for _, item := range items {
		if item.Status != model.ITEM_ON_STATUS {
			continue
		}
		module := blackboxService.ConvertItemToModule(item)
		if module != nil {
			modules = append(modules, *module)
		}
	}

	if len(modules) == 0 {
		modules = blackboxService.GetDefaultModules()
	}

	// 生成配置内容
	configContent, err := blackboxService.GenerateConfigContent(modules)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "生成配置预览失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":           200,
		"message":        "配置预览",
		"config_content": configContent,
		"modules":        modules,
		"count":          len(modules),
	})
}

// CreateUDPProbe 创建自定义UDP探测配置并返回base64字符串
func CreateUDPProbe(c *gin.Context) {
	var req struct {
		Name           string `json:"name" binding:"required"`
		Timeout        int    `json:"timeout"`
		QueryData      string `json:"query_data"`
		ExpectResponse string `json:"expect_response"`
		ProbeType      string `json:"probe_type"` // "generic", "dns", "syslog"
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数格式错误",
		})
		return
	}

	timeout := time.Duration(req.Timeout) * time.Second
	if timeout == 0 {
		timeout = 5 * time.Second
	}

	blackboxService := service.NewBlackboxService()
	var module model.BlackboxModule

	switch req.ProbeType {
	case "dns":
		module = blackboxService.CreateDNSOverUDPModule(req.Name, timeout, req.QueryData)
	case "syslog":
		module = blackboxService.CreateSyslogUDPModule(req.Name, timeout, req.QueryData)
	default:
		module = blackboxService.CreateUDPModule(req.Name, timeout, req.QueryData, req.ExpectResponse)
	}

	// 生成单个模块的配置
	modules := []model.BlackboxModule{module}
	configBase64, err := blackboxService.GenerateConfigBase64(modules)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "生成UDP探测配置失败",
			"error":   err.Error(),
		})
		return
	}

	configContent, _ := blackboxService.GenerateConfigContent(modules)

	c.JSON(http.StatusOK, gin.H{
		"code":           200,
		"message":        "UDP探测配置创建成功",
		"module":         module,
		"config_base64":  configBase64,
		"config_preview": configContent,
	})
}

// DecodeBlackboxConfig 解码base64配置字符串
func DecodeBlackboxConfig(c *gin.Context) {
	var req struct {
		ConfigBase64 string `json:"config_base64" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数格式错误",
		})
		return
	}

	// 解码base64
	configContent, err := base64.StdEncoding.DecodeString(req.ConfigBase64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "base64解码失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":           200,
		"message":        "配置解码成功",
		"config_content": string(configContent),
	})
}
