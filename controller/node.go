package controller

import (
	"boce-service/model"
	"boce-service/utils/log"
	"github.com/gin-gonic/gin"
	"net/http"
)

func IpRegister(c *gin.Context) {
	if c.Request.Method != "POST" {
		c.JSON(http.StatusMethodNotAllowed, gin.H{
			"code":    405,
			"message": "Method Not Allowed",
		})
		return
	}
	var clientIP string
	if c.<PERSON><PERSON>("ip") != "" {
		clientIP = c.Query("ip")
		log.Info("clientIP:", c.Query("ip"))

	} else {
		clientIP = c.ClientIP()
		log.Info("clientIP:", clientIP)
	}
	err, res := GetAwMeta(clientIP)
	if err != nil {
		log.Info("查询IP信息失败:", err)
	}
	log.Info("查询IP信息成功:", res)

	node := model.Node{
		IpAddr:    clientIP,
		Accuracy:  getStringValue(res, "accuracy"),
		Continent: getStringValue(res, "continent"),
		Country:   getStringValue(res, "country"),
		Province:  getStringValue(res, "province"),
		City:      getStringValue(res, "city"),
		District:  getStringValue(res, "district"),
		AreaCode:  getStringValue(res, "areacode"),
		Isp:       getStringValue(res, "isp"),
		ZipCode:   getStringValue(res, "zipcode"),
		AsNumber:  getStringValue(res, "asnumber"),
		Lngwgs:    getStringValue(res, "lngwgs"),
		Latwgs:    getStringValue(res, "latwgs"),
		Status:    model.NODE_ON_STATUS,
		Owner:     getStringValue(res, "owner"),
		AdCode:    getStringValue(res, "adcode"),
	}
	node.IspCode = model.ISP_MAP[node.Isp]
	if node.IspCode == "" {
		log.Errorf("查询ISP %s 代码失败: %v", node.Isp, err)
	}
	var cityDnsList, provinceDnsList []model.DNS
	cityDnsList = model.GetDNSByCityAndISP(node.City, node.Province, node.Isp)
	if len(cityDnsList) > 0 {
		node.DnsId = cityDnsList[0].Id
	}

	if len(cityDnsList) == 0 {
		log.Infof("未找到ISP %s 在 %s 的DNS记录，开始查找ISP %s 在 %s 的DNS记录", node.Isp, node.City, node.Isp, node.Province)
		provinceDnsList = model.GetDNSByProvinceAndISP(node.Province, node.Isp)
		if len(provinceDnsList) > 0 {
			node.DnsId = provinceDnsList[0].Id
		} else {
			log.Infof("未找到ISP %s 在 %s 的DNS记录，开始查找ISP %s 的DNS记录", node.Isp, node.Province, node.Isp)
		}
	}

	if node.DnsId == 0 {
		log.Infof("节点未能找到任何匹配省份和城市的DNS记录", node.Isp)
	}
	err = model.CreateOrUpdateNode(node)
	if err != nil {
		log.Error("写入数据库失败:%s", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "写入数据库失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"client_ip": clientIP,
	})
}

func GetClientIp(c *gin.Context) {
	clientIP := c.ClientIP()
	c.JSON(http.StatusOK, gin.H{
		"client_ip": clientIP,
	})
}

func GetNode(c *gin.Context) {
	var node model.Node
	if c.Request.Method != "GET" {
		c.JSON(http.StatusMethodNotAllowed, gin.H{
			"code":    405,
			"message": "Method Not Allowed",
		})
		return
	}
	if c.Query("ip") != "" {
		node.IpAddr = c.Query("ip")
		log.Info("clientIP:", c.Query("ip"))

	} else {
		node.IpAddr = c.ClientIP()
		log.Info("clientIP:", node.IpAddr)
	}
	if node.IpAddr == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "IP地址不能为空",
		})
		return
	}
	node, err := model.GetNodeByIp(node.IpAddr)
	if err != nil {
		log.Error("查询节点失败:%s", err)
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "查询成功",
		"data":    node,
	})
}
