package controller

import (
	"boce-service/model"
	"boce-service/utils/log"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// BatchCreateItems 批量创建Item记录
func BatchCreateItems(c *gin.Context) {
	var items []model.Item

	if err := c.ShouldBindJSON(&items); err != nil {
		log.Error("解析JSON失败:", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数格式错误",
		})
		return
	}

	if len(items) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Item列表不能为空",
		})
		return
	}

	log.Infof("开始批量创建 %d 条Item记录", len(items))

	// 使用批量插入方法
	if err := model.BatchCreateOrUpdateItems(items); err != nil {
		log.Error("批量创建Item失败:", err)
		c.<PERSON>(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "批量创建失败",
		})
		return
	}

	log.Infof("成功批量创建 %d 条Item记录", len(items))
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "批量创建成功",
		"count":   len(items),
	})
}

// CreateItem 创建单条Item记录
func CreateItem(c *gin.Context) {
	var item model.Item

	if err := c.ShouldBindJSON(&item); err != nil {
		log.Error("解析JSON失败:", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数格式错误",
		})
		return
	}

	if err := model.CreateOrUpdateItem(item); err != nil {
		log.Error("创建Item失败:", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "创建成功",
		"data":    item,
	})
}

// GetItems 查询Item列表
func GetItems(c *gin.Context) {
	status := c.Query("status")
	owner := c.Query("owner")

	var items []model.Item

	if status != "" {
		statusInt, err := strconv.Atoi(status)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "状态参数格式错误",
			})
			return
		}
		items = model.GetItemsByStatus(statusInt)
	} else if owner != "" {
		items = model.GetItemsByOwner(owner)
	} else {
		items = model.GetItemsByFilter("1=1")
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "查询成功",
		"data":    items,
		"count":   len(items),
	})
}
