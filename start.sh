#!/bin/bash

echo "Starting Blackbox Exporter Pusher Stack..."

# 检查Docker和Docker Compose是否安装
if ! command -v docker &> /dev/null; then
    echo "Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# 创建必要的目录
mkdir -p config

# 启动服务
echo "Starting services with Docker Compose..."
docker-compose up -d

# 等待服务启动
echo "Waiting for services to start..."
sleep 10

# 检查服务状态
echo "Checking service status..."
docker-compose ps

echo ""
echo "Services are starting up. You can access:"
echo "- Blackbox Exporter: http://localhost:8080"
echo "- Push Gateway: http://localhost:9091"
echo "- Prometheus: http://localhost:9090"
echo ""
echo "To view logs:"
echo "  docker-compose logs -f blackbox-pusher"
echo ""
echo "To stop services:"
echo "  docker-compose down"
