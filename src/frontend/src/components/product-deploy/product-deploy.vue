<template>
  <Modal
          v-model="innerVisible"
          title="产品已部署的包"
          width="60"
          :footer-hide="true"
          :mask-closable="false"
          v-on="modalListeners">
      <div>
          <Card>
              <Form :label-width="100">
                  <Row>
                      <Col span="5">
                          <FormItem label="集群" prop="ClusterName">
                              <Select v-model="clusterName" filterable clearable>
                                  <Option v-for="cluster in clusters" :value="cluster.ClusterName" :key="cluster.ClusterName">{{ cluster.ClusterName }}({{cluster.AliasName}})</Option>
                              </Select>
                          </FormItem>
                      </Col>
                      <Col span="5">
                          <FormItem label="环境:">
                              <Select v-model="env" style="width:95%" filterable clearable>
                                  <Option value="prod">线上环境</Option>
                                  <Option value="prev">预发布环境</Option>
                                  <Option value="test">测试环境</Option>
                              </Select>
                          </FormItem>
                      </Col>
                      <Col span="3">
                          <FormItem label="">
                              <Button icon="md-search" type="primary" @click="getDeploymentsByProduct">查询</Button>
                          </FormItem>
                      </Col>
                  </Row>
              </Form>
          </Card>
      <tables stripe ref="tables" editable v-model="tableData" :columns="columns"/>
      </div>
  </Modal>
</template>

<script>
import CodePopup from '_c/code-popup'
import Tables from '_c/tables'
import { getDeploymentsByProduct } from '@/api/deployment'
import { getClusterLists } from '@/api/cluster'

export default {
  name: 'config-modal',
  components: {
    CodePopup,
    Tables
  },
  props: {
    dataKey: {
      type: String,
      default () {
        return 'ConfigData'
      }
    },
    readonly: {
      type: Boolean,
      default () {
        return false
      }
    },
    visible: {
      type: Boolean,
      default () {
        return false
      }
    },
    pkgName: {
      type: String,
      default () {
        return ''
      }
    },
    productName: {
      type: String,
      default () {
        return ''
      }
    }
  },
  data () {
    return {
      innerVisible: false,
      index: 0,
      tableData: [],
      clusters: [],
      env: '',
      clusterName: '',
      columns: [
        { title: '产品名称', key: 'ProductName', sortable: true },
        { title: '集群', key: 'ClusterName', sortable: true },
        { title: '环境', key: 'DeployType', sortable: true },
        { title: '包名', key: 'PackageName', sortable: true },
        { title: '版本', key: 'ImgVersion', sortable: true }

      ]
    }
  },
  computed: {
    modalListeners () {
      return {
        ...this.$listeners,
        input (event) {
          this.$emit('input', event.target.value)
        }
      }
    }
  },
  watch: {
    innerVisible (newValue) {
      this.index = 0
      this.getDeploymentsByProduct()
      this.$emit('update:visible', newValue)
    },
    visible (newValue) {
      this.innerVisible = newValue
    }
  },
  mounted () {
    this.index = 0
    this.innerVisible = this.visible
    this.getDeploymentsByProduct()
    this.getClusters()
  },

  methods: {
    getDeploymentsByProduct () {
      if (this.productName) {
        getDeploymentsByProduct(this.productName, this.clusterName ? this.clusterName : '', this.env ? this.env : '').then(res => {
          this.tableData = res.data.data
        })
      }
    },
    getClusters () {
      let param = {
        status: 1,
        clusterType: 1
      }
      getClusterLists(param).then(res => {
        this.clusters = res.data.data
      })
    }
  }
}
</script>
