import axios from '@/libs/api.request'

export const login = ({ userName, password }) => {
  const data = {
    userName,
    password
  }
  return axios.request({
    url: 'login',
    data,
    method: 'post'
  })
}

export const getUserInfo = (token) => {
  return axios.request({
    url: 'get_info',
    params: {
      token
    },
    method: 'get'
  })
}

export const logout = (token) => {
  return axios.request({
    url: 'logout',
    method: 'post'
  })
}

export const updateUser = (user) => {
  var url = '/v1/user/' + user.Id
  return axios.request({
    url: url,
    data: user,
    method: 'put'
  })
}

export const getUserList = (param) => {
  var url = '/v1/user?'
  for (var key in param) {
    url += key + '=' + param[key] + '&'
  }
  return axios.request({
    url: url,
    method: 'get'
  })
}
