<template>
  <div>
    <Card style="padding-top: 20px">
      <Form :label-width="100" inline>
        <FormItem label="名称">
          <Select v-model="searchName" filterable clearable style="width: 300px" @on-change="changeSearch">
            <Option v-for="item in triggers" :value="item.Name" :key="item.Name">{{ item.Name }}({{ item.AliasName }})</Option>
          </Select>
        </FormItem>
        <FormItem label="">
          <Button icon="md-search" type="primary" @click="listTriggers">查询</Button>
        </FormItem>
      </Form>
    </Card>
    <Card style="margin-top: 10px">
      <Button icon="md-add" style="margin: 10px 0;" type="primary" @click="showModal">新增</Button>
      WebHook: aquaman.sysop.yy.com/ws/task/self_healing/webhook
      <tables border ref="tables" v-model="tableData" :columns="columns" :loading="loadingStatus"/>
      <div style="margin: 10px;overflow: hidden">
        <div style="float: right;">
          <Page v-model="pageData" v-bind:total="pageData.totalCount" v-bind:current="pageData.pageNo" @on-change="changePage" @on-page-size-change="changePageSize" show-sizer></Page>
        </div>
      </div>
    </Card>

    <!-- 加/修改配置 -->
    <Modal v-model="modal" :title="title" width="40" :mask-closable="false">
      <Form :label-width="130" :model="trigger">
        <FormItem label="名称">
          <Input v-model="trigger.Name" type="text" class="form-control" style="width: 95%" v-bind:disabled="edit"/>
        </FormItem>
        <FormItem label="别名">
          <Input v-model="trigger.AliasName" type="text" class="form-control" style="width: 95%"/>
        </FormItem>
       <!-- <FormItem label="数据源">
          <Select v-model="trigger.Type" style="width: 95%" @on-change="changeType">
            <Option v-for="item in triggerTypes" :value="item.value" :key="item.value">{{item.label}}</Option>
          </Select>
        </FormItem>-->
        <FormItem label="告警策略Fid" >
          <InputNumber v-model="trigger.AlarmFid" class="form-control" style="width: 95%"/>
        </FormItem>
        <FormItem label="告警策略Sid" >
          <InputNumber v-model="trigger.AlarmSid" type="text" class="form-control" style="width: 95%"/>
        </FormItem>
        <FormItem label="过滤规则">
          <Select v-model="trigger.FilterType" style="width: 95%">
            <Option v-for="item in filterTypes" :value="item.value" :key="item.value">{{item.label}}</Option>
          </Select>
        </FormItem>
        <FormItem label="过滤字段名称" v-if="trigger.FilterType == 'param'">
          <Select v-model="trigger.FilterParam" style="width: 95%">
            <Option value="server_id">server_id</Option>
            <Option value="room_id">room_id</Option>
          </Select>
        </FormItem>
        <FormItem label="过滤范围" v-if="trigger.FilterType == 'param'">
          <InputNumber v-model="trigger.FilterDuration" type="tex" class="form-control" style="width: 90%"/>min
        </FormItem>
      </Form>
      <Row slot="footer">
        <Button type="primary" @click="addOrUpdate">保存</Button>
      </Row>
    </Modal>

  </div>
</template>
<script>
import Tables from '_c/tables'
import DataList from '_c/data-list'
import { getTriggers, addOrUpdateTrigger, deleteTrigger, startOrStopTrigger } from '@/api/self_healing'
import { utcTimeFormat } from '@/libs/util'

export default {
  name: 'self_healing_trigger',
  components: {
    DataList,
    Tables
  },
  data () {
    return {
      searchName: '',
      modal: false,
      trigger: {},
      triggers: [],
      loadingStatus: false,
      edit: false,
      title: '',
      columns: [
        {
          title: '名称',
          key: 'Name',
          minWidth: 60,
          render: (h, params) => {
            let name = params.row.Name
            let aliasName = params.row.AliasName
            return h('div', name + '(' + aliasName + ')')
          }
        },
        { title: 'Fid', key: 'AlarmFid' },
        { title: 'Sid', key: 'AlarmSid' },
        {
          title: '过滤规则',
          key: 'FilterType',
          render: (h, params) => {
            let filterType = params.row.FilterType
            this.filterTypes.forEach(item => {
              if (item.value === filterType) {
                filterType = item.label
              }
            })
            return h('div', filterType)
          }
        },
        { title: '过滤字段', key: 'FilterParam' },
        { title: '过滤范围(分钟)', key: 'FilterDuration' },
        {
          title: '状态',
          key: 'Status',
          render: (h, params) => {
            var status = params.row.Status
            if (status === 1) {
              return h('Tag', { props: { color: 'success' } }, '可用')
            } else {
              return h('Tag', { props: { color: 'error' } }, '不可用')
            }
          }
        },
        { title: '更新时间',
          key: 'UpdateTime',
          tooltip: true,
          maxWidth: 200,
          render: (h, params) => {
            let updateTime = params.row.UpdateTime
            return h('div', utcTimeFormat(updateTime))
          }
        },
        { title: '更新人', key: 'Updator' },
        {
          title: '操作',
          key: 'handle',
          maxWidth: 100,
          button: [
            (h, params) => {
              return h('Button', {
                props: {
                  type: 'info',
                  size: 'small'
                },
                style: {
                  marginTop: '2px',
                  marginLeft: '5px'
                },
                on: {
                  click: () => {
                    this.showModal(params.row, '编辑触发器')
                  }
                }
              }, '编辑')
            },
            (h, params) => {
              let status = params.row.Status
              let action = 'start'
              let actionName = '启用'
              if (status === 1) {
                action = 'stop'
                actionName = '停用'
              }
              return h('Poptip', {
                props: {
                  confirm: true,
                  title: `你确定要${actionName}吗?`
                },
                on: {
                  'on-ok': () => {
                    this.startOrStop(params.row.Id, action)
                  }
                }
              }, [
                h('Button', {
                  props: {
                    type: 'warning',
                    size: 'small'
                  },
                  style: {
                    marginLeft: '5px',
                    marginTop: '2px'
                  }
                }, actionName)
              ])
            },
            (h, params) => {
              return h('Poptip', {
                props: {
                  confirm: true,
                  title: '你确定要删除吗?'
                },
                on: {
                  'on-ok': () => {
                    this.handleDelete(params.row)
                  }
                }
              }, [
                h('Button', {
                  props: {
                    type: 'error',
                    size: 'small'
                  },
                  style: {
                    marginLeft: '5px',
                    marginTop: '2px'
                  }
                }, '删除')
              ])
            }
          ]
        }
      ],
      pageData: { pageNo: 1, pageSize: 10 },
      tableData: [],
      triggerTypes: [ { value: 'alarm', label: '告警' }, { value: 'event', label: 'k8s事件' } ],
      filterTypes: [ { value: 'none', label: '不过滤' }, { value: 'param', label: '按字段过滤' } ]
    }
  },
  methods: {
    showModal (trigger, title) {
      this.title = title
      if (!title) {
        this.trigger = {}
        this.trigger.Type = 'alarm'
        this.trigger.AlarmFid = 0
        this.trigger.AlarmSid = 0
        this.trigger.FilterParam = ''
        this.trigger.FilterDuration = 1
        this.title = '新加触发器'
        this.edit = false
      } else {
        this.trigger = trigger
        this.edit = true
      }
      this.modal = true
    },
    addOrUpdate () {
      if (!this.trigger.Name) {
        this.$Message.error('名称必填')
        return
      }
      if (!this.trigger.FilterType) {
        this.trigger.FilterType = 'none'
      }
      this.$Spin.show()
      addOrUpdateTrigger(this.trigger).then(res => {
        this.$Spin.hide()
        this.modal = false
        if (res.data.code === 0) {
          this.$Message.success(this.title + '成功')
          this.listTriggers()
          this.init()
        } else {
          this.$Message.success(this.title + '失败:' + res.data.msg)
        }
      })
    },
    startOrStop (triggerId, action) {
      this.$Spin.show()
      startOrStopTrigger(triggerId, action).then(res => {
        this.$Spin.hide()
        if (res.data.code === 0) {
          this.$Message.success('操作成功')
          this.listTriggers()
        } else {
          this.$Message.error(res.data)
        }
      })
    },
    handleDelete (trigger) {
      this.$Spin.show()
      deleteTrigger(trigger.Id).then(res => {
        this.$Spin.hide()
        if (res.data.code === 0) {
          this.$Message.success('删除成功')
          this.listTriggers()
          this.init()
        } else {
          this.$Message.error(res.data)
        }
      })
    },
    changeSearch () {
      this.pageData.pageNo = 1
    },
    listTriggers () {
      var param = {
        pageNo: this.pageData.pageNo,
        pageSize: this.pageData.pageSize,
        query: ''
      }
      param['sort'] = 'create_time'
      param['order'] = 'desc'
      if (this.searchName) {
        param.query = 'name:' + this.searchCluster
      }
      this.tableData = []
      this.loadingStatus = true
      getTriggers(param).then(res => {
        this.loadingStatus = false
        this.pageData = res.data
        this.tableData = this.pageData.data
        this.pageData.data = []
      })
    },
    changePage (pageNo) {
      this.pageData.pageNo = pageNo
      this.listTriggers()
    },
    changePageSize (pageSize) {
      this.pageData.pageSize = pageSize
      this.listTriggers()
    },
    init () {
      var param = {
        pageNo: 1,
        pageSize: 999999
      }
      param['sort'] = 'create_time'
      param['order'] = 'desc'
      getTriggers(param).then(res => {
        if (res.data.code === 0) {
          this.triggers = res.data.data
        }
      })
    }
  },
  mounted () {
    this.$Message.config({
      top: 100,
      duration: 3
    })
    this.init()
    this.listTriggers()
  }

}
</script>
