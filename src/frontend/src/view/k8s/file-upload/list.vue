<template>
  <Tabs value="cluster_conf">
    <TabPane label="集群配置" name="cluster_conf">
      <div>
        <Card style="padding-top: 20px">
          <Form :label-width="100" inline>
            <FormItem label="集群">
              <Select v-model="conf.searchCluster" clearable filterable style="width:200px">
                <Option v-for="cluster in clusters" :value="cluster.ClusterName" :key="cluster.ClusterName">{{ cluster.ClusterName }}({{cluster.AliasName}})</Option>
              </Select>
            </FormItem>
            <FormItem label="">
              <Button icon="md-search" type="primary" @click="getFileUploadConfs">查询</Button>
            </FormItem>
          </Form>
        </Card>
        <Card style="margin-top: 10px">
          <Button icon="md-add" style="margin: 10px 0;" type="primary" @click="showConfModel">新增</Button>
          <Button icon="ios-cloud-download-outline" style="margin-left: 10px;" type="info" @click="showUploadModel">下发</Button>
          <Button icon="md-sync" style="margin-left: 10px;" type="success" @click="syncToCluster">同步</Button>
          <tables border ref="selection" v-model="conf.tableData" :columns="conf.columns" @on-selection-change="selectionChanged"/>
          <div style="margin: 10px;overflow: hidden">
            <div style="float: right;">
              <Page v-model="conf.pageData" v-bind:total="conf.pageData.totalCount" v-bind:current="conf.pageData.pageNo" @on-change="changeConfPage" @on-page-size-change="changeConfPageSize" show-sizer></Page>
            </div>
          </div>
        </Card>

        <!-- 加/修改配置 -->
        <Modal v-model="conf.modal" :title="conf.title" width="30" :mask-closable="false">
          <Form :label-width="100" :model="conf.confModel">
            <FormItem label="集群">
              <Select v-model="conf.confModel.ClusterName" clearable filterable style="width:300px" @on-change="changeCluster" v-bind:disabled="conf.isEditor">
                <Option v-for="cluster in clusters" :value="cluster.ClusterName" :key="cluster.ClusterName">{{ cluster.ClusterName }}({{cluster.AliasName}})</Option>
              </Select>
            </FormItem>
            <FormItem label="文件节点">
              <Input v-model="conf.confModel.NodeIp" placeholder="文件节点" style="width: 300px" />
              <!--<Select v-model="conf.confModel.NodeIp" clearable filterable style="width:200px">
                <Option v-for="node in nodes" :value="node.Ip" :key="node.Ip">{{ node.Ip }}</Option>
              </Select>-->
            </FormItem>
          </Form>
          <Row slot="footer">
            <Button type="primary" @click="addOrUpdate">确定</Button>
          </Row>
        </Modal>

        <!-- 下发文件 -->
        <Modal v-model="upload.modal" title="文件下发" width="35" :mask-closable="false">
          <Form :label-width="100">
            <template>
              <Upload
                :data="uploadParam"
                :show-upload-list="false"
                :max-size="3120000"
                :on-success="handleSuccess"
                :on-progress="handleProgress"
                multiple
                action="/v1/file_upload/upload" name="myfile" style="display: inline">
                <Button icon="ios-cloud-upload-outline" v-if="currentPath != 'root'">上传文件</Button>
              </Upload>
              <Button icon="ios-add-circle-outline" style="margin-left: 10px" @click="showAddDirectoryModel" v-if="currentPath == 'root'">新建模块</Button>
            </template>
            <div style="margin-top: 10px"></div>
            <div v-for="(item, index) in currentPaths"
                 :key="index" style="display: inline;">
              <Icon type="ios-folder" />
              <a @click="listFiles(item.path)" type="text">{{item.folderName}}</a> >>
            </div>
            <div class="clear"></div>
            <tables ref="files" v-model="files.tableData" :columns="files.columns"/>
            <div style="margin-bottom: 10px"></div>
          </Form>
          <Row slot="footer">
            <Button type="primary" @click="uploadToCluster">确定</Button>
          </Row>
        </Modal>
      </div>

      <Modal  v-model="directoryModel.modal" title="新建文件夹" width="20" @on-ok="addDirectory()" class-name="vertical-center-modal" :mask-closable="false">
        <Form  :label-width="80" ref="directoryModel" :model="directoryModel">
          <FormItem label="目录名称">
            <Input type="text" class="form-control" v-model="directoryModel.DirectoryName"/>
          </FormItem>
        </Form>
      </Modal>

      <Modal v-model="upInfo.modal" title="文件下发详情" width="70" :mask-closable="false">
        <tables border ref="tables" v-model="upInfo.tableData" :columns="upInfo.columns"/>
      </Modal>
    </TabPane>
    <TabPane label="下发日志" name="upload_log">
      <div>
        <Card style="padding-top: 20px">
          <Form :label-width="100" inline>
            <FormItem label="集群">
              <Select v-model="upLog.searchCluster" clearable filterable style="width:200px">
                <Option v-for="cluster in clusters" :value="cluster.ClusterName" :key="cluster.ClusterName">{{ cluster.ClusterName }}({{cluster.AliasName}})</Option>
              </Select>
            </FormItem>
            <FormItem label="">
              <Button icon="md-search" type="primary" @click="getFileUploadLogs">查询</Button>
            </FormItem>
          </Form>
        </Card>
        <Card style="margin-top: 10px">
          <tables ref="tables" v-model="upLog.tableData" :columns="upLog.columns"/>
          <div style="margin: 10px;overflow: hidden">
            <div style="float: right;">
              <Page v-model="upLog.pageData" v-bind:total="upLog.pageData.totalCount" v-bind:current="upLog.pageData.pageNo" @on-change="changeUpLogPage" @on-page-size-change="changeUpLogPageSize" show-sizer></Page>
            </div>
          </div>
        </Card>
      </div>
    </TabPane>
    <TabPane label="同步日志" name="sync_log">
      <div>
        <Card style="padding-top: 20px">
          <Form :label-width="100" inline>
            <FormItem label="集群">
              <Select v-model="syncLog.searchCluster" clearable filterable style="width:200px">
                <Option v-for="cluster in clusters" :value="cluster.ClusterName" :key="cluster.ClusterName">{{ cluster.ClusterName }}({{cluster.AliasName}})</Option>
              </Select>
            </FormItem>
            <FormItem label="">
              <Button icon="md-search" type="primary" @click="getFileUploadSyncLogs">查询</Button>
            </FormItem>
          </Form>
        </Card>
        <Card style="margin-top: 10px">
          <tables ref="tables" v-model="syncLog.tableData" :columns="syncLog.columns"/>
          <div style="margin: 10px;overflow: hidden">
            <div style="float: right;">
              <Page v-model="syncLog.pageData" v-bind:total="syncLog.pageData.totalCount" v-bind:current="syncLog.pageData.pageNo" @on-change="changeSyncLogPage" @on-page-size-change="changeSyncLogPageSize" show-sizer></Page>
            </div>
          </div>
        </Card>
      </div>
    </TabPane>
  </Tabs>
</template>

<script>
import Tables from '_c/tables'
import { getClusterLists, getClusterNodes } from '@/api/cluster'
import {
  getFileUploadSyncLogs,
  getFileUploadLogs,
  addOrUpdateFileUploadConf,
  getFileUploadConfs,
  deleteFileUploadConf,
  getFiles,
  deleteFiles,
  getNewWorkId,
  addDirectory,
  upToCluster,
  syncToCluster,
  syncToNode
} from '@/api/file_upload'
import { getXsrfToken } from '@/libs/util'

export default {
  name: 'file_upload',
  components: {
    Tables
  },
  data () {
    return {
      currentPaths: [{}],
      workId: '',
      currentPath: '',
      separator: '\\',
      directoryModel: {
        modal: false
      },
      upload: {
        modal: false
      },
      uploadParam: { path: '', workId: this.workId, _xsrf: getXsrfToken() },
      clusters: [],
      nodes: [],
      selectClusters: '',
      conf: {
        modal: false,
        title: '',
        confModel: {},
        searchCluster: '',
        columns: [
          { type: 'selection', width: 60, align: 'center' },
          { title: '集群名称', key: 'ClusterName' },
          { title: '集群别名', key: 'ClusterAlias', tooltip: true },
          { title: '文件节点', key: 'NodeIp' },
          { title: '创建人', key: 'Creator' },
          { title: '创建时间', key: 'CreateTime' },
          {
            title: '操作',
            key: 'handle',
            maxWidth: 140,
            button: [
              (h, params) => {
                return h('Button', {
                  props: {
                    type: 'primary',
                    size: 'small'
                  },
                  on: {
                    click: () => {
                      this.showConfModel(params.row, '编辑配置')
                    }
                  }
                }, '编辑')
              },
              (h, params) => {
                return h('Poptip', {
                  props: {
                    confirm: true,
                    title: '你确定要删除吗?'
                  },
                  on: {
                    'on-ok': () => {
                      this.handleDelete(params.row)
                    }
                  }
                }, [
                  h('Button', {
                    props: {
                      type: 'error',
                      size: 'small'
                    },
                    style: {
                      marginLeft: '5px'
                    }
                  }, '删除')
                ])
              }
            ]
          }
        ],
        pageData: { pageNo: 1, pageSize: 10 },
        tableData: []
      },
      upInfo: {
        modal: false,
        columns: [
          { title: '路径', key: 'path' },
          { title: '名称', key: 'name' },
          { title: '大小', key: 'size' },
          {
            title: '状态',
            key: 'status',
            sortable: true,
            render: (h, params) => {
              let status = params.row.status
              if (status === 1) {
                return h('Tag', { props: { color: 'success' } }, '成功')
              } else if (status === 2) {
                return h('Tag', { props: { color: 'error' } }, '失败')
              } else {
                return h('Tag', { props: { color: 'primary' } }, '同步中')
              }
            }
          },
          { title: '错误信息', key: 'errMsg' }
        ],
        tableData: []
      },
      upLog: {
        searchCluster: '',
        columns: [
          { title: '集群名称', key: 'ClusterName' },
          { title: '文件节点', key: 'NodeIp' },
          {
            title: '状态',
            key: 'Status',
            sortable: true,
            render: (h, params) => {
              let status = params.row.Status
              if (status === 1) {
                return h('Tag', { props: { color: 'success' } }, '成功')
              } else if (status === 2) {
                return h('Tag', { props: { color: 'error' } }, '失败')
              } else {
                return h('Tag', { props: { color: 'primary' } }, '下发中')
              }
            }
          },
          { title: '上传时间', key: 'CreateTime', sortable: true },
          { title: '失败原因', key: 'ErrMsg' },
          {
            title: '操作',
            key: 'handle',
            maxWidth: 100,
            button: [
              (h, params) => {
                return h('Button', {
                  props: {
                    type: 'primary',
                    size: 'small'
                  },
                  on: {
                    click: () => {
                      this.showUploadInfo(params.row.UploadInfo)
                    }
                  }
                }, '详情')
              }
            ]
          }
        ],
        pageData: { pageNo: 1, pageSize: 10 },
        tableData: []
      },
      syncLog: {
        searchCluster: '',
        columns: [
          { title: '集群名称', key: 'ClusterName' },
          { title: '同步节点', key: 'SyncIp' },
          { title: '同步时间', key: 'CreateTime', sortable: true },
          {
            title: '状态',
            key: 'Status',
            sortable: true,
            render: (h, params) => {
              let status = params.row.Status
              if (status === 1) {
                return h('Tag', { props: { color: 'success' } }, '成功')
              } else if (status === 2) {
                return h('Tag', { props: { color: 'error' } }, '失败')
              } else {
                return h('Tag', { props: { color: 'primary' } }, '同步中')
              }
            }
          },
          { title: '失败原因', key: 'ErrMsg' },
          {
            title: '操作',
            key: 'handle',
            maxWidth: 100,
            button: [
              (h, params) => {
                return h('Button', {
                  props: {
                    type: 'primary',
                    size: 'small'
                  },
                  on: {
                    click: () => {
                      this.syncToNode(params.row.Id)
                    }
                  }
                }, '重试')
              }
            ]
          }
        ],
        pageData: { pageNo: 1, pageSize: 10 },
        tableData: []
      },
      files: {
        columns: [
          {
            title: '文件名',
            key: 'FileName',
            sortable: true,
            minWidth: 60,
            render: (h, params) => {
              return h('div', [
                h('Icon', {
                  props: {
                    type: params.row.IsDir ? 'ios-folder' : 'ios-document-outline'
                  }
                }),
                h('a', {
                  props: {
                    type: 'primary',
                    size: 'small'
                  },
                  on: {
                    click: () => {
                      if (params.row.IsDir) {
                        this.listFiles(params.row.Path)
                      }
                    }
                  }
                }, params.row.FileName)
              ])
            }
          },
          { title: '大小(Byte)', key: 'Size', sortable: true, maxWidth: 125 },
          { title: '更新时间', key: 'UpdateTime', tooltip: true },
          {
            title: '操作',
            key: 'handle',
            maxWidth: 100,
            button: [
              (h, params) => {
                if (this.isEditor) {
                  return h('div', '')
                }
                return h('Poptip', {
                  props: {
                    confirm: true,
                    title: '你确定要删除吗?'
                  },
                  on: {
                    'on-ok': () => {
                      this.deleteFile(params.tableData[params.index].Path)
                    }
                  }
                }, [
                  h('Button', {
                    props: {
                      type: 'error',
                      size: 'small'
                    },
                    style: {
                      marginLeft: '5px',
                      display: (this.action === 'view') ? 'none' : 'inline-block'
                    }
                  }, '删除')
                ])
              }
            ]
          }
        ],
        tableData: []
      }
    }
  },
  methods: {
    showUploadInfo (upInfo) {
      let info = JSON.parse(upInfo)
      this.upInfo.modal = true
      this.upInfo.tableData = []
      this.addUpInfoData(info)
    },
    addUpInfoData (info) {
      if (info.files && info.files.length > 0) {
        info.files.forEach(file => {
          let ret = {
            'path': info.path,
            'name': file.name,
            'size': file.size,
            'status': file.status,
            'errMsg': file.errMsg
          }
          this.upInfo.tableData.push(ret)
        })
        console.log(this.upInfo.tableData)
      }
      if (info.childs && info.childs.length > 0) {
        info.childs.forEach(child => {
          this.addUpInfoData(child)
        })
      }
    },
    showUploadModel () {
      if (!this.selectClusters) {
        this.$Message.error('请选择要下发的集群')
        return
      }
      this.upload.modal = true
      if (!this.workId) {
        this.getNewWorkId()
      }
    },
    showConfModel (conf, title) {
      this.conf.modal = true
      if (title) {
        this.conf.confModel = { ...conf }
        this.conf.title = title
        this.conf.isEditor = true
        this.changeCluster(conf.ClusterName)
      } else {
        this.conf.confModel = {}
        this.conf.title = '新增集群配置'
        this.conf.isEditor = false
      }
    },
    selectionChanged (selection) {
      this.selectClusters = ''
      if (selection.length > 0) {
        selection.forEach(cluster => {
          if (this.selectClusters) {
            this.selectClusters += ',' + cluster.ClusterName
          } else {
            this.selectClusters = cluster.ClusterName
          }
        })
      }
    },
    syncToNode (logId) {
      this.$Spin.show()
      syncToNode(logId).then(res => {
        this.$Spin.hide()
        if (res.data.code === 0) {
          this.getFileUploadSyncLogs()
          this.$Message.success('同步成功')
        } else {
          this.$Message.error(res.data.msg)
        }
      })
    },
    syncToCluster () {
      if (!this.selectClusters) {
        this.$Message.error('请选择要同步的集群')
        return
      }
      syncToCluster(this.selectClusters).then(res => {
        if (res.data.code === 0) {
          this.upload.modal = false
          this.workId = ''
          this.$Message.success('请求成功,同步中...')
        } else {
          this.$Message.error(res.data.msg)
        }
      })
    },
    uploadToCluster () {
      let param = {
        clusters: this.selectClusters,
        workId: this.workId
      }
      if (this.files.tableData.length === 0) {
        this.$Message.error('请上传文件')
        return
      }
      upToCluster(param).then(res => {
        if (res.data.code === 0) {
          this.upload.modal = false
          this.workId = ''
          this.$Message.success('文件上传成功,下发中...')
        } else {
          this.$Message.error(res.data.msg)
        }
      })
    },
    addOrUpdate () {
      let conf = { ...this.conf.confModel }
      if (!conf.ClusterName) {
        this.$Message.error('集群未选择')
        return
      }
      if (!conf.NodeIp) {
        this.$Message.error('文件节点未选择')
        return
      }
      addOrUpdateFileUploadConf(conf).then(res => {
        if (res.data.code === 0) {
          this.conf.modal = false
          this.$Message.success(this.conf.title + '成功')
          this.getFileUploadConfs()
        } else {
          this.$Message.error(res.data.msg)
        }
      })
    },
    handleDelete (conf) {
      deleteFileUploadConf(conf.Id).then(res => {
        if (res.data.code === 0) {
          this.$Message.success('删除配置成功')
          this.getFileUploadConfs()
        } else {
          this.$Message.error(res.data.msg)
        }
      })
    },
    changeCluster (clusterName) {
      if (!clusterName) {
        return
      }
      getClusterNodes(clusterName).then(res => {
        this.nodes = res.data.data
      })
    },
    getFileUploadSyncLogs () {
      var param = {
        pageNo: this.syncLog.pageData.pageNo,
        pageSize: this.syncLog.pageData.pageSize
      }
      if (this.syncLog.searchCluster) {
        param['clusterName'] = this.syncLog.searchCluster
      }
      getFileUploadSyncLogs(param).then(res => {
        this.syncLog.pageData = res.data
        this.syncLog.tableData = [ ...this.syncLog.pageData.data ]
        this.syncLog.pageData.data = null
      })
    },
    getFileUploadLogs () {
      var param = {
        pageNo: this.upLog.pageData.pageNo,
        pageSize: this.upLog.pageData.pageSize
      }
      if (this.upLog.searchCluster) {
        param['clusterName'] = this.upLog.searchCluster
      }
      getFileUploadLogs(param).then(res => {
        this.upLog.pageData = res.data
        this.upLog.tableData = [ ...this.upLog.pageData.data ]
        this.upLog.pageData.data = null
      })
    },
    getFileUploadConfs () {
      var param = {
        pageNo: this.conf.pageData.pageNo,
        pageSize: this.conf.pageData.pageSize
      }
      if (this.conf.searchCluster) {
        param['clusterName'] = this.conf.searchCluster
      }
      getFileUploadConfs(param).then(res => {
        this.conf.pageData = res.data
        this.conf.tableData = [ ...this.conf.pageData.data ]
        this.conf.pageData.data = null
      })
    },
    getClusters () {
      var param = {
        status: 1
      }
      getClusterLists(param).then(res => {
        this.clusters = res.data.data
      })
    },
    listFiles (path) {
      this.currentPath = path
      this.uploadParam.path = path
      this.uploadParam.workId = this.workId
      let paths = path.split(this.separator)
      let pathMap = []
      let temp = ''
      for (let i = 0; i < paths.length; i++) {
        temp = temp + (i === 0 ? '' : this.separator) + paths[i]
        let object = {
          'path': temp,
          'folderName': paths[i]
        }
        pathMap.push(object)
      }
      this.currentPaths = pathMap
      this.fileResultReturn = false
      setTimeout(
        () => {
          if (!this.fileResultReturn) {
            this.$Spin.show({
              render: (h) => {
                return h('div', [
                  h('Icon', {
                    'class': 'demo-spin-icon-load',
                    props: {
                      type: 'ios-loading',
                      size: 24
                    }
                  }),
                  h('div', '本地没缓存,从bs2下载文件中,请稍候···')
                ])
              }
            })
          }
        }
        , 1000)
      getFiles(this.workId, path).then(res => {
        this.fileResultReturn = true
        this.$Spin.hide()
        if (res.data) {
          this.files.tableData = res.data
        } else {
          this.files.tableData = []
        }
      })
    },
    deleteFile (path) {
      deleteFiles(this.workId, path).then(res => {
        this.listFiles(this.currentPath)
      })
    },
    getNewWorkId () {
      getNewWorkId().then(res => {
        this.workId = res.data
        this.listFiles('root')
      })
    },
    addDirectory () {
      if (this.directoryModel.DirectoryName) {
        addDirectory(this.workId, this.currentPath, this.directoryModel.DirectoryName).then(res => {
          this.directoryModel.DirectoryName = ''
          this.listFiles(this.currentPath)
        })
      } else {
        this.$Message.error('文件夹名称不能为空!')
      }
    },
    showAddDirectoryModel () {
      if (this.currentPath !== 'root') {
        this.$Message.error('只支持2级目录')
        return
      }
      this.directoryModel.modal = true
    },
    handleProgress (event, result) {
      if (result.showProgress) {
        this.$Spin.show({
          render (h) {
            return h('Progress', {
              props: {
                percent: result.percentage.toFixed(1)
              },
              'style': 'width:800px;'
            })
          }
        })
      }
    },
    handleSuccess () {
      this.listFiles(this.currentPath)
      this.$Spin.hide()
    },
    changeConfPage (pageNo) {
      this.conf.pageData.pageNo = pageNo
      this.getFileUploadConfs()
    },
    changeConfPageSize (pageSize) {
      this.conf.pageData.pageSize = pageSize
      this.getFileUploadConfs()
    },
    changeUpLogPage (pageNo) {
      this.upLog.pageData.pageNo = pageNo
      this.getFileUploadLogs()
    },
    changeUpLogPageSize (pageSize) {
      this.upLog.pageData.pageSize = pageSize
      this.getFileUploadLogs()
    },
    changeSyncLogPage (pageNo) {
      this.syncLog.pageData.pageNo = pageNo
      this.getFileUploadSyncLogs()
    },
    changeSyncLogPageSize (pageSize) {
      this.syncLog.pageData.pageSize = pageSize
      this.getFileUploadSyncLogs()
    }
  },
  mounted () {
    this.getFileUploadConfs()
    this.getFileUploadLogs()
    this.getFileUploadSyncLogs()
    this.getClusters()
  }
}

</script>
