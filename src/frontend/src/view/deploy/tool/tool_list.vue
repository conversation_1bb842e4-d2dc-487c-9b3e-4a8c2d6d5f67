<template>
  <Tabs v-model="currentView" style="margin-top: 20px">
    <TabPane label="pod ip查实例" name="podInstance"><PodInstance /></TabPane>
    <TabPane label="镜像同步" name="repoImageSync"><RepoImageSync /></TabPane>
    <TabPane label="物理机部署" name="physicDeploy"><PhysicDeploy /></TabPane>
  </Tabs>
</template>

<script>
import PodInstance from '@/view/deploy/tool/pod_instance'
import RepoImageSync from '@/view/deploy/tool/repo_image_sync'
import PhysicDeploy from '@/view/deploy/deployment/physic_deploy_list'

export default {
  components: {
    PodInstance,
    RepoImageSync,
    PhysicDeploy
  },
  data () {
    return {
      currentView: 'podInstance'
    }
  },
  mounted () {
    let param = this.$route.query
    if (param.toolType) {
      this.currentView = param.toolType
    }
  }
}
</script>
