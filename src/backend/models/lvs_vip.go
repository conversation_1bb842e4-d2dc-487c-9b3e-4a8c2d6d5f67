package models

import (
	"errors"
	"fmt"
	"k8s.io/apimachinery/pkg/util/sets"
	"reflect"
	"strings"
	"time"

	"github.com/astaxie/beego/orm"
)

type LvsVip struct {
	Id           int       `orm:"column(id);auto"`
	LvsGroupName string    `orm:"column(lvs_group_name)"`
	Vip          string    `orm:"column(vip);size(128)"`
	IspType      int       `orm:"column(isp_type);null"`
	CreateTime   time.Time `orm:"column(create_time);type(datetime);null"`
	Creator      string    `orm:"column(creator);size(32);null"`
	IpType       int       `orm:"column(ip_type);null"`
	Occupied     bool      `orm:"-"`
}

func (t *LvsVip) TableName() string {
	return "lvs_vip"
}

// AddLvsVip insert a new LvsVip into database and returns
// last inserted Id on success.
func AddLvsVip(m *LvsVip, ormer ...orm.Ormer) (id int64, err error) {
	var o orm.Ormer
	if len(ormer) == 0 {
		o = orm.NewOrm()
	} else {
		o = ormer[0]
	}
	id, err = o.Insert(m)
	return
}

// GetLvsVipById retrieves LvsVip by Id. Returns error if
// Id doesn't exist
func GetLvsVipById(id int) (v *LvsVip, err error) {
	o := orm.NewOrm()
	v = &LvsVip{Id: id}
	if err = o.Read(v); err == nil {
		return v, nil
	}
	return nil, err
}

func GetLvsVipByVip(vip string) (*LvsVip, error) {
	o := orm.NewOrm()
	lvsVips := make([]*LvsVip, 0)
	_, err := o.Raw("select * from lvs_vip where vip=? order by create_time desc", vip).QueryRows(&lvsVips)
	if err != nil || len(lvsVips) == 0 {
		return nil, err
	}
	return lvsVips[0], nil
}

func GetLvsVipByVips(vips []string, ormer ...orm.Ormer) ([]*LvsVip, error) {
	var o orm.Ormer
	if len(ormer) == 0 {
		o = orm.NewOrm()
	} else {
		o = ormer[0]
	}
	lvsVips := make([]*LvsVip, 0)
	_, err := o.Raw("select * from lvs_vip where find_in_set(vip, ?) order by create_time desc", strings.Join(vips, ",")).QueryRows(&lvsVips)
	if err != nil {
		return nil, err
	}
	return lvsVips, nil
}

func GetAllLvsVipByLvsGroupName(lvsGroupName string) ([]*LvsVip, error) {
	o := orm.NewOrm()
	lvsVips := make([]*LvsVip, 0)
	_, err := o.Raw("select * from lvs_vip where lvs_group_name=? order by create_time desc", lvsGroupName).QueryRows(&lvsVips)
	if err != nil {
		return nil, err
	}
	return lvsVips, nil
}

func GetAllLvsVips() ([]string, error) {
	o := orm.NewOrm()
	lvsVips := make([]string, 0)
	_, err := o.Raw("select vip from lvs_vip order by create_time desc").QueryRows(&lvsVips)
	if err != nil {
		return nil, err
	}
	return lvsVips, nil
}

// GetAllLvsVip retrieves all LvsVip matches certain condition. Returns empty list if
// no records exist
func GetAllLvsVip(query map[string]string, fields []string, sortby []string, order []string,
	offset int64, limit int64) (ml []interface{}, count int64, err error) {
	o := orm.NewOrm()
	qs := o.QueryTable(new(LvsVip))
	// query k=v
	for k, v := range query {
		// rewrite dot-notation to Object__Attribute
		k = strings.Replace(k, ".", "__", -1)
		if strings.Contains(k, "isnull") {
			qs = qs.Filter(k, (v == "true" || v == "1"))
		} else {
			qs = qs.Filter(k, v)
		}
	}
	//分页
	count = 0
	if count, err = qs.Count(); err != nil {
		return nil, 0, errors.New("Error: Count error")
	}
	// order by:
	var sortFields []string
	if len(sortby) != 0 {
		if len(sortby) == len(order) {
			// 1) for each sort field, there is an associated order
			for i, v := range sortby {
				orderby := ""
				if order[i] == "desc" {
					orderby = "-" + v
				} else if order[i] == "asc" {
					orderby = v
				} else {
					return nil, 0, errors.New("Error: Invalid order. Must be either [asc|desc]")
				}
				sortFields = append(sortFields, orderby)
			}
			qs = qs.OrderBy(sortFields...)
		} else if len(sortby) != len(order) && len(order) == 1 {
			// 2) there is exactly one order, all the sorted fields will be sorted by this order
			for _, v := range sortby {
				orderby := ""
				if order[0] == "desc" {
					orderby = "-" + v
				} else if order[0] == "asc" {
					orderby = v
				} else {
					return nil, 0, errors.New("Error: Invalid order. Must be either [asc|desc]")
				}
				sortFields = append(sortFields, orderby)
			}
		} else if len(sortby) != len(order) && len(order) != 1 {
			return nil, 0, errors.New("Error: 'sortby', 'order' sizes mismatch or 'order' size is not 1")
		}
	} else {
		if len(order) != 0 {
			return nil, 0, errors.New("Error: unused 'order' fields")
		}
	}

	var l []LvsVip
	qs = qs.OrderBy(sortFields...)
	if _, err = qs.Limit(limit, offset).All(&l, fields...); err == nil {
		occupiedVips := filterOccupied(l)
		if len(fields) == 0 {
			for ind, v := range l {
				l[ind].Occupied = occupiedVips.Has(v.Vip)
				ml = append(ml, l[ind])
			}
		} else {
			// trim unused fields
			for ind, v := range l {
				l[ind].Occupied = occupiedVips.Has(v.Vip)
				m := make(map[string]interface{})
				val := reflect.ValueOf(l[ind])
				for _, fname := range fields {
					m[fname] = val.FieldByName(fname).Interface()
				}
				ml = append(ml, m)
			}
		}
		return ml, count, nil
	}
	return nil, 0, err
}

func filterOccupied(l []LvsVip) sets.String {
	vips := sets.NewString()
	for _, item := range l {
		vips.Insert("'" + item.Vip + "'")
	}
	var existingVips []string
	if vips.Len() == 0 {
		return sets.NewString()
	}
	Ormer().Raw(fmt.Sprintf(`
select distinct hvip.vip
from hulk_vip hvip left join hulk_business hb on  hb.business_name=hvip.business_name
where hb.business_name is not null and hvip.vip in (%s)`, strings.Join(vips.List(), ","))).QueryRows(&existingVips)
	return sets.NewString(existingVips...)
}

// UpdateLvsVip updates LvsVip by Id and returns error if
// the record to be updated doesn't exist
func UpdateLvsVipById(m *LvsVip) (err error) {
	o := orm.NewOrm()
	v := LvsVip{Id: m.Id}
	// ascertain id exists in the database
	if err = o.Read(&v); err == nil {
		var num int64
		if num, err = o.Update(m); err == nil {
			fmt.Println("Number of records updated in database:", num)
		}
	}
	return
}

// DeleteLvsVip deletes LvsVip by Id and returns error if
// the record to be deleted doesn't exist
func DeleteLvsVip(id int) (err error) {
	o := orm.NewOrm()
	v := LvsVip{Id: id}
	// ascertain id exists in the database
	if err = o.Read(&v); err == nil {
		var num int64
		if num, err = o.Delete(&LvsVip{Id: id}); err == nil {
			fmt.Println("Number of records deleted in database:", num)
		}
	}
	return
}
