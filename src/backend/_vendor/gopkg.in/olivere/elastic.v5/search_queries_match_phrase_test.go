// Copyright 2012-present <PERSON>. All rights reserved.
// Use of this source code is governed by a MIT-license.
// See http://olivere.mit-license.org/license.txt for details.

package elastic

import (
	"encoding/json"
	"testing"
)

func TestMatchPhraseQuery(t *testing.T) {
	q := NewMatchPhraseQuery("message", "this is a test").
		Analyzer("my_analyzer").
		Boost(0.7)
	src, err := q.Source()
	if err != nil {
		t.Fatal(err)
	}
	data, err := json.Marshal(src)
	if err != nil {
		t.Fatalf("marshaling to JSON failed: %v", err)
	}
	got := string(data)
	expected := `{"match_phrase":{"message":{"analyzer":"my_analyzer","boost":0.7,"query":"this is a test"}}}`
	if got != expected {
		t.<PERSON>("expected\n%s\n,got:\n%s", expected, got)
	}
}
