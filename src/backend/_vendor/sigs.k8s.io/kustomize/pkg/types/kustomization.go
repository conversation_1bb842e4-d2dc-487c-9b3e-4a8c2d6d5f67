/*
Copyright 2017 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Package types holds struct definitions that should find a better home.
package types

import (
	"sigs.k8s.io/kustomize/pkg/patch"
)

// TypeMeta copies apimachinery/pkg/apis/meta/v1.TypeMeta
type TypeMeta struct {
	// Kind copies apimachinery/pkg/apis/meta/v1.Typemeta.Kind
	Kind string `json:"kind,omitempty" protobuf:"bytes,1,opt,name=kind"`

	// APIVersion copies apimachinery/pkg/apis/meta/v1.Typemeta.APIVersion
	APIVersion string `json:"apiVersion,omitempty" protobuf:"bytes,2,opt,name=apiVersion"`
}

// Kustomization holds the information needed to generate customized k8s api resources.
type Kustomization struct {
	TypeMeta `json:",inline" yaml:",inline"`

	//
	// Operators - what kustomize can do.
	//

	// NamePrefix will prefix the names of all resources mentioned in the kustomization
	// file including generated configmaps and secrets.
	NamePrefix string `json:"namePrefix,omitempty" yaml:"namePrefix,omitempty"`

	// NameSuffix will suffix the names of all resources mentioned in the kustomization
	// file including generated configmaps and secrets.
	NameSuffix string `json:"nameSuffix,omitempty" yaml:"nameSuffix,omitempty"`

	// Namespace to add to all objects.
	Namespace string `json:"namespace,omitempty" yaml:"namespace,omitempty"`

	// CommonLabels to add to all objects and selectors.
	CommonLabels map[string]string `json:"commonLabels,omitempty" yaml:"commonLabels,omitempty"`

	// CommonAnnotations to add to all objects.
	CommonAnnotations map[string]string `json:"commonAnnotations,omitempty" yaml:"commonAnnotations,omitempty"`

	// PatchesStrategicMerge specifies the relative path to a file
	// containing a strategic merge patch.  Format documented at
	// https://github.com/kubernetes/community/blob/master/contributors/devel/strategic-merge-patch.md
	// URLs and globs are not supported.
	PatchesStrategicMerge []patch.StrategicMerge `json:"patchesStrategicMerge,omitempty" yaml:"patchesStrategicMerge,omitempty"`

	// JSONPatches is a list of JSONPatch for applying JSON patch.
	// Format documented at https://tools.ietf.org/html/rfc6902
	// and http://jsonpatch.com
	PatchesJson6902 []patch.Json6902 `json:"patchesJson6902,omitempty" yaml:"patchesJson6902,omitempty"`

	// ImageTags is a list of (image name, new tag) pairs for simply
	// changing a an image tag.  This can also be achieved with a
	// patch, but this operator is simpler to specify.
	ImageTags []ImageTag `json:"imageTags,omitempty" yaml:"imageTags,omitempty"`

	// Vars allow things modified by kustomize to be injected into a
	// container specification. A var is a name (e.g. FOO) associated
	// with a field in a specific resource instance.  The field must
	// contain a value of type string, and defaults to the name field
	// of the instance.  Any appearance of "$(FOO)" in the container
	// spec will be replaced at kustomize build time, after the final
	// value of the specified field has been determined.
	Vars []Var `json:"vars,omitempty" yaml:"vars,omitempty"`

	//
	// Operands - what kustomize operates on.
	//

	// Resources specifies relative paths to files holding YAML representations
	// of kubernetes API objects. URLs and globs not supported.
	Resources []string `json:"resources,omitempty" yaml:"resources,omitempty"`

	// Crds specifies relative paths to Custom Resource Definition files.
	// This allows custom resources to be recognized as operands, making
	// it possible to add them to the Resources list.
	// CRDs themselves are not modified.
	Crds []string `json:"crds,omitempty" yaml:"crds,omitempty"`

	// Bases are relative paths or github repository URLs specifying a
	// directory containing a kustomization.yaml file.
	// URL format: https://github.com/hashicorp/go-getter#url-format
	Bases []string `json:"bases,omitempty" yaml:"bases,omitempty"`

	//
	// Generators (operators that create operands)
	//

	// ConfigMapGenerator is a list of configmaps to generate from
	// local data (one configMap per list item).
	// The resulting resource is a normal operand, subject to
	// name prefixing, patching, etc.  By default, the name of
	// the map will have a suffix hash generated from its contents.
	ConfigMapGenerator []ConfigMapArgs `json:"configMapGenerator,omitempty" yaml:"configMapGenerator,omitempty"`

	// SecretGenerator is a list of secrets to generate from
	// local data (one secret per list item).
	// The resulting resource is a normal operand, subject to
	// name prefixing, patching, etc.  By default, the name of
	// the map will have a suffix hash generated from its contents.
	SecretGenerator []SecretArgs `json:"secretGenerator,omitempty" yaml:"secretGenerator,omitempty"`

	// GeneratorOptions modify behavior of all ConfigMap and Secret generators.
	GeneratorOptions *GeneratorOptions `json:"generatorOptions,omitempty" yaml:"generatorOptions,omitempty"`

	// Configurations is a list of transformer configuration files
	Configurations []string `json:"configurations,omitempty" yaml:"configurations,omitempty"`

	//
	// Deprecated fields - See DealWithDeprecatedFields
	//

	// Deprecated.
	Patches []string `json:"patches,omitempty" yaml:"patches,omitempty"`
}

// DealWithDeprecatedFields should be called immediately after
// loading from storage.
func (k *Kustomization) DealWithDeprecatedFields() {
	if len(k.Patches) > 0 {
		// The Patches field, meant to hold strategic merge
		// patches, is deprecated. Append anything found
		// there to the PatchesStrategicMerge field.
		// This happened when the PatchesJson6902 field
		// was introduced.
		k.PatchesStrategicMerge = patch.Append(
			k.PatchesStrategicMerge, k.Patches...)
		k.Patches = []string{}
	}
}

// ConfigMapArgs contains the metadata of how to generate a configmap.
type ConfigMapArgs struct {
	// Name of the configmap.
	// The full name should be Kustomization.NamePrefix + Configmap.Name +
	// hash(content of configmap).
	Name string `json:"name,omitempty" yaml:"name,omitempty"`

	// behavior of configmap, must be one of create, merge and replace
	// 'create': create a new one;
	// 'replace': replace the existing one;
	// 'merge': merge the existing one.
	Behavior string `json:"behavior,omitempty" yaml:"behavior,omitempty"`

	// DataSources for configmap.
	DataSources `json:",inline,omitempty" yaml:",inline,omitempty"`
}

// SecretArgs contains the metadata of how to generate a secret.
type SecretArgs struct {
	// Name of the secret.
	// The full name should be Kustomization.NamePrefix + SecretGenerator.Name +
	// hash(content of secret).
	Name string `json:"name,omitempty" yaml:"name,omitempty"`

	// behavior of secretGenerator, must be one of create, merge and replace
	// 'create': create a new one;
	// 'replace': replace the existing one;
	// 'merge': merge the existing one.
	Behavior string `json:"behavior,omitempty" yaml:"behavior,omitempty"`

	// Type of the secret.
	//
	// This is the same field as the secret type field in v1/Secret:
	// It can be "Opaque" (default), or "kubernetes.io/tls".
	//
	// If type is "kubernetes.io/tls", then "Commands" must have exactly two
	// keys: "tls.key" and "tls.crt"
	Type string `json:"type,omitempty" yaml:"type,omitempty"`

	// CommandSources for secret.
	CommandSources `json:",inline,omitempty" yaml:",inline,omitempty"`

	// Deprecated.
	// Replaced by GeneratorOptions.TimeoutSeconds
	// TimeoutSeconds specifies the timeout for commands.
	TimeoutSeconds *int64 `json:"timeoutSeconds,omitempty" yaml:"timeoutSeconds,omitempty"`
}

// CommandSources contains some generic sources for secrets.
type CommandSources struct {
	// Map of keys to commands to generate the values
	Commands map[string]string `json:"commands,omitempty" yaml:"commands,omitempty"`
	// EnvCommand to output lines of key=val pairs to create a secret.
	// i.e. a Docker .env file or a .ini file.
	EnvCommand string `json:"envCommand,omitempty" yaml:"envCommand,omitempty"`
}

// DataSources contains some generic sources for configmaps.
type DataSources struct {
	// LiteralSources is a list of literal sources.
	// Each literal source should be a key and literal value,
	// e.g. `somekey=somevalue`
	// It will be similar to kubectl create configmap|secret --from-literal
	LiteralSources []string `json:"literals,omitempty" yaml:"literals,omitempty"`

	// FileSources is a list of file sources.
	// Each file source can be specified using its file path, in which case file
	// basename will be used as configmap key, or optionally with a key and file
	// path, in which case the given key will be used.
	// Specifying a directory will iterate each named file in the directory
	// whose basename is a valid configmap key.
	// It will be similar to kubectl create configmap|secret --from-file
	FileSources []string `json:"files,omitempty" yaml:"files,omitempty"`

	// EnvSource format should be a path to a file to read lines of key=val
	// pairs to create a configmap.
	// i.e. a Docker .env file or a .ini file.
	EnvSource string `json:"env,omitempty" yaml:"env,omitempty"`
}

// ImageTag contains an image and a new tag, which will replace the original tag.
type ImageTag struct {
	// Name is a tag-less image name.
	Name string `json:"name,omitempty" yaml:"name,omitempty"`

	// NewTag is the value to use in replacing the original tag.
	NewTag string `json:"newTag,omitempty" yaml:"newTag,omitempty"`

	// Digest is the value used to replace the original image tag.
	// If digest is present NewTag value is ignored.
	Digest string `json:"digest,omitempty" yaml:"digest,omitempty"`
}

// GeneratorOptions modify behavior of all ConfigMap and Secret generators.
type GeneratorOptions struct {
	// Labels to add to all generated resources.
	Labels map[string]string `json:"labels,omitempty" yaml:"labels,omitempty"`

	// Annotations to add to all generated resources.
	Annotations map[string]string `json:"annotations,omitempty" yaml:"annotations,omitempty"`

	// TimeoutSeconds specifies the timeout for commands, if any,
	// used in resource generation.  At time of writing, the default
	// was specified in configmapandsecret.defaultCommandTimeout.
	TimeoutSeconds *int64 `json:"timeoutSeconds,omitempty" yaml:"timeoutSeconds,omitempty"`

	// Shell and arguments to use as a context for commands used in
	// resource generation.  Default at time of writing:  {'sh', '-c'}.
	Shell []string `json:"shell,omitempty" yaml:"shell,omitempty"`

	// DisableNameSuffixHash if true disables the default behavior of adding a
	// suffix to the names of generated resources that is a hash of the
	// resource contents.
	DisableNameSuffixHash bool `json:"disableNameSuffixHash,omitempty" yaml:"disableNameSuffixHash,omitempty"`
}
