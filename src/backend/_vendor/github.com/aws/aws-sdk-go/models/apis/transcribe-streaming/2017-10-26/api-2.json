{"version": "2.0", "metadata": {"apiVersion": "2017-10-26", "endpointPrefix": "transcribestreaming", "protocol": "rest-json", "protocolSettings": {"h2": "eventstream"}, "serviceFullName": "Amazon Transcribe Streaming Service", "serviceId": "Transcribe Streaming", "signatureVersion": "v4", "signingName": "transcribe", "uid": "transcribe-streaming-2017-10-26"}, "operations": {"StartStreamTranscription": {"name": "StartStreamTranscription", "http": {"method": "POST", "requestUri": "/stream-transcription"}, "input": {"shape": "StartStreamTranscriptionRequest"}, "output": {"shape": "StartStreamTranscriptionResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "LimitExceededException"}, {"shape": "InternalFailureException"}, {"shape": "ConflictException"}, {"shape": "ServiceUnavailableException"}]}}, "shapes": {"Alternative": {"type": "structure", "members": {"Transcript": {"shape": "String"}, "Items": {"shape": "ItemList"}}}, "AlternativeList": {"type": "list", "member": {"shape": "Alternative"}}, "AudioChunk": {"type": "blob"}, "AudioEvent": {"type": "structure", "members": {"AudioChunk": {"shape": "AudioChunk", "eventpayload": true}}, "event": true}, "AudioStream": {"type": "structure", "members": {"AudioEvent": {"shape": "AudioEvent"}}, "eventstream": true}, "BadRequestException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "error": {"httpStatusCode": 400}, "exception": true}, "Boolean": {"type": "boolean"}, "ConflictException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "error": {"httpStatusCode": 409}, "exception": true}, "Double": {"type": "double"}, "InternalFailureException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "Item": {"type": "structure", "members": {"StartTime": {"shape": "Double"}, "EndTime": {"shape": "Double"}, "Type": {"shape": "ItemType"}, "Content": {"shape": "String"}, "VocabularyFilterMatch": {"shape": "Boolean"}, "Speaker": {"shape": "String"}}}, "ItemList": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}}, "ItemType": {"type": "string", "enum": ["pronunciation", "punctuation"]}, "LanguageCode": {"type": "string", "enum": ["en-US", "en-GB", "es-US", "fr-CA", "fr-FR", "en-AU"]}, "LimitExceededException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "error": {"httpStatusCode": 429}, "exception": true}, "MediaEncoding": {"type": "string", "enum": ["pcm"]}, "MediaSampleRateHertz": {"type": "integer", "max": 48000, "min": 8000}, "RequestId": {"type": "string"}, "Result": {"type": "structure", "members": {"ResultId": {"shape": "String"}, "StartTime": {"shape": "Double"}, "EndTime": {"shape": "Double"}, "IsPartial": {"shape": "Boolean"}, "Alternatives": {"shape": "AlternativeList"}}}, "ResultList": {"type": "list", "member": {"shape": "Result"}}, "ServiceUnavailableException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "error": {"httpStatusCode": 503}, "exception": true}, "SessionId": {"type": "string", "pattern": "[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}"}, "StartStreamTranscriptionRequest": {"type": "structure", "required": ["LanguageCode", "MediaSampleRateHertz", "MediaEncoding", "AudioStream"], "members": {"LanguageCode": {"shape": "LanguageCode", "location": "header", "locationName": "x-amzn-transcribe-language-code"}, "MediaSampleRateHertz": {"shape": "MediaSampleRateHertz", "location": "header", "locationName": "x-amzn-transcribe-sample-rate"}, "MediaEncoding": {"shape": "MediaEncoding", "location": "header", "locationName": "x-amzn-transcribe-media-encoding"}, "VocabularyName": {"shape": "VocabularyName", "location": "header", "locationName": "x-amzn-transcribe-vocabulary-name"}, "SessionId": {"shape": "SessionId", "location": "header", "locationName": "x-amzn-transcribe-session-id"}, "AudioStream": {"shape": "AudioStream"}, "VocabularyFilterName": {"shape": "VocabularyFilterName", "location": "header", "locationName": "x-amzn-transcribe-vocabulary-filter-name"}, "VocabularyFilterMethod": {"shape": "VocabularyFilterMethod", "location": "header", "locationName": "x-amzn-transcribe-vocabulary-filter-method"}, "ShowSpeakerLabel": {"shape": "Boolean", "location": "header", "locationName": "x-amzn-transcribe-show-speaker-label"}}, "payload": "AudioStream"}, "StartStreamTranscriptionResponse": {"type": "structure", "members": {"RequestId": {"shape": "RequestId", "location": "header", "locationName": "x-amzn-request-id"}, "LanguageCode": {"shape": "LanguageCode", "location": "header", "locationName": "x-amzn-transcribe-language-code"}, "MediaSampleRateHertz": {"shape": "MediaSampleRateHertz", "location": "header", "locationName": "x-amzn-transcribe-sample-rate"}, "MediaEncoding": {"shape": "MediaEncoding", "location": "header", "locationName": "x-amzn-transcribe-media-encoding"}, "VocabularyName": {"shape": "VocabularyName", "location": "header", "locationName": "x-amzn-transcribe-vocabulary-name"}, "SessionId": {"shape": "SessionId", "location": "header", "locationName": "x-amzn-transcribe-session-id"}, "TranscriptResultStream": {"shape": "TranscriptResultStream"}, "VocabularyFilterName": {"shape": "VocabularyFilterName", "location": "header", "locationName": "x-amzn-transcribe-vocabulary-filter-name"}, "VocabularyFilterMethod": {"shape": "VocabularyFilterMethod", "location": "header", "locationName": "x-amzn-transcribe-vocabulary-filter-method"}, "ShowSpeakerLabel": {"shape": "Boolean", "location": "header", "locationName": "x-amzn-transcribe-show-speaker-label"}}, "payload": "TranscriptResultStream"}, "String": {"type": "string"}, "Transcript": {"type": "structure", "members": {"Results": {"shape": "ResultList"}}}, "TranscriptEvent": {"type": "structure", "members": {"Transcript": {"shape": "Transcript"}}, "event": true}, "TranscriptResultStream": {"type": "structure", "members": {"TranscriptEvent": {"shape": "TranscriptEvent"}, "BadRequestException": {"shape": "BadRequestException"}, "LimitExceededException": {"shape": "LimitExceededException"}, "InternalFailureException": {"shape": "InternalFailureException"}, "ConflictException": {"shape": "ConflictException"}, "ServiceUnavailableException": {"shape": "ServiceUnavailableException"}}, "eventstream": true}, "VocabularyFilterMethod": {"type": "string", "enum": ["remove", "mask", "tag"]}, "VocabularyFilterName": {"type": "string", "max": 200, "min": 1, "pattern": "^[0-9a-zA-Z._-]+"}, "VocabularyName": {"type": "string", "max": 200, "min": 1, "pattern": "^[0-9a-zA-Z._-]+"}}}