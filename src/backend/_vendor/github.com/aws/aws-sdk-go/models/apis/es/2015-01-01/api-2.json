{"version": "2.0", "metadata": {"apiVersion": "2015-01-01", "endpointPrefix": "es", "protocol": "rest-json", "serviceFullName": "Amazon Elasticsearch Service", "serviceId": "Elasticsearch Service", "signatureVersion": "v4", "uid": "es-2015-01-01"}, "operations": {"AcceptInboundCrossClusterSearchConnection": {"name": "AcceptInboundCrossClusterSearchConnection", "http": {"method": "PUT", "requestUri": "/2015-01-01/es/ccs/inboundConnection/{ConnectionId}/accept"}, "input": {"shape": "AcceptInboundCrossClusterSearchConnectionRequest"}, "output": {"shape": "AcceptInboundCrossClusterSearchConnectionResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "LimitExceededException"}, {"shape": "DisabledOperationException"}]}, "AddTags": {"name": "AddTags", "http": {"method": "POST", "requestUri": "/2015-01-01/tags"}, "input": {"shape": "AddTagsRequest"}, "errors": [{"shape": "BaseException"}, {"shape": "LimitExceededException"}, {"shape": "ValidationException"}, {"shape": "InternalException"}]}, "AssociatePackage": {"name": "AssociatePackage", "http": {"method": "POST", "requestUri": "/2015-01-01/packages/associate/{PackageID}/{DomainName}"}, "input": {"shape": "AssociatePackageRequest"}, "output": {"shape": "AssociatePackageResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}]}, "CancelElasticsearchServiceSoftwareUpdate": {"name": "CancelElasticsearchServiceSoftwareUpdate", "http": {"method": "POST", "requestUri": "/2015-01-01/es/serviceSoftwareUpdate/cancel"}, "input": {"shape": "CancelElasticsearchServiceSoftwareUpdateRequest"}, "output": {"shape": "CancelElasticsearchServiceSoftwareUpdateResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}]}, "CreateElasticsearchDomain": {"name": "CreateElasticsearchDomain", "http": {"method": "POST", "requestUri": "/2015-01-01/es/domain"}, "input": {"shape": "CreateElasticsearchDomainRequest"}, "output": {"shape": "CreateElasticsearchDomainResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "DisabledOperationException"}, {"shape": "InternalException"}, {"shape": "InvalidTypeException"}, {"shape": "LimitExceededException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ValidationException"}]}, "CreateOutboundCrossClusterSearchConnection": {"name": "CreateOutboundCrossClusterSearchConnection", "http": {"method": "POST", "requestUri": "/2015-01-01/es/ccs/outboundConnection"}, "input": {"shape": "CreateOutboundCrossClusterSearchConnectionRequest"}, "output": {"shape": "CreateOutboundCrossClusterSearchConnectionResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "InternalException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "DisabledOperationException"}]}, "CreatePackage": {"name": "CreatePackage", "http": {"method": "POST", "requestUri": "/2015-01-01/packages"}, "input": {"shape": "CreatePackageRequest"}, "output": {"shape": "CreatePackageResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "LimitExceededException"}, {"shape": "InvalidTypeException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}]}, "DeleteElasticsearchDomain": {"name": "DeleteElasticsearchDomain", "http": {"method": "DELETE", "requestUri": "/2015-01-01/es/domain/{DomainName}"}, "input": {"shape": "DeleteElasticsearchDomainRequest"}, "output": {"shape": "DeleteElasticsearchDomainResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}]}, "DeleteElasticsearchServiceRole": {"name": "DeleteElasticsearchServiceRole", "http": {"method": "DELETE", "requestUri": "/2015-01-01/es/role"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ValidationException"}]}, "DeleteInboundCrossClusterSearchConnection": {"name": "DeleteInboundCrossClusterSearchConnection", "http": {"method": "DELETE", "requestUri": "/2015-01-01/es/ccs/inboundConnection/{ConnectionId}"}, "input": {"shape": "DeleteInboundCrossClusterSearchConnectionRequest"}, "output": {"shape": "DeleteInboundCrossClusterSearchConnectionResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "DisabledOperationException"}]}, "DeleteOutboundCrossClusterSearchConnection": {"name": "DeleteOutboundCrossClusterSearchConnection", "http": {"method": "DELETE", "requestUri": "/2015-01-01/es/ccs/outboundConnection/{ConnectionId}"}, "input": {"shape": "DeleteOutboundCrossClusterSearchConnectionRequest"}, "output": {"shape": "DeleteOutboundCrossClusterSearchConnectionResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "DisabledOperationException"}]}, "DeletePackage": {"name": "DeletePackage", "http": {"method": "DELETE", "requestUri": "/2015-01-01/packages/{PackageID}"}, "input": {"shape": "DeletePackageRequest"}, "output": {"shape": "DeletePackageResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}]}, "DescribeElasticsearchDomain": {"name": "DescribeElasticsearchDomain", "http": {"method": "GET", "requestUri": "/2015-01-01/es/domain/{DomainName}"}, "input": {"shape": "DescribeElasticsearchDomainRequest"}, "output": {"shape": "DescribeElasticsearchDomainResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}]}, "DescribeElasticsearchDomainConfig": {"name": "DescribeElasticsearchDomainConfig", "http": {"method": "GET", "requestUri": "/2015-01-01/es/domain/{DomainName}/config"}, "input": {"shape": "DescribeElasticsearchDomainConfigRequest"}, "output": {"shape": "DescribeElasticsearchDomainConfigResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}]}, "DescribeElasticsearchDomains": {"name": "DescribeElasticsearchDomains", "http": {"method": "POST", "requestUri": "/2015-01-01/es/domain-info"}, "input": {"shape": "DescribeElasticsearchDomainsRequest"}, "output": {"shape": "DescribeElasticsearchDomainsResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ValidationException"}]}, "DescribeElasticsearchInstanceTypeLimits": {"name": "DescribeElasticsearchInstanceTypeLimits", "http": {"method": "GET", "requestUri": "/2015-01-01/es/instanceTypeLimits/{ElasticsearchVersion}/{InstanceType}"}, "input": {"shape": "DescribeElasticsearchInstanceTypeLimitsRequest"}, "output": {"shape": "DescribeElasticsearchInstanceTypeLimitsResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "InvalidTypeException"}, {"shape": "LimitExceededException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}]}, "DescribeInboundCrossClusterSearchConnections": {"name": "DescribeInboundCrossClusterSearchConnections", "http": {"method": "POST", "requestUri": "/2015-01-01/es/ccs/inboundConnection/search"}, "input": {"shape": "DescribeInboundCrossClusterSearchConnectionsRequest"}, "output": {"shape": "DescribeInboundCrossClusterSearchConnectionsResponse"}, "errors": [{"shape": "InvalidPaginationTokenException"}, {"shape": "DisabledOperationException"}]}, "DescribeOutboundCrossClusterSearchConnections": {"name": "DescribeOutboundCrossClusterSearchConnections", "http": {"method": "POST", "requestUri": "/2015-01-01/es/ccs/outboundConnection/search"}, "input": {"shape": "DescribeOutboundCrossClusterSearchConnectionsRequest"}, "output": {"shape": "DescribeOutboundCrossClusterSearchConnectionsResponse"}, "errors": [{"shape": "InvalidPaginationTokenException"}, {"shape": "DisabledOperationException"}]}, "DescribePackages": {"name": "DescribePackages", "http": {"method": "POST", "requestUri": "/2015-01-01/packages/describe"}, "input": {"shape": "DescribePackagesRequest"}, "output": {"shape": "DescribePackagesResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}]}, "DescribeReservedElasticsearchInstanceOfferings": {"name": "DescribeReservedElasticsearchInstanceOfferings", "http": {"method": "GET", "requestUri": "/2015-01-01/es/reservedInstanceOfferings"}, "input": {"shape": "DescribeReservedElasticsearchInstanceOfferingsRequest"}, "output": {"shape": "DescribeReservedElasticsearchInstanceOfferingsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "DisabledOperationException"}, {"shape": "InternalException"}]}, "DescribeReservedElasticsearchInstances": {"name": "DescribeReservedElasticsearchInstances", "http": {"method": "GET", "requestUri": "/2015-01-01/es/reservedInstances"}, "input": {"shape": "DescribeReservedElasticsearchInstancesRequest"}, "output": {"shape": "DescribeReservedElasticsearchInstancesResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalException"}, {"shape": "ValidationException"}, {"shape": "DisabledOperationException"}]}, "DissociatePackage": {"name": "DissociatePackage", "http": {"method": "POST", "requestUri": "/2015-01-01/packages/dissociate/{PackageID}/{DomainName}"}, "input": {"shape": "DissociatePackageRequest"}, "output": {"shape": "DissociatePackageResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}]}, "GetCompatibleElasticsearchVersions": {"name": "GetCompatibleElasticsearchVersions", "http": {"method": "GET", "requestUri": "/2015-01-01/es/compatibleVersions"}, "input": {"shape": "GetCompatibleElasticsearchVersionsRequest"}, "output": {"shape": "GetCompatibleElasticsearchVersionsResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "ResourceNotFoundException"}, {"shape": "DisabledOperationException"}, {"shape": "ValidationException"}, {"shape": "InternalException"}]}, "GetUpgradeHistory": {"name": "GetUpgradeHistory", "http": {"method": "GET", "requestUri": "/2015-01-01/es/upgradeDomain/{DomainName}/history"}, "input": {"shape": "GetUpgradeHistoryRequest"}, "output": {"shape": "GetUpgradeHistoryResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "ResourceNotFoundException"}, {"shape": "DisabledOperationException"}, {"shape": "ValidationException"}, {"shape": "InternalException"}]}, "GetUpgradeStatus": {"name": "GetUpgradeStatus", "http": {"method": "GET", "requestUri": "/2015-01-01/es/upgradeDomain/{DomainName}/status"}, "input": {"shape": "GetUpgradeStatusRequest"}, "output": {"shape": "GetUpgradeStatusResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "ResourceNotFoundException"}, {"shape": "DisabledOperationException"}, {"shape": "ValidationException"}, {"shape": "InternalException"}]}, "ListDomainNames": {"name": "ListDomainNames", "http": {"method": "GET", "requestUri": "/2015-01-01/domain"}, "output": {"shape": "ListDomainNamesResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "ValidationException"}]}, "ListDomainsForPackage": {"name": "ListDomainsForPackage", "http": {"method": "GET", "requestUri": "/2015-01-01/packages/{PackageID}/domains"}, "input": {"shape": "ListDomainsForPackageRequest"}, "output": {"shape": "ListDomainsForPackageResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}]}, "ListElasticsearchInstanceTypes": {"name": "ListElasticsearchInstanceTypes", "http": {"method": "GET", "requestUri": "/2015-01-01/es/instanceTypes/{ElasticsearchVersion}"}, "input": {"shape": "ListElasticsearchInstanceTypesRequest"}, "output": {"shape": "ListElasticsearchInstanceTypesResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}]}, "ListElasticsearchVersions": {"name": "ListElasticsearchVersions", "http": {"method": "GET", "requestUri": "/2015-01-01/es/versions"}, "input": {"shape": "ListElasticsearchVersionsRequest"}, "output": {"shape": "ListElasticsearchVersionsResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}]}, "ListPackagesForDomain": {"name": "ListPackagesForDomain", "http": {"method": "GET", "requestUri": "/2015-01-01/domain/{DomainName}/packages"}, "input": {"shape": "ListPackagesForDomainRequest"}, "output": {"shape": "ListPackagesForDomainResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}]}, "ListTags": {"name": "ListTags", "http": {"method": "GET", "requestUri": "/2015-01-01/tags/"}, "input": {"shape": "ListTagsRequest"}, "output": {"shape": "ListTagsResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalException"}]}, "PurchaseReservedElasticsearchInstanceOffering": {"name": "PurchaseReservedElasticsearchInstanceOffering", "http": {"method": "POST", "requestUri": "/2015-01-01/es/purchaseReservedInstanceOffering"}, "input": {"shape": "PurchaseReservedElasticsearchInstanceOfferingRequest"}, "output": {"shape": "PurchaseReservedElasticsearchInstanceOfferingResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "LimitExceededException"}, {"shape": "DisabledOperationException"}, {"shape": "ValidationException"}, {"shape": "InternalException"}]}, "RejectInboundCrossClusterSearchConnection": {"name": "RejectInboundCrossClusterSearchConnection", "http": {"method": "PUT", "requestUri": "/2015-01-01/es/ccs/inboundConnection/{ConnectionId}/reject"}, "input": {"shape": "RejectInboundCrossClusterSearchConnectionRequest"}, "output": {"shape": "RejectInboundCrossClusterSearchConnectionResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "DisabledOperationException"}]}, "RemoveTags": {"name": "RemoveTags", "http": {"method": "POST", "requestUri": "/2015-01-01/tags-removal"}, "input": {"shape": "RemoveTagsRequest"}, "errors": [{"shape": "BaseException"}, {"shape": "ValidationException"}, {"shape": "InternalException"}]}, "StartElasticsearchServiceSoftwareUpdate": {"name": "StartElasticsearchServiceSoftwareUpdate", "http": {"method": "POST", "requestUri": "/2015-01-01/es/serviceSoftwareUpdate/start"}, "input": {"shape": "StartElasticsearchServiceSoftwareUpdateRequest"}, "output": {"shape": "StartElasticsearchServiceSoftwareUpdateResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}]}, "UpdateElasticsearchDomainConfig": {"name": "UpdateElasticsearchDomainConfig", "http": {"method": "POST", "requestUri": "/2015-01-01/es/domain/{DomainName}/config"}, "input": {"shape": "UpdateElasticsearchDomainConfigRequest"}, "output": {"shape": "UpdateElasticsearchDomainConfigResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "InternalException"}, {"shape": "InvalidTypeException"}, {"shape": "LimitExceededException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}]}, "UpgradeElasticsearchDomain": {"name": "UpgradeElasticsearchDomain", "http": {"method": "POST", "requestUri": "/2015-01-01/es/upgradeDomain"}, "input": {"shape": "UpgradeElasticsearchDomainRequest"}, "output": {"shape": "UpgradeElasticsearchDomainResponse"}, "errors": [{"shape": "BaseException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "DisabledOperationException"}, {"shape": "ValidationException"}, {"shape": "InternalException"}]}}, "shapes": {"ARN": {"type": "string"}, "AcceptInboundCrossClusterSearchConnectionRequest": {"type": "structure", "required": ["CrossClusterSearchConnectionId"], "members": {"CrossClusterSearchConnectionId": {"shape": "CrossClusterSearchConnectionId", "location": "uri", "locationName": "ConnectionId"}}}, "AcceptInboundCrossClusterSearchConnectionResponse": {"type": "structure", "members": {"CrossClusterSearchConnection": {"shape": "InboundCrossClusterSearchConnection"}}}, "AccessDeniedException": {"type": "structure", "members": {}, "error": {"httpStatusCode": 403}, "exception": true}, "AccessPoliciesStatus": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "PolicyDocument"}, "Status": {"shape": "OptionStatus"}}}, "AddTagsRequest": {"type": "structure", "required": ["ARN", "TagList"], "members": {"ARN": {"shape": "ARN"}, "TagList": {"shape": "TagList"}}}, "AdditionalLimit": {"type": "structure", "members": {"LimitName": {"shape": "LimitName"}, "LimitValues": {"shape": "LimitValueList"}}}, "AdditionalLimitList": {"type": "list", "member": {"shape": "AdditionalLimit"}}, "AdvancedOptions": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "String"}}, "AdvancedOptionsStatus": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "AdvancedOptions"}, "Status": {"shape": "OptionStatus"}}}, "AdvancedSecurityOptions": {"type": "structure", "members": {"Enabled": {"shape": "Boolean"}, "InternalUserDatabaseEnabled": {"shape": "Boolean"}}}, "AdvancedSecurityOptionsInput": {"type": "structure", "members": {"Enabled": {"shape": "Boolean"}, "InternalUserDatabaseEnabled": {"shape": "Boolean"}, "MasterUserOptions": {"shape": "MasterUserOptions"}}}, "AdvancedSecurityOptionsStatus": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "AdvancedSecurityOptions"}, "Status": {"shape": "OptionStatus"}}}, "AssociatePackageRequest": {"type": "structure", "required": ["PackageID", "DomainName"], "members": {"PackageID": {"shape": "PackageID", "location": "uri", "locationName": "PackageID"}, "DomainName": {"shape": "DomainName", "location": "uri", "locationName": "DomainName"}}}, "AssociatePackageResponse": {"type": "structure", "members": {"DomainPackageDetails": {"shape": "DomainPackageDetails"}}}, "BaseException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "exception": true}, "Boolean": {"type": "boolean"}, "CancelElasticsearchServiceSoftwareUpdateRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "DomainName"}}}, "CancelElasticsearchServiceSoftwareUpdateResponse": {"type": "structure", "members": {"ServiceSoftwareOptions": {"shape": "ServiceSoftwareOptions"}}}, "CloudWatchLogsLogGroupArn": {"type": "string"}, "CognitoOptions": {"type": "structure", "members": {"Enabled": {"shape": "Boolean"}, "UserPoolId": {"shape": "UserPoolId"}, "IdentityPoolId": {"shape": "IdentityPoolId"}, "RoleArn": {"shape": "RoleArn"}}}, "CognitoOptionsStatus": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "CognitoOptions"}, "Status": {"shape": "OptionStatus"}}}, "CompatibleElasticsearchVersionsList": {"type": "list", "member": {"shape": "CompatibleVersionsMap"}}, "CompatibleVersionsMap": {"type": "structure", "members": {"SourceVersion": {"shape": "ElasticsearchVersionString"}, "TargetVersions": {"shape": "ElasticsearchVersionList"}}}, "ConflictException": {"type": "structure", "members": {}, "error": {"httpStatusCode": 409}, "exception": true}, "ConnectionAlias": {"type": "string", "max": 20}, "CreateElasticsearchDomainRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "DomainName"}, "ElasticsearchVersion": {"shape": "ElasticsearchVersionString"}, "ElasticsearchClusterConfig": {"shape": "ElasticsearchClusterConfig"}, "EBSOptions": {"shape": "EBSOptions"}, "AccessPolicies": {"shape": "PolicyDocument"}, "SnapshotOptions": {"shape": "SnapshotOptions"}, "VPCOptions": {"shape": "VPCOptions"}, "CognitoOptions": {"shape": "CognitoOptions"}, "EncryptionAtRestOptions": {"shape": "EncryptionAtRestOptions"}, "NodeToNodeEncryptionOptions": {"shape": "NodeToNodeEncryptionOptions"}, "AdvancedOptions": {"shape": "AdvancedOptions"}, "LogPublishingOptions": {"shape": "LogPublishingOptions"}, "DomainEndpointOptions": {"shape": "DomainEndpointOptions"}, "AdvancedSecurityOptions": {"shape": "AdvancedSecurityOptionsInput"}}}, "CreateElasticsearchDomainResponse": {"type": "structure", "members": {"DomainStatus": {"shape": "ElasticsearchDomainStatus"}}}, "CreateOutboundCrossClusterSearchConnectionRequest": {"type": "structure", "required": ["SourceDomainInfo", "DestinationDomainInfo", "ConnectionAlias"], "members": {"SourceDomainInfo": {"shape": "DomainInformation"}, "DestinationDomainInfo": {"shape": "DomainInformation"}, "ConnectionAlias": {"shape": "ConnectionAlias"}}}, "CreateOutboundCrossClusterSearchConnectionResponse": {"type": "structure", "members": {"SourceDomainInfo": {"shape": "DomainInformation"}, "DestinationDomainInfo": {"shape": "DomainInformation"}, "ConnectionAlias": {"shape": "ConnectionAlias"}, "ConnectionStatus": {"shape": "OutboundCrossClusterSearchConnectionStatus"}, "CrossClusterSearchConnectionId": {"shape": "CrossClusterSearchConnectionId"}}}, "CreatePackageRequest": {"type": "structure", "required": ["PackageName", "PackageType", "PackageSource"], "members": {"PackageName": {"shape": "PackageName"}, "PackageType": {"shape": "PackageType"}, "PackageDescription": {"shape": "PackageDescription"}, "PackageSource": {"shape": "PackageSource"}}}, "CreatePackageResponse": {"type": "structure", "members": {"PackageDetails": {"shape": "PackageDetails"}}}, "CreatedAt": {"type": "timestamp"}, "CrossClusterSearchConnectionId": {"type": "string"}, "CrossClusterSearchConnectionStatusMessage": {"type": "string"}, "DeleteElasticsearchDomainRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "DomainName", "location": "uri", "locationName": "DomainName"}}}, "DeleteElasticsearchDomainResponse": {"type": "structure", "members": {"DomainStatus": {"shape": "ElasticsearchDomainStatus"}}}, "DeleteInboundCrossClusterSearchConnectionRequest": {"type": "structure", "required": ["CrossClusterSearchConnectionId"], "members": {"CrossClusterSearchConnectionId": {"shape": "CrossClusterSearchConnectionId", "location": "uri", "locationName": "ConnectionId"}}}, "DeleteInboundCrossClusterSearchConnectionResponse": {"type": "structure", "members": {"CrossClusterSearchConnection": {"shape": "InboundCrossClusterSearchConnection"}}}, "DeleteOutboundCrossClusterSearchConnectionRequest": {"type": "structure", "required": ["CrossClusterSearchConnectionId"], "members": {"CrossClusterSearchConnectionId": {"shape": "CrossClusterSearchConnectionId", "location": "uri", "locationName": "ConnectionId"}}}, "DeleteOutboundCrossClusterSearchConnectionResponse": {"type": "structure", "members": {"CrossClusterSearchConnection": {"shape": "OutboundCrossClusterSearchConnection"}}}, "DeletePackageRequest": {"type": "structure", "required": ["PackageID"], "members": {"PackageID": {"shape": "PackageID", "location": "uri", "locationName": "PackageID"}}}, "DeletePackageResponse": {"type": "structure", "members": {"PackageDetails": {"shape": "PackageDetails"}}}, "DeploymentCloseDateTimeStamp": {"type": "timestamp"}, "DeploymentStatus": {"type": "string", "enum": ["PENDING_UPDATE", "IN_PROGRESS", "COMPLETED", "NOT_ELIGIBLE", "ELIGIBLE"]}, "DescribeElasticsearchDomainConfigRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "DomainName", "location": "uri", "locationName": "DomainName"}}}, "DescribeElasticsearchDomainConfigResponse": {"type": "structure", "required": ["DomainConfig"], "members": {"DomainConfig": {"shape": "ElasticsearchDomainConfig"}}}, "DescribeElasticsearchDomainRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "DomainName", "location": "uri", "locationName": "DomainName"}}}, "DescribeElasticsearchDomainResponse": {"type": "structure", "required": ["DomainStatus"], "members": {"DomainStatus": {"shape": "ElasticsearchDomainStatus"}}}, "DescribeElasticsearchDomainsRequest": {"type": "structure", "required": ["DomainNames"], "members": {"DomainNames": {"shape": "DomainNameList"}}}, "DescribeElasticsearchDomainsResponse": {"type": "structure", "required": ["DomainStatusList"], "members": {"DomainStatusList": {"shape": "ElasticsearchDomainStatusList"}}}, "DescribeElasticsearchInstanceTypeLimitsRequest": {"type": "structure", "required": ["InstanceType", "ElasticsearchVersion"], "members": {"DomainName": {"shape": "DomainName", "location": "querystring", "locationName": "domainName"}, "InstanceType": {"shape": "ESPartitionInstanceType", "location": "uri", "locationName": "InstanceType"}, "ElasticsearchVersion": {"shape": "ElasticsearchVersionString", "location": "uri", "locationName": "ElasticsearchVersion"}}}, "DescribeElasticsearchInstanceTypeLimitsResponse": {"type": "structure", "members": {"LimitsByRole": {"shape": "LimitsByRole"}}}, "DescribeInboundCrossClusterSearchConnectionsRequest": {"type": "structure", "members": {"Filters": {"shape": "FilterList"}, "MaxResults": {"shape": "MaxResults"}, "NextToken": {"shape": "NextToken"}}}, "DescribeInboundCrossClusterSearchConnectionsResponse": {"type": "structure", "members": {"CrossClusterSearchConnections": {"shape": "InboundCrossClusterSearchConnections"}, "NextToken": {"shape": "NextToken"}}}, "DescribeOutboundCrossClusterSearchConnectionsRequest": {"type": "structure", "members": {"Filters": {"shape": "FilterList"}, "MaxResults": {"shape": "MaxResults"}, "NextToken": {"shape": "NextToken"}}}, "DescribeOutboundCrossClusterSearchConnectionsResponse": {"type": "structure", "members": {"CrossClusterSearchConnections": {"shape": "OutboundCrossClusterSearchConnections"}, "NextToken": {"shape": "NextToken"}}}, "DescribePackagesFilter": {"type": "structure", "members": {"Name": {"shape": "DescribePackagesFilterName"}, "Value": {"shape": "DescribePackagesFilterValues"}}}, "DescribePackagesFilterList": {"type": "list", "member": {"shape": "DescribePackagesFilter"}}, "DescribePackagesFilterName": {"type": "string", "enum": ["PackageID", "PackageName", "PackageStatus"]}, "DescribePackagesFilterValue": {"type": "string", "pattern": "^[0-9a-zA-Z\\*\\.\\\\/\\?-]*$"}, "DescribePackagesFilterValues": {"type": "list", "member": {"shape": "DescribePackagesFilterValue"}}, "DescribePackagesRequest": {"type": "structure", "members": {"Filters": {"shape": "DescribePackagesFilterList"}, "MaxResults": {"shape": "MaxResults"}, "NextToken": {"shape": "NextToken"}}}, "DescribePackagesResponse": {"type": "structure", "members": {"PackageDetailsList": {"shape": "PackageDetailsList"}, "NextToken": {"shape": "String"}}}, "DescribeReservedElasticsearchInstanceOfferingsRequest": {"type": "structure", "members": {"ReservedElasticsearchInstanceOfferingId": {"shape": "GUID", "location": "querystring", "locationName": "offeringId"}, "MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "location": "querystring", "locationName": "nextToken"}}}, "DescribeReservedElasticsearchInstanceOfferingsResponse": {"type": "structure", "members": {"NextToken": {"shape": "NextToken"}, "ReservedElasticsearchInstanceOfferings": {"shape": "ReservedElasticsearchInstanceOfferingList"}}}, "DescribeReservedElasticsearchInstancesRequest": {"type": "structure", "members": {"ReservedElasticsearchInstanceId": {"shape": "GUID", "location": "querystring", "locationName": "reservationId"}, "MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "location": "querystring", "locationName": "nextToken"}}}, "DescribeReservedElasticsearchInstancesResponse": {"type": "structure", "members": {"NextToken": {"shape": "String"}, "ReservedElasticsearchInstances": {"shape": "ReservedElasticsearchInstanceList"}}}, "DisabledOperationException": {"type": "structure", "members": {}, "error": {"httpStatusCode": 409}, "exception": true}, "DissociatePackageRequest": {"type": "structure", "required": ["PackageID", "DomainName"], "members": {"PackageID": {"shape": "PackageID", "location": "uri", "locationName": "PackageID"}, "DomainName": {"shape": "DomainName", "location": "uri", "locationName": "DomainName"}}}, "DissociatePackageResponse": {"type": "structure", "members": {"DomainPackageDetails": {"shape": "DomainPackageDetails"}}}, "DomainEndpointOptions": {"type": "structure", "members": {"EnforceHTTPS": {"shape": "Boolean"}, "TLSSecurityPolicy": {"shape": "TLSSecurityPolicy"}}}, "DomainEndpointOptionsStatus": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "DomainEndpointOptions"}, "Status": {"shape": "OptionStatus"}}}, "DomainId": {"type": "string", "max": 64, "min": 1}, "DomainInfo": {"type": "structure", "members": {"DomainName": {"shape": "DomainName"}}}, "DomainInfoList": {"type": "list", "member": {"shape": "DomainInfo"}}, "DomainInformation": {"type": "structure", "required": ["DomainName"], "members": {"OwnerId": {"shape": "OwnerId"}, "DomainName": {"shape": "DomainName"}, "Region": {"shape": "Region"}}}, "DomainName": {"type": "string", "max": 28, "min": 3, "pattern": "[a-z][a-z0-9\\-]+"}, "DomainNameList": {"type": "list", "member": {"shape": "DomainName"}}, "DomainPackageDetails": {"type": "structure", "members": {"PackageID": {"shape": "PackageID"}, "PackageName": {"shape": "PackageName"}, "PackageType": {"shape": "PackageType"}, "LastUpdated": {"shape": "LastUpdated"}, "DomainName": {"shape": "DomainName"}, "DomainPackageStatus": {"shape": "DomainPackageStatus"}, "ReferencePath": {"shape": "ReferencePath"}, "ErrorDetails": {"shape": "ErrorDetails"}}}, "DomainPackageDetailsList": {"type": "list", "member": {"shape": "DomainPackageDetails"}}, "DomainPackageStatus": {"type": "string", "enum": ["ASSOCIATING", "ASSOCIATION_FAILED", "ACTIVE", "DISSOCIATING", "DISSOCIATION_FAILED"]}, "Double": {"type": "double"}, "EBSOptions": {"type": "structure", "members": {"EBSEnabled": {"shape": "Boolean"}, "VolumeType": {"shape": "VolumeType"}, "VolumeSize": {"shape": "IntegerClass"}, "Iops": {"shape": "IntegerClass"}}}, "EBSOptionsStatus": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "EBSOptions"}, "Status": {"shape": "OptionStatus"}}}, "ESPartitionInstanceType": {"type": "string", "enum": ["m3.medium.elasticsearch", "m3.large.elasticsearch", "m3.xlarge.elasticsearch", "m3.2xlarge.elasticsearch", "m4.large.elasticsearch", "m4.xlarge.elasticsearch", "m4.2xlarge.elasticsearch", "m4.4xlarge.elasticsearch", "m4.10xlarge.elasticsearch", "m5.large.elasticsearch", "m5.xlarge.elasticsearch", "m5.2xlarge.elasticsearch", "m5.4xlarge.elasticsearch", "m5.12xlarge.elasticsearch", "r5.large.elasticsearch", "r5.xlarge.elasticsearch", "r5.2xlarge.elasticsearch", "r5.4xlarge.elasticsearch", "r5.12xlarge.elasticsearch", "c5.large.elasticsearch", "c5.xlarge.elasticsearch", "c5.2xlarge.elasticsearch", "c5.4xlarge.elasticsearch", "c5.9xlarge.elasticsearch", "c5.18xlarge.elasticsearch", "ultrawarm1.medium.elasticsearch", "ultrawarm1.large.elasticsearch", "t2.micro.elasticsearch", "t2.small.elasticsearch", "t2.medium.elasticsearch", "r3.large.elasticsearch", "r3.xlarge.elasticsearch", "r3.2xlarge.elasticsearch", "r3.4xlarge.elasticsearch", "r3.8xlarge.elasticsearch", "i2.xlarge.elasticsearch", "i2.2xlarge.elasticsearch", "d2.xlarge.elasticsearch", "d2.2xlarge.elasticsearch", "d2.4xlarge.elasticsearch", "d2.8xlarge.elasticsearch", "c4.large.elasticsearch", "c4.xlarge.elasticsearch", "c4.2xlarge.elasticsearch", "c4.4xlarge.elasticsearch", "c4.8xlarge.elasticsearch", "r4.large.elasticsearch", "r4.xlarge.elasticsearch", "r4.2xlarge.elasticsearch", "r4.4xlarge.elasticsearch", "r4.8xlarge.elasticsearch", "r4.16xlarge.elasticsearch", "i3.large.elasticsearch", "i3.xlarge.elasticsearch", "i3.2xlarge.elasticsearch", "i3.4xlarge.elasticsearch", "i3.8xlarge.elasticsearch", "i3.16xlarge.elasticsearch"]}, "ESWarmPartitionInstanceType": {"type": "string", "enum": ["ultrawarm1.medium.elasticsearch", "ultrawarm1.large.elasticsearch"]}, "ElasticsearchClusterConfig": {"type": "structure", "members": {"InstanceType": {"shape": "ESPartitionInstanceType"}, "InstanceCount": {"shape": "IntegerClass"}, "DedicatedMasterEnabled": {"shape": "Boolean"}, "ZoneAwarenessEnabled": {"shape": "Boolean"}, "ZoneAwarenessConfig": {"shape": "ZoneAwarenessConfig"}, "DedicatedMasterType": {"shape": "ESPartitionInstanceType"}, "DedicatedMasterCount": {"shape": "IntegerClass"}, "WarmEnabled": {"shape": "Boolean"}, "WarmType": {"shape": "ESWarmPartitionInstanceType"}, "WarmCount": {"shape": "IntegerClass"}}}, "ElasticsearchClusterConfigStatus": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "ElasticsearchClusterConfig"}, "Status": {"shape": "OptionStatus"}}}, "ElasticsearchDomainConfig": {"type": "structure", "members": {"ElasticsearchVersion": {"shape": "ElasticsearchVersionStatus"}, "ElasticsearchClusterConfig": {"shape": "ElasticsearchClusterConfigStatus"}, "EBSOptions": {"shape": "EBSOptionsStatus"}, "AccessPolicies": {"shape": "AccessPoliciesStatus"}, "SnapshotOptions": {"shape": "SnapshotOptionsStatus"}, "VPCOptions": {"shape": "VPCDerivedInfoStatus"}, "CognitoOptions": {"shape": "CognitoOptionsStatus"}, "EncryptionAtRestOptions": {"shape": "EncryptionAtRestOptionsStatus"}, "NodeToNodeEncryptionOptions": {"shape": "NodeToNodeEncryptionOptionsStatus"}, "AdvancedOptions": {"shape": "AdvancedOptionsStatus"}, "LogPublishingOptions": {"shape": "LogPublishingOptionsStatus"}, "DomainEndpointOptions": {"shape": "DomainEndpointOptionsStatus"}, "AdvancedSecurityOptions": {"shape": "AdvancedSecurityOptionsStatus"}}}, "ElasticsearchDomainStatus": {"type": "structure", "required": ["DomainId", "DomainName", "ARN", "ElasticsearchClusterConfig"], "members": {"DomainId": {"shape": "DomainId"}, "DomainName": {"shape": "DomainName"}, "ARN": {"shape": "ARN"}, "Created": {"shape": "Boolean"}, "Deleted": {"shape": "Boolean"}, "Endpoint": {"shape": "ServiceUrl"}, "Endpoints": {"shape": "EndpointsMap"}, "Processing": {"shape": "Boolean"}, "UpgradeProcessing": {"shape": "Boolean"}, "ElasticsearchVersion": {"shape": "ElasticsearchVersionString"}, "ElasticsearchClusterConfig": {"shape": "ElasticsearchClusterConfig"}, "EBSOptions": {"shape": "EBSOptions"}, "AccessPolicies": {"shape": "PolicyDocument"}, "SnapshotOptions": {"shape": "SnapshotOptions"}, "VPCOptions": {"shape": "VPCDerivedInfo"}, "CognitoOptions": {"shape": "CognitoOptions"}, "EncryptionAtRestOptions": {"shape": "EncryptionAtRestOptions"}, "NodeToNodeEncryptionOptions": {"shape": "NodeToNodeEncryptionOptions"}, "AdvancedOptions": {"shape": "AdvancedOptions"}, "LogPublishingOptions": {"shape": "LogPublishingOptions"}, "ServiceSoftwareOptions": {"shape": "ServiceSoftwareOptions"}, "DomainEndpointOptions": {"shape": "DomainEndpointOptions"}, "AdvancedSecurityOptions": {"shape": "AdvancedSecurityOptions"}}}, "ElasticsearchDomainStatusList": {"type": "list", "member": {"shape": "ElasticsearchDomainStatus"}}, "ElasticsearchInstanceTypeList": {"type": "list", "member": {"shape": "ESPartitionInstanceType"}}, "ElasticsearchVersionList": {"type": "list", "member": {"shape": "ElasticsearchVersionString"}}, "ElasticsearchVersionStatus": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "ElasticsearchVersionString"}, "Status": {"shape": "OptionStatus"}}}, "ElasticsearchVersionString": {"type": "string"}, "EncryptionAtRestOptions": {"type": "structure", "members": {"Enabled": {"shape": "Boolean"}, "KmsKeyId": {"shape": "KmsKeyId"}}}, "EncryptionAtRestOptionsStatus": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "EncryptionAtRestOptions"}, "Status": {"shape": "OptionStatus"}}}, "EndpointsMap": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "ServiceUrl"}}, "ErrorDetails": {"type": "structure", "members": {"ErrorType": {"shape": "ErrorType"}, "ErrorMessage": {"shape": "ErrorMessage"}}}, "ErrorMessage": {"type": "string"}, "ErrorType": {"type": "string"}, "Filter": {"type": "structure", "members": {"Name": {"shape": "NonEmptyString"}, "Values": {"shape": "ValueStringList"}}}, "FilterList": {"type": "list", "member": {"shape": "Filter"}}, "GUID": {"type": "string", "pattern": "\\p{XDigit}{8}-\\p{XDigit}{4}-\\p{XDigit}{4}-\\p{XDigit}{4}-\\p{XDigit}{12}"}, "GetCompatibleElasticsearchVersionsRequest": {"type": "structure", "members": {"DomainName": {"shape": "DomainName", "location": "querystring", "locationName": "domainName"}}}, "GetCompatibleElasticsearchVersionsResponse": {"type": "structure", "members": {"CompatibleElasticsearchVersions": {"shape": "CompatibleElasticsearchVersionsList"}}}, "GetUpgradeHistoryRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "DomainName", "location": "uri", "locationName": "DomainName"}, "MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "location": "querystring", "locationName": "nextToken"}}}, "GetUpgradeHistoryResponse": {"type": "structure", "members": {"UpgradeHistories": {"shape": "UpgradeHistoryList"}, "NextToken": {"shape": "String"}}}, "GetUpgradeStatusRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "DomainName", "location": "uri", "locationName": "DomainName"}}}, "GetUpgradeStatusResponse": {"type": "structure", "members": {"UpgradeStep": {"shape": "UpgradeStep"}, "StepStatus": {"shape": "UpgradeStatus"}, "UpgradeName": {"shape": "UpgradeName"}}}, "IdentityPoolId": {"type": "string", "max": 55, "min": 1, "pattern": "[\\w-]+:[0-9a-f-]+"}, "InboundCrossClusterSearchConnection": {"type": "structure", "members": {"SourceDomainInfo": {"shape": "DomainInformation"}, "DestinationDomainInfo": {"shape": "DomainInformation"}, "CrossClusterSearchConnectionId": {"shape": "CrossClusterSearchConnectionId"}, "ConnectionStatus": {"shape": "InboundCrossClusterSearchConnectionStatus"}}}, "InboundCrossClusterSearchConnectionStatus": {"type": "structure", "members": {"StatusCode": {"shape": "InboundCrossClusterSearchConnectionStatusCode"}, "Message": {"shape": "CrossClusterSearchConnectionStatusMessage"}}}, "InboundCrossClusterSearchConnectionStatusCode": {"type": "string", "enum": ["PENDING_ACCEPTANCE", "APPROVED", "REJECTING", "REJECTED", "DELETING", "DELETED"]}, "InboundCrossClusterSearchConnections": {"type": "list", "member": {"shape": "InboundCrossClusterSearchConnection"}}, "InstanceCount": {"type": "integer", "min": 1}, "InstanceCountLimits": {"type": "structure", "members": {"MinimumInstanceCount": {"shape": "MinimumInstanceCount"}, "MaximumInstanceCount": {"shape": "MaximumInstanceCount"}}}, "InstanceLimits": {"type": "structure", "members": {"InstanceCountLimits": {"shape": "InstanceCountLimits"}}}, "InstanceRole": {"type": "string"}, "Integer": {"type": "integer"}, "IntegerClass": {"type": "integer"}, "InternalException": {"type": "structure", "members": {}, "error": {"httpStatusCode": 500}, "exception": true}, "InvalidPaginationTokenException": {"type": "structure", "members": {}, "error": {"httpStatusCode": 400}, "exception": true}, "InvalidTypeException": {"type": "structure", "members": {}, "error": {"httpStatusCode": 409}, "exception": true}, "Issue": {"type": "string"}, "Issues": {"type": "list", "member": {"shape": "Issue"}}, "KmsKeyId": {"type": "string", "max": 500, "min": 1}, "LastUpdated": {"type": "timestamp"}, "LimitExceededException": {"type": "structure", "members": {}, "error": {"httpStatusCode": 409}, "exception": true}, "LimitName": {"type": "string"}, "LimitValue": {"type": "string"}, "LimitValueList": {"type": "list", "member": {"shape": "LimitValue"}}, "Limits": {"type": "structure", "members": {"StorageTypes": {"shape": "StorageTypeList"}, "InstanceLimits": {"shape": "InstanceLimits"}, "AdditionalLimits": {"shape": "AdditionalLimitList"}}}, "LimitsByRole": {"type": "map", "key": {"shape": "InstanceRole"}, "value": {"shape": "Limits"}}, "ListDomainNamesResponse": {"type": "structure", "members": {"DomainNames": {"shape": "DomainInfoList"}}}, "ListDomainsForPackageRequest": {"type": "structure", "required": ["PackageID"], "members": {"PackageID": {"shape": "PackageID", "location": "uri", "locationName": "PackageID"}, "MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "location": "querystring", "locationName": "nextToken"}}}, "ListDomainsForPackageResponse": {"type": "structure", "members": {"DomainPackageDetailsList": {"shape": "DomainPackageDetailsList"}, "NextToken": {"shape": "String"}}}, "ListElasticsearchInstanceTypesRequest": {"type": "structure", "required": ["ElasticsearchVersion"], "members": {"ElasticsearchVersion": {"shape": "ElasticsearchVersionString", "location": "uri", "locationName": "ElasticsearchVersion"}, "DomainName": {"shape": "DomainName", "location": "querystring", "locationName": "domainName"}, "MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "location": "querystring", "locationName": "nextToken"}}}, "ListElasticsearchInstanceTypesResponse": {"type": "structure", "members": {"ElasticsearchInstanceTypes": {"shape": "ElasticsearchInstanceTypeList"}, "NextToken": {"shape": "NextToken"}}}, "ListElasticsearchVersionsRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "location": "querystring", "locationName": "nextToken"}}}, "ListElasticsearchVersionsResponse": {"type": "structure", "members": {"ElasticsearchVersions": {"shape": "ElasticsearchVersionList"}, "NextToken": {"shape": "NextToken"}}}, "ListPackagesForDomainRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "DomainName", "location": "uri", "locationName": "DomainName"}, "MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "location": "querystring", "locationName": "nextToken"}}}, "ListPackagesForDomainResponse": {"type": "structure", "members": {"DomainPackageDetailsList": {"shape": "DomainPackageDetailsList"}, "NextToken": {"shape": "String"}}}, "ListTagsRequest": {"type": "structure", "required": ["ARN"], "members": {"ARN": {"shape": "ARN", "location": "querystring", "locationName": "arn"}}}, "ListTagsResponse": {"type": "structure", "members": {"TagList": {"shape": "TagList"}}}, "LogPublishingOption": {"type": "structure", "members": {"CloudWatchLogsLogGroupArn": {"shape": "CloudWatchLogsLogGroupArn"}, "Enabled": {"shape": "Boolean"}}}, "LogPublishingOptions": {"type": "map", "key": {"shape": "LogType"}, "value": {"shape": "LogPublishingOption"}}, "LogPublishingOptionsStatus": {"type": "structure", "members": {"Options": {"shape": "LogPublishingOptions"}, "Status": {"shape": "OptionStatus"}}}, "LogType": {"type": "string", "enum": ["INDEX_SLOW_LOGS", "SEARCH_SLOW_LOGS", "ES_APPLICATION_LOGS"]}, "MasterUserOptions": {"type": "structure", "members": {"MasterUserARN": {"shape": "ARN"}, "MasterUserName": {"shape": "Username"}, "MasterUserPassword": {"shape": "Password"}}}, "MaxResults": {"type": "integer", "max": 100}, "MaximumInstanceCount": {"type": "integer"}, "MinimumInstanceCount": {"type": "integer"}, "NextToken": {"type": "string"}, "NodeToNodeEncryptionOptions": {"type": "structure", "members": {"Enabled": {"shape": "Boolean"}}}, "NodeToNodeEncryptionOptionsStatus": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "NodeToNodeEncryptionOptions"}, "Status": {"shape": "OptionStatus"}}}, "NonEmptyString": {"type": "string", "min": 1}, "OptionState": {"type": "string", "enum": ["RequiresIndexDocuments", "Processing", "Active"]}, "OptionStatus": {"type": "structure", "required": ["CreationDate", "UpdateDate", "State"], "members": {"CreationDate": {"shape": "UpdateTimestamp"}, "UpdateDate": {"shape": "UpdateTimestamp"}, "UpdateVersion": {"shape": "UIntValue"}, "State": {"shape": "OptionState"}, "PendingDeletion": {"shape": "Boolean"}}}, "OutboundCrossClusterSearchConnection": {"type": "structure", "members": {"SourceDomainInfo": {"shape": "DomainInformation"}, "DestinationDomainInfo": {"shape": "DomainInformation"}, "CrossClusterSearchConnectionId": {"shape": "CrossClusterSearchConnectionId"}, "ConnectionAlias": {"shape": "ConnectionAlias"}, "ConnectionStatus": {"shape": "OutboundCrossClusterSearchConnectionStatus"}}}, "OutboundCrossClusterSearchConnectionStatus": {"type": "structure", "members": {"StatusCode": {"shape": "OutboundCrossClusterSearchConnectionStatusCode"}, "Message": {"shape": "CrossClusterSearchConnectionStatusMessage"}}}, "OutboundCrossClusterSearchConnectionStatusCode": {"type": "string", "enum": ["PENDING_ACCEPTANCE", "VALIDATING", "VALIDATION_FAILED", "PROVISIONING", "ACTIVE", "REJECTED", "DELETING", "DELETED"]}, "OutboundCrossClusterSearchConnections": {"type": "list", "member": {"shape": "OutboundCrossClusterSearchConnection"}}, "OwnerId": {"type": "string", "max": 12, "min": 12}, "PackageDescription": {"type": "string", "max": 1024}, "PackageDetails": {"type": "structure", "members": {"PackageID": {"shape": "PackageID"}, "PackageName": {"shape": "PackageName"}, "PackageType": {"shape": "PackageType"}, "PackageDescription": {"shape": "PackageDescription"}, "PackageStatus": {"shape": "PackageStatus"}, "CreatedAt": {"shape": "CreatedAt"}, "ErrorDetails": {"shape": "ErrorDetails"}}}, "PackageDetailsList": {"type": "list", "member": {"shape": "PackageDetails"}}, "PackageID": {"type": "string"}, "PackageName": {"type": "string", "max": 28, "min": 3, "pattern": "[a-z][a-z0-9\\-]+"}, "PackageSource": {"type": "structure", "members": {"S3BucketName": {"shape": "S3BucketName"}, "S3Key": {"shape": "S3Key"}}}, "PackageStatus": {"type": "string", "enum": ["COPYING", "COPY_FAILED", "VALIDATING", "VALIDATION_FAILED", "AVAILABLE", "DELETING", "DELETED", "DELETE_FAILED"]}, "PackageType": {"type": "string", "enum": ["TXT-DICTIONARY"]}, "Password": {"type": "string", "min": 8, "sensitive": true}, "PolicyDocument": {"type": "string"}, "PurchaseReservedElasticsearchInstanceOfferingRequest": {"type": "structure", "required": ["ReservedElasticsearchInstanceOfferingId", "ReservationName"], "members": {"ReservedElasticsearchInstanceOfferingId": {"shape": "GUID"}, "ReservationName": {"shape": "ReservationToken"}, "InstanceCount": {"shape": "InstanceCount"}}}, "PurchaseReservedElasticsearchInstanceOfferingResponse": {"type": "structure", "members": {"ReservedElasticsearchInstanceId": {"shape": "GUID"}, "ReservationName": {"shape": "ReservationToken"}}}, "RecurringCharge": {"type": "structure", "members": {"RecurringChargeAmount": {"shape": "Double"}, "RecurringChargeFrequency": {"shape": "String"}}}, "RecurringChargeList": {"type": "list", "member": {"shape": "RecurringCharge"}}, "ReferencePath": {"type": "string"}, "Region": {"type": "string"}, "RejectInboundCrossClusterSearchConnectionRequest": {"type": "structure", "required": ["CrossClusterSearchConnectionId"], "members": {"CrossClusterSearchConnectionId": {"shape": "CrossClusterSearchConnectionId", "location": "uri", "locationName": "ConnectionId"}}}, "RejectInboundCrossClusterSearchConnectionResponse": {"type": "structure", "members": {"CrossClusterSearchConnection": {"shape": "InboundCrossClusterSearchConnection"}}}, "RemoveTagsRequest": {"type": "structure", "required": ["ARN", "TagKeys"], "members": {"ARN": {"shape": "ARN"}, "TagKeys": {"shape": "StringList"}}}, "ReservationToken": {"type": "string", "max": 64, "min": 5}, "ReservedElasticsearchInstance": {"type": "structure", "members": {"ReservationName": {"shape": "ReservationToken"}, "ReservedElasticsearchInstanceId": {"shape": "GUID"}, "ReservedElasticsearchInstanceOfferingId": {"shape": "String"}, "ElasticsearchInstanceType": {"shape": "ESPartitionInstanceType"}, "StartTime": {"shape": "UpdateTimestamp"}, "Duration": {"shape": "Integer"}, "FixedPrice": {"shape": "Double"}, "UsagePrice": {"shape": "Double"}, "CurrencyCode": {"shape": "String"}, "ElasticsearchInstanceCount": {"shape": "Integer"}, "State": {"shape": "String"}, "PaymentOption": {"shape": "ReservedElasticsearchInstancePaymentOption"}, "RecurringCharges": {"shape": "RecurringChargeList"}}}, "ReservedElasticsearchInstanceList": {"type": "list", "member": {"shape": "ReservedElasticsearchInstance"}}, "ReservedElasticsearchInstanceOffering": {"type": "structure", "members": {"ReservedElasticsearchInstanceOfferingId": {"shape": "GUID"}, "ElasticsearchInstanceType": {"shape": "ESPartitionInstanceType"}, "Duration": {"shape": "Integer"}, "FixedPrice": {"shape": "Double"}, "UsagePrice": {"shape": "Double"}, "CurrencyCode": {"shape": "String"}, "PaymentOption": {"shape": "ReservedElasticsearchInstancePaymentOption"}, "RecurringCharges": {"shape": "RecurringChargeList"}}}, "ReservedElasticsearchInstanceOfferingList": {"type": "list", "member": {"shape": "ReservedElasticsearchInstanceOffering"}}, "ReservedElasticsearchInstancePaymentOption": {"type": "string", "enum": ["ALL_UPFRONT", "PARTIAL_UPFRONT", "NO_UPFRONT"]}, "ResourceAlreadyExistsException": {"type": "structure", "members": {}, "error": {"httpStatusCode": 409}, "exception": true}, "ResourceNotFoundException": {"type": "structure", "members": {}, "error": {"httpStatusCode": 409}, "exception": true}, "RoleArn": {"type": "string", "max": 2048, "min": 20}, "S3BucketName": {"type": "string", "max": 63, "min": 3}, "S3Key": {"type": "string"}, "ServiceSoftwareOptions": {"type": "structure", "members": {"CurrentVersion": {"shape": "String"}, "NewVersion": {"shape": "String"}, "UpdateAvailable": {"shape": "Boolean"}, "Cancellable": {"shape": "Boolean"}, "UpdateStatus": {"shape": "DeploymentStatus"}, "Description": {"shape": "String"}, "AutomatedUpdateDate": {"shape": "DeploymentCloseDateTimeStamp"}, "OptionalDeployment": {"shape": "Boolean"}}}, "ServiceUrl": {"type": "string"}, "SnapshotOptions": {"type": "structure", "members": {"AutomatedSnapshotStartHour": {"shape": "IntegerClass"}}}, "SnapshotOptionsStatus": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "SnapshotOptions"}, "Status": {"shape": "OptionStatus"}}}, "StartElasticsearchServiceSoftwareUpdateRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "DomainName"}}}, "StartElasticsearchServiceSoftwareUpdateResponse": {"type": "structure", "members": {"ServiceSoftwareOptions": {"shape": "ServiceSoftwareOptions"}}}, "StartTimestamp": {"type": "timestamp"}, "StorageSubTypeName": {"type": "string"}, "StorageType": {"type": "structure", "members": {"StorageTypeName": {"shape": "StorageTypeName"}, "StorageSubTypeName": {"shape": "StorageSubTypeName"}, "StorageTypeLimits": {"shape": "StorageTypeLimitList"}}}, "StorageTypeLimit": {"type": "structure", "members": {"LimitName": {"shape": "LimitName"}, "LimitValues": {"shape": "LimitValueList"}}}, "StorageTypeLimitList": {"type": "list", "member": {"shape": "StorageTypeLimit"}}, "StorageTypeList": {"type": "list", "member": {"shape": "StorageType"}}, "StorageTypeName": {"type": "string"}, "String": {"type": "string"}, "StringList": {"type": "list", "member": {"shape": "String"}}, "TLSSecurityPolicy": {"type": "string", "enum": ["Policy-Min-TLS-1-0-2019-07", "Policy-Min-TLS-1-2-2019-07"]}, "Tag": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "TagKey"}, "Value": {"shape": "TagValue"}}}, "TagKey": {"type": "string", "max": 128, "min": 1}, "TagList": {"type": "list", "member": {"shape": "Tag"}}, "TagValue": {"type": "string", "max": 256, "min": 0}, "UIntValue": {"type": "integer", "min": 0}, "UpdateElasticsearchDomainConfigRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "DomainName", "location": "uri", "locationName": "DomainName"}, "ElasticsearchClusterConfig": {"shape": "ElasticsearchClusterConfig"}, "EBSOptions": {"shape": "EBSOptions"}, "SnapshotOptions": {"shape": "SnapshotOptions"}, "VPCOptions": {"shape": "VPCOptions"}, "CognitoOptions": {"shape": "CognitoOptions"}, "AdvancedOptions": {"shape": "AdvancedOptions"}, "AccessPolicies": {"shape": "PolicyDocument"}, "LogPublishingOptions": {"shape": "LogPublishingOptions"}, "DomainEndpointOptions": {"shape": "DomainEndpointOptions"}, "AdvancedSecurityOptions": {"shape": "AdvancedSecurityOptionsInput"}}}, "UpdateElasticsearchDomainConfigResponse": {"type": "structure", "required": ["DomainConfig"], "members": {"DomainConfig": {"shape": "ElasticsearchDomainConfig"}}}, "UpdateTimestamp": {"type": "timestamp"}, "UpgradeElasticsearchDomainRequest": {"type": "structure", "required": ["DomainName", "TargetVersion"], "members": {"DomainName": {"shape": "DomainName"}, "TargetVersion": {"shape": "ElasticsearchVersionString"}, "PerformCheckOnly": {"shape": "Boolean"}}}, "UpgradeElasticsearchDomainResponse": {"type": "structure", "members": {"DomainName": {"shape": "DomainName"}, "TargetVersion": {"shape": "ElasticsearchVersionString"}, "PerformCheckOnly": {"shape": "Boolean"}}}, "UpgradeHistory": {"type": "structure", "members": {"UpgradeName": {"shape": "UpgradeName"}, "StartTimestamp": {"shape": "StartTimestamp"}, "UpgradeStatus": {"shape": "UpgradeStatus"}, "StepsList": {"shape": "UpgradeStepsList"}}}, "UpgradeHistoryList": {"type": "list", "member": {"shape": "UpgradeHistory"}}, "UpgradeName": {"type": "string"}, "UpgradeStatus": {"type": "string", "enum": ["IN_PROGRESS", "SUCCEEDED", "SUCCEEDED_WITH_ISSUES", "FAILED"]}, "UpgradeStep": {"type": "string", "enum": ["PRE_UPGRADE_CHECK", "SNAPSHOT", "UPGRADE"]}, "UpgradeStepItem": {"type": "structure", "members": {"UpgradeStep": {"shape": "UpgradeStep"}, "UpgradeStepStatus": {"shape": "UpgradeStatus"}, "Issues": {"shape": "Issues"}, "ProgressPercent": {"shape": "Double"}}}, "UpgradeStepsList": {"type": "list", "member": {"shape": "UpgradeStepItem"}}, "UserPoolId": {"type": "string", "max": 55, "min": 1, "pattern": "[\\w-]+_[0-9a-zA-Z]+"}, "Username": {"type": "string", "min": 1, "sensitive": true}, "VPCDerivedInfo": {"type": "structure", "members": {"VPCId": {"shape": "String"}, "SubnetIds": {"shape": "StringList"}, "AvailabilityZones": {"shape": "StringList"}, "SecurityGroupIds": {"shape": "StringList"}}}, "VPCDerivedInfoStatus": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "VPCDerivedInfo"}, "Status": {"shape": "OptionStatus"}}}, "VPCOptions": {"type": "structure", "members": {"SubnetIds": {"shape": "StringList"}, "SecurityGroupIds": {"shape": "StringList"}}}, "ValidationException": {"type": "structure", "members": {}, "error": {"httpStatusCode": 400}, "exception": true}, "ValueStringList": {"type": "list", "member": {"shape": "NonEmptyString"}, "min": 1}, "VolumeType": {"type": "string", "enum": ["standard", "gp2", "io1"]}, "ZoneAwarenessConfig": {"type": "structure", "members": {"AvailabilityZoneCount": {"shape": "IntegerClass"}}}}}