{"version": "2.0", "service": "<p> AWS S3 Control provides access to Amazon S3 control plane operations. </p>", "operations": {"CreateAccessPoint": "<p>Creates an access point and associates it with the specified bucket.</p>", "CreateJob": "<p>You can use Amazon S3 Batch Operations to perform large-scale Batch Operations on Amazon S3 objects. Amazon S3 Batch Operations can execute a single operation or action on lists of Amazon S3 objects that you specify. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/batch-ops-basics.html\">Amazon S3 Batch Operations</a> in the Amazon Simple Storage Service Developer Guide.</p> <p>Related actions include:</p> <ul> <li> <p> <a>DescribeJob</a> </p> </li> <li> <p> <a>ListJobs</a> </p> </li> <li> <p> <a>UpdateJobPriority</a> </p> </li> <li> <p> <a>UpdateJobStatus</a> </p> </li> </ul>", "DeleteAccessPoint": "<p>Deletes the specified access point.</p>", "DeleteAccessPointPolicy": "<p>Deletes the access point policy for the specified access point.</p>", "DeleteJobTagging": "<p>Removes the entire tag set from the specified Amazon S3 Batch Operations job. To use this operation, you must have permission to perform the <code>s3:DeleteJobTagging</code> action. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/batch-ops-managing-jobs.html#batch-ops-job-tags\">Using Job Tags</a> in the Amazon Simple Storage Service Developer Guide.</p> <p/> <p>Related actions include:</p> <ul> <li> <p> <a>CreateJob</a> </p> </li> <li> <p> <a>GetJobTagging</a> </p> </li> <li> <p> <a>PutJobTagging</a> </p> </li> </ul>", "DeletePublicAccessBlock": "<p>Removes the <code>PublicAccessBlock</code> configuration for an Amazon Web Services account.</p>", "DescribeJob": "<p>Retrieves the configuration parameters and status for a Batch Operations job. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/batch-ops-basics.html\">Amazon S3 Batch Operations</a> in the Amazon Simple Storage Service Developer Guide.</p> <p/> <p>Related actions include:</p> <ul> <li> <p> <a>CreateJob</a> </p> </li> <li> <p> <a>ListJobs</a> </p> </li> <li> <p> <a>UpdateJobPriority</a> </p> </li> <li> <p> <a>UpdateJobStatus</a> </p> </li> </ul>", "GetAccessPoint": "<p>Returns configuration information about the specified access point.</p>", "GetAccessPointPolicy": "<p>Returns the access point policy associated with the specified access point.</p>", "GetAccessPointPolicyStatus": "<p>Indicates whether the specified access point currently has a policy that allows public access. For more information about public access through access points, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/access-points.html\">Managing Data Access with Amazon S3 Access Points</a> in the <i>Amazon Simple Storage Service Developer Guide</i>.</p>", "GetJobTagging": "<p>Returns the tags on an Amazon S3 Batch Operations job. To use this operation, you must have permission to perform the <code>s3:GetJobTagging</code> action. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/batch-ops-managing-jobs.html#batch-ops-job-tags\">Using Job Tags</a> in the <i>Amazon Simple Storage Service Developer Guide</i>.</p> <p/> <p>Related actions include:</p> <ul> <li> <p> <a>CreateJob</a> </p> </li> <li> <p> <a>PutJobTagging</a> </p> </li> <li> <p> <a>DeleteJobTagging</a> </p> </li> </ul>", "GetPublicAccessBlock": "<p>Retrieves the <code>PublicAccessBlock</code> configuration for an Amazon Web Services account.</p>", "ListAccessPoints": "<p>Returns a list of the access points currently associated with the specified bucket. You can retrieve up to 1000 access points per call. If the specified bucket has more than 1,000 access points (or the number specified in <code>maxResults</code>, whichever is less), the response will include a continuation token that you can use to list the additional access points.</p>", "ListJobs": "<p>Lists current Amazon S3 Batch Operations jobs and jobs that have ended within the last 30 days for the AWS account making the request. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/batch-ops-basics.html\">Amazon S3 Batch Operations</a> in the <i>Amazon Simple Storage Service Developer Guide</i>.</p> <p>Related actions include:</p> <p/> <ul> <li> <p> <a>CreateJob</a> </p> </li> <li> <p> <a>DescribeJob</a> </p> </li> <li> <p> <a>UpdateJobPriority</a> </p> </li> <li> <p> <a>UpdateJobStatus</a> </p> </li> </ul>", "PutAccessPointPolicy": "<p>Associates an access policy with the specified access point. Each access point can have only one policy, so a request made to this API replaces any existing policy associated with the specified access point.</p>", "PutJobTagging": "<p>Set the supplied tag-set on an Amazon S3 Batch Operations job.</p> <p>A tag is a key-value pair. You can associate Amazon S3 Batch Operations tags with any job by sending a PUT request against the tagging subresource that is associated with the job. To modify the existing tag set, you can either replace the existing tag set entirely, or make changes within the existing tag set by retrieving the existing tag set using <a>GetJobTagging</a>, modify that tag set, and use this API action to replace the tag set with the one you have modified.. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/batch-ops-managing-jobs.html#batch-ops-job-tags\">Using Job Tags</a> in the Amazon Simple Storage Service Developer Guide. </p> <p/> <note> <ul> <li> <p>If you send this request with an empty tag set, Amazon S3 deletes the existing tag set on the Batch Operations job. If you use this method, you will be charged for a Tier 1 Request (PUT). For more information, see <a href=\"http://aws.amazon.com/s3/pricing/\">Amazon S3 pricing</a>.</p> </li> <li> <p>For deleting existing tags for your batch operations job, <a>DeleteJobTagging</a> request is preferred because it achieves the same result without incurring charges.</p> </li> <li> <p>A few things to consider about using tags:</p> <ul> <li> <p>Amazon S3 limits the maximum number of tags to 50 tags per job.</p> </li> <li> <p>You can associate up to 50 tags with a job as long as they have unique tag keys.</p> </li> <li> <p>A tag key can be up to 128 Unicode characters in length, and tag values can be up to 256 Unicode characters in length.</p> </li> <li> <p>The key and values are case sensitive.</p> </li> <li> <p>For tagging-related restrictions related to characters and encodings, see <a href=\"https://docs.aws.amazon.com/awsaccountbilling/latest/aboutv2/allocation-tag-restrictions.html\">User-Defined Tag Restrictions</a>.</p> </li> </ul> </li> </ul> </note> <p/> <p>To use this operation, you must have permission to perform the <code>s3:PutJobTagging</code> action.</p> <p>Related actions include:</p> <ul> <li> <p> <a>CreateJob</a> </p> </li> <li> <p> <a>GetJobTagging</a> </p> </li> <li> <p> <a>DeleteJobTagging</a> </p> </li> </ul>", "PutPublicAccessBlock": "<p>Creates or modifies the <code>PublicAccessBlock</code> configuration for an Amazon Web Services account.</p>", "UpdateJobPriority": "<p>Updates an existing Amazon S3 Batch Operations job's priority. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/batch-ops-basics.html\">Amazon S3 Batch Operations</a> in the Amazon Simple Storage Service Developer Guide.</p> <p/> <p>Related actions include:</p> <ul> <li> <p> <a>CreateJob</a> </p> </li> <li> <p> <a>ListJobs</a> </p> </li> <li> <p> <a>DescribeJob</a> </p> </li> <li> <p> <a>UpdateJobStatus</a> </p> </li> </ul>", "UpdateJobStatus": "<p>Updates the status for the specified job. Use this operation to confirm that you want to run a job or to cancel an existing job. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/batch-ops-basics.html\">Amazon S3 Batch Operations</a> in the Amazon Simple Storage Service Developer Guide.</p> <p/> <p>Related actions include:</p> <ul> <li> <p> <a>CreateJob</a> </p> </li> <li> <p> <a>ListJobs</a> </p> </li> <li> <p> <a>DescribeJob</a> </p> </li> <li> <p> <a>UpdateJobStatus</a> </p> </li> </ul>"}, "shapes": {"AccessPoint": {"base": "<p>An access point used to access a bucket.</p>", "refs": {"AccessPointList$member": null}}, "AccessPointList": {"base": null, "refs": {"ListAccessPointsResult$AccessPointList": "<p>Contains identification and configuration information for one or more access points associated with the specified bucket.</p>"}}, "AccessPointName": {"base": null, "refs": {"AccessPoint$Name": "<p>The name of this access point.</p>", "CreateAccessPointRequest$Name": "<p>The name you want to assign to this access point.</p>", "DeleteAccessPointPolicyRequest$Name": "<p>The name of the access point whose policy you want to delete.</p>", "DeleteAccessPointRequest$Name": "<p>The name of the access point you want to delete.</p>", "GetAccessPointPolicyRequest$Name": "<p>The name of the access point whose policy you want to retrieve.</p>", "GetAccessPointPolicyStatusRequest$Name": "<p>The name of the access point whose policy status you want to retrieve.</p>", "GetAccessPointRequest$Name": "<p>The name of the access point whose configuration information you want to retrieve.</p>", "GetAccessPointResult$Name": "<p>The name of the specified access point.</p>", "PutAccessPointPolicyRequest$Name": "<p>The name of the access point that you want to associate with the specified policy.</p>"}}, "AccountId": {"base": null, "refs": {"CreateAccessPointRequest$AccountId": "<p>The AWS account ID for the owner of the bucket for which you want to create an access point.</p>", "CreateJobRequest$AccountId": "<p/>", "DeleteAccessPointPolicyRequest$AccountId": "<p>The account ID for the account that owns the specified access point.</p>", "DeleteAccessPointRequest$AccountId": "<p>The account ID for the account that owns the specified access point.</p>", "DeleteJobTaggingRequest$AccountId": "<p>The AWS account ID associated with the Amazon S3 Batch Operations job.</p>", "DeletePublicAccessBlockRequest$AccountId": "<p>The account ID for the Amazon Web Services account whose <code>PublicAccessBlock</code> configuration you want to remove.</p>", "DescribeJobRequest$AccountId": "<p/>", "GetAccessPointPolicyRequest$AccountId": "<p>The account ID for the account that owns the specified access point.</p>", "GetAccessPointPolicyStatusRequest$AccountId": "<p>The account ID for the account that owns the specified access point.</p>", "GetAccessPointRequest$AccountId": "<p>The account ID for the account that owns the specified access point.</p>", "GetJobTaggingRequest$AccountId": "<p>The AWS account ID associated with the Amazon S3 Batch Operations job.</p>", "GetPublicAccessBlockRequest$AccountId": "<p>The account ID for the Amazon Web Services account whose <code>PublicAccessBlock</code> configuration you want to retrieve.</p>", "ListAccessPointsRequest$AccountId": "<p>The AWS account ID for owner of the bucket whose access points you want to list.</p>", "ListJobsRequest$AccountId": "<p/>", "PutAccessPointPolicyRequest$AccountId": "<p>The AWS account ID for owner of the bucket associated with the specified access point.</p>", "PutJobTaggingRequest$AccountId": "<p>The AWS account ID associated with the Amazon S3 Batch Operations job.</p>", "PutPublicAccessBlockRequest$AccountId": "<p>The account ID for the Amazon Web Services account whose <code>PublicAccessBlock</code> configuration you want to set.</p>", "UpdateJobPriorityRequest$AccountId": "<p/>", "UpdateJobStatusRequest$AccountId": "<p/>"}}, "BadRequestException": {"base": "<p/>", "refs": {}}, "Boolean": {"base": null, "refs": {"JobReport$Enabled": "<p>Indicates whether the specified job will generate a job-completion report.</p>", "S3CopyObjectOperation$RequesterPays": "<p/>", "S3ObjectMetadata$RequesterCharged": "<p/>", "S3SetObjectRetentionOperation$BypassGovernanceRetention": "<p>Indicates if the operation should be applied to objects in the Batch Operations job even if they have Governance-type Object Lock in place.</p>"}}, "BucketName": {"base": null, "refs": {"AccessPoint$Bucket": "<p>The name of the bucket associated with this access point.</p>", "CreateAccessPointRequest$Bucket": "<p>The name of the bucket that you want to associate this access point with.</p>", "GetAccessPointResult$Bucket": "<p>The name of the bucket associated with the specified access point.</p>", "ListAccessPointsRequest$Bucket": "<p>The name of the bucket whose associated access points you want to list.</p>"}}, "ConfirmationRequired": {"base": null, "refs": {"CreateJobRequest$ConfirmationRequired": "<p>Indicates whether confirmation is required before Amazon S3 runs the job. Confirmation is only required for jobs created through the Amazon S3 console.</p>", "JobDescriptor$ConfirmationRequired": "<p>Indicates whether confirmation is required before Amazon S3 begins running the specified job. Confirmation is required only for jobs created through the Amazon S3 console.</p>"}}, "CreateAccessPointRequest": {"base": null, "refs": {}}, "CreateJobRequest": {"base": null, "refs": {}}, "CreateJobResult": {"base": null, "refs": {}}, "CreationDate": {"base": null, "refs": {"GetAccessPointResult$CreationDate": "<p>The date and time when the specified access point was created.</p>"}}, "DeleteAccessPointPolicyRequest": {"base": null, "refs": {}}, "DeleteAccessPointRequest": {"base": null, "refs": {}}, "DeleteJobTaggingRequest": {"base": null, "refs": {}}, "DeleteJobTaggingResult": {"base": null, "refs": {}}, "DeletePublicAccessBlockRequest": {"base": null, "refs": {}}, "DescribeJobRequest": {"base": null, "refs": {}}, "DescribeJobResult": {"base": null, "refs": {}}, "ExceptionMessage": {"base": null, "refs": {"BadRequestException$Message": null, "IdempotencyException$Message": null, "InternalServiceException$Message": null, "InvalidNextTokenException$Message": null, "InvalidRequestException$Message": null, "JobStatusException$Message": null, "NotFoundException$Message": null, "TooManyRequestsException$Message": null, "TooManyTagsException$Message": null}}, "FunctionArnString": {"base": null, "refs": {"LambdaInvokeOperation$FunctionArn": "<p>The Amazon Resource Name (ARN) for the AWS Lambda function that the specified job will invoke for each object in the manifest.</p>"}}, "GetAccessPointPolicyRequest": {"base": null, "refs": {}}, "GetAccessPointPolicyResult": {"base": null, "refs": {}}, "GetAccessPointPolicyStatusRequest": {"base": null, "refs": {}}, "GetAccessPointPolicyStatusResult": {"base": null, "refs": {}}, "GetAccessPointRequest": {"base": null, "refs": {}}, "GetAccessPointResult": {"base": null, "refs": {}}, "GetJobTaggingRequest": {"base": null, "refs": {}}, "GetJobTaggingResult": {"base": null, "refs": {}}, "GetPublicAccessBlockOutput": {"base": null, "refs": {}}, "GetPublicAccessBlockRequest": {"base": null, "refs": {}}, "IAMRoleArn": {"base": null, "refs": {"CreateJobRequest$RoleArn": "<p>The Amazon Resource Name (ARN) for the AWS Identity and Access Management (IAM) role that Batch Operations will use to execute this job's operation on each object in the manifest.</p>", "JobDescriptor$RoleArn": "<p>The Amazon Resource Name (ARN) for the AWS Identity and Access Management (IAM) role assigned to execute the tasks for this job.</p>"}}, "IdempotencyException": {"base": "<p/>", "refs": {}}, "InternalServiceException": {"base": "<p/>", "refs": {}}, "InvalidNextTokenException": {"base": "<p/>", "refs": {}}, "InvalidRequestException": {"base": "<p/>", "refs": {}}, "IsPublic": {"base": null, "refs": {"PolicyStatus$IsPublic": "<p/>"}}, "JobArn": {"base": null, "refs": {"JobDescriptor$JobArn": "<p>The Amazon Resource Name (ARN) for this job.</p>"}}, "JobCreationTime": {"base": null, "refs": {"JobDescriptor$CreationTime": "<p>A timestamp indicating when this job was created.</p>", "JobListDescriptor$CreationTime": "<p>A timestamp indicating when the specified job was created.</p>"}}, "JobDescriptor": {"base": "<p>A container element for the job configuration and status information returned by a <code>Describe Job</code> request.</p>", "refs": {"DescribeJobResult$Job": "<p>Contains the configuration parameters and status for the job specified in the <code>Describe Job</code> request.</p>"}}, "JobFailure": {"base": "<p>If this job failed, this element indicates why the job failed.</p>", "refs": {"JobFailureList$member": null}}, "JobFailureCode": {"base": null, "refs": {"JobFailure$FailureCode": "<p>The failure code, if any, for the specified job.</p>"}}, "JobFailureList": {"base": null, "refs": {"JobDescriptor$FailureReasons": "<p>If the specified job failed, this field contains information describing the failure.</p>"}}, "JobFailureReason": {"base": null, "refs": {"JobFailure$FailureReason": "<p>The failure reason, if any, for the specified job.</p>"}}, "JobId": {"base": null, "refs": {"CreateJobResult$JobId": "<p>The ID for this job. Amazon S3 generates this ID automatically and returns it after a successful <code>Create Job</code> request.</p>", "DeleteJobTaggingRequest$JobId": "<p>The ID for the Amazon S3 Batch Operations job whose tags you want to delete.</p>", "DescribeJobRequest$JobId": "<p>The ID for the job whose information you want to retrieve.</p>", "GetJobTaggingRequest$JobId": "<p>The ID for the Amazon S3 Batch Operations job whose tags you want to retrieve.</p>", "JobDescriptor$JobId": "<p>The ID for the specified job.</p>", "JobListDescriptor$JobId": "<p>The ID for the specified job.</p>", "PutJobTaggingRequest$JobId": "<p>The ID for the Amazon S3 Batch Operations job whose tags you want to replace.</p>", "UpdateJobPriorityRequest$JobId": "<p>The ID for the job whose priority you want to update.</p>", "UpdateJobPriorityResult$JobId": "<p>The ID for the job whose priority Amazon S3 updated.</p>", "UpdateJobStatusRequest$JobId": "<p>The ID of the job whose status you want to update.</p>", "UpdateJobStatusResult$JobId": "<p>The ID for the job whose status was updated.</p>"}}, "JobListDescriptor": {"base": "<p>Contains the configuration and status information for a single job retrieved as part of a job list.</p>", "refs": {"JobListDescriptorList$member": null}}, "JobListDescriptorList": {"base": null, "refs": {"ListJobsResult$Jobs": "<p>The list of current jobs and jobs that have ended within the last 30 days.</p>"}}, "JobManifest": {"base": "<p>Contains the configuration information for a job's manifest.</p>", "refs": {"CreateJobRequest$Manifest": "<p>Configuration parameters for the manifest.</p>", "JobDescriptor$Manifest": "<p>The configuration information for the specified job's manifest object.</p>"}}, "JobManifestFieldList": {"base": null, "refs": {"JobManifestSpec$Fields": "<p>If the specified manifest object is in the <code>S3BatchOperations_CSV_20180820</code> format, this element describes which columns contain the required data.</p>"}}, "JobManifestFieldName": {"base": null, "refs": {"JobManifestFieldList$member": null}}, "JobManifestFormat": {"base": null, "refs": {"JobManifestSpec$Format": "<p>Indicates which of the available formats the specified manifest uses.</p>"}}, "JobManifestLocation": {"base": "<p>Contains the information required to locate a manifest object.</p>", "refs": {"JobManifest$Location": "<p>Contains the information required to locate the specified job's manifest.</p>"}}, "JobManifestSpec": {"base": "<p>Describes the format of a manifest. If the manifest is in CSV format, also describes the columns contained within the manifest.</p>", "refs": {"JobManifest$Spec": "<p>Describes the format of the specified job's manifest. If the manifest is in CSV format, also describes the columns contained within the manifest.</p>"}}, "JobNumberOfTasksFailed": {"base": null, "refs": {"JobProgressSummary$NumberOfTasksFailed": "<p/>"}}, "JobNumberOfTasksSucceeded": {"base": null, "refs": {"JobProgressSummary$NumberOfTasksSucceeded": "<p/>"}}, "JobOperation": {"base": "<p>The operation that you want this job to perform on each object listed in the manifest. For more information about the available operations, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/batch-ops-operations.html\">Available Operations</a> in the <i>Amazon Simple Storage Service Developer Guide</i>.</p>", "refs": {"CreateJobRequest$Operation": "<p>The operation that you want this job to perform on each object listed in the manifest. For more information about the available operations, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/batch-ops-operations.html\">Available Operations</a> in the <i>Amazon Simple Storage Service Developer Guide</i>.</p>", "JobDescriptor$Operation": "<p>The operation that the specified job is configured to execute on the objects listed in the manifest.</p>"}}, "JobPriority": {"base": null, "refs": {"CreateJobRequest$Priority": "<p>The numerical priority for this job. Higher numbers indicate higher priority.</p>", "JobDescriptor$Priority": "<p>The priority of the specified job.</p>", "JobListDescriptor$Priority": "<p>The current priority for the specified job.</p>", "UpdateJobPriorityRequest$Priority": "<p>The priority you want to assign to this job.</p>", "UpdateJobPriorityResult$Priority": "<p>The new priority assigned to the specified job.</p>"}}, "JobProgressSummary": {"base": "<p>Describes the total number of tasks that the specified job has executed, the number of tasks that succeeded, and the number of tasks that failed.</p>", "refs": {"JobDescriptor$ProgressSummary": "<p>Describes the total number of tasks that the specified job has executed, the number of tasks that succeeded, and the number of tasks that failed.</p>", "JobListDescriptor$ProgressSummary": "<p>Describes the total number of tasks that the specified job has executed, the number of tasks that succeeded, and the number of tasks that failed.</p>"}}, "JobReport": {"base": "<p>Contains the configuration parameters for a job-completion report.</p>", "refs": {"CreateJobRequest$Report": "<p>Configuration parameters for the optional job-completion report.</p>", "JobDescriptor$Report": "<p>Contains the configuration information for the job-completion report if you requested one in the <code>Create Job</code> request.</p>"}}, "JobReportFormat": {"base": null, "refs": {"JobReport$Format": "<p>The format of the specified job-completion report.</p>"}}, "JobReportScope": {"base": null, "refs": {"JobReport$ReportScope": "<p>Indicates whether the job-completion report will include details of all tasks or only failed tasks.</p>"}}, "JobStatus": {"base": null, "refs": {"JobDescriptor$Status": "<p>The current status of the specified job.</p>", "JobListDescriptor$Status": "<p>The specified job's current status.</p>", "JobStatusList$member": null, "UpdateJobStatusResult$Status": "<p>The current status for the specified job.</p>"}}, "JobStatusException": {"base": "<p/>", "refs": {}}, "JobStatusList": {"base": null, "refs": {"ListJobsRequest$JobStatuses": "<p>The <code>List Jobs</code> request returns jobs that match the statuses listed in this element.</p>"}}, "JobStatusUpdateReason": {"base": null, "refs": {"JobDescriptor$StatusUpdateReason": "<p/>", "UpdateJobStatusRequest$StatusUpdateReason": "<p>A description of the reason why you want to change the specified job's status. This field can be any string up to the maximum length.</p>", "UpdateJobStatusResult$StatusUpdateReason": "<p>The reason that the specified job's status was updated.</p>"}}, "JobTerminationDate": {"base": null, "refs": {"JobDescriptor$TerminationDate": "<p>A timestamp indicating when this job terminated. A job's termination date is the date and time when it succeeded, failed, or was canceled.</p>", "JobListDescriptor$TerminationDate": "<p>A timestamp indicating when the specified job terminated. A job's termination date is the date and time when it succeeded, failed, or was canceled.</p>"}}, "JobTotalNumberOfTasks": {"base": null, "refs": {"JobProgressSummary$TotalNumberOfTasks": "<p/>"}}, "KmsKeyArnString": {"base": null, "refs": {"S3CopyObjectOperation$SSEAwsKmsKeyId": "<p/>"}}, "LambdaInvokeOperation": {"base": "<p>Contains the configuration parameters for a <code>Lambda Invoke</code> operation.</p>", "refs": {"JobOperation$LambdaInvoke": "<p>Directs the specified job to invoke an AWS Lambda function on each object in the manifest.</p>"}}, "ListAccessPointsRequest": {"base": null, "refs": {}}, "ListAccessPointsResult": {"base": null, "refs": {}}, "ListJobsRequest": {"base": null, "refs": {}}, "ListJobsResult": {"base": null, "refs": {}}, "MaxLength1024String": {"base": null, "refs": {"S3UserMetadata$value": null}}, "MaxResults": {"base": null, "refs": {"ListAccessPointsRequest$MaxResults": "<p>The maximum number of access points that you want to include in the list. If the specified bucket has more than this number of access points, then the response will include a continuation token in the <code>NextToken</code> field that you can use to retrieve the next page of access points.</p>", "ListJobsRequest$MaxResults": "<p>The maximum number of jobs that Amazon S3 will include in the <code>List Jobs</code> response. If there are more jobs than this number, the response will include a pagination token in the <code>NextToken</code> field to enable you to retrieve the next page of results.</p>"}}, "NetworkOrigin": {"base": null, "refs": {"AccessPoint$NetworkOrigin": "<p>Indicates whether this access point allows access from the public internet. If <code>VpcConfiguration</code> is specified for this access point, then <code>NetworkOrigin</code> is <code>VPC</code>, and the access point doesn't allow access from the public internet. Otherwise, <code>NetworkOrigin</code> is <code>Internet</code>, and the access point allows access from the public internet, subject to the access point and bucket access policies.</p>", "GetAccessPointResult$NetworkOrigin": "<p>Indicates whether this access point allows access from the public internet. If <code>VpcConfiguration</code> is specified for this access point, then <code>NetworkOrigin</code> is <code>VPC</code>, and the access point doesn't allow access from the public internet. Otherwise, <code>NetworkOrigin</code> is <code>Internet</code>, and the access point allows access from the public internet, subject to the access point and bucket access policies.</p>"}}, "NoSuchPublicAccessBlockConfiguration": {"base": "<p>Amazon S3 throws this exception if you make a <code>GetPublicAccessBlock</code> request against an account that doesn't have a <code>PublicAccessBlockConfiguration</code> set.</p>", "refs": {}}, "NoSuchPublicAccessBlockConfigurationMessage": {"base": null, "refs": {"NoSuchPublicAccessBlockConfiguration$Message": null}}, "NonEmptyMaxLength1024String": {"base": null, "refs": {"JobManifestLocation$ETag": "<p>The ETag for the specified manifest object.</p>", "ListAccessPointsRequest$NextToken": "<p>A continuation token. If a previous call to <code>ListAccessPoints</code> returned a continuation token in the <code>NextToken</code> field, then providing that value here causes Amazon S3 to retrieve the next page of results.</p>", "ListAccessPointsResult$NextToken": "<p>If the specified bucket has more access points than can be returned in one call to this API, then this field contains a continuation token that you can provide in subsequent calls to this API to retrieve additional access points.</p>", "S3CopyObjectOperation$TargetKeyPrefix": "<p/>", "S3Grantee$Identifier": "<p/>", "S3Grantee$DisplayName": "<p/>", "S3ObjectMetadata$CacheControl": "<p/>", "S3ObjectMetadata$ContentDisposition": "<p/>", "S3ObjectMetadata$ContentEncoding": "<p/>", "S3ObjectMetadata$ContentLanguage": "<p/>", "S3ObjectMetadata$ContentMD5": "<p/>", "S3ObjectMetadata$ContentType": "<p/>", "S3ObjectOwner$ID": "<p/>", "S3ObjectOwner$DisplayName": "<p/>", "S3UserMetadata$key": null}}, "NonEmptyMaxLength2048String": {"base": null, "refs": {"S3CopyObjectOperation$RedirectLocation": "<p/>"}}, "NonEmptyMaxLength256String": {"base": null, "refs": {"CreateJobRequest$Description": "<p>A description for this job. You can use any string within the permitted length. Descriptions don't need to be unique and can be used for multiple jobs.</p>", "JobDescriptor$Description": "<p>The description for this job, if one was provided in this job's <code>Create Job</code> request.</p>", "JobListDescriptor$Description": "<p>The user-specified description that was included in the specified job's <code>Create Job</code> request.</p>"}}, "NonEmptyMaxLength64String": {"base": null, "refs": {"CreateJobRequest$ClientRequestToken": "<p>An idempotency token to ensure that you don't accidentally submit the same request twice. You can use any string up to the maximum length.</p>"}}, "NotFoundException": {"base": "<p/>", "refs": {}}, "OperationName": {"base": null, "refs": {"JobListDescriptor$Operation": "<p>The operation that the specified job is configured to run on each object listed in the manifest.</p>"}}, "Policy": {"base": null, "refs": {"GetAccessPointPolicyResult$Policy": "<p>The access point policy associated with the specified access point.</p>", "PutAccessPointPolicyRequest$Policy": "<p>The policy that you want to apply to the specified access point. For more information about access point policies, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/access-points.html\">Managing Data Access with Amazon S3 Access Points</a> in the <i>Amazon Simple Storage Service Developer Guide</i>.</p>"}}, "PolicyStatus": {"base": "<p>Indicates whether this access point policy is public. For more information about how Amazon S3 evaluates policies to determine whether they are public, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/access-control-block-public-access.html#access-control-block-public-access-policy-status\">The Meaning of \"Public\"</a> in the <i>Amazon Simple Storage Service Developer Guide</i>. </p>", "refs": {"GetAccessPointPolicyStatusResult$PolicyStatus": "<p>Indicates the current policy status of the specified access point.</p>"}}, "PublicAccessBlockConfiguration": {"base": "<p>The <code>PublicAccessBlock</code> configuration that you want to apply to this Amazon S3 bucket. You can enable the configuration options in any combination. For more information about when Amazon S3 considers a bucket or object public, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/access-control-block-public-access.html#access-control-block-public-access-policy-status\">The Meaning of \"Public\"</a> in the Amazon Simple Storage Service Developer Guide.</p>", "refs": {"CreateAccessPointRequest$PublicAccessBlockConfiguration": null, "GetAccessPointResult$PublicAccessBlockConfiguration": null, "GetPublicAccessBlockOutput$PublicAccessBlockConfiguration": "<p>The <code>PublicAccessBlock</code> configuration currently in effect for this Amazon Web Services account.</p>", "PutPublicAccessBlockRequest$PublicAccessBlockConfiguration": "<p>The <code>PublicAccessBlock</code> configuration that you want to apply to the specified Amazon Web Services account.</p>"}}, "PutAccessPointPolicyRequest": {"base": null, "refs": {}}, "PutJobTaggingRequest": {"base": null, "refs": {}}, "PutJobTaggingResult": {"base": null, "refs": {}}, "PutPublicAccessBlockRequest": {"base": null, "refs": {}}, "ReportPrefixString": {"base": null, "refs": {"JobReport$Prefix": "<p>An optional prefix to describe where in the specified bucket the job-completion report will be stored. Amazon S3 will store the job-completion report at &lt;prefix&gt;/job-&lt;job-id&gt;/report.json.</p>"}}, "RequestedJobStatus": {"base": null, "refs": {"UpdateJobStatusRequest$RequestedJobStatus": "<p>The status that you want to move the specified job to.</p>"}}, "S3AccessControlList": {"base": "<p/>", "refs": {"S3AccessControlPolicy$AccessControlList": "<p/>"}}, "S3AccessControlPolicy": {"base": "<p/>", "refs": {"S3SetObjectAclOperation$AccessControlPolicy": "<p/>"}}, "S3BucketArnString": {"base": null, "refs": {"JobReport$Bucket": "<p>The Amazon Resource Name (ARN) for the bucket where specified job-completion report will be stored.</p>", "S3CopyObjectOperation$TargetResource": "<p/>"}}, "S3CannedAccessControlList": {"base": null, "refs": {"S3AccessControlPolicy$CannedAccessControlList": "<p/>", "S3CopyObjectOperation$CannedAccessControlList": "<p/>"}}, "S3ContentLength": {"base": null, "refs": {"S3ObjectMetadata$ContentLength": "<p/>"}}, "S3CopyObjectOperation": {"base": "<p>Contains the configuration parameters for a PUT Copy object operation. Amazon S3 Batch Operations passes each value through to the underlying PUT Copy object API. For more information about the parameters for this operation, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/RESTObjectCOPY.html\">PUT Object - Copy</a>.</p>", "refs": {"JobOperation$S3PutObjectCopy": "<p>Directs the specified job to execute a PUT Copy object call on each object in the manifest.</p>"}}, "S3ExpirationInDays": {"base": null, "refs": {"S3InitiateRestoreObjectOperation$ExpirationInDays": "<p/>"}}, "S3GlacierJobTier": {"base": null, "refs": {"S3InitiateRestoreObjectOperation$GlacierJobTier": "<p/>"}}, "S3Grant": {"base": "<p/>", "refs": {"S3GrantList$member": null}}, "S3GrantList": {"base": null, "refs": {"S3AccessControlList$Grants": "<p/>", "S3CopyObjectOperation$AccessControlGrants": "<p/>"}}, "S3Grantee": {"base": "<p/>", "refs": {"S3Grant$Grantee": "<p/>"}}, "S3GranteeTypeIdentifier": {"base": null, "refs": {"S3Grantee$TypeIdentifier": "<p/>"}}, "S3InitiateRestoreObjectOperation": {"base": "<p>Contains the configuration parameters for an Initiate Glacier Restore job. Amazon S3 Batch Operations passes each value through to the underlying POST Object restore API. For more information about the parameters for this operation, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/RESTObjectPOSTrestore.html#RESTObjectPOSTrestore-restore-request\">Restoring Archives</a>.</p>", "refs": {"JobOperation$S3InitiateRestoreObject": "<p>Directs the specified job to execute an Initiate Glacier Restore call on each object in the manifest.</p>"}}, "S3KeyArnString": {"base": null, "refs": {"JobManifestLocation$ObjectArn": "<p>The Amazon Resource Name (ARN) for a manifest object.</p>"}}, "S3MetadataDirective": {"base": null, "refs": {"S3CopyObjectOperation$MetadataDirective": "<p/>"}}, "S3ObjectLockLegalHold": {"base": "<p/>", "refs": {"S3SetObjectLegalHoldOperation$LegalHold": "<p>The Legal Hold contains the status to be applied to all objects in the Batch Operations job.</p>"}}, "S3ObjectLockLegalHoldStatus": {"base": null, "refs": {"S3CopyObjectOperation$ObjectLockLegalHoldStatus": "<p>The Legal Hold status to be applied to all objects in the Batch Operations job.</p>", "S3ObjectLockLegalHold$Status": "<p>The Legal Hold status to be applied to all objects in the Batch Operations job.</p>"}}, "S3ObjectLockMode": {"base": null, "refs": {"S3CopyObjectOperation$ObjectLockMode": "<p>The Retention mode to be applied to all objects in the Batch Operations job.</p>"}}, "S3ObjectLockRetentionMode": {"base": null, "refs": {"S3Retention$Mode": "<p>The Retention mode to be applied to all objects in the Batch Operations job.</p>"}}, "S3ObjectMetadata": {"base": "<p/>", "refs": {"S3CopyObjectOperation$NewObjectMetadata": "<p/>"}}, "S3ObjectOwner": {"base": "<p/>", "refs": {"S3AccessControlList$Owner": "<p/>"}}, "S3ObjectVersionId": {"base": null, "refs": {"JobManifestLocation$ObjectVersionId": "<p>The optional version ID to identify a specific version of the manifest object.</p>"}}, "S3Permission": {"base": null, "refs": {"S3Grant$Permission": "<p/>"}}, "S3Retention": {"base": "<p/>", "refs": {"S3SetObjectRetentionOperation$Retention": "<p>Amazon S3 object lock Retention contains the retention mode to be applied to all objects in the Batch Operations job.</p>"}}, "S3SSEAlgorithm": {"base": null, "refs": {"S3ObjectMetadata$SSEAlgorithm": "<p/>"}}, "S3SetObjectAclOperation": {"base": "<p>Contains the configuration parameters for a Set Object ACL operation. Amazon S3 Batch Operations passes each value through to the underlying PUT Object acl API. For more information about the parameters for this operation, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/RESTObjectPUTacl.html\">PUT Object acl</a>.</p>", "refs": {"JobOperation$S3PutObjectAcl": "<p>Directs the specified job to execute a PUT Object acl call on each object in the manifest.</p>"}}, "S3SetObjectLegalHoldOperation": {"base": "<p>Contains the configuration parameters for a Set Object Legal Hold operation. Amazon S3 Batch Operations passes each value through to the underlying PUT Object Legal Hold API. For more information about the parameters for this operation, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/object-lock-overview.htmll#object-lock-legal-holds\">PUT Object Legal Hold</a>.</p>", "refs": {"JobOperation$S3PutObjectLegalHold": null}}, "S3SetObjectRetentionOperation": {"base": "<p>Contains the configuration parameters for a Set Object Retention operation. Amazon S3 Batch Operations passes each value through to the underlying PUT Object Retention API. For more information about the parameters for this operation, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/object-lock-overview.html#object-lock-retention-modes\">PUT Object Retention</a>.</p>", "refs": {"JobOperation$S3PutObjectRetention": null}}, "S3SetObjectTaggingOperation": {"base": "<p>Contains the configuration parameters for a Set Object Tagging operation. Amazon S3 Batch Operations passes each value through to the underlying PUT Object tagging API. For more information about the parameters for this operation, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/RESTObjectPUTtagging.html\">PUT Object tagging</a>.</p>", "refs": {"JobOperation$S3PutObjectTagging": "<p>Directs the specified job to execute a PUT Object tagging call on each object in the manifest.</p>"}}, "S3StorageClass": {"base": null, "refs": {"S3CopyObjectOperation$StorageClass": "<p/>"}}, "S3Tag": {"base": "<p/>", "refs": {"S3TagSet$member": null}}, "S3TagSet": {"base": null, "refs": {"CreateJobRequest$Tags": "<p>A set of tags to associate with the Amazon S3 Batch Operations job. This is an optional parameter. </p>", "GetJobTaggingResult$Tags": "<p>The set of tags associated with the Amazon S3 Batch Operations job.</p>", "PutJobTaggingRequest$Tags": "<p>The set of tags to associate with the Amazon S3 Batch Operations job.</p>", "S3CopyObjectOperation$NewObjectTagging": "<p/>", "S3SetObjectTaggingOperation$TagSet": "<p/>"}}, "S3UserMetadata": {"base": null, "refs": {"S3ObjectMetadata$UserMetadata": "<p/>"}}, "Setting": {"base": null, "refs": {"PublicAccessBlockConfiguration$BlockPublicAcls": "<p>Specifies whether Amazon S3 should block public access control lists (ACLs) for buckets in this account. Setting this element to <code>TRUE</code> causes the following behavior:</p> <ul> <li> <p>PUT Bucket acl and PUT Object acl calls fail if the specified ACL is public.</p> </li> <li> <p>PUT Object calls fail if the request includes a public ACL.</p> </li> <li> <p>PUT Bucket calls fail if the request includes a public ACL.</p> </li> </ul> <p>Enabling this setting doesn't affect existing policies or ACLs.</p>", "PublicAccessBlockConfiguration$IgnorePublicAcls": "<p>Specifies whether Amazon S3 should ignore public ACLs for buckets in this account. Setting this element to <code>TRUE</code> causes Amazon S3 to ignore all public ACLs on buckets in this account and any objects that they contain. </p> <p>Enabling this setting doesn't affect the persistence of any existing ACLs and doesn't prevent new public ACLs from being set.</p>", "PublicAccessBlockConfiguration$BlockPublicPolicy": "<p>Specifies whether Amazon S3 should block public bucket policies for buckets in this account. Setting this element to <code>TRUE</code> causes Amazon S3 to reject calls to PUT Bucket policy if the specified bucket policy allows public access. </p> <p>Enabling this setting doesn't affect existing bucket policies.</p>", "PublicAccessBlockConfiguration$RestrictPublicBuckets": "<p>Specifies whether Amazon S3 should restrict public bucket policies for buckets in this account. Setting this element to <code>TRUE</code> restricts access to buckets with public policies to only AWS services and authorized users within this account.</p> <p>Enabling this setting doesn't affect previously stored bucket policies, except that public and cross-account access within any public bucket policy, including non-public delegation to specific accounts, is blocked.</p>"}}, "StringForNextToken": {"base": null, "refs": {"ListJobsRequest$NextToken": "<p>A pagination token to request the next page of results. Use the token that Amazon S3 returned in the <code>NextToken</code> element of the <code>ListJobsResult</code> from the previous <code>List Jobs</code> request.</p>", "ListJobsResult$NextToken": "<p>If the <code>List Jobs</code> request produced more than the maximum number of results, you can pass this value into a subsequent <code>List Jobs</code> request in order to retrieve the next page of results.</p>"}}, "SuspendedCause": {"base": null, "refs": {"JobDescriptor$SuspendedCause": "<p>The reason why the specified job was suspended. A job is only suspended if you create it through the Amazon S3 console. When you create the job, it enters the <code>Suspended</code> state to await confirmation before running. After you confirm the job, it automatically exits the <code>Suspended</code> state.</p>"}}, "SuspendedDate": {"base": null, "refs": {"JobDescriptor$SuspendedDate": "<p>The timestamp when this job was suspended, if it has been suspended.</p>"}}, "TagKeyString": {"base": null, "refs": {"S3Tag$Key": "<p/>"}}, "TagValueString": {"base": null, "refs": {"S3Tag$Value": "<p/>"}}, "TimeStamp": {"base": null, "refs": {"S3CopyObjectOperation$ModifiedSinceConstraint": "<p/>", "S3CopyObjectOperation$UnModifiedSinceConstraint": "<p/>", "S3CopyObjectOperation$ObjectLockRetainUntilDate": "<p>The date when the applied Object Retention configuration will expire on all objects in the Batch Operations job.</p>", "S3ObjectMetadata$HttpExpiresDate": "<p/>", "S3Retention$RetainUntilDate": "<p>The date when the applied Object Retention will expire on all objects in the Batch Operations job.</p>"}}, "TooManyRequestsException": {"base": "<p/>", "refs": {}}, "TooManyTagsException": {"base": "<p/>", "refs": {}}, "UpdateJobPriorityRequest": {"base": null, "refs": {}}, "UpdateJobPriorityResult": {"base": null, "refs": {}}, "UpdateJobStatusRequest": {"base": null, "refs": {}}, "UpdateJobStatusResult": {"base": null, "refs": {}}, "VpcConfiguration": {"base": "<p>The virtual private cloud (VPC) configuration for an access point.</p>", "refs": {"AccessPoint$VpcConfiguration": "<p>The virtual private cloud (VPC) configuration for this access point, if one exists.</p>", "CreateAccessPointRequest$VpcConfiguration": "<p>If you include this field, Amazon S3 restricts access to this access point to requests from the specified virtual private cloud (VPC).</p>", "GetAccessPointResult$VpcConfiguration": "<p>Contains the virtual private cloud (VPC) configuration for the specified access point.</p>"}}, "VpcId": {"base": null, "refs": {"VpcConfiguration$VpcId": "<p>If this field is specified, this access point will only allow connections from the specified VPC ID.</p>"}}}}