{"version": "2.0", "service": "<p>Amazon Lightsail is the easiest way to get started with Amazon Web Services (AWS) for developers who need to build websites or web applications. It includes everything you need to launch your project quickly – instances (virtual private servers), managed databases, SSD-based block storage, static IP addresses, load balancers, content delivery network (CDN) distributions, DNS management of registered domains, and snapshots (backups) – for a low, predictable monthly price.</p> <p>You can manage your Lightsail resources using the Lightsail console, Lightsail API, AWS Command Line Interface (AWS CLI), or SDKs. For more information about Lightsail concepts and tasks, see the <a href=\"http://lightsail.aws.amazon.com/ls/docs/how-to/article/lightsail-how-to-set-up-access-keys-to-use-sdk-api-cli\">Lightsail Dev Guide</a>.</p> <p>This API Reference provides detailed information about the actions, data types, parameters, and errors of the Lightsail service. For more information about the supported AWS Regions, endpoints, and service quotas for the Lightsail service, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/lightsail.html\">Amazon Lightsail Endpoints and Quotas</a> in the <i>AWS General Reference</i>.</p>", "operations": {"AllocateStaticIp": "<p>Allocates a static IP address.</p>", "AttachCertificateToDistribution": "<p>Attaches an SSL/TLS certificate to your Amazon Lightsail content delivery network (CDN) distribution.</p> <p>After the certificate is attached, your distribution accepts HTTPS traffic for all of the domains that are associated with the certificate.</p> <p>Use the <code>CreateCertificate</code> action to create a certificate that you can attach to your distribution.</p> <important> <p>Only certificates created in the <code>us-east-1</code> AWS Region can be attached to Lightsail distributions. Lightsail distributions are global resources that can reference an origin in any AWS Region, and distribute its content globally. However, all distributions are located in the <code>us-east-1</code> Region.</p> </important>", "AttachDisk": "<p>Attaches a block storage disk to a running or stopped Lightsail instance and exposes it to the instance with the specified disk name.</p> <p>The <code>attach disk</code> operation supports tag-based access control via resource tags applied to the resource identified by <code>disk name</code>. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "AttachInstancesToLoadBalancer": "<p>Attaches one or more Lightsail instances to a load balancer.</p> <p>After some time, the instances are attached to the load balancer and the health check status is available.</p> <p>The <code>attach instances to load balancer</code> operation supports tag-based access control via resource tags applied to the resource identified by <code>load balancer name</code>. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "AttachLoadBalancerTlsCertificate": "<p>Attaches a Transport Layer Security (TLS) certificate to your load balancer. TLS is just an updated, more secure version of Secure Socket Layer (SSL).</p> <p>Once you create and validate your certificate, you can attach it to your load balancer. You can also use this API to rotate the certificates on your account. Use the <code>AttachLoadBalancerTlsCertificate</code> action with the non-attached certificate, and it will replace the existing one and become the attached certificate.</p> <p>The <code>AttachLoadBalancerTlsCertificate</code> operation supports tag-based access control via resource tags applied to the resource identified by <code>load balancer name</code>. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "AttachStaticIp": "<p>Attaches a static IP address to a specific Amazon Lightsail instance.</p>", "CloseInstancePublicPorts": "<p>Closes ports for a specific Amazon Lightsail instance.</p> <p>The <code>CloseInstancePublicPorts</code> action supports tag-based access control via resource tags applied to the resource identified by <code>instanceName</code>. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "CopySnapshot": "<p>Copies a manual snapshot of an instance or disk as another manual snapshot, or copies an automatic snapshot of an instance or disk as a manual snapshot. This operation can also be used to copy a manual or automatic snapshot of an instance or a disk from one AWS Region to another in Amazon Lightsail.</p> <p>When copying a <i>manual snapshot</i>, be sure to define the <code>source region</code>, <code>source snapshot name</code>, and <code>target snapshot name</code> parameters.</p> <p>When copying an <i>automatic snapshot</i>, be sure to define the <code>source region</code>, <code>source resource name</code>, <code>target snapshot name</code>, and either the <code>restore date</code> or the <code>use latest restorable auto snapshot</code> parameters.</p>", "CreateCertificate": "<p>Creates an SSL/TLS certificate for a Amazon Lightsail content delivery network (CDN) distribution.</p> <p>After the certificate is created, use the <code>AttachCertificateToDistribution</code> action to attach the certificate to your distribution.</p> <important> <p>Only certificates created in the <code>us-east-1</code> AWS Region can be attached to Lightsail distributions. Lightsail distributions are global resources that can reference an origin in any AWS Region, and distribute its content globally. However, all distributions are located in the <code>us-east-1</code> Region.</p> </important>", "CreateCloudFormationStack": "<p>Creates an AWS CloudFormation stack, which creates a new Amazon EC2 instance from an exported Amazon Lightsail snapshot. This operation results in a CloudFormation stack record that can be used to track the AWS CloudFormation stack created. Use the <code>get cloud formation stack records</code> operation to get a list of the CloudFormation stacks created.</p> <important> <p>Wait until after your new Amazon EC2 instance is created before running the <code>create cloud formation stack</code> operation again with the same export snapshot record.</p> </important>", "CreateContactMethod": "<p>Creates an email or SMS text message contact method.</p> <p>A contact method is used to send you notifications about your Amazon Lightsail resources. You can add one email address and one mobile phone number contact method in each AWS Region. However, SMS text messaging is not supported in some AWS Regions, and SMS text messages cannot be sent to some countries/regions. For more information, see <a href=\"https://lightsail.aws.amazon.com/ls/docs/en_us/articles/amazon-lightsail-notifications\">Notifications in Amazon Lightsail</a>.</p>", "CreateDisk": "<p>Creates a block storage disk that can be attached to an Amazon Lightsail instance in the same Availability Zone (e.g., <code>us-east-2a</code>).</p> <p>The <code>create disk</code> operation supports tag-based access control via request tags. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "CreateDiskFromSnapshot": "<p>Creates a block storage disk from a manual or automatic snapshot of a disk. The resulting disk can be attached to an Amazon Lightsail instance in the same Availability Zone (e.g., <code>us-east-2a</code>).</p> <p>The <code>create disk from snapshot</code> operation supports tag-based access control via request tags and resource tags applied to the resource identified by <code>disk snapshot name</code>. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "CreateDiskSnapshot": "<p>Creates a snapshot of a block storage disk. You can use snapshots for backups, to make copies of disks, and to save data before shutting down a Lightsail instance.</p> <p>You can take a snapshot of an attached disk that is in use; however, snapshots only capture data that has been written to your disk at the time the snapshot command is issued. This may exclude any data that has been cached by any applications or the operating system. If you can pause any file systems on the disk long enough to take a snapshot, your snapshot should be complete. Nevertheless, if you cannot pause all file writes to the disk, you should unmount the disk from within the Lightsail instance, issue the create disk snapshot command, and then remount the disk to ensure a consistent and complete snapshot. You may remount and use your disk while the snapshot status is pending.</p> <p>You can also use this operation to create a snapshot of an instance's system volume. You might want to do this, for example, to recover data from the system volume of a botched instance or to create a backup of the system volume like you would for a block storage disk. To create a snapshot of a system volume, just define the <code>instance name</code> parameter when issuing the snapshot command, and a snapshot of the defined instance's system volume will be created. After the snapshot is available, you can create a block storage disk from the snapshot and attach it to a running instance to access the data on the disk.</p> <p>The <code>create disk snapshot</code> operation supports tag-based access control via request tags. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "CreateDistribution": "<p>Creates an Amazon Lightsail content delivery network (CDN) distribution.</p> <p>A distribution is a globally distributed network of caching servers that improve the performance of your website or web application hosted on a Lightsail instance. For more information, see <a href=\"https://lightsail.aws.amazon.com/ls/docs/en_us/articles/amazon-lightsail-content-delivery-networks\">Content delivery networks in Amazon Lightsail</a>.</p>", "CreateDomain": "<p>Creates a domain resource for the specified domain (e.g., example.com).</p> <p>The <code>create domain</code> operation supports tag-based access control via request tags. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "CreateDomainEntry": "<p>Creates one of the following entry records associated with the domain: Address (A), canonical name (CNAME), mail exchanger (MX), name server (NS), start of authority (SOA), service locator (SRV), or text (TXT).</p> <p>The <code>create domain entry</code> operation supports tag-based access control via resource tags applied to the resource identified by <code>domain name</code>. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "CreateInstanceSnapshot": "<p>Creates a snapshot of a specific virtual private server, or <i>instance</i>. You can use a snapshot to create a new instance that is based on that snapshot.</p> <p>The <code>create instance snapshot</code> operation supports tag-based access control via request tags. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "CreateInstances": "<p>Creates one or more Amazon Lightsail instances.</p> <p>The <code>create instances</code> operation supports tag-based access control via request tags. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "CreateInstancesFromSnapshot": "<p>Creates one or more new instances from a manual or automatic snapshot of an instance.</p> <p>The <code>create instances from snapshot</code> operation supports tag-based access control via request tags and resource tags applied to the resource identified by <code>instance snapshot name</code>. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "CreateKeyPair": "<p>Creates an SSH key pair.</p> <p>The <code>create key pair</code> operation supports tag-based access control via request tags. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "CreateLoadBalancer": "<p>Creates a Lightsail load balancer. To learn more about deciding whether to load balance your application, see <a href=\"https://lightsail.aws.amazon.com/ls/docs/how-to/article/configure-lightsail-instances-for-load-balancing\">Configure your Lightsail instances for load balancing</a>. You can create up to 5 load balancers per AWS Region in your account.</p> <p>When you create a load balancer, you can specify a unique name and port settings. To change additional load balancer settings, use the <code>UpdateLoadBalancerAttribute</code> operation.</p> <p>The <code>create load balancer</code> operation supports tag-based access control via request tags. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "CreateLoadBalancerTlsCertificate": "<p>Creates a Lightsail load balancer TLS certificate.</p> <p>TLS is just an updated, more secure version of Secure Socket Layer (SSL).</p> <p>The <code>CreateLoadBalancerTlsCertificate</code> operation supports tag-based access control via resource tags applied to the resource identified by <code>load balancer name</code>. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "CreateRelationalDatabase": "<p>Creates a new database in Amazon Lightsail.</p> <p>The <code>create relational database</code> operation supports tag-based access control via request tags. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "CreateRelationalDatabaseFromSnapshot": "<p>Creates a new database from an existing database snapshot in Amazon Lightsail.</p> <p>You can create a new database from a snapshot in if something goes wrong with your original database, or to change it to a different plan, such as a high availability or standard plan.</p> <p>The <code>create relational database from snapshot</code> operation supports tag-based access control via request tags and resource tags applied to the resource identified by relationalDatabaseSnapshotName. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "CreateRelationalDatabaseSnapshot": "<p>Creates a snapshot of your database in Amazon Lightsail. You can use snapshots for backups, to make copies of a database, and to save data before deleting a database.</p> <p>The <code>create relational database snapshot</code> operation supports tag-based access control via request tags. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "DeleteAlarm": "<p>Deletes an alarm.</p> <p>An alarm is used to monitor a single metric for one of your resources. When a metric condition is met, the alarm can notify you by email, SMS text message, and a banner displayed on the Amazon Lightsail console. For more information, see <a href=\"https://lightsail.aws.amazon.com/ls/docs/en_us/articles/amazon-lightsail-alarms\">Alarms in Amazon Lightsail</a>.</p>", "DeleteAutoSnapshot": "<p>Deletes an automatic snapshot of an instance or disk. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en_us/articles/amazon-lightsail-configuring-automatic-snapshots\">Lightsail Dev Guide</a>.</p>", "DeleteCertificate": "<p>Deletes an SSL/TLS certificate for your Amazon Lightsail content delivery network (CDN) distribution.</p> <p>Certificates that are currently attached to a distribution cannot be deleted. Use the <code>DetachCertificateFromDistribution</code> action to detach a certificate from a distribution.</p>", "DeleteContactMethod": "<p>Deletes a contact method.</p> <p>A contact method is used to send you notifications about your Amazon Lightsail resources. You can add one email address and one mobile phone number contact method in each AWS Region. However, SMS text messaging is not supported in some AWS Regions, and SMS text messages cannot be sent to some countries/regions. For more information, see <a href=\"https://lightsail.aws.amazon.com/ls/docs/en_us/articles/amazon-lightsail-notifications\">Notifications in Amazon Lightsail</a>.</p>", "DeleteDisk": "<p>Deletes the specified block storage disk. The disk must be in the <code>available</code> state (not attached to a Lightsail instance).</p> <note> <p>The disk may remain in the <code>deleting</code> state for several minutes.</p> </note> <p>The <code>delete disk</code> operation supports tag-based access control via resource tags applied to the resource identified by <code>disk name</code>. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "DeleteDiskSnapshot": "<p>Deletes the specified disk snapshot.</p> <p>When you make periodic snapshots of a disk, the snapshots are incremental, and only the blocks on the device that have changed since your last snapshot are saved in the new snapshot. When you delete a snapshot, only the data not needed for any other snapshot is removed. So regardless of which prior snapshots have been deleted, all active snapshots will have access to all the information needed to restore the disk.</p> <p>The <code>delete disk snapshot</code> operation supports tag-based access control via resource tags applied to the resource identified by <code>disk snapshot name</code>. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "DeleteDistribution": "<p>Deletes your Amazon Lightsail content delivery network (CDN) distribution.</p>", "DeleteDomain": "<p>Deletes the specified domain recordset and all of its domain records.</p> <p>The <code>delete domain</code> operation supports tag-based access control via resource tags applied to the resource identified by <code>domain name</code>. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "DeleteDomainEntry": "<p>Deletes a specific domain entry.</p> <p>The <code>delete domain entry</code> operation supports tag-based access control via resource tags applied to the resource identified by <code>domain name</code>. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "DeleteInstance": "<p>Deletes an Amazon Lightsail instance.</p> <p>The <code>delete instance</code> operation supports tag-based access control via resource tags applied to the resource identified by <code>instance name</code>. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "DeleteInstanceSnapshot": "<p>Deletes a specific snapshot of a virtual private server (or <i>instance</i>).</p> <p>The <code>delete instance snapshot</code> operation supports tag-based access control via resource tags applied to the resource identified by <code>instance snapshot name</code>. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "DeleteKeyPair": "<p>Deletes a specific SSH key pair.</p> <p>The <code>delete key pair</code> operation supports tag-based access control via resource tags applied to the resource identified by <code>key pair name</code>. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "DeleteKnownHostKeys": "<p>Deletes the known host key or certificate used by the Amazon Lightsail browser-based SSH or RDP clients to authenticate an instance. This operation enables the Lightsail browser-based SSH or RDP clients to connect to the instance after a host key mismatch.</p> <important> <p>Perform this operation only if you were expecting the host key or certificate mismatch or if you are familiar with the new host key or certificate on the instance. For more information, see <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-troubleshooting-browser-based-ssh-rdp-client-connection\">Troubleshooting connection issues when using the Amazon Lightsail browser-based SSH or RDP client</a>.</p> </important>", "DeleteLoadBalancer": "<p>Deletes a Lightsail load balancer and all its associated SSL/TLS certificates. Once the load balancer is deleted, you will need to create a new load balancer, create a new certificate, and verify domain ownership again.</p> <p>The <code>delete load balancer</code> operation supports tag-based access control via resource tags applied to the resource identified by <code>load balancer name</code>. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "DeleteLoadBalancerTlsCertificate": "<p>Deletes an SSL/TLS certificate associated with a Lightsail load balancer.</p> <p>The <code>DeleteLoadBalancerTlsCertificate</code> operation supports tag-based access control via resource tags applied to the resource identified by <code>load balancer name</code>. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "DeleteRelationalDatabase": "<p>Deletes a database in Amazon Lightsail.</p> <p>The <code>delete relational database</code> operation supports tag-based access control via resource tags applied to the resource identified by relationalDatabaseName. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "DeleteRelationalDatabaseSnapshot": "<p>Deletes a database snapshot in Amazon Lightsail.</p> <p>The <code>delete relational database snapshot</code> operation supports tag-based access control via resource tags applied to the resource identified by relationalDatabaseName. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "DetachCertificateFromDistribution": "<p>Detaches an SSL/TLS certificate from your Amazon Lightsail content delivery network (CDN) distribution.</p> <p>After the certificate is detached, your distribution stops accepting traffic for all of the domains that are associated with the certificate.</p>", "DetachDisk": "<p>Detaches a stopped block storage disk from a Lightsail instance. Make sure to unmount any file systems on the device within your operating system before stopping the instance and detaching the disk.</p> <p>The <code>detach disk</code> operation supports tag-based access control via resource tags applied to the resource identified by <code>disk name</code>. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "DetachInstancesFromLoadBalancer": "<p>Detaches the specified instances from a Lightsail load balancer.</p> <p>This operation waits until the instances are no longer needed before they are detached from the load balancer.</p> <p>The <code>detach instances from load balancer</code> operation supports tag-based access control via resource tags applied to the resource identified by <code>load balancer name</code>. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "DetachStaticIp": "<p>Detaches a static IP from the Amazon Lightsail instance to which it is attached.</p>", "DisableAddOn": "<p>Disables an add-on for an Amazon Lightsail resource. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en_us/articles/amazon-lightsail-configuring-automatic-snapshots\">Lightsail Dev Guide</a>.</p>", "DownloadDefaultKeyPair": "<p>Downloads the default SSH key pair from the user's account.</p>", "EnableAddOn": "<p>Enables or modifies an add-on for an Amazon Lightsail resource. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en_us/articles/amazon-lightsail-configuring-automatic-snapshots\">Lightsail Dev Guide</a>.</p>", "ExportSnapshot": "<p>Exports an Amazon Lightsail instance or block storage disk snapshot to Amazon Elastic Compute Cloud (Amazon EC2). This operation results in an export snapshot record that can be used with the <code>create cloud formation stack</code> operation to create new Amazon EC2 instances.</p> <p>Exported instance snapshots appear in Amazon EC2 as Amazon Machine Images (AMIs), and the instance system disk appears as an Amazon Elastic Block Store (Amazon EBS) volume. Exported disk snapshots appear in Amazon EC2 as Amazon EBS volumes. Snapshots are exported to the same Amazon Web Services Region in Amazon EC2 as the source Lightsail snapshot.</p> <p/> <p>The <code>export snapshot</code> operation supports tag-based access control via resource tags applied to the resource identified by <code>source snapshot name</code>. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p> <note> <p>Use the <code>get instance snapshots</code> or <code>get disk snapshots</code> operations to get a list of snapshots that you can export to Amazon EC2.</p> </note>", "GetActiveNames": "<p>Returns the names of all active (not deleted) resources.</p>", "GetAlarms": "<p>Returns information about the configured alarms. Specify an alarm name in your request to return information about a specific alarm, or specify a monitored resource name to return information about all alarms for a specific resource.</p> <p>An alarm is used to monitor a single metric for one of your resources. When a metric condition is met, the alarm can notify you by email, SMS text message, and a banner displayed on the Amazon Lightsail console. For more information, see <a href=\"https://lightsail.aws.amazon.com/ls/docs/en_us/articles/amazon-lightsail-alarms\">Alarms in Amazon Lightsail</a>.</p>", "GetAutoSnapshots": "<p>Returns the available automatic snapshots for an instance or disk. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en_us/articles/amazon-lightsail-configuring-automatic-snapshots\">Lightsail Dev Guide</a>.</p>", "GetBlueprints": "<p>Returns the list of available instance images, or <i>blueprints</i>. You can use a blueprint to create a new instance already running a specific operating system, as well as a preinstalled app or development stack. The software each instance is running depends on the blueprint image you choose.</p> <note> <p>Use active blueprints when creating new instances. Inactive blueprints are listed to support customers with existing instances and are not necessarily available to create new instances. Blueprints are marked inactive when they become outdated due to operating system updates or new application releases.</p> </note>", "GetBundles": "<p>Returns the list of bundles that are available for purchase. A bundle describes the specs for your virtual private server (or <i>instance</i>).</p>", "GetCertificates": "<p>Returns information about one or more Amazon Lightsail SSL/TLS certificates.</p> <note> <p>To get a summary of a certificate, ommit <code>includeCertificateDetails</code> from your request. The response will include only the certificate Amazon Resource Name (ARN), certificate name, domain name, and tags.</p> </note>", "GetCloudFormationStackRecords": "<p>Returns the CloudFormation stack record created as a result of the <code>create cloud formation stack</code> operation.</p> <p>An AWS CloudFormation stack is used to create a new Amazon EC2 instance from an exported Lightsail snapshot.</p>", "GetContactMethods": "<p>Returns information about the configured contact methods. Specify a protocol in your request to return information about a specific contact method.</p> <p>A contact method is used to send you notifications about your Amazon Lightsail resources. You can add one email address and one mobile phone number contact method in each AWS Region. However, SMS text messaging is not supported in some AWS Regions, and SMS text messages cannot be sent to some countries/regions. For more information, see <a href=\"https://lightsail.aws.amazon.com/ls/docs/en_us/articles/amazon-lightsail-notifications\">Notifications in Amazon Lightsail</a>.</p>", "GetDisk": "<p>Returns information about a specific block storage disk.</p>", "GetDiskSnapshot": "<p>Returns information about a specific block storage disk snapshot.</p>", "GetDiskSnapshots": "<p>Returns information about all block storage disk snapshots in your AWS account and region.</p>", "GetDisks": "<p>Returns information about all block storage disks in your AWS account and region.</p>", "GetDistributionBundles": "<p>Returns the list bundles that can be applied to you Amazon Lightsail content delivery network (CDN) distributions.</p> <p>A distribution bundle specifies the monthly network transfer quota and monthly cost of your dsitribution.</p>", "GetDistributionLatestCacheReset": "<p>Returns the timestamp and status of the last cache reset of a specific Amazon Lightsail content delivery network (CDN) distribution.</p>", "GetDistributionMetricData": "<p>Returns the data points of a specific metric for an Amazon Lightsail content delivery network (CDN) distribution.</p> <p>Metrics report the utilization of your resources, and the error counts generated by them. Monitor and collect metric data regularly to maintain the reliability, availability, and performance of your resources.</p>", "GetDistributions": "<p>Returns information about one or more of your Amazon Lightsail content delivery network (CDN) distributions.</p>", "GetDomain": "<p>Returns information about a specific domain recordset.</p>", "GetDomains": "<p>Returns a list of all domains in the user's account.</p>", "GetExportSnapshotRecords": "<p>Returns the export snapshot record created as a result of the <code>export snapshot</code> operation.</p> <p>An export snapshot record can be used to create a new Amazon EC2 instance and its related resources with the <code>create cloud formation stack</code> operation.</p>", "GetInstance": "<p>Returns information about a specific Amazon Lightsail instance, which is a virtual private server.</p>", "GetInstanceAccessDetails": "<p>Returns temporary SSH keys you can use to connect to a specific virtual private server, or <i>instance</i>.</p> <p>The <code>get instance access details</code> operation supports tag-based access control via resource tags applied to the resource identified by <code>instance name</code>. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "GetInstanceMetricData": "<p>Returns the data points for the specified Amazon Lightsail instance metric, given an instance name.</p> <p>Metrics report the utilization of your resources, and the error counts generated by them. Monitor and collect metric data regularly to maintain the reliability, availability, and performance of your resources.</p>", "GetInstancePortStates": "<p>Returns the firewall port states for a specific Amazon Lightsail instance, the IP addresses allowed to connect to the instance through the ports, and the protocol.</p>", "GetInstanceSnapshot": "<p>Returns information about a specific instance snapshot.</p>", "GetInstanceSnapshots": "<p>Returns all instance snapshots for the user's account.</p>", "GetInstanceState": "<p>Returns the state of a specific instance. Works on one instance at a time.</p>", "GetInstances": "<p>Returns information about all Amazon Lightsail virtual private servers, or <i>instances</i>.</p>", "GetKeyPair": "<p>Returns information about a specific key pair.</p>", "GetKeyPairs": "<p>Returns information about all key pairs in the user's account.</p>", "GetLoadBalancer": "<p>Returns information about the specified Lightsail load balancer.</p>", "GetLoadBalancerMetricData": "<p>Returns information about health metrics for your Lightsail load balancer.</p> <p>Metrics report the utilization of your resources, and the error counts generated by them. Monitor and collect metric data regularly to maintain the reliability, availability, and performance of your resources.</p>", "GetLoadBalancerTlsCertificates": "<p>Returns information about the TLS certificates that are associated with the specified Lightsail load balancer.</p> <p>TLS is just an updated, more secure version of Secure Socket Layer (SSL).</p> <p>You can have a maximum of 2 certificates associated with a Lightsail load balancer. One is active and the other is inactive.</p>", "GetLoadBalancers": "<p>Returns information about all load balancers in an account.</p>", "GetOperation": "<p>Returns information about a specific operation. Operations include events such as when you create an instance, allocate a static IP, attach a static IP, and so on.</p>", "GetOperations": "<p>Returns information about all operations.</p> <p>Results are returned from oldest to newest, up to a maximum of 200. Results can be paged by making each subsequent call to <code>GetOperations</code> use the maximum (last) <code>statusChangedAt</code> value from the previous request.</p>", "GetOperationsForResource": "<p>Gets operations for a specific resource (e.g., an instance or a static IP).</p>", "GetRegions": "<p>Returns a list of all valid regions for Amazon Lightsail. Use the <code>include availability zones</code> parameter to also return the Availability Zones in a region.</p>", "GetRelationalDatabase": "<p>Returns information about a specific database in Amazon Lightsail.</p>", "GetRelationalDatabaseBlueprints": "<p>Returns a list of available database blueprints in Amazon Lightsail. A blueprint describes the major engine version of a database.</p> <p>You can use a blueprint ID to create a new database that runs a specific database engine.</p>", "GetRelationalDatabaseBundles": "<p>Returns the list of bundles that are available in Amazon Lightsail. A bundle describes the performance specifications for a database.</p> <p>You can use a bundle ID to create a new database with explicit performance specifications.</p>", "GetRelationalDatabaseEvents": "<p>Returns a list of events for a specific database in Amazon Lightsail.</p>", "GetRelationalDatabaseLogEvents": "<p>Returns a list of log events for a database in Amazon Lightsail.</p>", "GetRelationalDatabaseLogStreams": "<p>Returns a list of available log streams for a specific database in Amazon Lightsail.</p>", "GetRelationalDatabaseMasterUserPassword": "<p>Returns the current, previous, or pending versions of the master user password for a Lightsail database.</p> <p>The <code>GetRelationalDatabaseMasterUserPassword</code> operation supports tag-based access control via resource tags applied to the resource identified by relationalDatabaseName.</p>", "GetRelationalDatabaseMetricData": "<p>Returns the data points of the specified metric for a database in Amazon Lightsail.</p> <p>Metrics report the utilization of your resources, and the error counts generated by them. Monitor and collect metric data regularly to maintain the reliability, availability, and performance of your resources.</p>", "GetRelationalDatabaseParameters": "<p>Returns all of the runtime parameters offered by the underlying database software, or engine, for a specific database in Amazon Lightsail.</p> <p>In addition to the parameter names and values, this operation returns other information about each parameter. This information includes whether changes require a reboot, whether the parameter is modifiable, the allowed values, and the data types.</p>", "GetRelationalDatabaseSnapshot": "<p>Returns information about a specific database snapshot in Amazon Lightsail.</p>", "GetRelationalDatabaseSnapshots": "<p>Returns information about all of your database snapshots in Amazon Lightsail.</p>", "GetRelationalDatabases": "<p>Returns information about all of your databases in Amazon Lightsail.</p>", "GetStaticIp": "<p>Returns information about a specific static IP.</p>", "GetStaticIps": "<p>Returns information about all static IPs in the user's account.</p>", "ImportKeyPair": "<p>Imports a public SSH key from a specific key pair.</p>", "IsVpcPeered": "<p>Returns a Boolean value indicating whether your Lightsail VPC is peered.</p>", "OpenInstancePublicPorts": "<p>Opens ports for a specific Amazon Lightsail instance, and specifies the IP addresses allowed to connect to the instance through the ports, and the protocol.</p> <p>The <code>OpenInstancePublicPorts</code> action supports tag-based access control via resource tags applied to the resource identified by <code>instanceName</code>. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "PeerVpc": "<p>Tries to peer the Lightsail VPC with the user's default VPC.</p>", "PutAlarm": "<p>Creates or updates an alarm, and associates it with the specified metric.</p> <p>An alarm is used to monitor a single metric for one of your resources. When a metric condition is met, the alarm can notify you by email, SMS text message, and a banner displayed on the Amazon Lightsail console. For more information, see <a href=\"https://lightsail.aws.amazon.com/ls/docs/en_us/articles/amazon-lightsail-alarms\">Alarms in Amazon Lightsail</a>.</p> <p>When this action creates an alarm, the alarm state is immediately set to <code>INSUFFICIENT_DATA</code>. The alarm is then evaluated and its state is set appropriately. Any actions associated with the new state are then executed.</p> <p>When you update an existing alarm, its state is left unchanged, but the update completely overwrites the previous configuration of the alarm. The alarm is then evaluated with the updated configuration.</p>", "PutInstancePublicPorts": "<p>Opens ports for a specific Amazon Lightsail instance, and specifies the IP addresses allowed to connect to the instance through the ports, and the protocol. This action also closes all currently open ports that are not included in the request. Include all of the ports and the protocols you want to open in your <code>PutInstancePublicPorts</code>request. Or use the <code>OpenInstancePublicPorts</code> action to open ports without closing currently open ports.</p> <p>The <code>PutInstancePublicPorts</code> action supports tag-based access control via resource tags applied to the resource identified by <code>instanceName</code>. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "RebootInstance": "<p>Restarts a specific instance.</p> <p>The <code>reboot instance</code> operation supports tag-based access control via resource tags applied to the resource identified by <code>instance name</code>. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "RebootRelationalDatabase": "<p>Restarts a specific database in Amazon Lightsail.</p> <p>The <code>reboot relational database</code> operation supports tag-based access control via resource tags applied to the resource identified by relationalDatabaseName. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "ReleaseStaticIp": "<p>Deletes a specific static IP from your account.</p>", "ResetDistributionCache": "<p>Deletes currently cached content from your Amazon Lightsail content delivery network (CDN) distribution.</p> <p>After resetting the cache, the next time a content request is made, your distribution pulls, serves, and caches it from the origin.</p>", "SendContactMethodVerification": "<p>Sends a verification request to an email contact method to ensure it's owned by the requester. SMS contact methods don't need to be verified.</p> <p>A contact method is used to send you notifications about your Amazon Lightsail resources. You can add one email address and one mobile phone number contact method in each AWS Region. However, SMS text messaging is not supported in some AWS Regions, and SMS text messages cannot be sent to some countries/regions. For more information, see <a href=\"https://lightsail.aws.amazon.com/ls/docs/en_us/articles/amazon-lightsail-notifications\">Notifications in Amazon Lightsail</a>.</p> <p>A verification request is sent to the contact method when you initially create it. Use this action to send another verification request if a previous verification request was deleted, or has expired.</p> <important> <p>Notifications are not sent to an email contact method until after it is verified, and confirmed as valid.</p> </important>", "StartInstance": "<p>Starts a specific Amazon Lightsail instance from a stopped state. To restart an instance, use the <code>reboot instance</code> operation.</p> <note> <p>When you start a stopped instance, Lightsail assigns a new public IP address to the instance. To use the same IP address after stopping and starting an instance, create a static IP address and attach it to the instance. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/lightsail-create-static-ip\">Lightsail Dev Guide</a>.</p> </note> <p>The <code>start instance</code> operation supports tag-based access control via resource tags applied to the resource identified by <code>instance name</code>. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "StartRelationalDatabase": "<p>Starts a specific database from a stopped state in Amazon Lightsail. To restart a database, use the <code>reboot relational database</code> operation.</p> <p>The <code>start relational database</code> operation supports tag-based access control via resource tags applied to the resource identified by relationalDatabaseName. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "StopInstance": "<p>Stops a specific Amazon Lightsail instance that is currently running.</p> <note> <p>When you start a stopped instance, Lightsail assigns a new public IP address to the instance. To use the same IP address after stopping and starting an instance, create a static IP address and attach it to the instance. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/lightsail-create-static-ip\">Lightsail Dev Guide</a>.</p> </note> <p>The <code>stop instance</code> operation supports tag-based access control via resource tags applied to the resource identified by <code>instance name</code>. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "StopRelationalDatabase": "<p>Stops a specific database that is currently running in Amazon Lightsail.</p> <p>The <code>stop relational database</code> operation supports tag-based access control via resource tags applied to the resource identified by relationalDatabaseName. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "TagResource": "<p>Adds one or more tags to the specified Amazon Lightsail resource. Each resource can have a maximum of 50 tags. Each tag consists of a key and an optional value. Tag keys must be unique per resource. For more information about tags, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-tags\">Lightsail Dev Guide</a>.</p> <p>The <code>tag resource</code> operation supports tag-based access control via request tags and resource tags applied to the resource identified by <code>resource name</code>. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "TestAlarm": "<p>Tests an alarm by displaying a banner on the Amazon Lightsail console. If a notification trigger is configured for the specified alarm, the test also sends a notification to the notification protocol (<code>Email</code> and/or <code>SMS</code>) configured for the alarm.</p> <p>An alarm is used to monitor a single metric for one of your resources. When a metric condition is met, the alarm can notify you by email, SMS text message, and a banner displayed on the Amazon Lightsail console. For more information, see <a href=\"https://lightsail.aws.amazon.com/ls/docs/en_us/articles/amazon-lightsail-alarms\">Alarms in Amazon Lightsail</a>.</p>", "UnpeerVpc": "<p>Attempts to unpeer the Lightsail VPC from the user's default VPC.</p>", "UntagResource": "<p>Deletes the specified set of tag keys and their values from the specified Amazon Lightsail resource.</p> <p>The <code>untag resource</code> operation supports tag-based access control via request tags and resource tags applied to the resource identified by <code>resource name</code>. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "UpdateDistribution": "<p>Updates an existing Amazon Lightsail content delivery network (CDN) distribution.</p> <p>Use this action to update the configuration of your existing distribution</p>", "UpdateDistributionBundle": "<p>Updates the bundle of your Amazon Lightsail content delivery network (CDN) distribution.</p> <p>A distribution bundle specifies the monthly network transfer quota and monthly cost of your dsitribution.</p> <p>Update your distribution's bundle if your distribution is going over its monthly network transfer quota and is incurring an overage fee.</p> <p>You can update your distribution's bundle only one time within your monthly AWS billing cycle. To determine if you can update your distribution's bundle, use the <code>GetDistributions</code> action. The <code>ableToUpdateBundle</code> parameter in the result will indicate whether you can currently update your distribution's bundle.</p>", "UpdateDomainEntry": "<p>Updates a domain recordset after it is created.</p> <p>The <code>update domain entry</code> operation supports tag-based access control via resource tags applied to the resource identified by <code>domain name</code>. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "UpdateLoadBalancerAttribute": "<p>Updates the specified attribute for a load balancer. You can only update one attribute at a time.</p> <p>The <code>update load balancer attribute</code> operation supports tag-based access control via resource tags applied to the resource identified by <code>load balancer name</code>. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "UpdateRelationalDatabase": "<p>Allows the update of one or more attributes of a database in Amazon Lightsail.</p> <p>Updates are applied immediately, or in cases where the updates could result in an outage, are applied during the database's predefined maintenance window.</p> <p>The <code>update relational database</code> operation supports tag-based access control via resource tags applied to the resource identified by relationalDatabaseName. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>", "UpdateRelationalDatabaseParameters": "<p>Allows the update of one or more parameters of a database in Amazon Lightsail.</p> <p>Parameter updates don't cause outages; therefore, their application is not subject to the preferred maintenance window. However, there are two ways in which parameter updates are applied: <code>dynamic</code> or <code>pending-reboot</code>. Parameters marked with a <code>dynamic</code> apply type are applied immediately. Parameters marked with a <code>pending-reboot</code> apply type are applied only after the database is rebooted using the <code>reboot relational database</code> operation.</p> <p>The <code>update relational database parameters</code> operation supports tag-based access control via resource tags applied to the resource identified by relationalDatabaseName. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-controlling-access-using-tags\">Lightsail Dev Guide</a>.</p>"}, "shapes": {"AccessDeniedException": {"base": "<p>Lightsail throws this exception when the user cannot be authenticated or uses invalid credentials to access a resource.</p>", "refs": {}}, "AccessDirection": {"base": null, "refs": {"InstancePortInfo$accessDirection": "<p>The access direction (<code>inbound</code> or <code>outbound</code>).</p> <note> <p>Lightsail currently supports only <code>inbound</code> access direction.</p> </note>"}}, "AccountSetupInProgressException": {"base": "<p>Lightsail throws this exception when an account is still in the setup in progress state.</p>", "refs": {}}, "AddOn": {"base": "<p>Describes an add-on that is enabled for an Amazon Lightsail resource.</p>", "refs": {"AddOnList$member": null}}, "AddOnList": {"base": null, "refs": {"Disk$addOns": "<p>An array of objects representing the add-ons enabled on the disk.</p>", "Instance$addOns": "<p>An array of objects representing the add-ons enabled on the instance.</p>"}}, "AddOnRequest": {"base": "<p>Describes a request to enable, modify, or disable an add-on for an Amazon Lightsail resource.</p> <note> <p>An additional cost may be associated with enabling add-ons. For more information, see the <a href=\"https://aws.amazon.com/lightsail/pricing/\">Lightsail pricing page</a>.</p> </note>", "refs": {"AddOnRequestList$member": null, "EnableAddOnRequest$addOnRequest": "<p>An array of strings representing the add-on to enable or modify.</p>"}}, "AddOnRequestList": {"base": null, "refs": {"CreateDiskFromSnapshotRequest$addOns": "<p>An array of objects that represent the add-ons to enable for the new disk.</p>", "CreateDiskRequest$addOns": "<p>An array of objects that represent the add-ons to enable for the new disk.</p>", "CreateInstancesFromSnapshotRequest$addOns": "<p>An array of objects representing the add-ons to enable for the new instance.</p>", "CreateInstancesRequest$addOns": "<p>An array of objects representing the add-ons to enable for the new instance.</p>"}}, "AddOnType": {"base": null, "refs": {"AddOnRequest$addOnType": "<p>The add-on type.</p>", "DisableAddOnRequest$addOnType": "<p>The add-on type to disable.</p>"}}, "Alarm": {"base": "<p>Describes an alarm.</p> <p>An alarm is a way to monitor your Amazon Lightsail resource metrics. For more information, see <a href=\"https://lightsail.aws.amazon.com/ls/docs/en_us/articles/amazon-lightsail-alarms\">Alarms in Amazon Lightsail</a>.</p>", "refs": {"AlarmsList$member": null}}, "AlarmState": {"base": null, "refs": {"Alarm$state": "<p>The current state of the alarm.</p> <p>An alarm has the following possible states:</p> <ul> <li> <p> <code>ALARM</code> - The metric is outside of the defined threshold.</p> </li> <li> <p> <code>INSUFFICIENT_DATA</code> - The alarm has just started, the metric is not available, or not enough data is available for the metric to determine the alarm state.</p> </li> <li> <p> <code>OK</code> - The metric is within the defined threshold.</p> </li> </ul>", "NotificationTriggerList$member": null, "TestAlarmRequest$state": "<p>The alarm state to test.</p> <p>An alarm has the following possible states that can be tested:</p> <ul> <li> <p> <code>ALARM</code> - The metric is outside of the defined threshold.</p> </li> <li> <p> <code>INSUFFICIENT_DATA</code> - The alarm has just started, the metric is not available, or not enough data is available for the metric to determine the alarm state.</p> </li> <li> <p> <code>OK</code> - The metric is within the defined threshold.</p> </li> </ul>"}}, "AlarmsList": {"base": null, "refs": {"GetAlarmsResult$alarms": "<p>An array of objects that describe the alarms.</p>"}}, "AllocateStaticIpRequest": {"base": null, "refs": {}}, "AllocateStaticIpResult": {"base": null, "refs": {}}, "AttachCertificateToDistributionRequest": {"base": null, "refs": {}}, "AttachCertificateToDistributionResult": {"base": null, "refs": {}}, "AttachDiskRequest": {"base": null, "refs": {}}, "AttachDiskResult": {"base": null, "refs": {}}, "AttachInstancesToLoadBalancerRequest": {"base": null, "refs": {}}, "AttachInstancesToLoadBalancerResult": {"base": null, "refs": {}}, "AttachLoadBalancerTlsCertificateRequest": {"base": null, "refs": {}}, "AttachLoadBalancerTlsCertificateResult": {"base": null, "refs": {}}, "AttachStaticIpRequest": {"base": null, "refs": {}}, "AttachStaticIpResult": {"base": null, "refs": {}}, "AttachedDisk": {"base": "<p>Describes a block storage disk that is attached to an instance, and is included in an automatic snapshot.</p>", "refs": {"AttachedDiskList$member": null}}, "AttachedDiskList": {"base": null, "refs": {"AutoSnapshotDetails$fromAttachedDisks": "<p>An array of objects that describe the block storage disks attached to the instance when the automatic snapshot was created.</p>"}}, "AttachedDiskMap": {"base": null, "refs": {"CreateInstancesFromSnapshotRequest$attachedDiskMapping": "<p>An object containing information about one or more disk mappings.</p>"}}, "AutoSnapshotAddOnRequest": {"base": "<p>Describes a request to enable or modify the automatic snapshot add-on for an Amazon Lightsail instance or disk.</p> <p>When you modify the automatic snapshot time for a resource, it is typically effective immediately except under the following conditions:</p> <ul> <li> <p>If an automatic snapshot has been created for the current day, and you change the snapshot time to a later time of day, then the new snapshot time will be effective the following day. This ensures that two snapshots are not created for the current day.</p> </li> <li> <p>If an automatic snapshot has not yet been created for the current day, and you change the snapshot time to an earlier time of day, then the new snapshot time will be effective the following day and a snapshot is automatically created at the previously set time for the current day. This ensures that a snapshot is created for the current day.</p> </li> <li> <p>If an automatic snapshot has not yet been created for the current day, and you change the snapshot time to a time that is within 30 minutes from your current time, then the new snapshot time will be effective the following day and a snapshot is automatically created at the previously set time for the current day. This ensures that a snapshot is created for the current day, because 30 minutes is required between your current time and the new snapshot time that you specify.</p> </li> <li> <p>If an automatic snapshot is scheduled to be created within 30 minutes from your current time and you change the snapshot time, then the new snapshot time will be effective the following day and a snapshot is automatically created at the previously set time for the current day. This ensures that a snapshot is created for the current day, because 30 minutes is required between your current time and the new snapshot time that you specify.</p> </li> </ul>", "refs": {"AddOnRequest$autoSnapshotAddOnRequest": "<p>An object that represents additional parameters when enabling or modifying the automatic snapshot add-on.</p>"}}, "AutoSnapshotDate": {"base": null, "refs": {"DeleteAutoSnapshotRequest$date": "<p>The date of the automatic snapshot to delete in <code>YYYY-MM-DD</code> format. Use the <code>get auto snapshots</code> operation to get the available automatic snapshots for a resource.</p>"}}, "AutoSnapshotDetails": {"base": "<p>Describes an automatic snapshot.</p>", "refs": {"AutoSnapshotDetailsList$member": null}}, "AutoSnapshotDetailsList": {"base": null, "refs": {"GetAutoSnapshotsResult$autoSnapshots": "<p>An array of objects that describe the automatic snapshots that are available for the specified source instance or disk.</p>"}}, "AutoSnapshotStatus": {"base": null, "refs": {"AutoSnapshotDetails$status": "<p>The status of the automatic snapshot.</p>"}}, "AvailabilityZone": {"base": "<p>Describes an Availability Zone.</p>", "refs": {"AvailabilityZoneList$member": null}}, "AvailabilityZoneList": {"base": null, "refs": {"Region$availabilityZones": "<p>The Availability Zones. Follows the format <code>us-east-2a</code> (case-sensitive).</p>", "Region$relationalDatabaseAvailabilityZones": "<p>The Availability Zones for databases. Follows the format <code>us-east-2a</code> (case-sensitive).</p>"}}, "Base64": {"base": null, "refs": {"CreateKeyPairResult$publicKeyBase64": "<p>A base64-encoded public key of the <code>ssh-rsa</code> type.</p>", "CreateKeyPairResult$privateKeyBase64": "<p>A base64-encoded RSA private key.</p>", "DownloadDefaultKeyPairResult$publicKeyBase64": "<p>A base64-encoded public key of the <code>ssh-rsa</code> type.</p>", "DownloadDefaultKeyPairResult$privateKeyBase64": "<p>A base64-encoded RSA private key.</p>", "ImportKeyPairRequest$publicKeyBase64": "<p>A base64-encoded public key of the <code>ssh-rsa</code> type.</p>", "KeyPair$fingerprint": "<p>The RSA fingerprint of the key pair.</p>"}}, "BehaviorEnum": {"base": null, "refs": {"CacheBehavior$behavior": "<p>The cache behavior of the distribution.</p> <p>The following cache behaviors can be specified:</p> <ul> <li> <p> <b> <code>cache</code> </b> - This option is best for static sites. When specified, your distribution caches and serves your entire website as static content. This behavior is ideal for websites with static content that doesn't change depending on who views it, or for websites that don't use cookies, headers, or query strings to personalize content.</p> </li> <li> <p> <b> <code>dont-cache</code> </b> - This option is best for sites that serve a mix of static and dynamic content. When specified, your distribution caches and serve only the content that is specified in the distribution's <code>CacheBehaviorPerPath</code> parameter. This behavior is ideal for websites or web applications that use cookies, headers, and query strings to personalize content for individual users.</p> </li> </ul>", "CacheBehaviorPerPath$behavior": "<p>The cache behavior for the specified path.</p> <p>You can specify one of the following per-path cache behaviors:</p> <ul> <li> <p> <b> <code>cache</code> </b> - This behavior caches the specified path. </p> </li> <li> <p> <b> <code>dont-cache</code> </b> - This behavior doesn't cache the specified path. </p> </li> </ul>"}}, "Blueprint": {"base": "<p>Describes a blueprint (a virtual private server image).</p>", "refs": {"BlueprintList$member": null}}, "BlueprintList": {"base": null, "refs": {"GetBlueprintsResult$blueprints": "<p>An array of key-value pairs that contains information about the available blueprints.</p>"}}, "BlueprintType": {"base": null, "refs": {"Blueprint$type": "<p>The type of the blueprint (e.g., <code>os</code> or <code>app</code>).</p>"}}, "Bundle": {"base": "<p>Describes a bundle, which is a set of specs describing your virtual private server (or <i>instance</i>).</p>", "refs": {"BundleList$member": null}}, "BundleList": {"base": null, "refs": {"GetBundlesResult$bundles": "<p>An array of key-value pairs that contains information about the available bundles.</p>"}}, "CacheBehavior": {"base": "<p>Describes the default cache behavior of an Amazon Lightsail content delivery network (CDN) distribution.</p>", "refs": {"CreateDistributionRequest$defaultCacheBehavior": "<p>An object that describes the default cache behavior for the distribution.</p>", "LightsailDistribution$defaultCacheBehavior": "<p>An object that describes the default cache behavior of the distribution.</p>", "UpdateDistributionRequest$defaultCacheBehavior": "<p>An object that describes the default cache behavior for the distribution.</p>"}}, "CacheBehaviorList": {"base": null, "refs": {"CreateDistributionRequest$cacheBehaviors": "<p>An array of objects that describe the per-path cache behavior for the distribution.</p>", "LightsailDistribution$cacheBehaviors": "<p>An array of objects that describe the per-path cache behavior of the distribution.</p>", "UpdateDistributionRequest$cacheBehaviors": "<p>An array of objects that describe the per-path cache behavior for the distribution.</p>"}}, "CacheBehaviorPerPath": {"base": "<p>Describes the per-path cache behavior of an Amazon Lightsail content delivery network (CDN) distribution.</p> <p>A per-path cache behavior is used to override, or add an exception to, the default cache behavior of a distribution. For example, if the <code>cacheBehavior</code> is set to <code>cache</code>, then a per-path cache behavior can be used to specify a directory, file, or file type that your distribution will cache. Alternately, if the distribution's <code>cacheBehavior</code> is <code>dont-cache</code>, then a per-path cache behavior can be used to specify a directory, file, or file type that your distribution will not cache.</p> <p>if the cacheBehavior's behavior is set to 'cache', then</p>", "refs": {"CacheBehaviorList$member": null}}, "CacheSettings": {"base": "<p>Describes the cache settings of an Amazon Lightsail content delivery network (CDN) distribution.</p> <p>These settings apply only to your distribution's <code>cacheBehaviors</code> (including the <code>defaultCacheBehavior</code>) that have a <code>behavior</code> of <code>cache</code>.</p>", "refs": {"CreateDistributionRequest$cacheBehaviorSettings": "<p>An object that describes the cache behavior settings for the distribution.</p>", "LightsailDistribution$cacheBehaviorSettings": "<p>An object that describes the cache behavior settings of the distribution.</p>", "UpdateDistributionRequest$cacheBehaviorSettings": "<p>An object that describes the cache behavior settings for the distribution.</p> <note> <p>The <code>cacheBehaviorSettings</code> specified in your <code>UpdateDistributionRequest</code> will replace your distribution's existing settings.</p> </note>"}}, "Certificate": {"base": "<p>Describes the full details of an Amazon Lightsail SSL/TLS certificate.</p> <note> <p>To get a summary of a certificate, use the <code>GetCertificates</code> action and ommit <code>includeCertificateDetails</code> from your request. The response will include only the certificate Amazon Resource Name (ARN), certificate name, domain name, and tags.</p> </note>", "refs": {"CertificateSummary$certificateDetail": "<p>An object that describes a certificate in detail.</p>"}}, "CertificateName": {"base": null, "refs": {"Certificate$name": "<p>The name of the certificate (e.g., <code>my-certificate</code>).</p>", "CertificateSummary$certificateName": "<p>The name of the certificate.</p>", "CreateCertificateRequest$certificateName": "<p>The name for the certificate.</p>", "DeleteCertificateRequest$certificateName": "<p>The name of the certificate to delete.</p> <p>Use the <code>GetCertificates</code> action to get a list of certificate names that you can specify.</p>", "GetCertificatesRequest$certificateName": "<p>The name for the certificate for which to return information.</p> <p>When omitted, the response includes all of your certificates in the AWS region where the request is made.</p>"}}, "CertificateStatus": {"base": null, "refs": {"Certificate$status": "<p>The validation status of the certificate.</p>", "CertificateStatusList$member": null}}, "CertificateStatusList": {"base": null, "refs": {"GetCertificatesRequest$certificateStatuses": "<p>The status of the certificates for which to return information.</p> <p>For example, specify <code>ISSUED</code> to return only certificates with an <code>ISSUED</code> status.</p> <p>When omitted, the response includes all of your certificates in the AWS region where the request is made, regardless of their current status.</p>"}}, "CertificateSummary": {"base": "<p>Describes an Amazon Lightsail SSL/TLS certificate.</p>", "refs": {"CertificateSummaryList$member": null, "CreateCertificateResult$certificate": "<p>An object that describes the certificate created.</p>"}}, "CertificateSummaryList": {"base": null, "refs": {"GetCertificatesResult$certificates": "<p>An object that describes certificates.</p>"}}, "CloseInstancePublicPortsRequest": {"base": null, "refs": {}}, "CloseInstancePublicPortsResult": {"base": null, "refs": {}}, "CloudFormationStackRecord": {"base": "<p>Describes a CloudFormation stack record created as a result of the <code>create cloud formation stack</code> operation.</p> <p>A CloudFormation stack record provides information about the AWS CloudFormation stack used to create a new Amazon Elastic Compute Cloud instance from an exported Lightsail instance snapshot.</p>", "refs": {"CloudFormationStackRecordList$member": null}}, "CloudFormationStackRecordList": {"base": null, "refs": {"GetCloudFormationStackRecordsResult$cloudFormationStackRecords": "<p>A list of objects describing the CloudFormation stack records.</p>"}}, "CloudFormationStackRecordSourceInfo": {"base": "<p>Describes the source of a CloudFormation stack record (i.e., the export snapshot record).</p>", "refs": {"CloudFormationStackRecordSourceInfoList$member": null}}, "CloudFormationStackRecordSourceInfoList": {"base": null, "refs": {"CloudFormationStackRecord$sourceInfo": "<p>A list of objects describing the source of the CloudFormation stack record.</p>"}}, "CloudFormationStackRecordSourceType": {"base": null, "refs": {"CloudFormationStackRecordSourceInfo$resourceType": "<p>The Lightsail resource type (e.g., <code>ExportSnapshotRecord</code>).</p>"}}, "ComparisonOperator": {"base": null, "refs": {"Alarm$comparisonOperator": "<p>The arithmetic operation used when comparing the specified statistic and threshold.</p>", "PutAlarmRequest$comparisonOperator": "<p>The arithmetic operation to use when comparing the specified statistic to the threshold. The specified statistic value is used as the first operand.</p>"}}, "ContactMethod": {"base": "<p>Describes a contact method.</p> <p>A contact method is a way to send you notifications. For more information, see <a href=\"https://lightsail.aws.amazon.com/ls/docs/en_us/articles/amazon-lightsail-notifications\">Notifications in Amazon Lightsail</a>.</p>", "refs": {"ContactMethodsList$member": null}}, "ContactMethodStatus": {"base": null, "refs": {"ContactMethod$status": "<p>The current status of the contact method.</p> <p>A contact method has the following possible status:</p> <ul> <li> <p> <code>PendingVerification</code> - The contact method has not yet been verified, and the verification has not yet expired.</p> </li> <li> <p> <code>Valid</code> - The contact method has been verified.</p> </li> <li> <p> <code>InValid</code> - An attempt was made to verify the contact method, but the verification has expired.</p> </li> </ul>"}}, "ContactMethodVerificationProtocol": {"base": null, "refs": {"SendContactMethodVerificationRequest$protocol": "<p>The protocol to verify, such as <code>Email</code> or <code>SMS</code> (text messaging).</p>"}}, "ContactMethodsList": {"base": null, "refs": {"GetContactMethodsResult$contactMethods": "<p>An array of objects that describe the contact methods.</p>"}}, "ContactProtocol": {"base": null, "refs": {"ContactMethod$protocol": "<p>The protocol of the contact method, such as email or SMS (text messaging).</p>", "ContactProtocolsList$member": null, "CreateContactMethodRequest$protocol": "<p>The protocol of the contact method, such as <code>Email</code> or <code>SMS</code> (text messaging).</p> <p>The <code>SMS</code> protocol is supported only in the following AWS Regions.</p> <ul> <li> <p>US East (N. Virginia) (<code>us-east-1</code>)</p> </li> <li> <p>US West (Oregon) (<code>us-west-2</code>)</p> </li> <li> <p>Europe (Ireland) (<code>eu-west-1</code>)</p> </li> <li> <p>Asia Pacific (Tokyo) (<code>ap-northeast-1</code>)</p> </li> <li> <p>Asia Pacific (Singapore) (<code>ap-southeast-1</code>)</p> </li> <li> <p>Asia Pacific (Sydney) (<code>ap-southeast-2</code>)</p> </li> </ul> <p>For a list of countries/regions where SMS text messages can be sent, and the latest AWS Regions where SMS text messaging is supported, see <a href=\"https://docs.aws.amazon.com/sns/latest/dg/sns-supported-regions-countries.html\">Supported Regions and Countries</a> in the <i>Amazon SNS Developer Guide</i>.</p> <p>For more information about notifications in Amazon Lightsail, see <a href=\"https://lightsail.aws.amazon.com/ls/docs/en_us/articles/amazon-lightsail-notifications\">Notifications in Amazon Lightsail</a>.</p>", "DeleteContactMethodRequest$protocol": "<p>The protocol that will be deleted, such as <code>Email</code> or <code>SMS</code> (text messaging).</p> <note> <p>To delete an <code>Email</code> and an <code>SMS</code> contact method if you added both, you must run separate <code>DeleteContactMethod</code> actions to delete each protocol.</p> </note>"}}, "ContactProtocolsList": {"base": null, "refs": {"Alarm$contactProtocols": "<p>The contact protocols for the alarm, such as <code>Email</code>, <code>SMS</code> (text messaging), or both.</p>", "GetContactMethodsRequest$protocols": "<p>The protocols used to send notifications, such as <code>Email</code>, or <code>SMS</code> (text messaging).</p> <p>Specify a protocol in your request to return information about a specific contact method protocol.</p>", "PutAlarmRequest$contactProtocols": "<p>The contact protocols to use for the alarm, such as <code>Email</code>, <code>SMS</code> (text messaging), or both.</p> <p>A notification is sent via the specified contact protocol if notifications are enabled for the alarm, and when the alarm is triggered.</p> <p>A notification is not sent if a contact protocol is not specified, if the specified contact protocol is not configured in the AWS Region, or if notifications are not enabled for the alarm using the <code>notificationEnabled</code> paramater.</p> <p>Use the <code>CreateContactMethod</code> action to configure a contact protocol in an AWS Region.</p>"}}, "CookieObject": {"base": "<p>Describes whether an Amazon Lightsail content delivery network (CDN) distribution forwards cookies to the origin and, if so, which ones.</p> <p>For the cookies that you specify, your distribution caches separate versions of the specified content based on the cookie values in viewer requests.</p>", "refs": {"CacheSettings$forwardedCookies": "<p>An object that describes the cookies that are forwarded to the origin. Your content is cached based on the cookies that are forwarded.</p>"}}, "CopySnapshotRequest": {"base": null, "refs": {}}, "CopySnapshotResult": {"base": null, "refs": {}}, "CreateCertificateRequest": {"base": null, "refs": {}}, "CreateCertificateResult": {"base": null, "refs": {}}, "CreateCloudFormationStackRequest": {"base": null, "refs": {}}, "CreateCloudFormationStackResult": {"base": null, "refs": {}}, "CreateContactMethodRequest": {"base": null, "refs": {}}, "CreateContactMethodResult": {"base": null, "refs": {}}, "CreateDiskFromSnapshotRequest": {"base": null, "refs": {}}, "CreateDiskFromSnapshotResult": {"base": null, "refs": {}}, "CreateDiskRequest": {"base": null, "refs": {}}, "CreateDiskResult": {"base": null, "refs": {}}, "CreateDiskSnapshotRequest": {"base": null, "refs": {}}, "CreateDiskSnapshotResult": {"base": null, "refs": {}}, "CreateDistributionRequest": {"base": null, "refs": {}}, "CreateDistributionResult": {"base": null, "refs": {}}, "CreateDomainEntryRequest": {"base": null, "refs": {}}, "CreateDomainEntryResult": {"base": null, "refs": {}}, "CreateDomainRequest": {"base": null, "refs": {}}, "CreateDomainResult": {"base": null, "refs": {}}, "CreateInstanceSnapshotRequest": {"base": null, "refs": {}}, "CreateInstanceSnapshotResult": {"base": null, "refs": {}}, "CreateInstancesFromSnapshotRequest": {"base": null, "refs": {}}, "CreateInstancesFromSnapshotResult": {"base": null, "refs": {}}, "CreateInstancesRequest": {"base": null, "refs": {}}, "CreateInstancesResult": {"base": null, "refs": {}}, "CreateKeyPairRequest": {"base": null, "refs": {}}, "CreateKeyPairResult": {"base": null, "refs": {}}, "CreateLoadBalancerRequest": {"base": null, "refs": {}}, "CreateLoadBalancerResult": {"base": null, "refs": {}}, "CreateLoadBalancerTlsCertificateRequest": {"base": null, "refs": {}}, "CreateLoadBalancerTlsCertificateResult": {"base": null, "refs": {}}, "CreateRelationalDatabaseFromSnapshotRequest": {"base": null, "refs": {}}, "CreateRelationalDatabaseFromSnapshotResult": {"base": null, "refs": {}}, "CreateRelationalDatabaseRequest": {"base": null, "refs": {}}, "CreateRelationalDatabaseResult": {"base": null, "refs": {}}, "CreateRelationalDatabaseSnapshotRequest": {"base": null, "refs": {}}, "CreateRelationalDatabaseSnapshotResult": {"base": null, "refs": {}}, "DeleteAlarmRequest": {"base": null, "refs": {}}, "DeleteAlarmResult": {"base": null, "refs": {}}, "DeleteAutoSnapshotRequest": {"base": null, "refs": {}}, "DeleteAutoSnapshotResult": {"base": null, "refs": {}}, "DeleteCertificateRequest": {"base": null, "refs": {}}, "DeleteCertificateResult": {"base": null, "refs": {}}, "DeleteContactMethodRequest": {"base": null, "refs": {}}, "DeleteContactMethodResult": {"base": null, "refs": {}}, "DeleteDiskRequest": {"base": null, "refs": {}}, "DeleteDiskResult": {"base": null, "refs": {}}, "DeleteDiskSnapshotRequest": {"base": null, "refs": {}}, "DeleteDiskSnapshotResult": {"base": null, "refs": {}}, "DeleteDistributionRequest": {"base": null, "refs": {}}, "DeleteDistributionResult": {"base": null, "refs": {}}, "DeleteDomainEntryRequest": {"base": null, "refs": {}}, "DeleteDomainEntryResult": {"base": null, "refs": {}}, "DeleteDomainRequest": {"base": null, "refs": {}}, "DeleteDomainResult": {"base": null, "refs": {}}, "DeleteInstanceRequest": {"base": null, "refs": {}}, "DeleteInstanceResult": {"base": null, "refs": {}}, "DeleteInstanceSnapshotRequest": {"base": null, "refs": {}}, "DeleteInstanceSnapshotResult": {"base": null, "refs": {}}, "DeleteKeyPairRequest": {"base": null, "refs": {}}, "DeleteKeyPairResult": {"base": null, "refs": {}}, "DeleteKnownHostKeysRequest": {"base": null, "refs": {}}, "DeleteKnownHostKeysResult": {"base": null, "refs": {}}, "DeleteLoadBalancerRequest": {"base": null, "refs": {}}, "DeleteLoadBalancerResult": {"base": null, "refs": {}}, "DeleteLoadBalancerTlsCertificateRequest": {"base": null, "refs": {}}, "DeleteLoadBalancerTlsCertificateResult": {"base": null, "refs": {}}, "DeleteRelationalDatabaseRequest": {"base": null, "refs": {}}, "DeleteRelationalDatabaseResult": {"base": null, "refs": {}}, "DeleteRelationalDatabaseSnapshotRequest": {"base": null, "refs": {}}, "DeleteRelationalDatabaseSnapshotResult": {"base": null, "refs": {}}, "DestinationInfo": {"base": "<p>Describes the destination of a record.</p>", "refs": {"CloudFormationStackRecord$destinationInfo": "<p>A list of objects describing the destination service, which is AWS CloudFormation, and the Amazon Resource Name (ARN) of the AWS CloudFormation stack.</p>", "ExportSnapshotRecord$destinationInfo": "<p>A list of objects describing the destination of the export snapshot record.</p>"}}, "DetachCertificateFromDistributionRequest": {"base": null, "refs": {}}, "DetachCertificateFromDistributionResult": {"base": null, "refs": {}}, "DetachDiskRequest": {"base": null, "refs": {}}, "DetachDiskResult": {"base": null, "refs": {}}, "DetachInstancesFromLoadBalancerRequest": {"base": null, "refs": {}}, "DetachInstancesFromLoadBalancerResult": {"base": null, "refs": {}}, "DetachStaticIpRequest": {"base": null, "refs": {}}, "DetachStaticIpResult": {"base": null, "refs": {}}, "DisableAddOnRequest": {"base": null, "refs": {}}, "DisableAddOnResult": {"base": null, "refs": {}}, "Disk": {"base": "<p>Describes a system disk or a block storage disk.</p>", "refs": {"DiskList$member": null, "GetDiskResult$disk": "<p>An object containing information about the disk.</p>"}}, "DiskInfo": {"base": "<p>Describes a disk.</p>", "refs": {"DiskInfoList$member": null}}, "DiskInfoList": {"base": null, "refs": {"InstanceSnapshotInfo$fromDiskInfo": "<p>A list of objects describing the disks that were attached to the source instance.</p>"}}, "DiskList": {"base": null, "refs": {"GetDisksResult$disks": "<p>An array of objects containing information about all block storage disks.</p>", "InstanceHardware$disks": "<p>The disks attached to the instance.</p>", "InstanceSnapshot$fromAttachedDisks": "<p>An array of disk objects containing information about all block storage disks.</p>"}}, "DiskMap": {"base": "<p>Describes a block storage disk mapping.</p>", "refs": {"DiskMapList$member": null}}, "DiskMapList": {"base": null, "refs": {"AttachedDiskMap$value": null}}, "DiskSnapshot": {"base": "<p>Describes a block storage disk snapshot.</p>", "refs": {"DiskSnapshotList$member": null, "GetDiskSnapshotResult$diskSnapshot": "<p>An object containing information about the disk snapshot.</p>"}}, "DiskSnapshotInfo": {"base": "<p>Describes a disk snapshot.</p>", "refs": {"ExportSnapshotRecordSourceInfo$diskSnapshotInfo": "<p>A list of objects describing a disk snapshot.</p>"}}, "DiskSnapshotList": {"base": null, "refs": {"GetDiskSnapshotsResult$diskSnapshots": "<p>An array of objects containing information about all block storage disk snapshots.</p>"}}, "DiskSnapshotState": {"base": null, "refs": {"DiskSnapshot$state": "<p>The status of the disk snapshot operation.</p>"}}, "DiskState": {"base": null, "refs": {"Disk$state": "<p>Describes the status of the disk.</p>"}}, "DistributionBundle": {"base": "<p>Describes the specifications of a distribution bundle.</p>", "refs": {"DistributionBundleList$member": null}}, "DistributionBundleList": {"base": null, "refs": {"GetDistributionBundlesResult$bundles": "<p>An object that describes a distribution bundle.</p>"}}, "DistributionList": {"base": null, "refs": {"GetDistributionsResult$distributions": "<p>An array of objects that describe your distributions.</p>"}}, "DistributionMetricName": {"base": null, "refs": {"GetDistributionMetricDataRequest$metricName": "<p>The metric for which you want to return information.</p> <p>Valid distribution metric names are listed below, along with the most useful <code>statistics</code> to include in your request, and the published <code>unit</code> value.</p> <ul> <li> <p> <b> <code>Requests</code> </b> - The total number of viewer requests received by your Lightsail distribution, for all HTTP methods, and for both HTTP and HTTPS requests.</p> <p> <code>Statistics</code>: The most useful statistic is <code>Sum</code>.</p> <p> <code>Unit</code>: The published unit is <code>None</code>.</p> </li> <li> <p> <b> <code>BytesDownloaded</code> </b> - The number of bytes downloaded by viewers for GET, HEAD, and OPTIONS requests.</p> <p> <code>Statistics</code>: The most useful statistic is <code>Sum</code>.</p> <p> <code>Unit</code>: The published unit is <code>None</code>.</p> </li> <li> <p> <b> <code>BytesUploaded </code> </b> - The number of bytes uploaded to your origin by your Lightsail distribution, using POST and PUT requests.</p> <p> <code>Statistics</code>: The most useful statistic is <code>Sum</code>.</p> <p> <code>Unit</code>: The published unit is <code>None</code>.</p> </li> <li> <p> <b> <code>TotalErrorRate</code> </b> - The percentage of all viewer requests for which the response's HTTP status code was 4xx or 5xx.</p> <p> <code>Statistics</code>: The most useful statistic is <code>Average</code>.</p> <p> <code>Unit</code>: The published unit is <code>Percent</code>.</p> </li> <li> <p> <b> <code>4xxErrorRate</code> </b> - The percentage of all viewer requests for which the response's HTTP status cod was 4xx. In these cases, the client or client viewer may have made an error. For example, a status code of 404 (Not Found) means that the client requested an object that could not be found.</p> <p> <code>Statistics</code>: The most useful statistic is <code>Average</code>.</p> <p> <code>Unit</code>: The published unit is <code>Percent</code>.</p> </li> <li> <p> <b> <code>5xxErrorRate</code> </b> - The percentage of all viewer requests for which the response's HTTP status code was 5xx. In these cases, the origin server did not satisfy the requests. For example, a status code of 503 (Service Unavailable) means that the origin server is currently unavailable.</p> <p> <code>Statistics</code>: The most useful statistic is <code>Average</code>.</p> <p> <code>Unit</code>: The published unit is <code>Percent</code>.</p> </li> </ul>", "GetDistributionMetricDataResult$metricName": "<p>The name of the metric returned.</p>"}}, "Domain": {"base": "<p>Describes a domain where you are storing recordsets in Lightsail.</p>", "refs": {"DomainList$member": null, "GetDomainResult$domain": "<p>An array of key-value pairs containing information about your get domain request.</p>"}}, "DomainEntry": {"base": "<p>Describes a domain recordset entry.</p>", "refs": {"CreateDomainEntryRequest$domainEntry": "<p>An array of key-value pairs containing information about the domain entry request.</p>", "DeleteDomainEntryRequest$domainEntry": "<p>An array of key-value pairs containing information about your domain entries.</p>", "DomainEntryList$member": null, "UpdateDomainEntryRequest$domainEntry": "<p>An array of key-value pairs containing information about the domain entry.</p>"}}, "DomainEntryList": {"base": null, "refs": {"Domain$domainEntries": "<p>An array of key-value pairs containing information about the domain entries.</p>"}}, "DomainEntryOptions": {"base": null, "refs": {"DomainEntry$options": "<p>(Deprecated) The options for the domain entry.</p> <note> <p>In releases prior to November 29, 2017, this parameter was not included in the API response. It is now deprecated.</p> </note>"}}, "DomainEntryOptionsKeys": {"base": null, "refs": {"DomainEntryOptions$key": null}}, "DomainEntryType": {"base": null, "refs": {"DomainEntry$type": "<p>The type of domain entry, such as address (A), canonical name (CNAME), mail exchanger (MX), name server (NS), start of authority (SOA), service locator (SRV), or text (TXT).</p> <p>The following domain entry types can be used:</p> <ul> <li> <p> <code>A</code> </p> </li> <li> <p> <code>CNAME</code> </p> </li> <li> <p> <code>MX</code> </p> </li> <li> <p> <code>NS</code> </p> </li> <li> <p> <code>SOA</code> </p> </li> <li> <p> <code>SRV</code> </p> </li> <li> <p> <code>TXT</code> </p> </li> </ul>"}}, "DomainList": {"base": null, "refs": {"GetDomainsResult$domains": "<p>An array of key-value pairs containing information about each of the domain entries in the user's account.</p>"}}, "DomainName": {"base": null, "refs": {"Certificate$domainName": "<p>The domain name of the certificate.</p>", "CertificateSummary$domainName": "<p>The domain name of the certificate.</p>", "CreateCertificateRequest$domainName": "<p>The domain name (e.g., <code>example.com</code>) for the certificate.</p>", "CreateDomainEntryRequest$domainName": "<p>The domain name (e.g., <code>example.com</code>) for which you want to create the domain entry.</p>", "CreateDomainRequest$domainName": "<p>The domain name to manage (e.g., <code>example.com</code>).</p> <note> <p>You cannot register a new domain name using Lightsail. You must register a domain name using Amazon Route 53 or another domain name registrar. If you have already registered your domain, you can enter its name in this parameter to manage the DNS records for that domain.</p> </note>", "CreateLoadBalancerRequest$certificateDomainName": "<p>The domain name with which your certificate is associated (e.g., <code>example.com</code>).</p> <p>If you specify <code>certificateDomainName</code>, then <code>certificateName</code> is required (and vice-versa).</p>", "CreateLoadBalancerTlsCertificateRequest$certificateDomainName": "<p>The domain name (e.g., <code>example.com</code>) for your SSL/TLS certificate.</p>", "DeleteDomainEntryRequest$domainName": "<p>The name of the domain entry to delete.</p>", "DeleteDomainRequest$domainName": "<p>The specific domain name to delete.</p>", "DomainEntry$name": "<p>The name of the domain.</p>", "DomainNameList$member": null, "DomainValidationRecord$domainName": "<p>The domain name of the certificate validation record. For example, <code>example.com</code> or <code>www.example.com</code>.</p>", "GetDomainRequest$domainName": "<p>The domain name for which your want to return information about.</p>", "LoadBalancerTlsCertificate$domainName": "<p>The domain name for your SSL/TLS certificate.</p>", "LoadBalancerTlsCertificateDomainValidationOption$domainName": "<p>The fully qualified domain name in the certificate request.</p>", "LoadBalancerTlsCertificateDomainValidationRecord$domainName": "<p>The domain name against which your SSL/TLS certificate was validated.</p>", "SubjectAlternativeNameList$member": null, "UpdateDomainEntryRequest$domainName": "<p>The name of the domain recordset to update.</p>"}}, "DomainNameList": {"base": null, "refs": {"CreateLoadBalancerRequest$certificateAlternativeNames": "<p>The optional alternative domains and subdomains to use with your SSL/TLS certificate (e.g., <code>www.example.com</code>, <code>example.com</code>, <code>m.example.com</code>, <code>blog.example.com</code>).</p>", "CreateLoadBalancerTlsCertificateRequest$certificateAlternativeNames": "<p>An array of strings listing alternative domains and subdomains for your SSL/TLS certificate. Lightsail will de-dupe the names for you. You can have a maximum of 9 alternative names (in addition to the 1 primary domain). We do not support wildcards (e.g., <code>*.example.com</code>).</p>"}}, "DomainValidationRecord": {"base": "<p>Describes the domain validation records of an Amazon Lightsail SSL/TLS certificate.</p>", "refs": {"DomainValidationRecordList$member": null}}, "DomainValidationRecordList": {"base": null, "refs": {"Certificate$domainValidationRecords": "<p>An array of objects that describe the domain validation records of the certificate.</p>", "RenewalSummary$domainValidationRecords": "<p>An array of objects that describe the domain validation records of the certificate.</p>"}}, "DownloadDefaultKeyPairRequest": {"base": null, "refs": {}}, "DownloadDefaultKeyPairResult": {"base": null, "refs": {}}, "EligibleToRenew": {"base": null, "refs": {"Certificate$eligibleToRenew": "<p>The renewal eligibility of the certificate.</p>"}}, "EnableAddOnRequest": {"base": null, "refs": {}}, "EnableAddOnResult": {"base": null, "refs": {}}, "ExportSnapshotRecord": {"base": "<p>Describes an export snapshot record.</p>", "refs": {"ExportSnapshotRecordList$member": null}}, "ExportSnapshotRecordList": {"base": null, "refs": {"GetExportSnapshotRecordsResult$exportSnapshotRecords": "<p>A list of objects describing the export snapshot records.</p>"}}, "ExportSnapshotRecordSourceInfo": {"base": "<p>Describes the source of an export snapshot record.</p>", "refs": {"ExportSnapshotRecord$sourceInfo": "<p>A list of objects describing the source of the export snapshot record.</p>"}}, "ExportSnapshotRecordSourceType": {"base": null, "refs": {"ExportSnapshotRecordSourceInfo$resourceType": "<p>The Lightsail resource type (e.g., <code>InstanceSnapshot</code> or <code>DiskSnapshot</code>).</p>"}}, "ExportSnapshotRequest": {"base": null, "refs": {}}, "ExportSnapshotResult": {"base": null, "refs": {}}, "ForwardValues": {"base": null, "refs": {"CookieObject$option": "<p>Specifies which cookies to forward to the distribution's origin for a cache behavior: <code>all</code>, <code>none</code>, or <code>allow-list</code> to forward only the cookies specified in the <code>cookiesAllowList</code> parameter.</p>", "HeaderObject$option": "<p>The headers that you want your distribution to forward to your origin and base caching on.</p> <p>You can configure your distribution to do one of the following:</p> <ul> <li> <p> <b> <code>all</code> </b> - Forward all headers to your origin.</p> </li> <li> <p> <b> <code>none</code> </b> - Forward only the default headers.</p> </li> <li> <p> <b> <code>allow-list</code> </b> - Forward only the headers you specify using the <code>headersAllowList</code> parameter.</p> </li> </ul>"}}, "GetActiveNamesRequest": {"base": null, "refs": {}}, "GetActiveNamesResult": {"base": null, "refs": {}}, "GetAlarmsRequest": {"base": null, "refs": {}}, "GetAlarmsResult": {"base": null, "refs": {}}, "GetAutoSnapshotsRequest": {"base": null, "refs": {}}, "GetAutoSnapshotsResult": {"base": null, "refs": {}}, "GetBlueprintsRequest": {"base": null, "refs": {}}, "GetBlueprintsResult": {"base": null, "refs": {}}, "GetBundlesRequest": {"base": null, "refs": {}}, "GetBundlesResult": {"base": null, "refs": {}}, "GetCertificatesRequest": {"base": null, "refs": {}}, "GetCertificatesResult": {"base": null, "refs": {}}, "GetCloudFormationStackRecordsRequest": {"base": null, "refs": {}}, "GetCloudFormationStackRecordsResult": {"base": null, "refs": {}}, "GetContactMethodsRequest": {"base": null, "refs": {}}, "GetContactMethodsResult": {"base": null, "refs": {}}, "GetDiskRequest": {"base": null, "refs": {}}, "GetDiskResult": {"base": null, "refs": {}}, "GetDiskSnapshotRequest": {"base": null, "refs": {}}, "GetDiskSnapshotResult": {"base": null, "refs": {}}, "GetDiskSnapshotsRequest": {"base": null, "refs": {}}, "GetDiskSnapshotsResult": {"base": null, "refs": {}}, "GetDisksRequest": {"base": null, "refs": {}}, "GetDisksResult": {"base": null, "refs": {}}, "GetDistributionBundlesRequest": {"base": null, "refs": {}}, "GetDistributionBundlesResult": {"base": null, "refs": {}}, "GetDistributionLatestCacheResetRequest": {"base": null, "refs": {}}, "GetDistributionLatestCacheResetResult": {"base": null, "refs": {}}, "GetDistributionMetricDataRequest": {"base": null, "refs": {}}, "GetDistributionMetricDataResult": {"base": null, "refs": {}}, "GetDistributionsRequest": {"base": null, "refs": {}}, "GetDistributionsResult": {"base": null, "refs": {}}, "GetDomainRequest": {"base": null, "refs": {}}, "GetDomainResult": {"base": null, "refs": {}}, "GetDomainsRequest": {"base": null, "refs": {}}, "GetDomainsResult": {"base": null, "refs": {}}, "GetExportSnapshotRecordsRequest": {"base": null, "refs": {}}, "GetExportSnapshotRecordsResult": {"base": null, "refs": {}}, "GetInstanceAccessDetailsRequest": {"base": null, "refs": {}}, "GetInstanceAccessDetailsResult": {"base": null, "refs": {}}, "GetInstanceMetricDataRequest": {"base": null, "refs": {}}, "GetInstanceMetricDataResult": {"base": null, "refs": {}}, "GetInstancePortStatesRequest": {"base": null, "refs": {}}, "GetInstancePortStatesResult": {"base": null, "refs": {}}, "GetInstanceRequest": {"base": null, "refs": {}}, "GetInstanceResult": {"base": null, "refs": {}}, "GetInstanceSnapshotRequest": {"base": null, "refs": {}}, "GetInstanceSnapshotResult": {"base": null, "refs": {}}, "GetInstanceSnapshotsRequest": {"base": null, "refs": {}}, "GetInstanceSnapshotsResult": {"base": null, "refs": {}}, "GetInstanceStateRequest": {"base": null, "refs": {}}, "GetInstanceStateResult": {"base": null, "refs": {}}, "GetInstancesRequest": {"base": null, "refs": {}}, "GetInstancesResult": {"base": null, "refs": {}}, "GetKeyPairRequest": {"base": null, "refs": {}}, "GetKeyPairResult": {"base": null, "refs": {}}, "GetKeyPairsRequest": {"base": null, "refs": {}}, "GetKeyPairsResult": {"base": null, "refs": {}}, "GetLoadBalancerMetricDataRequest": {"base": null, "refs": {}}, "GetLoadBalancerMetricDataResult": {"base": null, "refs": {}}, "GetLoadBalancerRequest": {"base": null, "refs": {}}, "GetLoadBalancerResult": {"base": null, "refs": {}}, "GetLoadBalancerTlsCertificatesRequest": {"base": null, "refs": {}}, "GetLoadBalancerTlsCertificatesResult": {"base": null, "refs": {}}, "GetLoadBalancersRequest": {"base": null, "refs": {}}, "GetLoadBalancersResult": {"base": null, "refs": {}}, "GetOperationRequest": {"base": null, "refs": {}}, "GetOperationResult": {"base": null, "refs": {}}, "GetOperationsForResourceRequest": {"base": null, "refs": {}}, "GetOperationsForResourceResult": {"base": null, "refs": {}}, "GetOperationsRequest": {"base": null, "refs": {}}, "GetOperationsResult": {"base": null, "refs": {}}, "GetRegionsRequest": {"base": null, "refs": {}}, "GetRegionsResult": {"base": null, "refs": {}}, "GetRelationalDatabaseBlueprintsRequest": {"base": null, "refs": {}}, "GetRelationalDatabaseBlueprintsResult": {"base": null, "refs": {}}, "GetRelationalDatabaseBundlesRequest": {"base": null, "refs": {}}, "GetRelationalDatabaseBundlesResult": {"base": null, "refs": {}}, "GetRelationalDatabaseEventsRequest": {"base": null, "refs": {}}, "GetRelationalDatabaseEventsResult": {"base": null, "refs": {}}, "GetRelationalDatabaseLogEventsRequest": {"base": null, "refs": {}}, "GetRelationalDatabaseLogEventsResult": {"base": null, "refs": {}}, "GetRelationalDatabaseLogStreamsRequest": {"base": null, "refs": {}}, "GetRelationalDatabaseLogStreamsResult": {"base": null, "refs": {}}, "GetRelationalDatabaseMasterUserPasswordRequest": {"base": null, "refs": {}}, "GetRelationalDatabaseMasterUserPasswordResult": {"base": null, "refs": {}}, "GetRelationalDatabaseMetricDataRequest": {"base": null, "refs": {}}, "GetRelationalDatabaseMetricDataResult": {"base": null, "refs": {}}, "GetRelationalDatabaseParametersRequest": {"base": null, "refs": {}}, "GetRelationalDatabaseParametersResult": {"base": null, "refs": {}}, "GetRelationalDatabaseRequest": {"base": null, "refs": {}}, "GetRelationalDatabaseResult": {"base": null, "refs": {}}, "GetRelationalDatabaseSnapshotRequest": {"base": null, "refs": {}}, "GetRelationalDatabaseSnapshotResult": {"base": null, "refs": {}}, "GetRelationalDatabaseSnapshotsRequest": {"base": null, "refs": {}}, "GetRelationalDatabaseSnapshotsResult": {"base": null, "refs": {}}, "GetRelationalDatabasesRequest": {"base": null, "refs": {}}, "GetRelationalDatabasesResult": {"base": null, "refs": {}}, "GetStaticIpRequest": {"base": null, "refs": {}}, "GetStaticIpResult": {"base": null, "refs": {}}, "GetStaticIpsRequest": {"base": null, "refs": {}}, "GetStaticIpsResult": {"base": null, "refs": {}}, "HeaderEnum": {"base": null, "refs": {"HeaderForwardList$member": null}}, "HeaderForwardList": {"base": null, "refs": {"HeaderObject$headersAllowList": "<p>The specific headers to forward to your distribution's origin.</p>"}}, "HeaderObject": {"base": "<p>Describes the request headers that a Lightsail distribution bases caching on.</p> <p>For the headers that you specify, your distribution caches separate versions of the specified content based on the header values in viewer requests. For example, suppose viewer requests for <code>logo.jpg</code> contain a custom <code>product</code> header that has a value of either <code>acme</code> or <code>apex</code>, and you configure your distribution to cache your content based on values in the <code>product</code> header. Your distribution forwards the <code>product</code> header to the origin and caches the response from the origin once for each header value. </p>", "refs": {"CacheSettings$forwardedHeaders": "<p>An object that describes the headers that are forwarded to the origin. Your content is cached based on the headers that are forwarded.</p>"}}, "HostKeyAttributes": {"base": "<p>Describes the public SSH host keys or the RDP certificate.</p>", "refs": {"HostKeysList$member": null}}, "HostKeysList": {"base": null, "refs": {"InstanceAccessDetails$hostKeys": "<p>Describes the public SSH host keys or the RDP certificate.</p>"}}, "ImportKeyPairRequest": {"base": null, "refs": {}}, "ImportKeyPairResult": {"base": null, "refs": {}}, "InUseResourceCount": {"base": null, "refs": {"Certificate$inUseResourceCount": "<p>The number of Lightsail resources that the certificate is attached to.</p>"}}, "IncludeCertificateDetails": {"base": null, "refs": {"GetCertificatesRequest$includeCertificateDetails": "<p>Indicates whether to include detailed information about the certificates in the response.</p> <p>When omitted, the response includes only the certificate names, Amazon Resource Names (ARNs), domain names, and tags.</p>"}}, "InputOrigin": {"base": "<p>Describes the origin resource of an Amazon Lightsail content delivery network (CDN) distribution.</p> <p>An origin can be a Lightsail instance or load balancer. A distribution pulls content from an origin, caches it, and serves it to viewers via a worldwide network of edge servers.</p>", "refs": {"CreateDistributionRequest$origin": "<p>An object that describes the origin resource for the distribution, such as a Lightsail instance or load balancer.</p> <p>The distribution pulls, caches, and serves content from the origin.</p>", "UpdateDistributionRequest$origin": "<p>An object that describes the origin resource for the distribution, such as a Lightsail instance or load balancer.</p> <p>The distribution pulls, caches, and serves content from the origin.</p>"}}, "Instance": {"base": "<p>Describes an instance (a virtual private server).</p>", "refs": {"GetInstanceResult$instance": "<p>An array of key-value pairs containing information about the specified instance.</p>", "InstanceList$member": null}}, "InstanceAccessDetails": {"base": "<p>The parameters for gaining temporary access to one of your Amazon Lightsail instances.</p>", "refs": {"GetInstanceAccessDetailsResult$accessDetails": "<p>An array of key-value pairs containing information about a get instance access request.</p>"}}, "InstanceAccessProtocol": {"base": null, "refs": {"GetInstanceAccessDetailsRequest$protocol": "<p>The protocol to use to connect to your instance. Defaults to <code>ssh</code>.</p>", "InstanceAccessDetails$protocol": "<p>The protocol for these Amazon Lightsail instance access details.</p>"}}, "InstanceEntry": {"base": "<p>Describes the Amazon Elastic Compute Cloud instance and related resources to be created using the <code>create cloud formation stack</code> operation.</p>", "refs": {"InstanceEntryList$member": null}}, "InstanceEntryList": {"base": null, "refs": {"CreateCloudFormationStackRequest$instances": "<p>An array of parameters that will be used to create the new Amazon EC2 instance. You can only pass one instance entry at a time in this array. You will get an invalid parameter error if you pass more than one instance entry in this array.</p>"}}, "InstanceHardware": {"base": "<p>Describes the hardware for the instance.</p>", "refs": {"Instance$hardware": "<p>The size of the vCPU and the amount of RAM for the instance.</p>"}}, "InstanceHealthReason": {"base": null, "refs": {"InstanceHealthSummary$instanceHealthReason": "<p>More information about the instance health. If the <code>instanceHealth</code> is <code>healthy</code>, then an <code>instanceHealthReason</code> value is not provided.</p> <p>If <b> <code>instanceHealth</code> </b> is <code>initial</code>, the <b> <code>instanceHealthReason</code> </b> value can be one of the following:</p> <ul> <li> <p> <b> <code>Lb.RegistrationInProgress</code> </b> - The target instance is in the process of being registered with the load balancer.</p> </li> <li> <p> <b> <code>Lb.InitialHealthChecking</code> </b> - The Lightsail load balancer is still sending the target instance the minimum number of health checks required to determine its health status.</p> </li> </ul> <p>If <b> <code>instanceHealth</code> </b> is <code>unhealthy</code>, the <b> <code>instanceHealthReason</code> </b> value can be one of the following:</p> <ul> <li> <p> <b> <code>Instance.ResponseCodeMismatch</code> </b> - The health checks did not return an expected HTTP code.</p> </li> <li> <p> <b> <code>Instance.Timeout</code> </b> - The health check requests timed out.</p> </li> <li> <p> <b> <code>Instance.FailedHealthChecks</code> </b> - The health checks failed because the connection to the target instance timed out, the target instance response was malformed, or the target instance failed the health check for an unknown reason.</p> </li> <li> <p> <b> <code>Lb.InternalError</code> </b> - The health checks failed due to an internal error.</p> </li> </ul> <p>If <b> <code>instanceHealth</code> </b> is <code>unused</code>, the <b> <code>instanceHealthReason</code> </b> value can be one of the following:</p> <ul> <li> <p> <b> <code>Instance.NotRegistered</code> </b> - The target instance is not registered with the target group.</p> </li> <li> <p> <b> <code>Instance.NotInUse</code> </b> - The target group is not used by any load balancer, or the target instance is in an Availability Zone that is not enabled for its load balancer.</p> </li> <li> <p> <b> <code>Instance.IpUnusable</code> </b> - The target IP address is reserved for use by a Lightsail load balancer.</p> </li> <li> <p> <b> <code>Instance.InvalidState</code> </b> - The target is in the stopped or terminated state.</p> </li> </ul> <p>If <b> <code>instanceHealth</code> </b> is <code>draining</code>, the <b> <code>instanceHealthReason</code> </b> value can be one of the following:</p> <ul> <li> <p> <b> <code>Instance.DeregistrationInProgress</code> </b> - The target instance is in the process of being deregistered and the deregistration delay period has not expired.</p> </li> </ul>"}}, "InstanceHealthState": {"base": null, "refs": {"InstanceHealthSummary$instanceHealth": "<p>Describes the overall instance health. Valid values are below.</p>"}}, "InstanceHealthSummary": {"base": "<p>Describes information about the health of the instance.</p>", "refs": {"InstanceHealthSummaryList$member": null}}, "InstanceHealthSummaryList": {"base": null, "refs": {"LoadBalancer$instanceHealthSummary": "<p>An array of InstanceHealthSummary objects describing the health of the load balancer.</p>"}}, "InstanceList": {"base": null, "refs": {"GetInstancesResult$instances": "<p>An array of key-value pairs containing information about your instances.</p>"}}, "InstanceMetricName": {"base": null, "refs": {"GetInstanceMetricDataRequest$metricName": "<p>The metric for which you want to return information.</p> <p>Valid instance metric names are listed below, along with the most useful <code>statistics</code> to include in your request, and the published <code>unit</code> value.</p> <ul> <li> <p> <b> <code>BurstCapacityPercentage</code> </b> - The percentage of CPU performance available for your instance to burst above its baseline. Your instance continuously accrues and consumes burst capacity. Burst capacity stops accruing when your instance's <code>BurstCapacityPercentage</code> reaches 100%. For more information, see <a href=\"https://lightsail.aws.amazon.com/ls/docs/en_us/articles/amazon-lightsail-viewing-instance-burst-capacity\">Viewing instance burst capacity in Amazon Lightsail</a>.</p> <p> <code>Statistics</code>: The most useful statistics are <code>Maximum</code> and <code>Average</code>.</p> <p> <code>Unit</code>: The published unit is <code>Percent</code>.</p> </li> <li> <p> <b> <code>BurstCapacityTime</code> </b> - The available amount of time for your instance to burst at 100% CPU utilization. Your instance continuously accrues and consumes burst capacity. Burst capacity time stops accruing when your instance's <code>BurstCapacityPercentage</code> metric reaches 100%.</p> <p>Burst capacity time is consumed at the full rate only when your instance operates at 100% CPU utilization. For example, if your instance operates at 50% CPU utilization in the burstable zone for a 5-minute period, then it consumes CPU burst capacity minutes at a 50% rate in that period. Your instance consumed 2 minutes and 30 seconds of CPU burst capacity minutes in the 5-minute period. For more information, see <a href=\"https://lightsail.aws.amazon.com/ls/docs/en_us/articles/amazon-lightsail-viewing-instance-burst-capacity\">Viewing instance burst capacity in Amazon Lightsail</a>.</p> <p> <code>Statistics</code>: The most useful statistics are <code>Maximum</code> and <code>Average</code>.</p> <p> <code>Unit</code>: The published unit is <code>Seconds</code>.</p> </li> <li> <p> <b> <code>CPUUtilization</code> </b> - The percentage of allocated compute units that are currently in use on the instance. This metric identifies the processing power to run the applications on the instance. Tools in your operating system can show a lower percentage than Lightsail when the instance is not allocated a full processor core.</p> <p> <code>Statistics</code>: The most useful statistics are <code>Maximum</code> and <code>Average</code>.</p> <p> <code>Unit</code>: The published unit is <code>Percent</code>.</p> </li> <li> <p> <b> <code>NetworkIn</code> </b> - The number of bytes received on all network interfaces by the instance. This metric identifies the volume of incoming network traffic to the instance. The number reported is the number of bytes received during the period. Because this metric is reported in 5-minute intervals, divide the reported number by 300 to find Bytes/second.</p> <p> <code>Statistics</code>: The most useful statistic is <code>Sum</code>.</p> <p> <code>Unit</code>: The published unit is <code>Bytes</code>.</p> </li> <li> <p> <b> <code>NetworkOut</code> </b> - The number of bytes sent out on all network interfaces by the instance. This metric identifies the volume of outgoing network traffic from the instance. The number reported is the number of bytes sent during the period. Because this metric is reported in 5-minute intervals, divide the reported number by 300 to find Bytes/second.</p> <p> <code>Statistics</code>: The most useful statistic is <code>Sum</code>.</p> <p> <code>Unit</code>: The published unit is <code>Bytes</code>.</p> </li> <li> <p> <b> <code>StatusCheckFailed</code> </b> - Reports whether the instance passed or failed both the instance status check and the system status check. This metric can be either 0 (passed) or 1 (failed). This metric data is available in 1-minute (60 seconds) granularity.</p> <p> <code>Statistics</code>: The most useful statistic is <code>Sum</code>.</p> <p> <code>Unit</code>: The published unit is <code>Count</code>.</p> </li> <li> <p> <b> <code>StatusCheckFailed_Instance</code> </b> - Reports whether the instance passed or failed the instance status check. This metric can be either 0 (passed) or 1 (failed). This metric data is available in 1-minute (60 seconds) granularity.</p> <p> <code>Statistics</code>: The most useful statistic is <code>Sum</code>.</p> <p> <code>Unit</code>: The published unit is <code>Count</code>.</p> </li> <li> <p> <b> <code>StatusCheckFailed_System</code> </b> - Reports whether the instance passed or failed the system status check. This metric can be either 0 (passed) or 1 (failed). This metric data is available in 1-minute (60 seconds) granularity.</p> <p> <code>Statistics</code>: The most useful statistic is <code>Sum</code>.</p> <p> <code>Unit</code>: The published unit is <code>Count</code>.</p> </li> </ul>", "GetInstanceMetricDataResult$metricName": "<p>The name of the metric returned.</p>"}}, "InstanceNetworking": {"base": "<p>Describes monthly data transfer rates and port information for an instance.</p>", "refs": {"Instance$networking": "<p>Information about the public ports and monthly data transfer rates for the instance.</p>"}}, "InstancePlatform": {"base": null, "refs": {"Blueprint$platform": "<p>The operating system platform (either Linux/Unix-based or Windows Server-based) of the blueprint.</p>", "InstancePlatformList$member": null}}, "InstancePlatformList": {"base": null, "refs": {"Bundle$supportedPlatforms": "<p>The operating system platform (Linux/Unix-based or Windows Server-based) that the bundle supports. You can only launch a <code>WINDOWS</code> bundle on a blueprint that supports the <code>WINDOWS</code> platform. <code>LINUX_UNIX</code> blueprints require a <code>LINUX_UNIX</code> bundle.</p>"}}, "InstancePortInfo": {"base": "<p>Describes information about ports for an Amazon Lightsail instance.</p>", "refs": {"InstancePortInfoList$member": null}}, "InstancePortInfoList": {"base": null, "refs": {"InstanceNetworking$ports": "<p>An array of key-value pairs containing information about the ports on the instance.</p>"}}, "InstancePortState": {"base": "<p>Describes open ports on an instance, the IP addresses allowed to connect to the instance through the ports, and the protocol.</p>", "refs": {"InstancePortStateList$member": null}}, "InstancePortStateList": {"base": null, "refs": {"GetInstancePortStatesResult$portStates": "<p>An array of objects that describe the firewall port states for the specified instance.</p>"}}, "InstanceSnapshot": {"base": "<p>Describes an instance snapshot.</p>", "refs": {"GetInstanceSnapshotResult$instanceSnapshot": "<p>An array of key-value pairs containing information about the results of your get instance snapshot request.</p>", "InstanceSnapshotList$member": null}}, "InstanceSnapshotInfo": {"base": "<p>Describes an instance snapshot.</p>", "refs": {"ExportSnapshotRecordSourceInfo$instanceSnapshotInfo": "<p>A list of objects describing an instance snapshot.</p>"}}, "InstanceSnapshotList": {"base": null, "refs": {"GetInstanceSnapshotsResult$instanceSnapshots": "<p>An array of key-value pairs containing information about the results of your get instance snapshots request.</p>"}}, "InstanceSnapshotState": {"base": null, "refs": {"InstanceSnapshot$state": "<p>The state the snapshot is in.</p>"}}, "InstanceState": {"base": "<p>Describes the virtual private server (or <i>instance</i>) status.</p>", "refs": {"GetInstanceStateResult$state": "<p>The state of the instance.</p>", "Instance$state": "<p>The status code and the state (e.g., <code>running</code>) for the instance.</p>"}}, "InvalidInputException": {"base": "<p>Lightsail throws this exception when user input does not conform to the validation rules of an input field.</p> <note> <p>Domain-related APIs are only available in the N. Virginia (us-east-1) Region. Please set your AWS Region configuration to us-east-1 to create, view, or edit these resources.</p> </note>", "refs": {}}, "IpAddress": {"base": null, "refs": {"Instance$privateIpAddress": "<p>The private IP address of the instance.</p>", "Instance$publicIpAddress": "<p>The public IP address of the instance.</p>", "InstanceAccessDetails$ipAddress": "<p>The public IP address of the Amazon Lightsail instance.</p>", "StaticIp$ipAddress": "<p>The static IP address.</p>"}}, "IpV6Address": {"base": null, "refs": {"Instance$ipv6Address": "<p>The IPv6 address of the instance.</p>"}}, "IsVpcPeeredRequest": {"base": null, "refs": {}}, "IsVpcPeeredResult": {"base": null, "refs": {}}, "IsoDate": {"base": null, "refs": {"Alarm$createdAt": "<p>The timestamp when the alarm was created.</p>", "AutoSnapshotDetails$createdAt": "<p>The timestamp when the automatic snapshot was created.</p>", "Certificate$createdAt": "<p>The timestamp when the certificate was created.</p>", "Certificate$issuedAt": "<p>The timestamp when the certificate was issued.</p>", "Certificate$notBefore": "<p>The timestamp when the certificate is first valid.</p>", "Certificate$notAfter": "<p>The timestamp when the certificate expires.</p>", "Certificate$revokedAt": "<p>The timestamp when the certificate was revoked. This value is present only when the certificate status is <code>REVOKED</code>.</p>", "CloudFormationStackRecord$createdAt": "<p>The date when the CloudFormation stack record was created.</p>", "ContactMethod$createdAt": "<p>The timestamp when the contact method was created.</p>", "CreateRelationalDatabaseFromSnapshotRequest$restoreTime": "<p>The date and time to restore your database from.</p> <p>Constraints:</p> <ul> <li> <p>Must be before the latest restorable time for the database.</p> </li> <li> <p>Cannot be specified if the <code>use latest restorable time</code> parameter is <code>true</code>.</p> </li> <li> <p>Specified in Coordinated Universal Time (UTC).</p> </li> <li> <p>Specified in the Unix time format.</p> <p>For example, if you wish to use a restore time of October 1, 2018, at 8 PM UTC, then you input <code>1538424000</code> as the restore time.</p> </li> </ul>", "Disk$createdAt": "<p>The date when the disk was created.</p>", "DiskSnapshot$createdAt": "<p>The date when the disk snapshot was created.</p>", "Domain$createdAt": "<p>The date when the domain recordset was created.</p>", "ExportSnapshotRecord$createdAt": "<p>The date when the export snapshot record was created.</p>", "ExportSnapshotRecordSourceInfo$createdAt": "<p>The date when the source instance or disk snapshot was created.</p>", "GetDistributionLatestCacheResetResult$createTime": "<p>The timestamp of the last cache reset (e.g., <code>1479734909.17</code>) in Unix time format.</p>", "GetRelationalDatabaseLogEventsRequest$startTime": "<p>The start of the time interval from which to get log events.</p> <p>Constraints:</p> <ul> <li> <p>Specified in Coordinated Universal Time (UTC).</p> </li> <li> <p>Specified in the Unix time format.</p> <p>For example, if you wish to use a start time of October 1, 2018, at 8 PM UTC, then you input <code>1538424000</code> as the start time.</p> </li> </ul>", "GetRelationalDatabaseLogEventsRequest$endTime": "<p>The end of the time interval from which to get log events.</p> <p>Constraints:</p> <ul> <li> <p>Specified in Coordinated Universal Time (UTC).</p> </li> <li> <p>Specified in the Unix time format.</p> <p>For example, if you wish to use an end time of October 1, 2018, at 8 PM UTC, then you input <code>1538424000</code> as the end time.</p> </li> </ul>", "GetRelationalDatabaseMasterUserPasswordResult$createdAt": "<p>The timestamp when the specified version of the master user password was created.</p>", "GetRelationalDatabaseMetricDataRequest$startTime": "<p>The start of the time interval from which to get metric data.</p> <p>Constraints:</p> <ul> <li> <p>Specified in Coordinated Universal Time (UTC).</p> </li> <li> <p>Specified in the Unix time format.</p> <p>For example, if you wish to use a start time of October 1, 2018, at 8 PM UTC, then you input <code>1538424000</code> as the start time.</p> </li> </ul>", "GetRelationalDatabaseMetricDataRequest$endTime": "<p>The end of the time interval from which to get metric data.</p> <p>Constraints:</p> <ul> <li> <p>Specified in Coordinated Universal Time (UTC).</p> </li> <li> <p>Specified in the Unix time format.</p> <p>For example, if you wish to use an end time of October 1, 2018, at 8 PM UTC, then you input <code>1538424000</code> as the end time.</p> </li> </ul>", "HostKeyAttributes$witnessedAt": "<p>The time that the SSH host key or RDP certificate was recorded by Lightsail.</p>", "HostKeyAttributes$notValidBefore": "<p>The returned RDP certificate is valid after this point in time.</p> <p>This value is listed only for RDP certificates.</p>", "HostKeyAttributes$notValidAfter": "<p>The returned RDP certificate is not valid after this point in time.</p> <p>This value is listed only for RDP certificates.</p>", "Instance$createdAt": "<p>The timestamp when the instance was created (e.g., <code>1479734909.17</code>) in Unix time format.</p>", "InstanceAccessDetails$expiresAt": "<p>For SSH access, the date on which the temporary keys expire.</p>", "InstanceSnapshot$createdAt": "<p>The timestamp when the snapshot was created (e.g., <code>1479907467.024</code>).</p>", "KeyPair$createdAt": "<p>The timestamp when the key pair was created (e.g., <code>1479816991.349</code>).</p>", "LightsailDistribution$createdAt": "<p>The timestamp when the distribution was created.</p>", "LoadBalancer$createdAt": "<p>The date when your load balancer was created.</p>", "LoadBalancerTlsCertificate$createdAt": "<p>The time when you created your SSL/TLS certificate.</p>", "LoadBalancerTlsCertificate$issuedAt": "<p>The time when the SSL/TLS certificate was issued.</p>", "LoadBalancerTlsCertificate$notAfter": "<p>The timestamp when the SSL/TLS certificate expires.</p>", "LoadBalancerTlsCertificate$notBefore": "<p>The timestamp when the SSL/TLS certificate is first valid.</p>", "LoadBalancerTlsCertificate$revokedAt": "<p>The timestamp when the certificate was revoked. This value is present only when the certificate status is <code>REVOKED</code>.</p>", "LogEvent$createdAt": "<p>The timestamp when the database log event was created.</p>", "Operation$createdAt": "<p>The timestamp when the operation was initialized (e.g., <code>1479816991.349</code>).</p>", "Operation$statusChangedAt": "<p>The timestamp when the status was changed (e.g., <code>1479816991.349</code>).</p>", "PendingMaintenanceAction$currentApplyDate": "<p>The effective date of the pending database maintenance action.</p>", "RelationalDatabase$createdAt": "<p>The timestamp when the database was created. Formatted in Unix time.</p>", "RelationalDatabase$latestRestorableTime": "<p>The latest point in time to which the database can be restored. Formatted in Unix time.</p>", "RelationalDatabaseEvent$createdAt": "<p>The timestamp when the database event was created.</p>", "RelationalDatabaseSnapshot$createdAt": "<p>The timestamp when the database snapshot was created.</p>", "RenewalSummary$updatedAt": "<p>The timestamp when the certificate was last updated.</p>", "ResetDistributionCacheResult$createTime": "<p>The timestamp of the reset cache request (e.g., <code>1479734909.17</code>) in Unix time format.</p>", "StaticIp$createdAt": "<p>The timestamp when the static IP was created (e.g., <code>1479735304.222</code>).</p>"}}, "IssuerCA": {"base": null, "refs": {"Certificate$issuerCA": "<p>The certificate authority that issued the certificate.</p>"}}, "KeyAlgorithm": {"base": null, "refs": {"Certificate$keyAlgorithm": "<p>The algorithm used to generate the key pair (the public and private key) of the certificate.</p>"}}, "KeyPair": {"base": "<p>Describes the SSH key pair.</p>", "refs": {"CreateKeyPairResult$keyPair": "<p>An array of key-value pairs containing information about the new key pair you just created.</p>", "GetKeyPairResult$keyPair": "<p>An array of key-value pairs containing information about the key pair.</p>", "KeyPairList$member": null}}, "KeyPairList": {"base": null, "refs": {"GetKeyPairsResult$keyPairs": "<p>An array of key-value pairs containing information about the key pairs.</p>"}}, "LightsailDistribution": {"base": "<p>Describes an Amazon Lightsail content delivery network (CDN) distribution.</p>", "refs": {"CreateDistributionResult$distribution": "<p>An object that describes the distribution created.</p>", "DistributionList$member": null}}, "LoadBalancer": {"base": "<p>Describes the Lightsail load balancer.</p>", "refs": {"GetLoadBalancerResult$loadBalancer": "<p>An object containing information about your load balancer.</p>", "LoadBalancerList$member": null}}, "LoadBalancerAttributeName": {"base": null, "refs": {"LoadBalancerConfigurationOptions$key": null, "UpdateLoadBalancerAttributeRequest$attributeName": "<p>The name of the attribute you want to update. Valid values are below.</p>"}}, "LoadBalancerConfigurationOptions": {"base": null, "refs": {"LoadBalancer$configurationOptions": "<p>A string to string map of the configuration options for your load balancer. Valid values are listed below.</p>"}}, "LoadBalancerList": {"base": null, "refs": {"GetLoadBalancersResult$loadBalancers": "<p>An array of LoadBalancer objects describing your load balancers.</p>"}}, "LoadBalancerMetricName": {"base": null, "refs": {"GetLoadBalancerMetricDataRequest$metricName": "<p>The metric for which you want to return information.</p> <p>Valid load balancer metric names are listed below, along with the most useful <code>statistics</code> to include in your request, and the published <code>unit</code> value.</p> <ul> <li> <p> <b> <code>ClientTLSNegotiationErrorCount</code> </b> - The number of TLS connections initiated by the client that did not establish a session with the load balancer due to a TLS error generated by the load balancer. Possible causes include a mismatch of ciphers or protocols.</p> <p> <code>Statistics</code>: The most useful statistic is <code>Sum</code>.</p> <p> <code>Unit</code>: The published unit is <code>Count</code>.</p> </li> <li> <p> <b> <code>HealthyHostCount</code> </b> - The number of target instances that are considered healthy.</p> <p> <code>Statistics</code>: The most useful statistic are <code>Average</code>, <code>Minimum</code>, and <code>Maximum</code>.</p> <p> <code>Unit</code>: The published unit is <code>Count</code>.</p> </li> <li> <p> <b> <code>HTTPCode_Instance_2XX_Count</code> </b> - The number of HTTP 2XX response codes generated by the target instances. This does not include any response codes generated by the load balancer.</p> <p> <code>Statistics</code>: The most useful statistic is <code>Sum</code>. Note that <code>Minimum</code>, <code>Maximum</code>, and <code>Average</code> all return <code>1</code>.</p> <p> <code>Unit</code>: The published unit is <code>Count</code>.</p> </li> <li> <p> <b> <code>HTTPCode_Instance_3XX_Count</code> </b> - The number of HTTP 3XX response codes generated by the target instances. This does not include any response codes generated by the load balancer.</p> <p> <code>Statistics</code>: The most useful statistic is <code>Sum</code>. Note that <code>Minimum</code>, <code>Maximum</code>, and <code>Average</code> all return <code>1</code>.</p> <p> <code>Unit</code>: The published unit is <code>Count</code>.</p> </li> <li> <p> <b> <code>HTTPCode_Instance_4XX_Count</code> </b> - The number of HTTP 4XX response codes generated by the target instances. This does not include any response codes generated by the load balancer.</p> <p> <code>Statistics</code>: The most useful statistic is <code>Sum</code>. Note that <code>Minimum</code>, <code>Maximum</code>, and <code>Average</code> all return <code>1</code>.</p> <p> <code>Unit</code>: The published unit is <code>Count</code>.</p> </li> <li> <p> <b> <code>HTTPCode_Instance_5XX_Count</code> </b> - The number of HTTP 5XX response codes generated by the target instances. This does not include any response codes generated by the load balancer.</p> <p> <code>Statistics</code>: The most useful statistic is <code>Sum</code>. Note that <code>Minimum</code>, <code>Maximum</code>, and <code>Average</code> all return <code>1</code>.</p> <p> <code>Unit</code>: The published unit is <code>Count</code>.</p> </li> <li> <p> <b> <code>HTTPCode_LB_4XX_Count</code> </b> - The number of HTTP 4XX client error codes that originated from the load balancer. Client errors are generated when requests are malformed or incomplete. These requests were not received by the target instance. This count does not include response codes generated by the target instances.</p> <p> <code>Statistics</code>: The most useful statistic is <code>Sum</code>. Note that <code>Minimum</code>, <code>Maximum</code>, and <code>Average</code> all return <code>1</code>.</p> <p> <code>Unit</code>: The published unit is <code>Count</code>.</p> </li> <li> <p> <b> <code>HTTPCode_LB_5XX_Count</code> </b> - The number of HTTP 5XX server error codes that originated from the load balancer. This does not include any response codes generated by the target instance. This metric is reported if there are no healthy instances attached to the load balancer, or if the request rate exceeds the capacity of the instances (spillover) or the load balancer.</p> <p> <code>Statistics</code>: The most useful statistic is <code>Sum</code>. Note that <code>Minimum</code>, <code>Maximum</code>, and <code>Average</code> all return <code>1</code>.</p> <p> <code>Unit</code>: The published unit is <code>Count</code>.</p> </li> <li> <p> <b> <code>InstanceResponseTime</code> </b> - The time elapsed, in seconds, after the request leaves the load balancer until a response from the target instance is received.</p> <p> <code>Statistics</code>: The most useful statistic is <code>Average</code>.</p> <p> <code>Unit</code>: The published unit is <code>Seconds</code>.</p> </li> <li> <p> <b> <code>RejectedConnectionCount</code> </b> - The number of connections that were rejected because the load balancer had reached its maximum number of connections.</p> <p> <code>Statistics</code>: The most useful statistic is <code>Sum</code>.</p> <p> <code>Unit</code>: The published unit is <code>Count</code>.</p> </li> <li> <p> <b> <code>RequestCount</code> </b> - The number of requests processed over IPv4. This count includes only the requests with a response generated by a target instance of the load balancer.</p> <p> <code>Statistics</code>: The most useful statistic is <code>Sum</code>. Note that <code>Minimum</code>, <code>Maximum</code>, and <code>Average</code> all return <code>1</code>.</p> <p> <code>Unit</code>: The published unit is <code>Count</code>.</p> </li> <li> <p> <b> <code>UnhealthyHostCount</code> </b> - The number of target instances that are considered unhealthy.</p> <p> <code>Statistics</code>: The most useful statistic are <code>Average</code>, <code>Minimum</code>, and <code>Maximum</code>.</p> <p> <code>Unit</code>: The published unit is <code>Count</code>.</p> </li> </ul>", "GetLoadBalancerMetricDataResult$metricName": "<p>The name of the metric returned.</p>"}}, "LoadBalancerProtocol": {"base": null, "refs": {"LoadBalancer$protocol": "<p>The protocol you have enabled for your load balancer. Valid values are below.</p> <p>You can't just have <code>HTTP_HTTPS</code>, but you can have just <code>HTTP</code>.</p>"}}, "LoadBalancerState": {"base": null, "refs": {"LoadBalancer$state": "<p>The status of your load balancer. Valid values are below.</p>"}}, "LoadBalancerTlsCertificate": {"base": "<p>Describes a load balancer SSL/TLS certificate.</p> <p>TLS is just an updated, more secure version of Secure Socket Layer (SSL).</p>", "refs": {"LoadBalancerTlsCertificateList$member": null}}, "LoadBalancerTlsCertificateDomainStatus": {"base": null, "refs": {"LoadBalancerTlsCertificateDomainValidationOption$validationStatus": "<p>The status of the domain validation. Valid values are listed below.</p>", "LoadBalancerTlsCertificateDomainValidationRecord$validationStatus": "<p>The validation status. Valid values are listed below.</p>"}}, "LoadBalancerTlsCertificateDomainValidationOption": {"base": "<p>Contains information about the domain names on an SSL/TLS certificate that you will use to validate domain ownership.</p>", "refs": {"LoadBalancerTlsCertificateDomainValidationOptionList$member": null}}, "LoadBalancerTlsCertificateDomainValidationOptionList": {"base": null, "refs": {"LoadBalancerTlsCertificateRenewalSummary$domainValidationOptions": "<p>Contains information about the validation of each domain name in the certificate, as it pertains to Lightsail's managed renewal. This is different from the initial validation that occurs as a result of the RequestCertificate request.</p>"}}, "LoadBalancerTlsCertificateDomainValidationRecord": {"base": "<p>Describes the validation record of each domain name in the SSL/TLS certificate.</p>", "refs": {"LoadBalancerTlsCertificateDomainValidationRecordList$member": null}}, "LoadBalancerTlsCertificateDomainValidationRecordList": {"base": null, "refs": {"LoadBalancerTlsCertificate$domainValidationRecords": "<p>An array of LoadBalancerTlsCertificateDomainValidationRecord objects describing the records.</p>"}}, "LoadBalancerTlsCertificateFailureReason": {"base": null, "refs": {"LoadBalancerTlsCertificate$failureReason": "<p>The validation failure reason, if any, of the certificate.</p> <p>The following failure reasons are possible:</p> <ul> <li> <p> <b> <code>NO_AVAILABLE_CONTACTS</code> </b> - This failure applies to email validation, which is not available for Lightsail certificates.</p> </li> <li> <p> <b> <code>ADDITIONAL_VERIFICATION_REQUIRED</code> </b> - Lightsail requires additional information to process this certificate request. This can happen as a fraud-protection measure, such as when the domain ranks within the Alexa top 1000 websites. To provide the required information, use the <a href=\"https://console.aws.amazon.com/support/home\">AWS Support Center</a> to contact AWS Support.</p> <note> <p>You cannot request a certificate for Amazon-owned domain names such as those ending in amazonaws.com, cloudfront.net, or elasticbeanstalk.com.</p> </note> </li> <li> <p> <b> <code>DOMAIN_NOT_ALLOWED</code> </b> - One or more of the domain names in the certificate request was reported as an unsafe domain by <a href=\"https://www.virustotal.com/gui/home/<USER>\">VirusTotal</a>. To correct the problem, search for your domain name on the <a href=\"https://www.virustotal.com/gui/home/<USER>\">VirusTotal</a> website. If your domain is reported as suspicious, see <a href=\"https://www.google.com/webmasters/hacked/?hl=en\">Google Help for Hacked Websites</a> to learn what you can do.</p> <p>If you believe that the result is a false positive, notify the organization that is reporting the domain. VirusTotal is an aggregate of several antivirus and URL scanners and cannot remove your domain from a block list itself. After you correct the problem and the VirusTotal registry has been updated, request a new certificate.</p> <p>If you see this error and your domain is not included in the VirusTotal list, visit the <a href=\"https://console.aws.amazon.com/support/home\">AWS Support Center</a> and create a case.</p> </li> <li> <p> <b> <code>INVALID_PUBLIC_DOMAIN</code> </b> - One or more of the domain names in the certificate request is not valid. Typically, this is because a domain name in the request is not a valid top-level domain. Try to request a certificate again, correcting any spelling errors or typos that were in the failed request, and ensure that all domain names in the request are for valid top-level domains. For example, you cannot request a certificate for <code>example.invalidpublicdomain</code> because <code>invalidpublicdomain</code> is not a valid top-level domain.</p> </li> <li> <p> <b> <code>OTHER</code> </b> - Typically, this failure occurs when there is a typographical error in one or more of the domain names in the certificate request. Try to request a certificate again, correcting any spelling errors or typos that were in the failed request. </p> </li> </ul>"}}, "LoadBalancerTlsCertificateList": {"base": null, "refs": {"GetLoadBalancerTlsCertificatesResult$tlsCertificates": "<p>An array of LoadBalancerTlsCertificate objects describing your SSL/TLS certificates.</p>"}}, "LoadBalancerTlsCertificateRenewalStatus": {"base": null, "refs": {"LoadBalancerTlsCertificateRenewalSummary$renewalStatus": "<p>The renewal status of the certificate.</p> <p>The following renewal status are possible:</p> <ul> <li> <p> <b> <code>PendingAutoRenewal</code> </b> - Lights<PERSON> is attempting to automatically validate the domain names of the certificate. No further action is required. </p> </li> <li> <p> <b> <code>PendingValidation</code> </b> - Lightsail couldn't automatically validate one or more domain names of the certificate. You must take action to validate these domain names or the certificate won't be renewed. Check to make sure your certificate's domain validation records exist in your domain's DNS, and that your certificate remains in use.</p> </li> <li> <p> <b> <code>Success</code> </b> - All domain names in the certificate are validated, and Lightsail renewed the certificate. No further action is required. </p> </li> <li> <p> <b> <code>Failed</code> </b> - One or more domain names were not validated before the certificate expired, and Lightsail did not renew the certificate. You can request a new certificate using the <code>CreateCertificate</code> action.</p> </li> </ul>"}}, "LoadBalancerTlsCertificateRenewalSummary": {"base": "<p>Contains information about the status of Lightsail's managed renewal for the certificate.</p> <p>The renewal status of the certificate.</p> <p>The following renewal status are possible:</p> <ul> <li> <p> <b> <code>PendingAutoRenewal</code> </b> - Lightsail is attempting to automatically validate the domain names in the certificate. No further action is required. </p> </li> <li> <p> <b> <code>PendingValidation</code> </b> - Lightsail couldn't automatically validate one or more domain names in the certificate. You must take action to validate these domain names or the certificate won't be renewed. If you used DNS validation, check to make sure your certificate's domain validation records exist in your domain's DNS, and that your certificate remains in use.</p> </li> <li> <p> <b> <code>Success</code> </b> - All domain names in the certificate are validated, and Lightsail renewed the certificate. No further action is required. </p> </li> <li> <p> <b> <code>Failed</code> </b> - One or more domain names were not validated before the certificate expired, and Lights<PERSON> did not renew the certificate. You can request a new certificate using the <code>CreateCertificate</code> action.</p> </li> </ul>", "refs": {"LoadBalancerTlsCertificate$renewalSummary": "<p>An object that describes the status of the certificate renewal managed by Lightsail.</p>"}}, "LoadBalancerTlsCertificateRevocationReason": {"base": null, "refs": {"LoadBalancerTlsCertificate$revocationReason": "<p>The reason the certificate was revoked. This value is present only when the certificate status is <code>REVOKED</code>.</p>"}}, "LoadBalancerTlsCertificateStatus": {"base": null, "refs": {"LoadBalancerTlsCertificate$status": "<p>The validation status of the SSL/TLS certificate. Valid values are below.</p>"}}, "LoadBalancerTlsCertificateSummary": {"base": "<p>Provides a summary of SSL/TLS certificate metadata.</p>", "refs": {"LoadBalancerTlsCertificateSummaryList$member": null}}, "LoadBalancerTlsCertificateSummaryList": {"base": null, "refs": {"LoadBalancer$tlsCertificateSummaries": "<p>An array of LoadBalancerTlsCertificateSummary objects that provide additional information about the SSL/TLS certificates. For example, if <code>true</code>, the certificate is attached to the load balancer.</p>"}}, "LogEvent": {"base": "<p>Describes a database log event.</p>", "refs": {"LogEventList$member": null}}, "LogEventList": {"base": null, "refs": {"GetRelationalDatabaseLogEventsResult$resourceLogEvents": "<p>An object describing the result of your get relational database log events request.</p>"}}, "MetricDatapoint": {"base": "<p>Describes the metric data point.</p>", "refs": {"MetricDatapointList$member": null}}, "MetricDatapointList": {"base": null, "refs": {"GetDistributionMetricDataResult$metricData": "<p>An array of objects that describe the metric data returned.</p>", "GetInstanceMetricDataResult$metricData": "<p>An array of objects that describe the metric data returned.</p>", "GetLoadBalancerMetricDataResult$metricData": "<p>An array of objects that describe the metric data returned.</p>", "GetRelationalDatabaseMetricDataResult$metricData": "<p>An array of objects that describe the metric data returned.</p>"}}, "MetricName": {"base": null, "refs": {"Alarm$metricName": "<p>The name of the metric associated with the alarm.</p>", "PutAlarmRequest$metricName": "<p>The name of the metric to associate with the alarm.</p> <p>You can configure up to two alarms per metric.</p> <p>The following metrics are available for each resource type:</p> <ul> <li> <p> <b>Instances</b>: <code>BurstCapacityPercentage</code>, <code>BurstCapacityTime</code>, <code>CPUUtilization</code>, <code>NetworkIn</code>, <code>NetworkOut</code>, <code>StatusCheckFailed</code>, <code>StatusCheckFailed_Instance</code>, and <code>StatusCheckFailed_System</code>.</p> </li> <li> <p> <b>Load balancers</b>: <code>ClientTLSNegotiationErrorCount</code>, <code>HealthyHostCount</code>, <code>UnhealthyHostCount</code>, <code>HTTPCode_LB_4XX_Count</code>, <code>HTTPCode_LB_5XX_Count</code>, <code>HTTPCode_Instance_2XX_Count</code>, <code>HTTPCode_Instance_3XX_Count</code>, <code>HTTPCode_Instance_4XX_Count</code>, <code>HTTPCode_Instance_5XX_Count</code>, <code>InstanceResponseTime</code>, <code>RejectedConnectionCount</code>, and <code>RequestCount</code>.</p> </li> <li> <p> <b>Relational databases</b>: <code>CPUUtilization</code>, <code>DatabaseConnections</code>, <code>DiskQueueDepth</code>, <code>FreeStorageSpace</code>, <code>NetworkReceiveThroughput</code>, and <code>NetworkTransmitThroughput</code>.</p> </li> </ul> <p>For more information about these metrics, see <a href=\"https://lightsail.aws.amazon.com/ls/docs/en_us/articles/amazon-lightsail-resource-health-metrics#available-metrics\">Metrics available in Lightsail</a>.</p>"}}, "MetricPeriod": {"base": null, "refs": {"Alarm$period": "<p>The period, in seconds, over which the statistic is applied.</p>", "GetDistributionMetricDataRequest$period": "<p>The granularity, in seconds, for the metric data points that will be returned.</p>", "GetInstanceMetricDataRequest$period": "<p>The granularity, in seconds, of the returned data points.</p> <p>The <code>StatusCheckFailed</code>, <code>StatusCheckFailed_Instance</code>, and <code>StatusCheckFailed_System</code> instance metric data is available in 1-minute (60 seconds) granularity. All other instance metric data is available in 5-minute (300 seconds) granularity.</p>", "GetLoadBalancerMetricDataRequest$period": "<p>The granularity, in seconds, of the returned data points.</p>", "GetRelationalDatabaseMetricDataRequest$period": "<p>The granularity, in seconds, of the returned data points.</p> <p>All relational database metric data is available in 1-minute (60 seconds) granularity.</p>"}}, "MetricStatistic": {"base": null, "refs": {"Alarm$statistic": "<p>The statistic for the metric associated with the alarm.</p> <p>The following statistics are available:</p> <ul> <li> <p> <code>Minimum</code> - The lowest value observed during the specified period. Use this value to determine low volumes of activity for your application.</p> </li> <li> <p> <code>Maximum</code> - The highest value observed during the specified period. Use this value to determine high volumes of activity for your application.</p> </li> <li> <p> <code>Sum</code> - All values submitted for the matching metric added together. You can use this statistic to determine the total volume of a metric.</p> </li> <li> <p> <code>Average</code> - The value of Sum / SampleCount during the specified period. By comparing this statistic with the Minimum and Maximum values, you can determine the full scope of a metric and how close the average use is to the Minimum and Maximum values. This comparison helps you to know when to increase or decrease your resources.</p> </li> <li> <p> <code>SampleCount</code> - The count, or number, of data points used for the statistical calculation.</p> </li> </ul>", "MetricStatisticList$member": null}}, "MetricStatisticList": {"base": null, "refs": {"GetDistributionMetricDataRequest$statistics": "<p>The statistic for the metric.</p> <p>The following statistics are available:</p> <ul> <li> <p> <code>Minimum</code> - The lowest value observed during the specified period. Use this value to determine low volumes of activity for your application.</p> </li> <li> <p> <code>Maximum</code> - The highest value observed during the specified period. Use this value to determine high volumes of activity for your application.</p> </li> <li> <p> <code>Sum</code> - All values submitted for the matching metric added together. You can use this statistic to determine the total volume of a metric.</p> </li> <li> <p> <code>Average</code> - The value of Sum / SampleCount during the specified period. By comparing this statistic with the Minimum and Maximum values, you can determine the full scope of a metric and how close the average use is to the Minimum and Maximum values. This comparison helps you to know when to increase or decrease your resources.</p> </li> <li> <p> <code>SampleCount</code> - The count, or number, of data points used for the statistical calculation.</p> </li> </ul>", "GetInstanceMetricDataRequest$statistics": "<p>The statistic for the metric.</p> <p>The following statistics are available:</p> <ul> <li> <p> <code>Minimum</code> - The lowest value observed during the specified period. Use this value to determine low volumes of activity for your application.</p> </li> <li> <p> <code>Maximum</code> - The highest value observed during the specified period. Use this value to determine high volumes of activity for your application.</p> </li> <li> <p> <code>Sum</code> - All values submitted for the matching metric added together. You can use this statistic to determine the total volume of a metric.</p> </li> <li> <p> <code>Average</code> - The value of Sum / SampleCount during the specified period. By comparing this statistic with the Minimum and Maximum values, you can determine the full scope of a metric and how close the average use is to the Minimum and Maximum values. This comparison helps you to know when to increase or decrease your resources.</p> </li> <li> <p> <code>SampleCount</code> - The count, or number, of data points used for the statistical calculation.</p> </li> </ul>", "GetLoadBalancerMetricDataRequest$statistics": "<p>The statistic for the metric.</p> <p>The following statistics are available:</p> <ul> <li> <p> <code>Minimum</code> - The lowest value observed during the specified period. Use this value to determine low volumes of activity for your application.</p> </li> <li> <p> <code>Maximum</code> - The highest value observed during the specified period. Use this value to determine high volumes of activity for your application.</p> </li> <li> <p> <code>Sum</code> - All values submitted for the matching metric added together. You can use this statistic to determine the total volume of a metric.</p> </li> <li> <p> <code>Average</code> - The value of Sum / SampleCount during the specified period. By comparing this statistic with the Minimum and Maximum values, you can determine the full scope of a metric and how close the average use is to the Minimum and Maximum values. This comparison helps you to know when to increase or decrease your resources.</p> </li> <li> <p> <code>SampleCount</code> - The count, or number, of data points used for the statistical calculation.</p> </li> </ul>", "GetRelationalDatabaseMetricDataRequest$statistics": "<p>The statistic for the metric.</p> <p>The following statistics are available:</p> <ul> <li> <p> <code>Minimum</code> - The lowest value observed during the specified period. Use this value to determine low volumes of activity for your application.</p> </li> <li> <p> <code>Maximum</code> - The highest value observed during the specified period. Use this value to determine high volumes of activity for your application.</p> </li> <li> <p> <code>Sum</code> - All values submitted for the matching metric added together. You can use this statistic to determine the total volume of a metric.</p> </li> <li> <p> <code>Average</code> - The value of Sum / SampleCount during the specified period. By comparing this statistic with the Minimum and Maximum values, you can determine the full scope of a metric and how close the average use is to the Minimum and Maximum values. This comparison helps you to know when to increase or decrease your resources.</p> </li> <li> <p> <code>SampleCount</code> - The count, or number, of data points used for the statistical calculation.</p> </li> </ul>"}}, "MetricUnit": {"base": null, "refs": {"Alarm$unit": "<p>The unit of the metric associated with the alarm.</p>", "GetDistributionMetricDataRequest$unit": "<p>The unit for the metric data request.</p> <p>Valid units depend on the metric data being requested. For the valid units with each available metric, see the <code>metricName</code> parameter.</p>", "GetInstanceMetricDataRequest$unit": "<p>The unit for the metric data request. Valid units depend on the metric data being requested. For the valid units to specify with each available metric, see the <code>metricName</code> parameter.</p>", "GetLoadBalancerMetricDataRequest$unit": "<p>The unit for the metric data request. Valid units depend on the metric data being requested. For the valid units with each available metric, see the <code>metricName</code> parameter.</p>", "GetRelationalDatabaseMetricDataRequest$unit": "<p>The unit for the metric data request. Valid units depend on the metric data being requested. For the valid units with each available metric, see the <code>metricName</code> parameter.</p>", "MetricDatapoint$unit": "<p>The unit. </p>"}}, "MonitoredResourceInfo": {"base": "<p>Describes resource being monitored by an alarm.</p> <p>An alarm is a way to monitor your Amazon Lightsail resource metrics. For more information, see <a href=\"https://lightsail.aws.amazon.com/ls/docs/en_us/articles/amazon-lightsail-alarms\">Alarms in Amazon Lightsail</a>.</p>", "refs": {"Alarm$monitoredResourceInfo": "<p>An object that lists information about the resource monitored by the alarm.</p>"}}, "MonthlyTransfer": {"base": "<p>Describes the monthly data transfer in and out of your virtual private server (or <i>instance</i>).</p>", "refs": {"InstanceNetworking$monthlyTransfer": "<p>The amount of data in GB allocated for monthly data transfers.</p>"}}, "NetworkProtocol": {"base": null, "refs": {"InstancePortInfo$protocol": "<p>The IP protocol name.</p> <p>The name can be one of the following:</p> <ul> <li> <p> <code>tcp</code> - Transmission Control Protocol (TCP) provides reliable, ordered, and error-checked delivery of streamed data between applications running on hosts communicating by an IP network. If you have an application that doesn't require reliable data stream service, use UDP instead.</p> </li> <li> <p> <code>all</code> - All transport layer protocol types. For more general information, see <a href=\"https://en.wikipedia.org/wiki/Transport_layer\">Transport layer</a> on <i>Wikipedia</i>.</p> </li> <li> <p> <code>udp</code> - With User Datagram Protocol (UDP), computer applications can send messages (or datagrams) to other hosts on an Internet Protocol (IP) network. Prior communications are not required to set up transmission channels or data paths. Applications that don't require reliable data stream service can use UDP, which provides a connectionless datagram service that emphasizes reduced latency over reliability. If you do require reliable data stream service, use TCP instead.</p> </li> <li> <p> <code>icmp</code> - Internet Control Message Protocol (ICMP) is used to send error messages and operational information indicating success or failure when communicating with an instance. For example, an error is indicated when an instance could not be reached. When you specify <code>icmp</code> as the <code>protocol</code>, you must specify the ICMP type using the <code>fromPort</code> parameter, and ICMP code using the <code>toPort</code> parameter.</p> </li> </ul>", "InstancePortState$protocol": "<p>The IP protocol name.</p> <p>The name can be one of the following:</p> <ul> <li> <p> <code>tcp</code> - Transmission Control Protocol (TCP) provides reliable, ordered, and error-checked delivery of streamed data between applications running on hosts communicating by an IP network. If you have an application that doesn't require reliable data stream service, use UDP instead.</p> </li> <li> <p> <code>all</code> - All transport layer protocol types. For more general information, see <a href=\"https://en.wikipedia.org/wiki/Transport_layer\">Transport layer</a> on <i>Wikipedia</i>.</p> </li> <li> <p> <code>udp</code> - With User Datagram Protocol (UDP), computer applications can send messages (or datagrams) to other hosts on an Internet Protocol (IP) network. Prior communications are not required to set up transmission channels or data paths. Applications that don't require reliable data stream service can use UDP, which provides a connectionless datagram service that emphasizes reduced latency over reliability. If you do require reliable data stream service, use TCP instead.</p> </li> <li> <p> <code>icmp</code> - Internet Control Message Protocol (ICMP) is used to send error messages and operational information indicating success or failure when communicating with an instance. For example, an error is indicated when an instance could not be reached. When you specify <code>icmp</code> as the <code>protocol</code>, you must specify the ICMP type using the <code>fromPort</code> parameter, and ICMP code using the <code>toPort</code> parameter.</p> </li> </ul>", "PortInfo$protocol": "<p>The IP protocol name.</p> <p>The name can be one of the following:</p> <ul> <li> <p> <code>tcp</code> - Transmission Control Protocol (TCP) provides reliable, ordered, and error-checked delivery of streamed data between applications running on hosts communicating by an IP network. If you have an application that doesn't require reliable data stream service, use UDP instead.</p> </li> <li> <p> <code>all</code> - All transport layer protocol types. For more general information, see <a href=\"https://en.wikipedia.org/wiki/Transport_layer\">Transport layer</a> on <i>Wikipedia</i>.</p> </li> <li> <p> <code>udp</code> - With User Datagram Protocol (UDP), computer applications can send messages (or datagrams) to other hosts on an Internet Protocol (IP) network. Prior communications are not required to set up transmission channels or data paths. Applications that don't require reliable data stream service can use UDP, which provides a connectionless datagram service that emphasizes reduced latency over reliability. If you do require reliable data stream service, use TCP instead.</p> </li> <li> <p> <code>icmp</code> - Internet Control Message Protocol (ICMP) is used to send error messages and operational information indicating success or failure when communicating with an instance. For example, an error is indicated when an instance could not be reached. When you specify <code>icmp</code> as the <code>protocol</code>, you must specify the ICMP type using the <code>fromPort</code> parameter, and ICMP code using the <code>toPort</code> parameter.</p> </li> </ul>"}}, "NonEmptyString": {"base": null, "refs": {"Alarm$arn": "<p>The Amazon Resource Name (ARN) of the alarm.</p>", "AttachDiskRequest$diskPath": "<p>The disk path to expose to the instance (e.g., <code>/dev/xvdf</code>).</p>", "AvailabilityZone$zoneName": "<p>The name of the Availability Zone. The format is <code>us-east-2a</code> (case-sensitive).</p>", "AvailabilityZone$state": "<p>The state of the Availability Zone.</p>", "Blueprint$blueprintId": "<p>The ID for the virtual private server image (e.g., <code>app_wordpress_4_4</code> or <code>app_lamp_7_0</code>).</p>", "Blueprint$group": "<p>The group name of the blueprint (e.g., <code>amazon-linux</code>).</p>", "Bundle$bundleId": "<p>The bundle ID (e.g., <code>micro_1_0</code>).</p>", "CacheSettings$allowedHTTPMethods": "<p>The HTTP methods that are processed and forwarded to the distribution's origin.</p> <p>You can specify the following options:</p> <ul> <li> <p> <code>GET,HEAD</code> - The distribution forwards the <code>GET</code> and <code>HEAD</code> methods.</p> </li> <li> <p> <code>GET,HEAD,OPTIONS</code> - The distribution forwards the <code>GET</code>, <code>HEAD</code>, and <code>OPTIONS</code> methods.</p> </li> <li> <p> <code>GET,HEAD,OPTIONS,PUT,PATCH,POST,DELETE</code> - The distribution forwards the <code>GET</code>, <code>HEAD</code>, <code>OPTIONS</code>, <code>PUT</code>, <code>PATCH</code>, <code>POST</code>, and <code>DELETE</code> methods.</p> </li> </ul> <p>If you specify the third option, you might need to restrict access to your distribution's origin so users can't perform operations that you don't want them to. For example, you might not want users to have permission to delete objects from your origin.</p>", "CacheSettings$cachedHTTPMethods": "<p>The HTTP method responses that are cached by your distribution.</p> <p>You can specify the following options:</p> <ul> <li> <p> <code>GET,HEAD</code> - The distribution caches responses to the <code>GET</code> and <code>HEAD</code> methods.</p> </li> <li> <p> <code>GET,HEAD,OPTIONS</code> - The distribution caches responses to the <code>GET</code>, <code>HEAD</code>, and <code>OPTIONS</code> methods.</p> </li> </ul>", "Certificate$arn": "<p>The Amazon Resource Name (ARN) of the certificate.</p>", "CertificateSummary$certificateArn": "<p>The Amazon Resource Name (ARN) of the certificate.</p>", "CloudFormationStackRecord$arn": "<p>The Amazon Resource Name (ARN) of the CloudFormation stack record.</p>", "CloudFormationStackRecordSourceInfo$name": "<p>The name of the record.</p>", "CloudFormationStackRecordSourceInfo$arn": "<p>The Amazon Resource Name (ARN) of the export snapshot record.</p>", "ContactMethod$contactEndpoint": "<p>The destination of the contact method, such as an email address or a mobile phone number.</p>", "ContactMethod$arn": "<p>The Amazon Resource Name (ARN) of the contact method.</p>", "CreateDiskFromSnapshotRequest$availabilityZone": "<p>The Availability Zone where you want to create the disk (e.g., <code>us-east-2a</code>). Choose the same Availability Zone as the Lightsail instance where you want to create the disk.</p> <p>Use the GetRegions operation to list the Availability Zones where Lightsail is currently available.</p>", "CreateDiskRequest$availabilityZone": "<p>The Availability Zone where you want to create the disk (e.g., <code>us-east-2a</code>). Use the same Availability Zone as the Lightsail instance to which you want to attach the disk.</p> <p>Use the <code>get regions</code> operation to list the Availability Zones where Lightsail is currently available.</p>", "CreateInstancesFromSnapshotRequest$bundleId": "<p>The bundle of specification information for your virtual private server (or <i>instance</i>), including the pricing plan (e.g., <code>micro_1_0</code>).</p>", "CreateInstancesRequest$blueprintId": "<p>The ID for a virtual private server image (e.g., <code>app_wordpress_4_4</code> or <code>app_lamp_7_0</code>). Use the <code>get blueprints</code> operation to return a list of available images (or <i>blueprints</i>).</p> <note> <p>Use active blueprints when creating new instances. Inactive blueprints are listed to support customers with existing instances and are not necessarily available to create new instances. Blueprints are marked inactive when they become outdated due to operating system updates or new application releases.</p> </note>", "CreateInstancesRequest$bundleId": "<p>The bundle of specification information for your virtual private server (or <i>instance</i>), including the pricing plan (e.g., <code>micro_1_0</code>).</p>", "DestinationInfo$id": "<p>The ID of the resource created at the destination.</p>", "DestinationInfo$service": "<p>The destination service of the record.</p>", "Disk$arn": "<p>The Amazon Resource Name (ARN) of the disk.</p>", "DiskInfo$path": "<p>The disk path.</p>", "DiskMap$originalDiskPath": "<p>The original disk path exposed to the instance (for example, <code>/dev/sdh</code>).</p>", "DiskSnapshot$arn": "<p>The Amazon Resource Name (ARN) of the disk snapshot.</p>", "DiskSnapshot$fromDiskArn": "<p>The Amazon Resource Name (ARN) of the source disk from which the disk snapshot was created.</p>", "DiskSnapshot$fromInstanceArn": "<p>The Amazon Resource Name (ARN) of the source instance from which the disk (system volume) snapshot was created.</p>", "Domain$arn": "<p>The Amazon Resource Name (ARN) of the domain recordset (e.g., <code>arn:aws:lightsail:global:123456789101:Domain/824cede0-abc7-4f84-8dbc-12345EXAMPLE</code>).</p>", "DomainEntry$id": "<p>The ID of the domain recordset entry.</p>", "ExportSnapshotRecord$arn": "<p>The Amazon Resource Name (ARN) of the export snapshot record.</p>", "ExportSnapshotRecordSourceInfo$name": "<p>The name of the source instance or disk snapshot.</p>", "ExportSnapshotRecordSourceInfo$arn": "<p>The Amazon Resource Name (ARN) of the source instance or disk snapshot.</p>", "ExportSnapshotRecordSourceInfo$fromResourceName": "<p>The name of the snapshot's source instance or disk.</p>", "ExportSnapshotRecordSourceInfo$fromResourceArn": "<p>The Amazon Resource Name (ARN) of the snapshot's source instance or disk.</p>", "GetOperationRequest$operationId": "<p>A GUID used to identify the operation.</p>", "Instance$arn": "<p>The Amazon Resource Name (ARN) of the instance (e.g., <code>arn:aws:lightsail:us-east-2:123456789101:Instance/244ad76f-8aad-4741-809f-12345EXAMPLE</code>).</p>", "Instance$blueprintId": "<p>The blueprint ID (e.g., <code>os_amlinux_2016_03</code>).</p>", "Instance$blueprintName": "<p>The friendly name of the blueprint (e.g., <code>Amazon Linux</code>).</p>", "Instance$bundleId": "<p>The bundle for the instance (e.g., <code>micro_1_0</code>).</p>", "Instance$username": "<p>The user name for connecting to the instance (e.g., <code>ec2-user</code>).</p>", "InstanceEntry$instanceType": "<p>The instance type (e.g., <code>t2.micro</code>) to use for the new Amazon EC2 instance.</p>", "InstanceSnapshot$arn": "<p>The Amazon Resource Name (ARN) of the snapshot (e.g., <code>arn:aws:lightsail:us-east-2:123456789101:InstanceSnapshot/d23b5706-3322-4d83-81e5-12345EXAMPLE</code>).</p>", "InstanceSnapshot$fromInstanceArn": "<p>The Amazon Resource Name (ARN) of the instance from which the snapshot was created (e.g., <code>arn:aws:lightsail:us-east-2:123456789101:Instance/64b8404c-ccb1-430b-8daf-12345EXAMPLE</code>).</p>", "InstanceSnapshotInfo$fromBundleId": "<p>The bundle ID from which the source instance was created (e.g., <code>micro_1_0</code>).</p>", "InstanceSnapshotInfo$fromBlueprintId": "<p>The blueprint ID from which the source instance (e.g., <code>os_debian_8_3</code>).</p>", "KeyPair$arn": "<p>The Amazon Resource Name (ARN) of the key pair (e.g., <code>arn:aws:lightsail:us-east-2:123456789101:KeyPair/05859e3d-331d-48ba-9034-12345EXAMPLE</code>).</p>", "LightsailDistribution$arn": "<p>The Amazon Resource Name (ARN) of the distribution.</p>", "LoadBalancer$arn": "<p>The Amazon Resource Name (ARN) of the load balancer.</p>", "LoadBalancer$dnsName": "<p>The DNS name of your Lightsail load balancer.</p>", "LoadBalancer$healthCheckPath": "<p>The path you specified to perform your health checks. If no path is specified, the load balancer tries to make a request to the default (root) page.</p>", "LoadBalancerTlsCertificate$arn": "<p>The Amazon Resource Name (ARN) of the SSL/TLS certificate.</p>", "LoadBalancerTlsCertificate$issuer": "<p>The issuer of the certificate.</p>", "LoadBalancerTlsCertificate$keyAlgorithm": "<p>The algorithm used to generate the key pair (the public and private key).</p>", "LoadBalancerTlsCertificate$serial": "<p>The serial number of the certificate.</p>", "LoadBalancerTlsCertificate$signatureAlgorithm": "<p>The algorithm that was used to sign the certificate.</p>", "LoadBalancerTlsCertificate$subject": "<p>The name of the entity that is associated with the public key contained in the certificate.</p>", "LoadBalancerTlsCertificateDomainValidationRecord$name": "<p>A fully qualified domain name in the certificate. For example, <code>example.com</code>.</p>", "LoadBalancerTlsCertificateDomainValidationRecord$type": "<p>The type of validation record. For example, <code>CNAME</code> for domain validation.</p>", "LoadBalancerTlsCertificateDomainValidationRecord$value": "<p>The value for that type.</p>", "Operation$id": "<p>The ID of the operation.</p>", "PendingMaintenanceAction$action": "<p>The type of pending database maintenance action.</p>", "PendingMaintenanceAction$description": "<p>Additional detail about the pending database maintenance action.</p>", "RelationalDatabase$arn": "<p>The Amazon Resource Name (ARN) of the database.</p>", "RelationalDatabase$relationalDatabaseBlueprintId": "<p>The blueprint ID for the database. A blueprint describes the major engine version of a database.</p>", "RelationalDatabase$relationalDatabaseBundleId": "<p>The bundle ID for the database. A bundle describes the performance specifications for your database.</p>", "RelationalDatabase$state": "<p>Describes the current state of the database.</p>", "RelationalDatabase$engine": "<p>The database software (for example, <code>MySQL</code>).</p>", "RelationalDatabase$engineVersion": "<p>The database engine version (for example, <code>5.7.23</code>).</p>", "RelationalDatabase$masterUsername": "<p>The master user name of the database.</p>", "RelationalDatabase$parameterApplyStatus": "<p>The status of parameter updates for the database.</p>", "RelationalDatabase$preferredBackupWindow": "<p>The daily time range during which automated backups are created for the database (for example, <code>16:00-16:30</code>).</p>", "RelationalDatabase$preferredMaintenanceWindow": "<p>The weekly time range during which system maintenance can occur on the database.</p> <p>In the format <code>ddd:hh24:mi-ddd:hh24:mi</code>. For example, <code>Tue:17:00-Tue:17:30</code>.</p>", "RelationalDatabaseEndpoint$address": "<p>Specifies the DNS address of the database.</p>", "RelationalDatabaseSnapshot$arn": "<p>The Amazon Resource Name (ARN) of the database snapshot.</p>", "RelationalDatabaseSnapshot$engine": "<p>The software of the database snapshot (for example, <code>MySQL</code>)</p>", "RelationalDatabaseSnapshot$engineVersion": "<p>The database engine version for the database snapshot (for example, <code>5.7.23</code>).</p>", "RelationalDatabaseSnapshot$state": "<p>The state of the database snapshot.</p>", "RelationalDatabaseSnapshot$fromRelationalDatabaseName": "<p>The name of the source database from which the database snapshot was created.</p>", "RelationalDatabaseSnapshot$fromRelationalDatabaseArn": "<p>The Amazon Resource Name (ARN) of the database from which the database snapshot was created.</p>", "StaticIp$arn": "<p>The Amazon Resource Name (ARN) of the static IP (e.g., <code>arn:aws:lightsail:us-east-2:123456789101:StaticIp/9cbb4a9e-f8e3-4dfe-b57e-12345EXAMPLE</code>).</p>"}}, "NotFoundException": {"base": "<p>Lightsail throws this exception when it cannot find a resource.</p>", "refs": {}}, "NotificationTriggerList": {"base": null, "refs": {"Alarm$notificationTriggers": "<p>The alarm states that trigger a notification.</p>", "PutAlarmRequest$notificationTriggers": "<p>The alarm states that trigger a notification.</p> <p>An alarm has the following possible states:</p> <ul> <li> <p> <code>ALARM</code> - The metric is outside of the defined threshold.</p> </li> <li> <p> <code>INSUFFICIENT_DATA</code> - The alarm has just started, the metric is not available, or not enough data is available for the metric to determine the alarm state.</p> </li> <li> <p> <code>OK</code> - The metric is within the defined threshold.</p> </li> </ul> <p>When you specify a notification trigger, the <code>ALARM</code> state must be specified. The <code>INSUFFICIENT_DATA</code> and <code>OK</code> states can be specified in addition to the <code>ALARM</code> state.</p> <ul> <li> <p>If you specify <code>OK</code> as an alarm trigger, a notification is sent when the alarm switches from an <code>ALARM</code> or <code>INSUFFICIENT_DATA</code> alarm state to an <code>OK</code> state. This can be thought of as an <i>all clear</i> alarm notification.</p> </li> <li> <p>If you specify <code>INSUFFICIENT_DATA</code> as the alarm trigger, a notification is sent when the alarm switches from an <code>OK</code> or <code>ALARM</code> alarm state to an <code>INSUFFICIENT_DATA</code> state.</p> </li> </ul> <p>The notification trigger defaults to <code>ALARM</code> if you don't specify this parameter.</p>"}}, "OpenInstancePublicPortsRequest": {"base": null, "refs": {}}, "OpenInstancePublicPortsResult": {"base": null, "refs": {}}, "Operation": {"base": "<p>Describes the API operation.</p>", "refs": {"AttachCertificateToDistributionResult$operation": "<p>An object that describes the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "CloseInstancePublicPortsResult$operation": "<p>An object that describes the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "CreateDistributionResult$operation": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "CreateDomainEntryResult$operation": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "CreateDomainResult$operation": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "CreateKeyPairResult$operation": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "DeleteDistributionResult$operation": "<p>An object that describes the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "DeleteDomainEntryResult$operation": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "DeleteDomainResult$operation": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "DeleteKeyPairResult$operation": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "DetachCertificateFromDistributionResult$operation": "<p>An object that describes the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "GetOperationResult$operation": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "ImportKeyPairResult$operation": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "OpenInstancePublicPortsResult$operation": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "OperationList$member": null, "PeerVpcResult$operation": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "PutInstancePublicPortsResult$operation": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "ResetDistributionCacheResult$operation": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "UnpeerVpcResult$operation": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "UpdateDistributionBundleResult$operation": null, "UpdateDistributionResult$operation": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>"}}, "OperationFailureException": {"base": "<p>Lights<PERSON> throws this exception when an operation fails to execute.</p>", "refs": {}}, "OperationList": {"base": null, "refs": {"AllocateStaticIpResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "AttachDiskResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "AttachInstancesToLoadBalancerResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "AttachLoadBalancerTlsCertificateResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p> <p>These SSL/TLS certificates are only usable by Lightsail load balancers. You can't get the certificate and use it for another purpose.</p>", "AttachStaticIpResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "CopySnapshotResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "CreateCertificateResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "CreateCloudFormationStackResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "CreateContactMethodResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "CreateDiskFromSnapshotResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "CreateDiskResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "CreateDiskSnapshotResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "CreateInstanceSnapshotResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "CreateInstancesFromSnapshotResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "CreateInstancesResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "CreateLoadBalancerResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "CreateLoadBalancerTlsCertificateResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "CreateRelationalDatabaseFromSnapshotResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "CreateRelationalDatabaseResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "CreateRelationalDatabaseSnapshotResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "DeleteAlarmResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "DeleteAutoSnapshotResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "DeleteCertificateResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "DeleteContactMethodResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "DeleteDiskResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "DeleteDiskSnapshotResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "DeleteInstanceResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "DeleteInstanceSnapshotResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "DeleteKnownHostKeysResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "DeleteLoadBalancerResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "DeleteLoadBalancerTlsCertificateResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "DeleteRelationalDatabaseResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "DeleteRelationalDatabaseSnapshotResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "DetachDiskResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "DetachInstancesFromLoadBalancerResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "DetachStaticIpResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "DisableAddOnResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "EnableAddOnResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "ExportSnapshotResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "GetOperationsForResourceResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "GetOperationsResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "PutAlarmResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "RebootInstanceResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "RebootRelationalDatabaseResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "ReleaseStaticIpResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "SendContactMethodVerificationResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "StartInstanceResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "StartRelationalDatabaseResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "StopInstanceResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "StopRelationalDatabaseResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "TagResourceResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "TestAlarmResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "UntagResourceResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "UpdateDomainEntryResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "UpdateLoadBalancerAttributeResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "UpdateRelationalDatabaseParametersResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>", "UpdateRelationalDatabaseResult$operations": "<p>An array of objects that describe the result of the action, such as the status of the request, the timestamp of the request, and the resources affected by the request.</p>"}}, "OperationStatus": {"base": null, "refs": {"Operation$status": "<p>The status of the operation. </p>"}}, "OperationType": {"base": null, "refs": {"Operation$operationType": "<p>The type of operation. </p>"}}, "Origin": {"base": "<p>Describes the origin resource of an Amazon Lightsail content delivery network (CDN) distribution.</p> <p>An origin can be a Lightsail instance or load balancer. A distribution pulls content from an origin, caches it, and serves it to viewers via a worldwide network of edge servers.</p>", "refs": {"LightsailDistribution$origin": "<p>An object that describes the origin resource of the distribution, such as a Lightsail instance or load balancer.</p> <p>The distribution pulls, caches, and serves content from the origin.</p>"}}, "OriginProtocolPolicyEnum": {"base": null, "refs": {"InputOrigin$protocolPolicy": "<p>The protocol that your Amazon Lightsail distribution uses when establishing a connection with your origin to pull content.</p>", "Origin$protocolPolicy": "<p>The protocol that your Amazon Lightsail distribution uses when establishing a connection with your origin to pull content.</p>"}}, "PasswordData": {"base": "<p>The password data for the Windows Server-based instance, including the ciphertext and the key pair name.</p>", "refs": {"InstanceAccessDetails$passwordData": "<p>For a Windows Server-based instance, an object with the data you can use to retrieve your password. This is only needed if <code>password</code> is empty and the instance is not new (and therefore the password is not ready yet). When you create an instance, it can take up to 15 minutes for the instance to be ready.</p>"}}, "PeerVpcRequest": {"base": null, "refs": {}}, "PeerVpcResult": {"base": null, "refs": {}}, "PendingMaintenanceAction": {"base": "<p>Describes a pending database maintenance action.</p>", "refs": {"PendingMaintenanceActionList$member": null}}, "PendingMaintenanceActionList": {"base": null, "refs": {"RelationalDatabase$pendingMaintenanceActions": "<p>Describes the pending maintenance actions for the database.</p>"}}, "PendingModifiedRelationalDatabaseValues": {"base": "<p>Describes a pending database value modification.</p>", "refs": {"RelationalDatabase$pendingModifiedValues": "<p>Describes pending database value modifications.</p>"}}, "Port": {"base": null, "refs": {"CreateLoadBalancerRequest$instancePort": "<p>The instance port where you're creating your load balancer.</p>", "InstancePortInfo$fromPort": "<p>The first port in a range of open ports on an instance.</p> <p>Allowed ports:</p> <ul> <li> <p>TCP and UDP - <code>0</code> to <code>65535</code> </p> </li> <li> <p>ICMP - The ICMP type. For example, specify <code>8</code> as the <code>fromPort</code> (ICMP type), and <code>-1</code> as the <code>toPort</code> (ICMP code), to enable ICMP Ping. For more information, see <a href=\"https://en.wikipedia.org/wiki/Internet_Control_Message_Protocol#Control_messages\">Control Messages</a> on <i>Wikipedia</i>.</p> </li> </ul>", "InstancePortInfo$toPort": "<p>The last port in a range of open ports on an instance.</p> <p>Allowed ports:</p> <ul> <li> <p>TCP and UDP - <code>0</code> to <code>65535</code> </p> </li> <li> <p>ICMP - The ICMP code. For example, specify <code>8</code> as the <code>fromPort</code> (ICMP type), and <code>-1</code> as the <code>toPort</code> (ICMP code), to enable ICMP Ping. For more information, see <a href=\"https://en.wikipedia.org/wiki/Internet_Control_Message_Protocol#Control_messages\">Control Messages</a> on <i>Wikipedia</i>.</p> </li> </ul>", "InstancePortState$fromPort": "<p>The first port in a range of open ports on an instance.</p> <p>Allowed ports:</p> <ul> <li> <p>TCP and UDP - <code>0</code> to <code>65535</code> </p> </li> <li> <p>ICMP - The ICMP type. For example, specify <code>8</code> as the <code>fromPort</code> (ICMP type), and <code>-1</code> as the <code>toPort</code> (ICMP code), to enable ICMP Ping. For more information, see <a href=\"https://en.wikipedia.org/wiki/Internet_Control_Message_Protocol#Control_messages\">Control Messages</a> on <i>Wikipedia</i>.</p> </li> </ul>", "InstancePortState$toPort": "<p>The last port in a range of open ports on an instance.</p> <p>Allowed ports:</p> <ul> <li> <p>TCP and UDP - <code>0</code> to <code>65535</code> </p> </li> <li> <p>ICMP - The ICMP code. For example, specify <code>8</code> as the <code>fromPort</code> (ICMP type), and <code>-1</code> as the <code>toPort</code> (ICMP code), to enable ICMP Ping. For more information, see <a href=\"https://en.wikipedia.org/wiki/Internet_Control_Message_Protocol#Control_messages\">Control Messages</a> on <i>Wikipedia</i>.</p> </li> </ul>", "PortInfo$fromPort": "<p>The first port in a range of open ports on an instance.</p> <p>Allowed ports:</p> <ul> <li> <p>TCP and UDP - <code>0</code> to <code>65535</code> </p> </li> <li> <p>ICMP - The ICMP type. For example, specify <code>8</code> as the <code>fromPort</code> (ICMP type), and <code>-1</code> as the <code>toPort</code> (ICMP code), to enable ICMP Ping. For more information, see <a href=\"https://en.wikipedia.org/wiki/Internet_Control_Message_Protocol#Control_messages\">Control Messages</a> on <i>Wikipedia</i>.</p> </li> </ul>", "PortInfo$toPort": "<p>The last port in a range of open ports on an instance.</p> <p>Allowed ports:</p> <ul> <li> <p>TCP and UDP - <code>0</code> to <code>65535</code> </p> </li> <li> <p>ICMP - The ICMP code. For example, specify <code>8</code> as the <code>fromPort</code> (ICMP type), and <code>-1</code> as the <code>toPort</code> (ICMP code), to enable ICMP Ping. For more information, see <a href=\"https://en.wikipedia.org/wiki/Internet_Control_Message_Protocol#Control_messages\">Control Messages</a> on <i>Wikipedia</i>.</p> </li> </ul>", "PortList$member": null}}, "PortAccessType": {"base": null, "refs": {"InstancePortInfo$accessType": "<p>The type of access (<code>Public</code> or <code>Private</code>).</p>"}}, "PortInfo": {"base": "<p>Describes ports to open on an instance, the IP addresses allowed to connect to the instance through the ports, and the protocol.</p>", "refs": {"CloseInstancePublicPortsRequest$portInfo": "<p>An object to describe the ports to close for the specified instance.</p>", "OpenInstancePublicPortsRequest$portInfo": "<p>An object to describe the ports to open for the specified instance.</p>", "PortInfoList$member": null}}, "PortInfoList": {"base": null, "refs": {"PutInstancePublicPortsRequest$portInfos": "<p>An array of objects to describe the ports to open for the specified instance.</p>"}}, "PortInfoSourceType": {"base": null, "refs": {"InstanceEntry$portInfoSource": "<p>The port configuration to use for the new Amazon EC2 instance.</p> <p>The following configuration options are available:</p> <ul> <li> <p> <code>DEFAULT</code> - Use the default firewall settings from the Lightsail instance blueprint.</p> </li> <li> <p> <code>INSTANCE</code> - Use the configured firewall settings from the source Lightsail instance.</p> </li> <li> <p> <code>NONE</code> - Use the default Amazon EC2 security group.</p> </li> <li> <p> <code>CLOSED</code> - All ports closed.</p> </li> </ul> <note> <p>If you configured <code>lightsail-connect</code> as a <code>cidrListAliases</code> on your instance, or if you chose to allow the Lightsail browser-based SSH or RDP clients to connect to your instance, that configuration is not carried over to your new Amazon EC2 instance.</p> </note>"}}, "PortList": {"base": null, "refs": {"LoadBalancer$publicPorts": "<p>An array of public port settings for your load balancer. For HTTP, use port 80. For HTTPS, use port 443.</p>"}}, "PortState": {"base": null, "refs": {"InstancePortState$state": "<p>Specifies whether the instance port is <code>open</code> or <code>closed</code>.</p> <note> <p>The port state for Lightsail instances is always <code>open</code>.</p> </note>"}}, "PutAlarmRequest": {"base": null, "refs": {}}, "PutAlarmResult": {"base": null, "refs": {}}, "PutInstancePublicPortsRequest": {"base": null, "refs": {}}, "PutInstancePublicPortsResult": {"base": null, "refs": {}}, "QueryStringObject": {"base": "<p>Describes the query string parameters that an Amazon Lightsail content delivery network (CDN) distribution to bases caching on.</p> <p>For the query strings that you specify, your distribution caches separate versions of the specified content based on the query string values in viewer requests.</p>", "refs": {"CacheSettings$forwardedQueryStrings": "<p>An object that describes the query strings that are forwarded to the origin. Your content is cached based on the query strings that are forwarded.</p>"}}, "RebootInstanceRequest": {"base": null, "refs": {}}, "RebootInstanceResult": {"base": null, "refs": {}}, "RebootRelationalDatabaseRequest": {"base": null, "refs": {}}, "RebootRelationalDatabaseResult": {"base": null, "refs": {}}, "RecordState": {"base": null, "refs": {"CloudFormationStackRecord$state": "<p>The current state of the CloudFormation stack record.</p>", "ExportSnapshotRecord$state": "<p>The state of the export snapshot record.</p>"}}, "Region": {"base": "<p>Describes the AWS Region.</p>", "refs": {"RegionList$member": null}}, "RegionList": {"base": null, "refs": {"GetRegionsResult$regions": "<p>An array of key-value pairs containing information about your get regions request.</p>"}}, "RegionName": {"base": null, "refs": {"CopySnapshotRequest$sourceRegion": "<p>The AWS Region where the source manual or automatic snapshot is located.</p>", "InputOrigin$regionName": "<p>The AWS Region name of the origin resource.</p>", "Origin$regionName": "<p>The AWS Region name of the origin resource.</p>", "Region$name": "<p>The region name (e.g., <code>us-east-2</code>).</p>", "ResourceLocation$regionName": "<p>The AWS Region name.</p>"}}, "RelationalDatabase": {"base": "<p>Describes a database.</p>", "refs": {"GetRelationalDatabaseResult$relationalDatabase": "<p>An object describing the specified database.</p>", "RelationalDatabaseList$member": null}}, "RelationalDatabaseBlueprint": {"base": "<p>Describes a database image, or blueprint. A blueprint describes the major engine version of a database.</p>", "refs": {"RelationalDatabaseBlueprintList$member": null}}, "RelationalDatabaseBlueprintList": {"base": null, "refs": {"GetRelationalDatabaseBlueprintsResult$blueprints": "<p>An object describing the result of your get relational database blueprints request.</p>"}}, "RelationalDatabaseBundle": {"base": "<p>Describes a database bundle. A bundle describes the performance specifications of the database.</p>", "refs": {"RelationalDatabaseBundleList$member": null}}, "RelationalDatabaseBundleList": {"base": null, "refs": {"GetRelationalDatabaseBundlesResult$bundles": "<p>An object describing the result of your get relational database bundles request.</p>"}}, "RelationalDatabaseEndpoint": {"base": "<p>Describes an endpoint for a database.</p>", "refs": {"RelationalDatabase$masterEndpoint": "<p>The master endpoint for the database.</p>"}}, "RelationalDatabaseEngine": {"base": null, "refs": {"RelationalDatabaseBlueprint$engine": "<p>The database software of the database blueprint (for example, <code>MySQL</code>).</p>"}}, "RelationalDatabaseEvent": {"base": "<p>Describes an event for a database.</p>", "refs": {"RelationalDatabaseEventList$member": null}}, "RelationalDatabaseEventList": {"base": null, "refs": {"GetRelationalDatabaseEventsResult$relationalDatabaseEvents": "<p>An object describing the result of your get relational database events request.</p>"}}, "RelationalDatabaseHardware": {"base": "<p>Describes the hardware of a database.</p>", "refs": {"RelationalDatabase$hardware": "<p>Describes the hardware of the database.</p>"}}, "RelationalDatabaseList": {"base": null, "refs": {"GetRelationalDatabasesResult$relationalDatabases": "<p>An object describing the result of your get relational databases request.</p>"}}, "RelationalDatabaseMetricName": {"base": null, "refs": {"GetRelationalDatabaseMetricDataRequest$metricName": "<p>The metric for which you want to return information.</p> <p>Valid relational database metric names are listed below, along with the most useful <code>statistics</code> to include in your request, and the published <code>unit</code> value. All relational database metric data is available in 1-minute (60 seconds) granularity.</p> <ul> <li> <p> <b> <code>CPUUtilization</code> </b> - The percentage of CPU utilization currently in use on the database.</p> <p> <code>Statistics</code>: The most useful statistics are <code>Maximum</code> and <code>Average</code>.</p> <p> <code>Unit</code>: The published unit is <code>Percent</code>.</p> </li> <li> <p> <b> <code>DatabaseConnections</code> </b> - The number of database connections in use.</p> <p> <code>Statistics</code>: The most useful statistics are <code>Maximum</code> and <code>Sum</code>.</p> <p> <code>Unit</code>: The published unit is <code>Count</code>.</p> </li> <li> <p> <b> <code>DiskQueueDepth</code> </b> - The number of outstanding IOs (read/write requests) that are waiting to access the disk.</p> <p> <code>Statistics</code>: The most useful statistic is <code>Sum</code>.</p> <p> <code>Unit</code>: The published unit is <code>Count</code>.</p> </li> <li> <p> <b> <code>FreeStorageSpace</code> </b> - The amount of available storage space.</p> <p> <code>Statistics</code>: The most useful statistic is <code>Sum</code>.</p> <p> <code>Unit</code>: The published unit is <code>Bytes</code>.</p> </li> <li> <p> <b> <code>NetworkReceiveThroughput</code> </b> - The incoming (Receive) network traffic on the database, including both customer database traffic and AWS traffic used for monitoring and replication.</p> <p> <code>Statistics</code>: The most useful statistic is <code>Average</code>.</p> <p> <code>Unit</code>: The published unit is <code>Bytes/Second</code>.</p> </li> <li> <p> <b> <code>NetworkTransmitThroughput</code> </b> - The outgoing (Transmit) network traffic on the database, including both customer database traffic and AWS traffic used for monitoring and replication.</p> <p> <code>Statistics</code>: The most useful statistic is <code>Average</code>.</p> <p> <code>Unit</code>: The published unit is <code>Bytes/Second</code>.</p> </li> </ul>", "GetRelationalDatabaseMetricDataResult$metricName": "<p>The name of the metric returned.</p>"}}, "RelationalDatabaseParameter": {"base": "<p>Describes the parameters of a database.</p>", "refs": {"RelationalDatabaseParameterList$member": null}}, "RelationalDatabaseParameterList": {"base": null, "refs": {"GetRelationalDatabaseParametersResult$parameters": "<p>An object describing the result of your get relational database parameters request.</p>", "UpdateRelationalDatabaseParametersRequest$parameters": "<p>The database parameters to update.</p>"}}, "RelationalDatabasePasswordVersion": {"base": null, "refs": {"GetRelationalDatabaseMasterUserPasswordRequest$passwordVersion": "<p>The password version to return.</p> <p>Specifying <code>CURRENT</code> or <code>PREVIOUS</code> returns the current or previous passwords respectively. Specifying <code>PENDING</code> returns the newest version of the password that will rotate to <code>CURRENT</code>. After the <code>PENDING</code> password rotates to <code>CURRENT</code>, the <code>PENDING</code> password is no longer available.</p> <p>Default: <code>CURRENT</code> </p>"}}, "RelationalDatabaseSnapshot": {"base": "<p>Describes a database snapshot.</p>", "refs": {"GetRelationalDatabaseSnapshotResult$relationalDatabaseSnapshot": "<p>An object describing the specified database snapshot.</p>", "RelationalDatabaseSnapshotList$member": null}}, "RelationalDatabaseSnapshotList": {"base": null, "refs": {"GetRelationalDatabaseSnapshotsResult$relationalDatabaseSnapshots": "<p>An object describing the result of your get relational database snapshots request.</p>"}}, "ReleaseStaticIpRequest": {"base": null, "refs": {}}, "ReleaseStaticIpResult": {"base": null, "refs": {}}, "RenewalStatus": {"base": null, "refs": {"RenewalSummary$renewalStatus": "<p>The renewal status of the certificate.</p> <p>The following renewal status are possible:</p> <ul> <li> <p> <b> <code>PendingAutoRenewal</code> </b> - Lights<PERSON> is attempting to automatically validate the domain names of the certificate. No further action is required. </p> </li> <li> <p> <b> <code>PendingValidation</code> </b> - Lightsail couldn't automatically validate one or more domain names of the certificate. You must take action to validate these domain names or the certificate won't be renewed. Check to make sure your certificate's domain validation records exist in your domain's DNS, and that your certificate remains in use.</p> </li> <li> <p> <b> <code>Success</code> </b> - All domain names in the certificate are validated, and Lightsail renewed the certificate. No further action is required. </p> </li> <li> <p> <b> <code>Failed</code> </b> - One or more domain names were not validated before the certificate expired, and Lightsail did not renew the certificate. You can request a new certificate using the <code>CreateCertificate</code> action.</p> </li> </ul>"}}, "RenewalStatusReason": {"base": null, "refs": {"RenewalSummary$renewalStatusReason": "<p>The reason for the renewal status of the certificate.</p>"}}, "RenewalSummary": {"base": "<p>Describes the status of a SSL/TLS certificate renewal managed by Amazon Lightsail.</p>", "refs": {"Certificate$renewalSummary": "<p>An object that describes the status of the certificate renewal managed by Lightsail.</p>"}}, "RequestFailureReason": {"base": null, "refs": {"Certificate$requestFailureReason": "<p>The validation failure reason, if any, of the certificate.</p> <p>The following failure reasons are possible:</p> <ul> <li> <p> <b> <code>NO_AVAILABLE_CONTACTS</code> </b> - This failure applies to email validation, which is not available for Lightsail certificates.</p> </li> <li> <p> <b> <code>ADDITIONAL_VERIFICATION_REQUIRED</code> </b> - Lightsail requires additional information to process this certificate request. This can happen as a fraud-protection measure, such as when the domain ranks within the Alexa top 1000 websites. To provide the required information, use the <a href=\"https://console.aws.amazon.com/support/home\">AWS Support Center</a> to contact AWS Support.</p> <note> <p>You cannot request a certificate for Amazon-owned domain names such as those ending in amazonaws.com, cloudfront.net, or elasticbeanstalk.com.</p> </note> </li> <li> <p> <b> <code>DOMAIN_NOT_ALLOWED</code> </b> - One or more of the domain names in the certificate request was reported as an unsafe domain by <a href=\"https://www.virustotal.com/gui/home/<USER>\">VirusTotal</a>. To correct the problem, search for your domain name on the <a href=\"https://www.virustotal.com/gui/home/<USER>\">VirusTotal</a> website. If your domain is reported as suspicious, see <a href=\"https://www.google.com/webmasters/hacked/?hl=en\">Google Help for Hacked Websites</a> to learn what you can do.</p> <p>If you believe that the result is a false positive, notify the organization that is reporting the domain. VirusTotal is an aggregate of several antivirus and URL scanners and cannot remove your domain from a block list itself. After you correct the problem and the VirusTotal registry has been updated, request a new certificate.</p> <p>If you see this error and your domain is not included in the VirusTotal list, visit the <a href=\"https://console.aws.amazon.com/support/home\">AWS Support Center</a> and create a case.</p> </li> <li> <p> <b> <code>INVALID_PUBLIC_DOMAIN</code> </b> - One or more of the domain names in the certificate request is not valid. Typically, this is because a domain name in the request is not a valid top-level domain. Try to request a certificate again, correcting any spelling errors or typos that were in the failed request, and ensure that all domain names in the request are for valid top-level domains. For example, you cannot request a certificate for <code>example.invalidpublicdomain</code> because <code>invalidpublicdomain</code> is not a valid top-level domain.</p> </li> <li> <p> <b> <code>OTHER</code> </b> - Typically, this failure occurs when there is a typographical error in one or more of the domain names in the certificate request. Try to request a certificate again, correcting any spelling errors or typos that were in the failed request. </p> </li> </ul>"}}, "ResetDistributionCacheRequest": {"base": null, "refs": {}}, "ResetDistributionCacheResult": {"base": null, "refs": {}}, "ResourceArn": {"base": null, "refs": {"MonitoredResourceInfo$arn": "<p>The Amazon Resource Name (ARN) of the resource being monitored.</p>", "TagResourceRequest$resourceArn": "<p>The Amazon Resource Name (ARN) of the resource to which you want to add a tag.</p>", "UntagResourceRequest$resourceArn": "<p>The Amazon Resource Name (ARN) of the resource from which you want to remove a tag.</p>"}}, "ResourceLocation": {"base": "<p>Describes the resource location.</p>", "refs": {"Alarm$location": "<p>An object that lists information about the location of the alarm.</p>", "CloudFormationStackRecord$location": "<p>A list of objects describing the Availability Zone and AWS Region of the CloudFormation stack record.</p>", "ContactMethod$location": null, "Disk$location": "<p>The AWS Region and Availability Zone where the disk is located.</p>", "DiskSnapshot$location": "<p>The AWS Region and Availability Zone where the disk snapshot was created.</p>", "Domain$location": "<p>The AWS Region and Availability Zones where the domain recordset was created.</p>", "ExportSnapshotRecord$location": "<p>The AWS Region and Availability Zone where the export snapshot record is located.</p>", "Instance$location": "<p>The region name and Availability Zone where the instance is located.</p>", "InstanceSnapshot$location": "<p>The region name and Availability Zone where you created the snapshot.</p>", "KeyPair$location": "<p>The region name and Availability Zone where the key pair was created.</p>", "LightsailDistribution$location": "<p>An object that describes the location of the distribution, such as the AWS Region and Availability Zone.</p> <note> <p>Lightsail distributions are global resources that can reference an origin in any AWS Region, and distribute its content globally. However, all distributions are located in the <code>us-east-1</code> Region.</p> </note>", "LoadBalancer$location": "<p>The AWS Region where your load balancer was created (e.g., <code>us-east-2a</code>). Lightsail automatically creates your load balancer across Availability Zones.</p>", "LoadBalancerTlsCertificate$location": "<p>The AWS Region and Availability Zone where you created your certificate.</p>", "Operation$location": "<p>The AWS Region and Availability Zone.</p>", "RelationalDatabase$location": "<p>The Region name and Availability Zone where the database is located.</p>", "RelationalDatabaseSnapshot$location": "<p>The Region name and Availability Zone where the database snapshot is located.</p>", "StaticIp$location": "<p>The region and Availability Zone where the static IP was created.</p>"}}, "ResourceName": {"base": null, "refs": {"Alarm$name": "<p>The name of the alarm.</p>", "AllocateStaticIpRequest$staticIpName": "<p>The name of the static IP address.</p>", "AttachCertificateToDistributionRequest$distributionName": "<p>The name of the distribution that the certificate will be attached to.</p> <p>Use the <code>GetDistributions</code> action to get a list of distribution names that you can specify.</p>", "AttachCertificateToDistributionRequest$certificateName": "<p>The name of the certificate to attach to a distribution.</p> <p>Only certificates with a status of <code>ISSUED</code> can be attached to a distribution.</p> <p>Use the <code>GetCertificates</code> action to get a list of certificate names that you can specify.</p> <note> <p>This is the name of the certificate resource type and is used only to reference the certificate in other API actions. It can be different than the domain name of the certificate. For example, your certificate name might be <code>WordPress-Blog-Certificate</code> and the domain name of the certificate might be <code>example.com</code>.</p> </note>", "AttachDiskRequest$diskName": "<p>The unique Lightsail disk name (e.g., <code>my-disk</code>).</p>", "AttachDiskRequest$instanceName": "<p>The name of the Lightsail instance where you want to utilize the storage disk.</p>", "AttachInstancesToLoadBalancerRequest$loadBalancerName": "<p>The name of the load balancer.</p>", "AttachLoadBalancerTlsCertificateRequest$loadBalancerName": "<p>The name of the load balancer to which you want to associate the SSL/TLS certificate.</p>", "AttachLoadBalancerTlsCertificateRequest$certificateName": "<p>The name of your SSL/TLS certificate.</p>", "AttachStaticIpRequest$staticIpName": "<p>The name of the static IP.</p>", "AttachStaticIpRequest$instanceName": "<p>The instance name to which you want to attach the static IP address.</p>", "AttachedDiskMap$key": null, "Blueprint$name": "<p>The friendly name of the blueprint (e.g., <code>Amazon Linux</code>).</p>", "CloseInstancePublicPortsRequest$instanceName": "<p>The name of the instance for which to close ports.</p>", "CloudFormationStackRecord$name": "<p>The name of the CloudFormation stack record. It starts with <code>CloudFormationStackRecord</code> followed by a GUID.</p>", "ContactMethod$name": "<p>The name of the contact method.</p>", "CopySnapshotRequest$sourceSnapshotName": "<p>The name of the source manual snapshot to copy.</p> <p>Constraint:</p> <ul> <li> <p>Define this parameter only when copying a manual snapshot as another manual snapshot.</p> </li> </ul>", "CopySnapshotRequest$targetSnapshotName": "<p>The name of the new manual snapshot to be created as a copy.</p>", "CreateDiskFromSnapshotRequest$diskName": "<p>The unique Lightsail disk name (e.g., <code>my-disk</code>).</p>", "CreateDiskFromSnapshotRequest$diskSnapshotName": "<p>The name of the disk snapshot (e.g., <code>my-snapshot</code>) from which to create the new storage disk.</p> <p>Constraint:</p> <ul> <li> <p>This parameter cannot be defined together with the <code>source disk name</code> parameter. The <code>disk snapshot name</code> and <code>source disk name</code> parameters are mutually exclusive.</p> </li> </ul>", "CreateDiskRequest$diskName": "<p>The unique Lightsail disk name (e.g., <code>my-disk</code>).</p>", "CreateDiskSnapshotRequest$diskName": "<p>The unique name of the source disk (e.g., <code>Disk-Virginia-1</code>).</p> <note> <p>This parameter cannot be defined together with the <code>instance name</code> parameter. The <code>disk name</code> and <code>instance name</code> parameters are mutually exclusive.</p> </note>", "CreateDiskSnapshotRequest$diskSnapshotName": "<p>The name of the destination disk snapshot (e.g., <code>my-disk-snapshot</code>) based on the source disk.</p>", "CreateDiskSnapshotRequest$instanceName": "<p>The unique name of the source instance (e.g., <code>Amazon_Linux-512MB-Virginia-1</code>). When this is defined, a snapshot of the instance's system volume is created.</p> <note> <p>This parameter cannot be defined together with the <code>disk name</code> parameter. The <code>instance name</code> and <code>disk name</code> parameters are mutually exclusive.</p> </note>", "CreateDistributionRequest$distributionName": "<p>The name for the distribution.</p>", "CreateInstanceSnapshotRequest$instanceSnapshotName": "<p>The name for your new snapshot.</p>", "CreateInstanceSnapshotRequest$instanceName": "<p>The Lightsail instance on which to base your snapshot.</p>", "CreateInstancesFromSnapshotRequest$instanceSnapshotName": "<p>The name of the instance snapshot on which you are basing your new instances. Use the get instance snapshots operation to return information about your existing snapshots.</p> <p>Constraint:</p> <ul> <li> <p>This parameter cannot be defined together with the <code>source instance name</code> parameter. The <code>instance snapshot name</code> and <code>source instance name</code> parameters are mutually exclusive.</p> </li> </ul>", "CreateInstancesFromSnapshotRequest$keyPairName": "<p>The name for your key pair.</p>", "CreateInstancesRequest$customImageName": "<p>(Deprecated) The name for your custom image.</p> <note> <p>In releases prior to June 12, 2017, this parameter was ignored by the API. It is now deprecated.</p> </note>", "CreateInstancesRequest$keyPairName": "<p>The name of your key pair.</p>", "CreateKeyPairRequest$keyPairName": "<p>The name for your new key pair.</p>", "CreateLoadBalancerRequest$loadBalancerName": "<p>The name of your load balancer.</p>", "CreateLoadBalancerRequest$certificateName": "<p>The name of the SSL/TLS certificate.</p> <p>If you specify <code>certificateName</code>, then <code>certificateDomainName</code> is required (and vice-versa).</p>", "CreateLoadBalancerTlsCertificateRequest$loadBalancerName": "<p>The load balancer name where you want to create the SSL/TLS certificate.</p>", "CreateLoadBalancerTlsCertificateRequest$certificateName": "<p>The SSL/TLS certificate name.</p> <p>You can have up to 10 certificates in your account at one time. Each Lightsail load balancer can have up to 2 certificates associated with it at one time. There is also an overall limit to the number of certificates that can be issue in a 365-day period. For more information, see <a href=\"http://docs.aws.amazon.com/acm/latest/userguide/acm-limits.html\">Limits</a>.</p>", "CreateRelationalDatabaseFromSnapshotRequest$relationalDatabaseName": "<p>The name to use for your new database.</p> <p>Constraints:</p> <ul> <li> <p>Must contain from 2 to 255 alphanumeric characters, or hyphens.</p> </li> <li> <p>The first and last character must be a letter or number.</p> </li> </ul>", "CreateRelationalDatabaseFromSnapshotRequest$relationalDatabaseSnapshotName": "<p>The name of the database snapshot from which to create your new database.</p>", "CreateRelationalDatabaseFromSnapshotRequest$sourceRelationalDatabaseName": "<p>The name of the source database.</p>", "CreateRelationalDatabaseRequest$relationalDatabaseName": "<p>The name to use for your new database.</p> <p>Constraints:</p> <ul> <li> <p>Must contain from 2 to 255 alphanumeric characters, or hyphens.</p> </li> <li> <p>The first and last character must be a letter or number.</p> </li> </ul>", "CreateRelationalDatabaseSnapshotRequest$relationalDatabaseName": "<p>The name of the database on which to base your new snapshot.</p>", "CreateRelationalDatabaseSnapshotRequest$relationalDatabaseSnapshotName": "<p>The name for your new database snapshot.</p> <p>Constraints:</p> <ul> <li> <p>Must contain from 2 to 255 alphanumeric characters, or hyphens.</p> </li> <li> <p>The first and last character must be a letter or number.</p> </li> </ul>", "DeleteAlarmRequest$alarmName": "<p>The name of the alarm to delete.</p>", "DeleteAutoSnapshotRequest$resourceName": "<p>The name of the source instance or disk from which to delete the automatic snapshot.</p>", "DeleteDiskRequest$diskName": "<p>The unique name of the disk you want to delete (e.g., <code>my-disk</code>).</p>", "DeleteDiskSnapshotRequest$diskSnapshotName": "<p>The name of the disk snapshot you want to delete (e.g., <code>my-disk-snapshot</code>).</p>", "DeleteDistributionRequest$distributionName": "<p>The name of the distribution to delete.</p> <p>Use the <code>GetDistributions</code> action to get a list of distribution names that you can specify.</p>", "DeleteInstanceRequest$instanceName": "<p>The name of the instance to delete.</p>", "DeleteInstanceSnapshotRequest$instanceSnapshotName": "<p>The name of the snapshot to delete.</p>", "DeleteKeyPairRequest$keyPairName": "<p>The name of the key pair to delete.</p>", "DeleteKnownHostKeysRequest$instanceName": "<p>The name of the instance for which you want to reset the host key or certificate.</p>", "DeleteLoadBalancerRequest$loadBalancerName": "<p>The name of the load balancer you want to delete.</p>", "DeleteLoadBalancerTlsCertificateRequest$loadBalancerName": "<p>The load balancer name.</p>", "DeleteLoadBalancerTlsCertificateRequest$certificateName": "<p>The SSL/TLS certificate name.</p>", "DeleteRelationalDatabaseRequest$relationalDatabaseName": "<p>The name of the database that you are deleting.</p>", "DeleteRelationalDatabaseRequest$finalRelationalDatabaseSnapshotName": "<p>The name of the database snapshot created if <code>skip final snapshot</code> is <code>false</code>, which is the default value for that parameter.</p> <note> <p>Specifying this parameter and also specifying the <code>skip final snapshot</code> parameter to <code>true</code> results in an error.</p> </note> <p>Constraints:</p> <ul> <li> <p>Must contain from 2 to 255 alphanumeric characters, or hyphens.</p> </li> <li> <p>The first and last character must be a letter or number.</p> </li> </ul>", "DeleteRelationalDatabaseSnapshotRequest$relationalDatabaseSnapshotName": "<p>The name of the database snapshot that you are deleting.</p>", "DetachCertificateFromDistributionRequest$distributionName": "<p>The name of the distribution from which to detach the certificate.</p> <p>Use the <code>GetDistributions</code> action to get a list of distribution names that you can specify.</p>", "DetachDiskRequest$diskName": "<p>The unique name of the disk you want to detach from your instance (e.g., <code>my-disk</code>).</p>", "DetachInstancesFromLoadBalancerRequest$loadBalancerName": "<p>The name of the Lightsail load balancer.</p>", "DetachStaticIpRequest$staticIpName": "<p>The name of the static IP to detach from the instance.</p>", "DisableAddOnRequest$resourceName": "<p>The name of the source resource for which to disable the add-on.</p>", "Disk$name": "<p>The unique name of the disk.</p>", "Disk$attachedTo": "<p>The resources to which the disk is attached.</p>", "DiskMap$newDiskName": "<p>The new disk name (e.g., <code>my-new-disk</code>).</p>", "DiskSnapshot$name": "<p>The name of the disk snapshot (e.g., <code>my-disk-snapshot</code>).</p>", "DiskSnapshot$fromDiskName": "<p>The unique name of the source disk from which the disk snapshot was created.</p>", "DiskSnapshot$fromInstanceName": "<p>The unique name of the source instance from which the disk (system volume) snapshot was created.</p>", "Domain$name": "<p>The name of the domain.</p>", "EnableAddOnRequest$resourceName": "<p>The name of the source resource for which to enable or modify the add-on.</p>", "ExportSnapshotRecord$name": "<p>The export snapshot record name.</p>", "ExportSnapshotRequest$sourceSnapshotName": "<p>The name of the instance or disk snapshot to be exported to Amazon EC2.</p>", "GetAlarmsRequest$alarmName": "<p>The name of the alarm.</p> <p>Specify an alarm name to return information about a specific alarm.</p>", "GetAlarmsRequest$monitoredResourceName": "<p>The name of the Lightsail resource being monitored by the alarm.</p> <p>Specify a monitored resource name to return information about all alarms for a specific resource.</p>", "GetAutoSnapshotsRequest$resourceName": "<p>The name of the source instance or disk from which to get automatic snapshot information.</p>", "GetAutoSnapshotsResult$resourceName": "<p>The name of the source instance or disk for the automatic snapshots.</p>", "GetDiskRequest$diskName": "<p>The name of the disk (e.g., <code>my-disk</code>).</p>", "GetDiskSnapshotRequest$diskSnapshotName": "<p>The name of the disk snapshot (e.g., <code>my-disk-snapshot</code>).</p>", "GetDistributionLatestCacheResetRequest$distributionName": "<p>The name of the distribution for which to return the timestamp of the last cache reset.</p> <p>Use the <code>GetDistributions</code> action to get a list of distribution names that you can specify.</p> <p>When omitted, the response includes the latest cache reset timestamp of all your distributions.</p>", "GetDistributionMetricDataRequest$distributionName": "<p>The name of the distribution for which to get metric data.</p> <p>Use the <code>GetDistributions</code> action to get a list of distribution names that you can specify.</p>", "GetDistributionsRequest$distributionName": "<p>The name of the distribution for which to return information.</p> <p>Use the <code>GetDistributions</code> action to get a list of distribution names that you can specify.</p> <p>When omitted, the response includes all of your distributions in the AWS Region where the request is made.</p>", "GetInstanceAccessDetailsRequest$instanceName": "<p>The name of the instance to access.</p>", "GetInstanceMetricDataRequest$instanceName": "<p>The name of the instance for which you want to get metrics data.</p>", "GetInstancePortStatesRequest$instanceName": "<p>The name of the instance for which to return firewall port states.</p>", "GetInstanceRequest$instanceName": "<p>The name of the instance.</p>", "GetInstanceSnapshotRequest$instanceSnapshotName": "<p>The name of the snapshot for which you are requesting information.</p>", "GetInstanceStateRequest$instanceName": "<p>The name of the instance to get state information about.</p>", "GetKeyPairRequest$keyPairName": "<p>The name of the key pair for which you are requesting information.</p>", "GetLoadBalancerMetricDataRequest$loadBalancerName": "<p>The name of the load balancer.</p>", "GetLoadBalancerRequest$loadBalancerName": "<p>The name of the load balancer.</p>", "GetLoadBalancerTlsCertificatesRequest$loadBalancerName": "<p>The name of the load balancer you associated with your SSL/TLS certificate.</p>", "GetOperationsForResourceRequest$resourceName": "<p>The name of the resource for which you are requesting information.</p>", "GetRelationalDatabaseEventsRequest$relationalDatabaseName": "<p>The name of the database from which to get events.</p>", "GetRelationalDatabaseLogEventsRequest$relationalDatabaseName": "<p>The name of your database for which to get log events.</p>", "GetRelationalDatabaseLogStreamsRequest$relationalDatabaseName": "<p>The name of your database for which to get log streams.</p>", "GetRelationalDatabaseMasterUserPasswordRequest$relationalDatabaseName": "<p>The name of your database for which to get the master user password.</p>", "GetRelationalDatabaseMetricDataRequest$relationalDatabaseName": "<p>The name of your database from which to get metric data.</p>", "GetRelationalDatabaseParametersRequest$relationalDatabaseName": "<p>The name of your database for which to get parameters.</p>", "GetRelationalDatabaseRequest$relationalDatabaseName": "<p>The name of the database that you are looking up.</p>", "GetRelationalDatabaseSnapshotRequest$relationalDatabaseSnapshotName": "<p>The name of the database snapshot for which to get information.</p>", "GetStaticIpRequest$staticIpName": "<p>The name of the static IP in Lightsail.</p>", "ImportKeyPairRequest$keyPairName": "<p>The name of the key pair for which you want to import the public key.</p>", "InputOrigin$name": "<p>The name of the origin resource.</p>", "Instance$name": "<p>The name the user gave the instance (e.g., <code>Amazon_Linux-1GB-Ohio-1</code>).</p>", "Instance$sshKeyName": "<p>The name of the SSH key being used to connect to the instance (e.g., <code>LightsailDefaultKeyPair</code>).</p>", "InstanceAccessDetails$instanceName": "<p>The name of this Amazon Lightsail instance.</p>", "InstanceEntry$sourceName": "<p>The name of the export snapshot record, which contains the exported Lightsail instance snapshot that will be used as the source of the new Amazon EC2 instance.</p> <p>Use the <code>get export snapshot records</code> operation to get a list of export snapshot records that you can use to create a CloudFormation stack.</p>", "InstanceHealthSummary$instanceName": "<p>The name of the Lightsail instance for which you are requesting health check data.</p>", "InstanceSnapshot$name": "<p>The name of the snapshot.</p>", "InstanceSnapshot$fromInstanceName": "<p>The instance from which the snapshot was created.</p>", "KeyPair$name": "<p>The friendly name of the SSH key pair.</p>", "LightsailDistribution$name": "<p>The name of the distribution.</p>", "LightsailDistribution$certificateName": "<p>The name of the SSL/TLS certificate attached to the distribution, if any.</p>", "LoadBalancer$name": "<p>The name of the load balancer (e.g., <code>my-load-balancer</code>).</p>", "LoadBalancerTlsCertificate$name": "<p>The name of the SSL/TLS certificate (e.g., <code>my-certificate</code>).</p>", "LoadBalancerTlsCertificate$loadBalancerName": "<p>The load balancer name where your SSL/TLS certificate is attached.</p>", "LoadBalancerTlsCertificateSummary$name": "<p>The name of the SSL/TLS certificate.</p>", "MonitoredResourceInfo$name": "<p>The name of the Lightsail resource being monitored.</p>", "OpenInstancePublicPortsRequest$instanceName": "<p>The name of the instance for which to open ports.</p>", "Operation$resourceName": "<p>The resource name.</p>", "Origin$name": "<p>The name of the origin resource.</p>", "PasswordData$keyPairName": "<p>The name of the key pair that you used when creating your instance. If no key pair name was specified when creating the instance, <PERSON>ail uses the default key pair (<code>LightsailDefaultKeyPair</code>).</p> <p>If you are using a custom key pair, you need to use your own means of decrypting your password using the <code>ciphertext</code>. <PERSON><PERSON> creates the ciphertext by encrypting your password with the public key part of this key pair.</p>", "PutAlarmRequest$alarmName": "<p>The name for the alarm. Specify the name of an existing alarm to update, and overwrite the previous configuration of the alarm.</p>", "PutAlarmRequest$monitoredResourceName": "<p>The name of the Lightsail resource that will be monitored.</p> <p>Instances, load balancers, and relational databases are the only Lightsail resources that can currently be monitored by alarms.</p>", "PutInstancePublicPortsRequest$instanceName": "<p>The name of the instance for which to open ports.</p>", "RebootInstanceRequest$instanceName": "<p>The name of the instance to reboot.</p>", "RebootRelationalDatabaseRequest$relationalDatabaseName": "<p>The name of your database to reboot.</p>", "RelationalDatabase$name": "<p>The unique name of the database resource in Lightsail.</p>", "RelationalDatabaseEvent$resource": "<p>The database that the database event relates to.</p>", "RelationalDatabaseSnapshot$name": "<p>The name of the database snapshot.</p>", "ReleaseStaticIpRequest$staticIpName": "<p>The name of the static IP to delete.</p>", "ResetDistributionCacheRequest$distributionName": "<p>The name of the distribution for which to reset cache.</p> <p>Use the <code>GetDistributions</code> action to get a list of distribution names that you can specify.</p>", "ResourceNameList$member": null, "StartInstanceRequest$instanceName": "<p>The name of the instance (a virtual private server) to start.</p>", "StartRelationalDatabaseRequest$relationalDatabaseName": "<p>The name of your database to start.</p>", "StaticIp$name": "<p>The name of the static IP (e.g., <code>StaticIP-Ohio-EXAMPLE</code>).</p>", "StaticIp$attachedTo": "<p>The instance where the static IP is attached (e.g., <code>Amazon_Linux-1GB-Ohio-1</code>).</p>", "StopInstanceRequest$instanceName": "<p>The name of the instance (a virtual private server) to stop.</p>", "StopRelationalDatabaseRequest$relationalDatabaseName": "<p>The name of your database to stop.</p>", "StopRelationalDatabaseRequest$relationalDatabaseSnapshotName": "<p>The name of your new database snapshot to be created before stopping your database.</p>", "TagResourceRequest$resourceName": "<p>The name of the resource to which you are adding tags.</p>", "TestAlarmRequest$alarmName": "<p>The name of the alarm to test.</p>", "UntagResourceRequest$resourceName": "<p>The name of the resource from which you are removing a tag.</p>", "UpdateDistributionBundleRequest$distributionName": "<p>The name of the distribution for which to update the bundle.</p> <p>Use the <code>GetDistributions</code> action to get a list of distribution names that you can specify.</p>", "UpdateDistributionRequest$distributionName": "<p>The name of the distribution to update.</p> <p>Use the <code>GetDistributions</code> action to get a list of distribution names that you can specify.</p>", "UpdateLoadBalancerAttributeRequest$loadBalancerName": "<p>The name of the load balancer that you want to modify (e.g., <code>my-load-balancer</code>.</p>", "UpdateRelationalDatabaseParametersRequest$relationalDatabaseName": "<p>The name of your database for which to update parameters.</p>", "UpdateRelationalDatabaseRequest$relationalDatabaseName": "<p>The name of your database to update.</p>"}}, "ResourceNameList": {"base": null, "refs": {"AttachInstancesToLoadBalancerRequest$instanceNames": "<p>An array of strings representing the instance name(s) you want to attach to your load balancer.</p> <p>An instance must be <code>running</code> before you can attach it to your load balancer.</p> <p>There are no additional limits on the number of instances you can attach to your load balancer, aside from the limit of Lightsail instances you can create in your account (20).</p>", "DetachInstancesFromLoadBalancerRequest$instanceNames": "<p>An array of strings containing the names of the instances you want to detach from the load balancer.</p>"}}, "ResourceRecord": {"base": "<p>Describes the domain name system (DNS) records to add to your domain's DNS to validate it for an Amazon Lightsail certificate.</p>", "refs": {"DomainValidationRecord$resourceRecord": "<p>An object that describes the DNS records to add to your domain's DNS to validate it for the certificate.</p>"}}, "ResourceType": {"base": null, "refs": {"Alarm$resourceType": "<p>The Lightsail resource type (e.g., <code>Alarm</code>).</p>", "CloudFormationStackRecord$resourceType": "<p>The Lightsail resource type (e.g., <code>CloudFormationStackRecord</code>).</p>", "ContactMethod$resourceType": "<p>The Lightsail resource type (e.g., <code>ContactMethod</code>).</p>", "Disk$resourceType": "<p>The Lightsail resource type (e.g., <code>Disk</code>).</p>", "DiskSnapshot$resourceType": "<p>The Lightsail resource type (e.g., <code>DiskSnapshot</code>).</p>", "Domain$resourceType": "<p>The resource type. </p>", "ExportSnapshotRecord$resourceType": "<p>The Lightsail resource type (e.g., <code>ExportSnapshotRecord</code>).</p>", "GetAutoSnapshotsResult$resourceType": "<p>The resource type (e.g., <code>Instance</code> or <code>Disk</code>).</p>", "Instance$resourceType": "<p>The type of resource (usually <code>Instance</code>).</p>", "InstanceSnapshot$resourceType": "<p>The type of resource (usually <code>InstanceSnapshot</code>).</p>", "KeyPair$resourceType": "<p>The resource type (usually <code>KeyPair</code>).</p>", "LightsailDistribution$resourceType": "<p>The Lightsail resource type (e.g., <code>Distribution</code>).</p>", "LoadBalancer$resourceType": "<p>The resource type (e.g., <code>LoadBalancer</code>.</p>", "LoadBalancerTlsCertificate$resourceType": "<p>The resource type (e.g., <code>LoadBalancerTlsCertificate</code>).</p> <ul> <li> <p> <b> <code>Instance</code> </b> - A Lightsail instance (a virtual private server)</p> </li> <li> <p> <b> <code>StaticIp</code> </b> - A static IP address</p> </li> <li> <p> <b> <code>KeyPair</code> </b> - The key pair used to connect to a Lightsail instance</p> </li> <li> <p> <b> <code>InstanceSnapshot</code> </b> - A Lightsail instance snapshot</p> </li> <li> <p> <b> <code>Domain</code> </b> - A DNS zone</p> </li> <li> <p> <b> <code>PeeredVpc</code> </b> - A peered VPC</p> </li> <li> <p> <b> <code>LoadBalancer</code> </b> - A Lightsail load balancer</p> </li> <li> <p> <b> <code>LoadBalancerTlsCertificate</code> </b> - An SSL/TLS certificate associated with a Lightsail load balancer</p> </li> <li> <p> <b> <code>Disk</code> </b> - A Lightsail block storage disk</p> </li> <li> <p> <b> <code>DiskSnapshot</code> </b> - A block storage disk snapshot</p> </li> </ul>", "MonitoredResourceInfo$resourceType": "<p>The Lightsail resource type of the resource being monitored.</p> <p>Instances, load balancers, and relational databases are the only Lightsail resources that can currently be monitored by alarms.</p>", "Operation$resourceType": "<p>The resource type. </p>", "Origin$resourceType": "<p>The resource type of the origin resource (e.g., <i>Instance</i>).</p>", "RelationalDatabase$resourceType": "<p>The Lightsail resource type for the database (for example, <code>RelationalDatabase</code>).</p>", "RelationalDatabaseSnapshot$resourceType": "<p>The Lightsail resource type.</p>", "StaticIp$resourceType": "<p>The resource type (usually <code>StaticIp</code>).</p>"}}, "RevocationReason": {"base": null, "refs": {"Certificate$revocationReason": "<p>The reason the certificate was revoked. This value is present only when the certificate status is <code>REVOKED</code>.</p>"}}, "SendContactMethodVerificationRequest": {"base": null, "refs": {}}, "SendContactMethodVerificationResult": {"base": null, "refs": {}}, "SensitiveString": {"base": null, "refs": {"CreateRelationalDatabaseRequest$masterUserPassword": "<p>The password for the master user of your new database. The password can include any printable ASCII character except \"/\", \"\"\", or \"@\".</p> <p>Constraints: Must contain 8 to 41 characters.</p>", "GetRelationalDatabaseMasterUserPasswordResult$masterUserPassword": "<p>The master user password for the <code>password version</code> specified.</p>", "UpdateRelationalDatabaseRequest$masterUserPassword": "<p>The password for the master user of your database. The password can include any printable ASCII character except \"/\", \"\"\", or \"@\".</p> <p>Constraints: Must contain 8 to 41 characters.</p>"}}, "SerialNumber": {"base": null, "refs": {"Certificate$serialNumber": "<p>The serial number of the certificate.</p>"}}, "ServiceException": {"base": "<p>A general service exception.</p>", "refs": {}}, "StartInstanceRequest": {"base": null, "refs": {}}, "StartInstanceResult": {"base": null, "refs": {}}, "StartRelationalDatabaseRequest": {"base": null, "refs": {}}, "StartRelationalDatabaseResult": {"base": null, "refs": {}}, "StaticIp": {"base": "<p>Describes the static IP.</p>", "refs": {"GetStaticIpResult$staticIp": "<p>An array of key-value pairs containing information about the requested static IP.</p>", "StaticIpList$member": null}}, "StaticIpList": {"base": null, "refs": {"GetStaticIpsResult$staticIps": "<p>An array of key-value pairs containing information about your get static IPs request.</p>"}}, "StopInstanceRequest": {"base": null, "refs": {}}, "StopInstanceResult": {"base": null, "refs": {}}, "StopRelationalDatabaseRequest": {"base": null, "refs": {}}, "StopRelationalDatabaseResult": {"base": null, "refs": {}}, "StringList": {"base": null, "refs": {"CookieObject$cookiesAllowList": "<p>The specific cookies to forward to your distribution's origin.</p>", "CreateInstancesFromSnapshotRequest$instanceNames": "<p>The names for your new instances.</p>", "CreateInstancesRequest$instanceNames": "<p>The names to use for your new Lightsail instances. Separate multiple values using quotation marks and commas, for example: <code>[\"MyFirstInstance\",\"MySecondInstance\"]</code> </p>", "GetActiveNamesResult$activeNames": "<p>The list of active names returned by the get active names request.</p>", "GetRelationalDatabaseLogStreamsResult$logStreams": "<p>An object describing the result of your get relational database log streams request.</p>", "InstancePortInfo$cidrs": "<p>The IP address, or range of IP addresses in CIDR notation, that are allowed to connect to an instance through the ports, and the protocol. Lightsail supports IPv4 addresses.</p> <p>For more information about CIDR block notation, see <a href=\"https://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing#CIDR_notation\">Classless Inter-Domain Routing</a> on <i>Wikipedia</i>.</p>", "InstancePortInfo$cidrListAliases": "<p>An alias that defines access for a preconfigured range of IP addresses.</p> <p>The only alias currently supported is <code>lightsail-connect</code>, which allows IP addresses of the browser-based RDP/SSH client in the Lightsail console to connect to your instance.</p>", "InstancePortState$cidrs": "<p>The IP address, or range of IP addresses in CIDR notation, that are allowed to connect to an instance through the ports, and the protocol. Lightsail supports IPv4 addresses.</p> <p>For more information about CIDR block notation, see <a href=\"https://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing#CIDR_notation\">Classless Inter-Domain Routing</a> on <i>Wikipedia</i>.</p>", "InstancePortState$cidrListAliases": "<p>An alias that defines access for a preconfigured range of IP addresses.</p> <p>The only alias currently supported is <code>lightsail-connect</code>, which allows IP addresses of the browser-based RDP/SSH client in the Lightsail console to connect to your instance.</p>", "LightsailDistribution$alternativeDomainNames": "<p>The alternate domain names of the distribution.</p>", "LoadBalancerTlsCertificate$subjectAlternativeNames": "<p>An array of strings that specify the alternate domains (e.g., <code>example2.com</code>) and subdomains (e.g., <code>blog.example.com</code>) for the certificate.</p>", "PortInfo$cidrs": "<p>The IP address, or range of IP addresses in CIDR notation, that are allowed to connect to an instance through the ports, and the protocol. Lightsail supports IPv4 addresses.</p> <p>Examples:</p> <ul> <li> <p>To allow the IP address <code>**********</code>, specify <code>**********</code> or <code>**********/32</code>. </p> </li> <li> <p>To allow the IP addresses <code>*********</code> to <code>***********</code>, specify <code>*********/24</code>.</p> </li> </ul> <p>For more information about CIDR block notation, see <a href=\"https://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing#CIDR_notation\">Classless Inter-Domain Routing</a> on <i>Wikipedia</i>.</p>", "PortInfo$cidrListAliases": "<p>An alias that defines access for a preconfigured range of IP addresses.</p> <p>The only alias currently supported is <code>lightsail-connect</code>, which allows IP addresses of the browser-based RDP/SSH client in the Lightsail console to connect to your instance.</p>", "QueryStringObject$queryStringsAllowList": "<p>The specific query strings that the distribution forwards to the origin.</p> <p>Your distribution will cache content based on the specified query strings.</p> <p>If the <code>option</code> parameter is true, then your distribution forwards all query strings, regardless of what you specify using the <code>queryStringsAllowList</code> parameter.</p>", "RelationalDatabaseEvent$eventCategories": "<p>The category that the database event belongs to.</p>"}}, "StringMax256": {"base": null, "refs": {"CreateContactMethodRequest$contactEndpoint": "<p>The destination of the contact method, such as an email address or a mobile phone number.</p> <p>Use the E.164 format when specifying a mobile phone number. E.164 is a standard for the phone number structure used for international telecommunication. Phone numbers that follow this format can have a maximum of 15 digits, and they are prefixed with the plus character (+) and the country code. For example, a U.S. phone number in E.164 format would be specified as +1XXX5550100. For more information, see <a href=\"https://en.wikipedia.org/wiki/E.164\">E.164</a> on <i>Wikipedia</i>.</p>", "UpdateLoadBalancerAttributeRequest$attributeValue": "<p>The value that you want to specify for the attribute name.</p>"}}, "SubjectAlternativeNameList": {"base": null, "refs": {"Certificate$subjectAlternativeNames": "<p>An array of strings that specify the alternate domains (e.g., <code>example2.com</code>) and subdomains (e.g., <code>blog.example.com</code>) of the certificate.</p>", "CreateCertificateRequest$subjectAlternativeNames": "<p>An array of strings that specify the alternate domains (e.g., <code>example2.com</code>) and subdomains (e.g., <code>blog.example.com</code>) for the certificate.</p> <p>You can specify a maximum of nine alternate domains (in addition to the primary domain name).</p> <p>Wildcard domain entries (e.g., <code>*.example.com</code>) are not supported.</p>"}}, "Tag": {"base": "<p>Describes a tag key and optional value assigned to an Amazon Lightsail resource.</p> <p>For more information about tags in Lightsail, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-tags\">Lightsail Dev Guide</a>.</p>", "refs": {"TagList$member": null}}, "TagKey": {"base": null, "refs": {"Tag$key": "<p>The key of the tag.</p> <p>Constraints: Tag keys accept a maximum of 128 letters, numbers, spaces in UTF-8, or the following characters: + - = . _ : / @</p>", "TagKeyList$member": null}}, "TagKeyList": {"base": null, "refs": {"UntagResourceRequest$tagKeys": "<p>The tag keys to delete from the specified resource.</p>"}}, "TagList": {"base": null, "refs": {"Certificate$tags": "<p>The tag keys and optional values for the resource. For more information about tags in Lightsail, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-tags\">Lightsail Dev Guide</a>.</p>", "CertificateSummary$tags": "<p>The tag keys and optional values for the resource. For more information about tags in Lightsail, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-tags\">Lightsail Dev Guide</a>.</p>", "CreateCertificateRequest$tags": "<p>The tag keys and optional values to add to the certificate during create.</p> <p>Use the <code>TagResource</code> action to tag a resource after it's created.</p>", "CreateDiskFromSnapshotRequest$tags": "<p>The tag keys and optional values to add to the resource during create.</p> <p>Use the <code>TagResource</code> action to tag a resource after it's created.</p>", "CreateDiskRequest$tags": "<p>The tag keys and optional values to add to the resource during create.</p> <p>Use the <code>TagResource</code> action to tag a resource after it's created.</p>", "CreateDiskSnapshotRequest$tags": "<p>The tag keys and optional values to add to the resource during create.</p> <p>Use the <code>TagResource</code> action to tag a resource after it's created.</p>", "CreateDistributionRequest$tags": "<p>The tag keys and optional values to add to the distribution during create.</p> <p>Use the <code>TagResource</code> action to tag a resource after it's created.</p>", "CreateDomainRequest$tags": "<p>The tag keys and optional values to add to the resource during create.</p> <p>Use the <code>TagResource</code> action to tag a resource after it's created.</p>", "CreateInstanceSnapshotRequest$tags": "<p>The tag keys and optional values to add to the resource during create.</p> <p>Use the <code>TagResource</code> action to tag a resource after it's created.</p>", "CreateInstancesFromSnapshotRequest$tags": "<p>The tag keys and optional values to add to the resource during create.</p> <p>Use the <code>TagResource</code> action to tag a resource after it's created.</p>", "CreateInstancesRequest$tags": "<p>The tag keys and optional values to add to the resource during create.</p> <p>Use the <code>TagResource</code> action to tag a resource after it's created.</p>", "CreateKeyPairRequest$tags": "<p>The tag keys and optional values to add to the resource during create.</p> <p>Use the <code>TagResource</code> action to tag a resource after it's created.</p>", "CreateLoadBalancerRequest$tags": "<p>The tag keys and optional values to add to the resource during create.</p> <p>Use the <code>TagResource</code> action to tag a resource after it's created.</p>", "CreateLoadBalancerTlsCertificateRequest$tags": "<p>The tag keys and optional values to add to the resource during create.</p> <p>Use the <code>TagResource</code> action to tag a resource after it's created.</p>", "CreateRelationalDatabaseFromSnapshotRequest$tags": "<p>The tag keys and optional values to add to the resource during create.</p> <p>Use the <code>TagResource</code> action to tag a resource after it's created.</p>", "CreateRelationalDatabaseRequest$tags": "<p>The tag keys and optional values to add to the resource during create.</p> <p>Use the <code>TagResource</code> action to tag a resource after it's created.</p>", "CreateRelationalDatabaseSnapshotRequest$tags": "<p>The tag keys and optional values to add to the resource during create.</p> <p>Use the <code>TagResource</code> action to tag a resource after it's created.</p>", "Disk$tags": "<p>The tag keys and optional values for the resource. For more information about tags in Lightsail, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-tags\">Lightsail Dev Guide</a>.</p>", "DiskSnapshot$tags": "<p>The tag keys and optional values for the resource. For more information about tags in Lightsail, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-tags\">Lightsail Dev Guide</a>.</p>", "Domain$tags": "<p>The tag keys and optional values for the resource. For more information about tags in Lightsail, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-tags\">Lightsail Dev Guide</a>.</p>", "Instance$tags": "<p>The tag keys and optional values for the resource. For more information about tags in Lightsail, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-tags\">Lightsail Dev Guide</a>.</p>", "InstanceSnapshot$tags": "<p>The tag keys and optional values for the resource. For more information about tags in Lightsail, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-tags\">Lightsail Dev Guide</a>.</p>", "KeyPair$tags": "<p>The tag keys and optional values for the resource. For more information about tags in Lightsail, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-tags\">Lightsail Dev Guide</a>.</p>", "LightsailDistribution$tags": "<p>The tag keys and optional values for the resource. For more information about tags in Lightsail, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-tags\">Lightsail Dev Guide</a>.</p>", "LoadBalancer$tags": "<p>The tag keys and optional values for the resource. For more information about tags in Lightsail, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-tags\">Lightsail Dev Guide</a>.</p>", "LoadBalancerTlsCertificate$tags": "<p>The tag keys and optional values for the resource. For more information about tags in Lightsail, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-tags\">Lightsail Dev Guide</a>.</p>", "RelationalDatabase$tags": "<p>The tag keys and optional values for the resource. For more information about tags in Lightsail, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-tags\">Lightsail Dev Guide</a>.</p>", "RelationalDatabaseSnapshot$tags": "<p>The tag keys and optional values for the resource. For more information about tags in Lightsail, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en/articles/amazon-lightsail-tags\">Lightsail Dev Guide</a>.</p>", "TagResourceRequest$tags": "<p>The tag key and optional value.</p>"}}, "TagResourceRequest": {"base": null, "refs": {}}, "TagResourceResult": {"base": null, "refs": {}}, "TagValue": {"base": null, "refs": {"Tag$value": "<p>The value of the tag.</p> <p>Constraints: Tag values accept a maximum of 256 letters, numbers, spaces in UTF-8, or the following characters: + - = . _ : / @</p>"}}, "TestAlarmRequest": {"base": null, "refs": {}}, "TestAlarmResult": {"base": null, "refs": {}}, "TimeOfDay": {"base": null, "refs": {"AddOn$snapshotTimeOfDay": "<p>The daily time when an automatic snapshot is created.</p> <p>The time shown is in <code>HH:00</code> format, and in Coordinated Universal Time (UTC).</p> <p>The snapshot is automatically created between the time shown and up to 45 minutes after.</p>", "AddOn$nextSnapshotTimeOfDay": "<p>The next daily time an automatic snapshot will be created.</p> <p>The time shown is in <code>HH:00</code> format, and in Coordinated Universal Time (UTC).</p> <p>The snapshot is automatically created between the time shown and up to 45 minutes after.</p>", "AutoSnapshotAddOnRequest$snapshotTimeOfDay": "<p>The daily time when an automatic snapshot will be created.</p> <p>Constraints:</p> <ul> <li> <p>Must be in <code>HH:00</code> format, and in an hourly increment.</p> </li> <li> <p>Specified in Coordinated Universal Time (UTC).</p> </li> <li> <p>The snapshot will be automatically created between the time specified and up to 45 minutes after.</p> </li> </ul>"}}, "TreatMissingData": {"base": null, "refs": {"Alarm$treatMissingData": "<p>Specifies how the alarm handles missing data points.</p> <p>An alarm can treat missing data in the following ways:</p> <ul> <li> <p> <code>breaching</code> - Assume the missing data is not within the threshold. Missing data counts towards the number of times the metric is not within the threshold.</p> </li> <li> <p> <code>notBreaching</code> - Assume the missing data is within the threshold. Missing data does not count towards the number of times the metric is not within the threshold.</p> </li> <li> <p> <code>ignore</code> - Ignore the missing data. Maintains the current alarm state.</p> </li> <li> <p> <code>missing</code> - Missing data is treated as missing.</p> </li> </ul>", "PutAlarmRequest$treatMissingData": "<p>Sets how this alarm will handle missing data points.</p> <p>An alarm can treat missing data in the following ways:</p> <ul> <li> <p> <code>breaching</code> - Assume the missing data is not within the threshold. Missing data counts towards the number of times the metric is not within the threshold.</p> </li> <li> <p> <code>notBreaching</code> - Assume the missing data is within the threshold. Missing data does not count towards the number of times the metric is not within the threshold.</p> </li> <li> <p> <code>ignore</code> - Ignore the missing data. Maintains the current alarm state.</p> </li> <li> <p> <code>missing</code> - Missing data is treated as missing.</p> </li> </ul> <p>If <code>treatMissingData</code> is not specified, the default behavior of <code>missing</code> is used.</p>"}}, "UnauthenticatedException": {"base": "<p><PERSON><PERSON> throws this exception when the user has not been authenticated.</p>", "refs": {}}, "UnpeerVpcRequest": {"base": null, "refs": {}}, "UnpeerVpcResult": {"base": null, "refs": {}}, "UntagResourceRequest": {"base": null, "refs": {}}, "UntagResourceResult": {"base": null, "refs": {}}, "UpdateDistributionBundleRequest": {"base": null, "refs": {}}, "UpdateDistributionBundleResult": {"base": null, "refs": {}}, "UpdateDistributionRequest": {"base": null, "refs": {}}, "UpdateDistributionResult": {"base": null, "refs": {}}, "UpdateDomainEntryRequest": {"base": null, "refs": {}}, "UpdateDomainEntryResult": {"base": null, "refs": {}}, "UpdateLoadBalancerAttributeRequest": {"base": null, "refs": {}}, "UpdateLoadBalancerAttributeResult": {"base": null, "refs": {}}, "UpdateRelationalDatabaseParametersRequest": {"base": null, "refs": {}}, "UpdateRelationalDatabaseParametersResult": {"base": null, "refs": {}}, "UpdateRelationalDatabaseRequest": {"base": null, "refs": {}}, "UpdateRelationalDatabaseResult": {"base": null, "refs": {}}, "boolean": {"base": null, "refs": {"Alarm$notificationEnabled": "<p>Indicates whether the alarm is enabled.</p>", "Blueprint$isActive": "<p>A Boolean value indicating whether the blueprint is active. Inactive blueprints are listed to support customers with existing instances but are not necessarily available for launch of new instances. Blueprints are marked inactive when they become outdated due to operating system updates or new application releases.</p>", "Bundle$isActive": "<p>A Boolean value indicating whether the bundle is active.</p>", "CopySnapshotRequest$useLatestRestorableAutoSnapshot": "<p>A Boolean value to indicate whether to use the latest available automatic snapshot of the specified source instance or disk.</p> <p>Constraints:</p> <ul> <li> <p>This parameter cannot be defined together with the <code>restore date</code> parameter. The <code>use latest restorable auto snapshot</code> and <code>restore date</code> parameters are mutually exclusive.</p> </li> <li> <p>Define this parameter only when copying an automatic snapshot as a manual snapshot. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en_us/articles/amazon-lightsail-keeping-automatic-snapshots\">Lightsail Dev Guide</a>.</p> </li> </ul>", "CreateDiskFromSnapshotRequest$useLatestRestorableAutoSnapshot": "<p>A Boolean value to indicate whether to use the latest available automatic snapshot.</p> <p>Constraints:</p> <ul> <li> <p>This parameter cannot be defined together with the <code>restore date</code> parameter. The <code>use latest restorable auto snapshot</code> and <code>restore date</code> parameters are mutually exclusive.</p> </li> <li> <p>Define this parameter only when creating a new disk from an automatic snapshot. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en_us/articles/amazon-lightsail-configuring-automatic-snapshots\">Lightsail Dev Guide</a>.</p> </li> </ul>", "CreateInstancesFromSnapshotRequest$useLatestRestorableAutoSnapshot": "<p>A Boolean value to indicate whether to use the latest available automatic snapshot.</p> <p>Constraints:</p> <ul> <li> <p>This parameter cannot be defined together with the <code>restore date</code> parameter. The <code>use latest restorable auto snapshot</code> and <code>restore date</code> parameters are mutually exclusive.</p> </li> <li> <p>Define this parameter only when creating a new instance from an automatic snapshot. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en_us/articles/amazon-lightsail-configuring-automatic-snapshots\">Lightsail Dev Guide</a>.</p> </li> </ul>", "CreateRelationalDatabaseFromSnapshotRequest$publiclyAccessible": "<p>Specifies the accessibility options for your new database. A value of <code>true</code> specifies a database that is available to resources outside of your Lightsail account. A value of <code>false</code> specifies a database that is available only to your Lightsail resources in the same region as your database.</p>", "CreateRelationalDatabaseFromSnapshotRequest$useLatestRestorableTime": "<p>Specifies whether your database is restored from the latest backup time. A value of <code>true</code> restores from the latest backup time. </p> <p>Default: <code>false</code> </p> <p>Constraints: Cannot be specified if the <code>restore time</code> parameter is provided.</p>", "CreateRelationalDatabaseRequest$publiclyAccessible": "<p>Specifies the accessibility options for your new database. A value of <code>true</code> specifies a database that is available to resources outside of your Lightsail account. A value of <code>false</code> specifies a database that is available only to your Lightsail resources in the same region as your database.</p>", "DeleteDiskRequest$forceDeleteAddOns": "<p>A Boolean value to indicate whether to delete the enabled add-ons for the disk.</p>", "DeleteInstanceRequest$forceDeleteAddOns": "<p>A Boolean value to indicate whether to delete the enabled add-ons for the disk.</p>", "DeleteLoadBalancerTlsCertificateRequest$force": "<p>When <code>true</code>, forces the deletion of an SSL/TLS certificate.</p> <p>There can be two certificates associated with a Lightsail load balancer: the primary and the backup. The <code>force</code> parameter is required when the primary SSL/TLS certificate is in use by an instance attached to the load balancer.</p>", "DeleteRelationalDatabaseRequest$skipFinalSnapshot": "<p>Determines whether a final database snapshot is created before your database is deleted. If <code>true</code> is specified, no database snapshot is created. If <code>false</code> is specified, a database snapshot is created before your database is deleted.</p> <p>You must specify the <code>final relational database snapshot name</code> parameter if the <code>skip final snapshot</code> parameter is <code>false</code>.</p> <p>Default: <code>false</code> </p>", "Disk$isSystemDisk": "<p>A Boolean value indicating whether this disk is a system disk (has an operating system loaded on it).</p>", "Disk$isAttached": "<p>A Boolean value indicating whether the disk is attached.</p>", "DiskInfo$isSystemDisk": "<p>A Boolean value indicating whether this disk is a system disk (has an operating system loaded on it).</p>", "DiskSnapshot$isFromAutoSnapshot": "<p>A Boolean value indicating whether the snapshot was created from an automatic snapshot.</p>", "DistributionBundle$isActive": "<p>Indicates whether the bundle is active, and can be specified for a new distribution.</p>", "DomainEntry$isAlias": "<p>When <code>true</code>, specifies whether the domain entry is an alias used by the Lightsail load balancer. You can include an alias (A type) record in your request, which points to a load balancer DNS name and routes traffic to your load balancer</p>", "GetBlueprintsRequest$includeInactive": "<p>A Boolean value indicating whether to include inactive results in your request.</p>", "GetBundlesRequest$includeInactive": "<p>A Boolean value that indicates whether to include inactive bundle results in your request.</p>", "GetRegionsRequest$includeAvailabilityZones": "<p>A Boolean value indicating whether to also include Availability Zones in your get regions request. Availability Zones are indicated with a letter: e.g., <code>us-east-2a</code>.</p>", "GetRegionsRequest$includeRelationalDatabaseAvailabilityZones": "<p>&gt;A Boolean value indicating whether to also include Availability Zones for databases in your get regions request. Availability Zones are indicated with a letter (e.g., <code>us-east-2a</code>).</p>", "GetRelationalDatabaseLogEventsRequest$startFromHead": "<p>Parameter to specify if the log should start from head or tail. If <code>true</code> is specified, the log event starts from the head of the log. If <code>false</code> is specified, the log event starts from the tail of the log.</p> <note> <p>For PostgreSQL, the default value of <code>false</code> is the only option available.</p> </note>", "Instance$isStaticIp": "<p>A Boolean value indicating whether this instance has a static IP assigned to it.</p>", "InstanceSnapshot$isFromAutoSnapshot": "<p>A Boolean value indicating whether the snapshot was created from an automatic snapshot.</p>", "IsVpcPeeredResult$isPeered": "<p>Returns <code>true</code> if the Lightsail VPC is peered; otherwise, <code>false</code>.</p>", "LightsailDistribution$isEnabled": "<p>Indicates whether the distribution is enabled.</p>", "LightsailDistribution$ableToUpdateBundle": "<p>Indicates whether the bundle that is currently applied to your distribution, specified using the <code>distributionName</code> parameter, can be changed to another bundle.</p> <p>Use the <code>UpdateDistributionBundle</code> action to change your distribution's bundle.</p>", "LoadBalancerTlsCertificate$isAttached": "<p>When <code>true</code>, the SSL/TLS certificate is attached to the Lightsail load balancer.</p>", "LoadBalancerTlsCertificateSummary$isAttached": "<p>When <code>true</code>, the SSL/TLS certificate is attached to the Lightsail load balancer.</p>", "Operation$isTerminal": "<p>A Boolean value indicating whether the operation is terminal.</p>", "PendingModifiedRelationalDatabaseValues$backupRetentionEnabled": "<p>A Boolean value indicating whether automated backup retention is enabled.</p>", "PutAlarmRequest$notificationEnabled": "<p>Indicates whether the alarm is enabled.</p> <p>Notifications are enabled by default if you don't specify this parameter.</p>", "QueryStringObject$option": "<p>Indicates whether the distribution forwards and caches based on query strings.</p>", "RelationalDatabase$backupRetentionEnabled": "<p>A Boolean value indicating whether automated backup retention is enabled for the database.</p>", "RelationalDatabase$publiclyAccessible": "<p>A Boolean value indicating whether the database is publicly accessible.</p>", "RelationalDatabaseBlueprint$isEngineDefault": "<p>A Boolean value indicating whether the engine version is the default for the database blueprint.</p>", "RelationalDatabaseBundle$isEncrypted": "<p>A Boolean value indicating whether the database bundle is encrypted.</p>", "RelationalDatabaseBundle$isActive": "<p>A Boolean value indicating whether the database bundle is active.</p>", "RelationalDatabaseParameter$isModifiable": "<p>A Boolean value indicating whether the parameter can be modified.</p>", "StaticIp$isAttached": "<p>A Boolean value indicating whether the static IP is attached.</p>", "StopInstanceRequest$force": "<p>When set to <code>True</code>, forces a Lightsail instance that is stuck in a <code>stopping</code> state to stop.</p> <important> <p>Only use the <code>force</code> parameter if your instance is stuck in the <code>stopping</code> state. In any other state, your instance should stop normally without adding this parameter to your API request.</p> </important>", "UpdateDistributionRequest$isEnabled": "<p>Indicates whether to enable the distribution.</p>", "UpdateRelationalDatabaseRequest$rotateMasterUserPassword": "<p>When <code>true</code>, the master user password is changed to a new strong password generated by Lightsail.</p> <p>Use the <code>get relational database master user password</code> operation to get the new password.</p>", "UpdateRelationalDatabaseRequest$enableBackupRetention": "<p>When <code>true</code>, enables automated backup retention for your database.</p> <p>Updates are applied during the next maintenance window because this can result in an outage.</p>", "UpdateRelationalDatabaseRequest$disableBackupRetention": "<p>When <code>true</code>, disables automated backup retention for your database.</p> <p>Disabling backup retention deletes all automated database backups. Before disabling this, you may want to create a snapshot of your database using the <code>create relational database snapshot</code> operation.</p> <p>Updates are applied during the next maintenance window because this can result in an outage.</p>", "UpdateRelationalDatabaseRequest$publiclyAccessible": "<p>Specifies the accessibility options for your database. A value of <code>true</code> specifies a database that is available to resources outside of your Lightsail account. A value of <code>false</code> specifies a database that is available only to your Lightsail resources in the same region as your database.</p>", "UpdateRelationalDatabaseRequest$applyImmediately": "<p>When <code>true</code>, applies changes immediately. When <code>false</code>, applies changes during the preferred maintenance window. Some changes may cause an outage.</p> <p>Default: <code>false</code> </p>"}}, "double": {"base": null, "refs": {"Alarm$threshold": "<p>The value against which the specified statistic is compared.</p>", "MetricDatapoint$average": "<p>The average.</p>", "MetricDatapoint$maximum": "<p>The maximum.</p>", "MetricDatapoint$minimum": "<p>The minimum.</p>", "MetricDatapoint$sampleCount": "<p>The sample count.</p>", "MetricDatapoint$sum": "<p>The sum.</p>", "PutAlarmRequest$threshold": "<p>The value against which the specified statistic is compared.</p>"}}, "float": {"base": null, "refs": {"Bundle$price": "<p>The price in US dollars (e.g., <code>5.0</code>) of the bundle.</p>", "Bundle$ramSizeInGb": "<p>The amount of RAM in GB (e.g., <code>2.0</code>).</p>", "DistributionBundle$price": "<p>The monthly price, in US dollars, of the bundle.</p>", "InstanceHardware$ramSizeInGb": "<p>The amount of RAM in GB on the instance (e.g., <code>1.0</code>).</p>", "RelationalDatabaseBundle$price": "<p>The cost of the database bundle in US currency.</p>", "RelationalDatabaseBundle$ramSizeInGb": "<p>The amount of RAM in GB (for example, <code>2.0</code>) for the database bundle.</p>", "RelationalDatabaseHardware$ramSizeInGb": "<p>The amount of RAM in GB for the database.</p>"}}, "integer": {"base": null, "refs": {"Alarm$evaluationPeriods": "<p>The number of periods over which data is compared to the specified threshold.</p>", "Alarm$datapointsToAlarm": "<p>The number of data points that must not within the specified threshold to trigger the alarm.</p>", "AttachedDisk$sizeInGb": "<p>The size of the disk in GB.</p>", "Blueprint$minPower": "<p>The minimum bundle power required to run this blueprint. For example, you need a bundle with a power value of 500 or more to create an instance that uses a blueprint with a minimum power value of 500. <code>0</code> indicates that the blueprint runs on all instance sizes. </p>", "Bundle$cpuCount": "<p>The number of vCPUs included in the bundle (e.g., <code>2</code>).</p>", "Bundle$diskSizeInGb": "<p>The size of the SSD (e.g., <code>30</code>).</p>", "Bundle$power": "<p>A numeric value that represents the power of the bundle (e.g., <code>500</code>). You can use the bundle's power value in conjunction with a blueprint's minimum power value to determine whether the blueprint will run on the bundle. For example, you need a bundle with a power value of 500 or more to create an instance that uses a blueprint with a minimum power value of 500.</p>", "Bundle$transferPerMonthInGb": "<p>The data transfer rate per month in GB (e.g., <code>2000</code>).</p>", "CreateDiskFromSnapshotRequest$sizeInGb": "<p>The size of the disk in GB (e.g., <code>32</code>).</p>", "CreateDiskRequest$sizeInGb": "<p>The size of the disk in GB (e.g., <code>32</code>).</p>", "Disk$sizeInGb": "<p>The size of the disk in GB.</p>", "Disk$iops": "<p>The input/output operations per second (IOPS) of the disk.</p>", "Disk$gbInUse": "<p>(Deprecated) The number of GB in use by the disk.</p> <note> <p>In releases prior to November 14, 2017, this parameter was not included in the API response. It is now deprecated.</p> </note>", "DiskInfo$sizeInGb": "<p>The size of the disk in GB (e.g., <code>32</code>).</p>", "DiskSnapshot$sizeInGb": "<p>The size of the disk in GB.</p>", "DiskSnapshotInfo$sizeInGb": "<p>The size of the disk in GB (e.g., <code>32</code>).</p>", "DistributionBundle$transferPerMonthInGb": "<p>The monthly network transfer quota of the bundle.</p>", "GetRelationalDatabaseEventsRequest$durationInMinutes": "<p>The number of minutes in the past from which to retrieve events. For example, to get all events from the past 2 hours, enter 120.</p> <p>Default: <code>60</code> </p> <p>The minimum is 1 and the maximum is 14 days (20160 minutes).</p>", "InstanceHardware$cpuCount": "<p>The number of vCPUs the instance has.</p>", "InstanceSnapshot$sizeInGb": "<p>The size in GB of the SSD.</p>", "InstanceState$code": "<p>The status code for the instance.</p>", "LoadBalancer$instancePort": "<p>The port where the load balancer will direct traffic to your Lightsail instances. For HTTP traffic, it's port 80. For HTTPS traffic, it's port 443.</p>", "MonthlyTransfer$gbPerMonthAllocated": "<p>The amount allocated per month (in GB).</p>", "PutAlarmRequest$evaluationPeriods": "<p>The number of most recent periods over which data is compared to the specified threshold. If you are setting an \"M out of N\" alarm, this value (<code>evaluationPeriods</code>) is the N.</p> <p>If you are setting an alarm that requires that a number of consecutive data points be breaching to trigger the alarm, this value specifies the rolling period of time in which data points are evaluated.</p> <p>Each evaluation period is five minutes long. For example, specify an evaluation period of 24 to evaluate a metric over a rolling period of two hours.</p> <p>You can specify a minimum valuation period of 1 (5 minutes), and a maximum evaluation period of 288 (24 hours).</p>", "PutAlarmRequest$datapointsToAlarm": "<p>The number of data points that must be not within the specified threshold to trigger the alarm. If you are setting an \"M out of N\" alarm, this value (<code>datapointsToAlarm</code>) is the M.</p>", "RelationalDatabaseBundle$diskSizeInGb": "<p>The size of the disk for the database bundle.</p>", "RelationalDatabaseBundle$transferPerMonthInGb": "<p>The data transfer rate per month in GB for the database bundle.</p>", "RelationalDatabaseBundle$cpuCount": "<p>The number of virtual CPUs (vCPUs) for the database bundle.</p>", "RelationalDatabaseEndpoint$port": "<p>Specifies the port that the database is listening on.</p>", "RelationalDatabaseHardware$cpuCount": "<p>The number of vCPUs for the database.</p>", "RelationalDatabaseHardware$diskSizeInGb": "<p>The size of the disk for the database.</p>", "RelationalDatabaseSnapshot$sizeInGb": "<p>The size of the disk in GB (for example, <code>32</code>) for the database snapshot.</p>"}}, "long": {"base": null, "refs": {"CacheSettings$defaultTTL": "<p>The default amount of time that objects stay in the distribution's cache before the distribution forwards another request to the origin to determine whether the content has been updated.</p> <note> <p>The value specified applies only when the origin does not add HTTP headers such as <code>Cache-Control max-age</code>, <code>Cache-Control s-maxage</code>, and <code>Expires</code> to objects.</p> </note>", "CacheSettings$minimumTTL": "<p>The minimum amount of time that objects stay in the distribution's cache before the distribution forwards another request to the origin to determine whether the object has been updated.</p> <p>A value of <code>0</code> must be specified for <code>minimumTTL</code> if the distribution is configured to forward all headers to the origin.</p>", "CacheSettings$maximumTTL": "<p>The maximum amount of time that objects stay in the distribution's cache before the distribution forwards another request to the origin to determine whether the object has been updated.</p> <p>The value specified applies only when the origin adds HTTP headers such as <code>Cache-Control max-age</code>, <code>Cache-Control s-maxage</code>, and <code>Expires</code> to objects.</p>"}}, "string": {"base": null, "refs": {"AccessDeniedException$code": null, "AccessDeniedException$docs": null, "AccessDeniedException$message": null, "AccessDeniedException$tip": null, "AccountSetupInProgressException$code": null, "AccountSetupInProgressException$docs": null, "AccountSetupInProgressException$message": null, "AccountSetupInProgressException$tip": null, "AddOn$name": "<p>The name of the add-on.</p>", "AddOn$status": "<p>The status of the add-on.</p>", "Alarm$supportCode": "<p>The support code. Include this code in your email to support when you have questions about your Lightsail alarm. This code enables our support team to look up your Lightsail information more easily.</p>", "AttachedDisk$path": "<p>The path of the disk (e.g., <code>/dev/xvdf</code>).</p>", "AutoSnapshotDetails$date": "<p>The date of the automatic snapshot in <code>YYYY-MM-DD</code> format.</p>", "Blueprint$description": "<p>The description of the blueprint.</p>", "Blueprint$version": "<p>The version number of the operating system, application, or stack (e.g., <code>2016.03.0</code>).</p>", "Blueprint$versionCode": "<p>The version code.</p>", "Blueprint$productUrl": "<p>The product URL to learn more about the image or blueprint.</p>", "Blueprint$licenseUrl": "<p>The end-user license agreement URL for the image or blueprint.</p>", "Bundle$instanceType": "<p>The Amazon EC2 instance type (e.g., <code>t2.micro</code>).</p>", "Bundle$name": "<p>A friendly name for the bundle (e.g., <code>Micro</code>).</p>", "CacheBehaviorPerPath$path": "<p>The path to a directory or file to cached, or not cache. Use an asterisk symbol to specify wildcard directories (<code>path/to/assets/*</code>), and file types (<code>*.html, *jpg, *js</code>). Directories and file paths are case-sensitive.</p> <p>Examples:</p> <ul> <li> <p>Specify the following to cache all files in the document root of an Apache web server running on a Lightsail instance.</p> <p> <code>var/www/html/</code> </p> </li> <li> <p>Specify the following file to cache only the index page in the document root of an Apache web server.</p> <p> <code>var/www/html/index.html</code> </p> </li> <li> <p>Specify the following to cache only the .html files in the document root of an Apache web server.</p> <p> <code>var/www/html/*.html</code> </p> </li> <li> <p>Specify the following to cache only the .jpg, .png, and .gif files in the images sub-directory of the document root of an Apache web server.</p> <p> <code>var/www/html/images/*.jpg</code> </p> <p> <code>var/www/html/images/*.png</code> </p> <p> <code>var/www/html/images/*.gif</code> </p> <p>Specify the following to cache all files in the images sub-directory of the document root of an Apache web server.</p> <p> <code>var/www/html/images/</code> </p> </li> </ul>", "Certificate$supportCode": "<p>The support code. Include this code in your email to support when you have questions about your Lightsail certificate. This code enables our support team to look up your Lightsail information more easily.</p>", "ContactMethod$supportCode": "<p>The support code. Include this code in your email to support when you have questions about your Lightsail contact method. This code enables our support team to look up your Lightsail information more easily.</p>", "CopySnapshotRequest$sourceResourceName": "<p>The name of the source instance or disk from which the source automatic snapshot was created.</p> <p>Constraint:</p> <ul> <li> <p>Define this parameter only when copying an automatic snapshot as a manual snapshot. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en_us/articles/amazon-lightsail-keeping-automatic-snapshots\">Lightsail Dev Guide</a>.</p> </li> </ul>", "CopySnapshotRequest$restoreDate": "<p>The date of the source automatic snapshot to copy. Use the <code>get auto snapshots</code> operation to identify the dates of the available automatic snapshots.</p> <p>Constraints:</p> <ul> <li> <p>Must be specified in <code>YYYY-MM-DD</code> format.</p> </li> <li> <p>This parameter cannot be defined together with the <code>use latest restorable auto snapshot</code> parameter. The <code>restore date</code> and <code>use latest restorable auto snapshot</code> parameters are mutually exclusive.</p> </li> <li> <p>Define this parameter only when copying an automatic snapshot as a manual snapshot. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en_us/articles/amazon-lightsail-keeping-automatic-snapshots\">Lightsail Dev Guide</a>.</p> </li> </ul>", "CreateDiskFromSnapshotRequest$sourceDiskName": "<p>The name of the source disk from which the source automatic snapshot was created.</p> <p>Constraints:</p> <ul> <li> <p>This parameter cannot be defined together with the <code>disk snapshot name</code> parameter. The <code>source disk name</code> and <code>disk snapshot name</code> parameters are mutually exclusive.</p> </li> <li> <p>Define this parameter only when creating a new disk from an automatic snapshot. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en_us/articles/amazon-lightsail-configuring-automatic-snapshots\">Lightsail Dev Guide</a>.</p> </li> </ul>", "CreateDiskFromSnapshotRequest$restoreDate": "<p>The date of the automatic snapshot to use for the new disk. Use the <code>get auto snapshots</code> operation to identify the dates of the available automatic snapshots.</p> <p>Constraints:</p> <ul> <li> <p>Must be specified in <code>YYYY-MM-DD</code> format.</p> </li> <li> <p>This parameter cannot be defined together with the <code>use latest restorable auto snapshot</code> parameter. The <code>restore date</code> and <code>use latest restorable auto snapshot</code> parameters are mutually exclusive.</p> </li> <li> <p>Define this parameter only when creating a new disk from an automatic snapshot. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en_us/articles/amazon-lightsail-configuring-automatic-snapshots\">Lightsail Dev Guide</a>.</p> </li> </ul>", "CreateDistributionRequest$bundleId": "<p>The bundle ID to use for the distribution.</p> <p>A distribution bundle describes the specifications of your distribution, such as the monthly cost and monthly network transfer quota.</p> <p>Use the <code>GetDistributionBundles</code> action to get a list of distribution bundle IDs that you can specify.</p>", "CreateInstancesFromSnapshotRequest$availabilityZone": "<p>The Availability Zone where you want to create your instances. Use the following formatting: <code>us-east-2a</code> (case sensitive). You can get a list of Availability Zones by using the <a href=\"http://docs.aws.amazon.com/lightsail/2016-11-28/api-reference/API_GetRegions.html\">get regions</a> operation. Be sure to add the <code>include Availability Zones</code> parameter to your request.</p>", "CreateInstancesFromSnapshotRequest$userData": "<p>You can create a launch script that configures a server with additional user data. For example, <code>apt-get -y update</code>.</p> <note> <p>Depending on the machine image you choose, the command to get software on your instance varies. Amazon Linux and CentOS use <code>yum</code>, Debian and Ubuntu use <code>apt-get</code>, and FreeBSD uses <code>pkg</code>. For a complete list, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/getting-started/article/compare-options-choose-lightsail-instance-image\">Dev Guide</a>.</p> </note>", "CreateInstancesFromSnapshotRequest$sourceInstanceName": "<p>The name of the source instance from which the source automatic snapshot was created.</p> <p>Constraints:</p> <ul> <li> <p>This parameter cannot be defined together with the <code>instance snapshot name</code> parameter. The <code>source instance name</code> and <code>instance snapshot name</code> parameters are mutually exclusive.</p> </li> <li> <p>Define this parameter only when creating a new instance from an automatic snapshot. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en_us/articles/amazon-lightsail-configuring-automatic-snapshots\">Lightsail Dev Guide</a>.</p> </li> </ul>", "CreateInstancesFromSnapshotRequest$restoreDate": "<p>The date of the automatic snapshot to use for the new instance. Use the <code>get auto snapshots</code> operation to identify the dates of the available automatic snapshots.</p> <p>Constraints:</p> <ul> <li> <p>Must be specified in <code>YYYY-MM-DD</code> format.</p> </li> <li> <p>This parameter cannot be defined together with the <code>use latest restorable auto snapshot</code> parameter. The <code>restore date</code> and <code>use latest restorable auto snapshot</code> parameters are mutually exclusive.</p> </li> <li> <p>Define this parameter only when creating a new instance from an automatic snapshot. For more information, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/en_us/articles/amazon-lightsail-configuring-automatic-snapshots\">Lightsail Dev Guide</a>.</p> </li> </ul>", "CreateInstancesRequest$availabilityZone": "<p>The Availability Zone in which to create your instance. Use the following format: <code>us-east-2a</code> (case sensitive). You can get a list of Availability Zones by using the <a href=\"http://docs.aws.amazon.com/lightsail/2016-11-28/api-reference/API_GetRegions.html\">get regions</a> operation. Be sure to add the <code>include Availability Zones</code> parameter to your request.</p>", "CreateInstancesRequest$userData": "<p>A launch script you can create that configures a server with additional user data. For example, you might want to run <code>apt-get -y update</code>.</p> <note> <p>Depending on the machine image you choose, the command to get software on your instance varies. Amazon Linux and CentOS use <code>yum</code>, Debian and Ubuntu use <code>apt-get</code>, and FreeBSD uses <code>pkg</code>. For a complete list, see the <a href=\"https://lightsail.aws.amazon.com/ls/docs/getting-started/article/compare-options-choose-lightsail-instance-image\">Dev Guide</a>.</p> </note>", "CreateLoadBalancerRequest$healthCheckPath": "<p>The path you provided to perform the load balancer health check. If you didn't specify a health check path, Lightsail uses the root path of your website (e.g., <code>\"/\"</code>).</p> <p>You may want to specify a custom health check path other than the root of your application if your home page loads slowly or has a lot of media or scripting on it.</p>", "CreateRelationalDatabaseFromSnapshotRequest$availabilityZone": "<p>The Availability Zone in which to create your new database. Use the <code>us-east-2a</code> case-sensitive format.</p> <p>You can get a list of Availability Zones by using the <code>get regions</code> operation. Be sure to add the <code>include relational database Availability Zones</code> parameter to your request.</p>", "CreateRelationalDatabaseFromSnapshotRequest$relationalDatabaseBundleId": "<p>The bundle ID for your new database. A bundle describes the performance specifications for your database.</p> <p>You can get a list of database bundle IDs by using the <code>get relational database bundles</code> operation.</p> <p>When creating a new database from a snapshot, you cannot choose a bundle that is smaller than the bundle of the source database.</p>", "CreateRelationalDatabaseRequest$availabilityZone": "<p>The Availability Zone in which to create your new database. Use the <code>us-east-2a</code> case-sensitive format.</p> <p>You can get a list of Availability Zones by using the <code>get regions</code> operation. Be sure to add the <code>include relational database Availability Zones</code> parameter to your request.</p>", "CreateRelationalDatabaseRequest$relationalDatabaseBlueprintId": "<p>The blueprint ID for your new database. A blueprint describes the major engine version of a database.</p> <p>You can get a list of database blueprints IDs by using the <code>get relational database blueprints</code> operation.</p>", "CreateRelationalDatabaseRequest$relationalDatabaseBundleId": "<p>The bundle ID for your new database. A bundle describes the performance specifications for your database.</p> <p>You can get a list of database bundle IDs by using the <code>get relational database bundles</code> operation.</p>", "CreateRelationalDatabaseRequest$masterDatabaseName": "<p>The name of the master database created when the Lightsail database resource is created.</p> <p>Constraints:</p> <ul> <li> <p>Must contain from 1 to 64 alphanumeric characters.</p> </li> <li> <p>Cannot be a word reserved by the specified database engine</p> </li> </ul>", "CreateRelationalDatabaseRequest$masterUsername": "<p>The master user name for your new database.</p> <p>Constraints:</p> <ul> <li> <p>Master user name is required.</p> </li> <li> <p>Must contain from 1 to 16 alphanumeric characters.</p> </li> <li> <p>The first character must be a letter.</p> </li> <li> <p>Cannot be a reserved word for the database engine you choose.</p> <p>For more information about reserved words in MySQL 5.6 or 5.7, see the Keywords and Reserved Words articles for <a href=\"https://dev.mysql.com/doc/refman/5.6/en/keywords.html\">MySQL 5.6</a> or <a href=\"https://dev.mysql.com/doc/refman/5.7/en/keywords.html\">MySQL 5.7</a> respectively.</p> </li> </ul>", "CreateRelationalDatabaseRequest$preferredBackupWindow": "<p>The daily time range during which automated backups are created for your new database if automated backups are enabled.</p> <p>The default is a 30-minute window selected at random from an 8-hour block of time for each AWS Region. For more information about the preferred backup window time blocks for each region, see the <a href=\"https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_WorkingWithAutomatedBackups.html#USER_WorkingWithAutomatedBackups.BackupWindow\">Working With Backups</a> guide in the Amazon Relational Database Service (Amazon RDS) documentation.</p> <p>Constraints:</p> <ul> <li> <p>Must be in the <code>hh24:mi-hh24:mi</code> format.</p> <p>Example: <code>16:00-16:30</code> </p> </li> <li> <p>Specified in Coordinated Universal Time (UTC).</p> </li> <li> <p>Must not conflict with the preferred maintenance window.</p> </li> <li> <p>Must be at least 30 minutes.</p> </li> </ul>", "CreateRelationalDatabaseRequest$preferredMaintenanceWindow": "<p>The weekly time range during which system maintenance can occur on your new database.</p> <p>The default is a 30-minute window selected at random from an 8-hour block of time for each AWS Region, occurring on a random day of the week.</p> <p>Constraints:</p> <ul> <li> <p>Must be in the <code>ddd:hh24:mi-ddd:hh24:mi</code> format.</p> </li> <li> <p>Valid days: Mon, Tue, Wed, Thu, Fri, Sat, Sun.</p> </li> <li> <p>Must be at least 30 minutes.</p> </li> <li> <p>Specified in Coordinated Universal Time (UTC).</p> </li> <li> <p>Example: <code>Tue:17:00-Tue:17:30</code> </p> </li> </ul>", "Disk$supportCode": "<p>The support code. Include this code in your email to support when you have questions about an instance or another resource in Lightsail. This code enables our support team to look up your Lightsail information more easily.</p>", "Disk$path": "<p>The disk path.</p>", "Disk$attachmentState": "<p>(Deprecated) The attachment state of the disk.</p> <note> <p>In releases prior to November 14, 2017, this parameter returned <code>attached</code> for system disks in the API response. It is now deprecated, but still included in the response. Use <code>isAttached</code> instead.</p> </note>", "DiskInfo$name": "<p>The disk name.</p>", "DiskSnapshot$supportCode": "<p>The support code. Include this code in your email to support when you have questions about an instance or another resource in Lightsail. This code enables our support team to look up your Lightsail information more easily.</p>", "DiskSnapshot$progress": "<p>The progress of the disk snapshot operation.</p>", "DistributionBundle$bundleId": "<p>The ID of the bundle.</p>", "DistributionBundle$name": "<p>The name of the distribution bundle.</p>", "Domain$supportCode": "<p>The support code. Include this code in your email to support when you have questions about an instance or another resource in Lightsail. This code enables our support team to look up your Lightsail information more easily.</p>", "DomainEntry$target": "<p>The target AWS name server (e.g., <code>ns-111.awsdns-22.com.</code>).</p> <p>For Lightsail load balancers, the value looks like <code>ab1234c56789c6b86aba6fb203d443bc-123456789.us-east-2.elb.amazonaws.com</code>. Be sure to also set <code>isAlias</code> to <code>true</code> when setting up an A record for a load balancer.</p>", "DomainEntryOptions$value": null, "GetActiveNamesRequest$pageToken": "<p>The token to advance to the next page of results from your request.</p> <p>To get a page token, perform an initial <code>GetActiveNames</code> request. If your results are paginated, the response will return a next page token that you can specify as the page token in a subsequent request.</p>", "GetActiveNamesResult$nextPageToken": "<p>The token to advance to the next page of resutls from your request.</p> <p>A next page token is not returned if there are no more results to display.</p> <p>To get the next page of results, perform another <code>GetActiveNames</code> request and specify the next page token using the <code>pageToken</code> parameter.</p>", "GetAlarmsRequest$pageToken": "<p>The token to advance to the next page of results from your request.</p> <p>To get a page token, perform an initial <code>GetAlarms</code> request. If your results are paginated, the response will return a next page token that you can specify as the page token in a subsequent request.</p>", "GetAlarmsResult$nextPageToken": "<p>The token to advance to the next page of resutls from your request.</p> <p>A next page token is not returned if there are no more results to display.</p> <p>To get the next page of results, perform another <code>GetAlarms</code> request and specify the next page token using the <code>pageToken</code> parameter.</p>", "GetBlueprintsRequest$pageToken": "<p>The token to advance to the next page of results from your request.</p> <p>To get a page token, perform an initial <code>GetBlueprints</code> request. If your results are paginated, the response will return a next page token that you can specify as the page token in a subsequent request.</p>", "GetBlueprintsResult$nextPageToken": "<p>The token to advance to the next page of resutls from your request.</p> <p>A next page token is not returned if there are no more results to display.</p> <p>To get the next page of results, perform another <code>GetBlueprints</code> request and specify the next page token using the <code>pageToken</code> parameter.</p>", "GetBundlesRequest$pageToken": "<p>The token to advance to the next page of results from your request.</p> <p>To get a page token, perform an initial <code>GetBundles</code> request. If your results are paginated, the response will return a next page token that you can specify as the page token in a subsequent request.</p>", "GetBundlesResult$nextPageToken": "<p>The token to advance to the next page of resutls from your request.</p> <p>A next page token is not returned if there are no more results to display.</p> <p>To get the next page of results, perform another <code>GetBundles</code> request and specify the next page token using the <code>pageToken</code> parameter.</p>", "GetCloudFormationStackRecordsRequest$pageToken": "<p>The token to advance to the next page of results from your request.</p> <p>To get a page token, perform an initial <code>GetClouFormationStackRecords</code> request. If your results are paginated, the response will return a next page token that you can specify as the page token in a subsequent request.</p>", "GetCloudFormationStackRecordsResult$nextPageToken": "<p>The token to advance to the next page of resutls from your request.</p> <p>A next page token is not returned if there are no more results to display.</p> <p>To get the next page of results, perform another <code>GetCloudFormationStackRecords</code> request and specify the next page token using the <code>pageToken</code> parameter.</p>", "GetDiskSnapshotsRequest$pageToken": "<p>The token to advance to the next page of results from your request.</p> <p>To get a page token, perform an initial <code>GetDiskSnapshots</code> request. If your results are paginated, the response will return a next page token that you can specify as the page token in a subsequent request.</p>", "GetDiskSnapshotsResult$nextPageToken": "<p>The token to advance to the next page of resutls from your request.</p> <p>A next page token is not returned if there are no more results to display.</p> <p>To get the next page of results, perform another <code>GetDiskSnapshots</code> request and specify the next page token using the <code>pageToken</code> parameter.</p>", "GetDisksRequest$pageToken": "<p>The token to advance to the next page of results from your request.</p> <p>To get a page token, perform an initial <code>GetDisks</code> request. If your results are paginated, the response will return a next page token that you can specify as the page token in a subsequent request.</p>", "GetDisksResult$nextPageToken": "<p>The token to advance to the next page of resutls from your request.</p> <p>A next page token is not returned if there are no more results to display.</p> <p>To get the next page of results, perform another <code>GetDisks</code> request and specify the next page token using the <code>pageToken</code> parameter.</p>", "GetDistributionLatestCacheResetResult$status": "<p>The status of the last cache reset.</p>", "GetDistributionsRequest$pageToken": "<p>The token to advance to the next page of results from your request.</p> <p>To get a page token, perform an initial <code>GetDistributions</code> request. If your results are paginated, the response will return a next page token that you can specify as the page token in a subsequent request.</p>", "GetDistributionsResult$nextPageToken": "<p>The token to advance to the next page of results from your request.</p> <p>A next page token is not returned if there are no more results to display.</p> <p>To get the next page of results, perform another <code>GetDistributions</code> request and specify the next page token using the <code>pageToken</code> parameter.</p>", "GetDomainsRequest$pageToken": "<p>The token to advance to the next page of results from your request.</p> <p>To get a page token, perform an initial <code>GetDomains</code> request. If your results are paginated, the response will return a next page token that you can specify as the page token in a subsequent request.</p>", "GetDomainsResult$nextPageToken": "<p>The token to advance to the next page of resutls from your request.</p> <p>A next page token is not returned if there are no more results to display.</p> <p>To get the next page of results, perform another <code>GetDomains</code> request and specify the next page token using the <code>pageToken</code> parameter.</p>", "GetExportSnapshotRecordsRequest$pageToken": "<p>The token to advance to the next page of results from your request.</p> <p>To get a page token, perform an initial <code>GetExportSnapshotRecords</code> request. If your results are paginated, the response will return a next page token that you can specify as the page token in a subsequent request.</p>", "GetExportSnapshotRecordsResult$nextPageToken": "<p>The token to advance to the next page of resutls from your request.</p> <p>A next page token is not returned if there are no more results to display.</p> <p>To get the next page of results, perform another <code>GetExportSnapshotRecords</code> request and specify the next page token using the <code>pageToken</code> parameter.</p>", "GetInstanceSnapshotsRequest$pageToken": "<p>The token to advance to the next page of results from your request.</p> <p>To get a page token, perform an initial <code>GetInstanceSnapshots</code> request. If your results are paginated, the response will return a next page token that you can specify as the page token in a subsequent request.</p>", "GetInstanceSnapshotsResult$nextPageToken": "<p>The token to advance to the next page of resutls from your request.</p> <p>A next page token is not returned if there are no more results to display.</p> <p>To get the next page of results, perform another <code>GetInstanceSnapshots</code> request and specify the next page token using the <code>pageToken</code> parameter.</p>", "GetInstancesRequest$pageToken": "<p>The token to advance to the next page of results from your request.</p> <p>To get a page token, perform an initial <code>GetInstances</code> request. If your results are paginated, the response will return a next page token that you can specify as the page token in a subsequent request.</p>", "GetInstancesResult$nextPageToken": "<p>The token to advance to the next page of resutls from your request.</p> <p>A next page token is not returned if there are no more results to display.</p> <p>To get the next page of results, perform another <code>GetInstances</code> request and specify the next page token using the <code>pageToken</code> parameter.</p>", "GetKeyPairsRequest$pageToken": "<p>The token to advance to the next page of results from your request.</p> <p>To get a page token, perform an initial <code>Get<PERSON>eyPairs</code> request. If your results are paginated, the response will return a next page token that you can specify as the page token in a subsequent request.</p>", "GetKeyPairsResult$nextPageToken": "<p>The token to advance to the next page of resutls from your request.</p> <p>A next page token is not returned if there are no more results to display.</p> <p>To get the next page of results, perform another <code>GetKeyPairs</code> request and specify the next page token using the <code>pageToken</code> parameter.</p>", "GetLoadBalancersRequest$pageToken": "<p>The token to advance to the next page of results from your request.</p> <p>To get a page token, perform an initial <code>GetLoadBalancers</code> request. If your results are paginated, the response will return a next page token that you can specify as the page token in a subsequent request.</p>", "GetLoadBalancersResult$nextPageToken": "<p>The token to advance to the next page of resutls from your request.</p> <p>A next page token is not returned if there are no more results to display.</p> <p>To get the next page of results, perform another <code>GetLoadBalancers</code> request and specify the next page token using the <code>pageToken</code> parameter.</p>", "GetOperationsForResourceRequest$pageToken": "<p>The token to advance to the next page of results from your request.</p> <p>To get a page token, perform an initial <code>GetOperationsForResource</code> request. If your results are paginated, the response will return a next page token that you can specify as the page token in a subsequent request.</p>", "GetOperationsForResourceResult$nextPageCount": "<p>(Deprecated) Returns the number of pages of results that remain.</p> <note> <p>In releases prior to June 12, 2017, this parameter returned <code>null</code> by the API. It is now deprecated, and the API returns the <code>next page token</code> parameter instead.</p> </note>", "GetOperationsForResourceResult$nextPageToken": "<p>The token to advance to the next page of resutls from your request.</p> <p>A next page token is not returned if there are no more results to display.</p> <p>To get the next page of results, perform another <code>GetOperationsForResource</code> request and specify the next page token using the <code>pageToken</code> parameter.</p>", "GetOperationsRequest$pageToken": "<p>The token to advance to the next page of results from your request.</p> <p>To get a page token, perform an initial <code>GetOperations</code> request. If your results are paginated, the response will return a next page token that you can specify as the page token in a subsequent request.</p>", "GetOperationsResult$nextPageToken": "<p>The token to advance to the next page of resutls from your request.</p> <p>A next page token is not returned if there are no more results to display.</p> <p>To get the next page of results, perform another <code>GetOperations</code> request and specify the next page token using the <code>pageToken</code> parameter.</p>", "GetRelationalDatabaseBlueprintsRequest$pageToken": "<p>The token to advance to the next page of results from your request.</p> <p>To get a page token, perform an initial <code>GetRelationalDatabaseBlueprints</code> request. If your results are paginated, the response will return a next page token that you can specify as the page token in a subsequent request.</p>", "GetRelationalDatabaseBlueprintsResult$nextPageToken": "<p>The token to advance to the next page of resutls from your request.</p> <p>A next page token is not returned if there are no more results to display.</p> <p>To get the next page of results, perform another <code>GetRelationalDatabaseBlueprints</code> request and specify the next page token using the <code>pageToken</code> parameter.</p>", "GetRelationalDatabaseBundlesRequest$pageToken": "<p>The token to advance to the next page of results from your request.</p> <p>To get a page token, perform an initial <code>GetRelationalDatabaseBundles</code> request. If your results are paginated, the response will return a next page token that you can specify as the page token in a subsequent request.</p>", "GetRelationalDatabaseBundlesResult$nextPageToken": "<p>The token to advance to the next page of resutls from your request.</p> <p>A next page token is not returned if there are no more results to display.</p> <p>To get the next page of results, perform another <code>GetRelationalDatabaseBundles</code> request and specify the next page token using the <code>pageToken</code> parameter.</p>", "GetRelationalDatabaseEventsRequest$pageToken": "<p>The token to advance to the next page of results from your request.</p> <p>To get a page token, perform an initial <code>GetRelationalDatabaseEvents</code> request. If your results are paginated, the response will return a next page token that you can specify as the page token in a subsequent request.</p>", "GetRelationalDatabaseEventsResult$nextPageToken": "<p>The token to advance to the next page of resutls from your request.</p> <p>A next page token is not returned if there are no more results to display.</p> <p>To get the next page of results, perform another <code>GetRelationalDatabaseEvents</code> request and specify the next page token using the <code>pageToken</code> parameter.</p>", "GetRelationalDatabaseLogEventsRequest$logStreamName": "<p>The name of the log stream.</p> <p>Use the <code>get relational database log streams</code> operation to get a list of available log streams.</p>", "GetRelationalDatabaseLogEventsRequest$pageToken": "<p>The token to advance to the next or previous page of results from your request.</p> <p>To get a page token, perform an initial <code>GetRelationalDatabaseLogEvents</code> request. If your results are paginated, the response will return a next forward token and/or next backward token that you can specify as the page token in a subsequent request.</p>", "GetRelationalDatabaseLogEventsResult$nextBackwardToken": "<p>A token used for advancing to the previous page of results from your get relational database log events request.</p>", "GetRelationalDatabaseLogEventsResult$nextForwardToken": "<p>A token used for advancing to the next page of results from your get relational database log events request.</p>", "GetRelationalDatabaseParametersRequest$pageToken": "<p>The token to advance to the next page of results from your request.</p> <p>To get a page token, perform an initial <code>GetRelationalDatabaseParameters</code> request. If your results are paginated, the response will return a next page token that you can specify as the page token in a subsequent request.</p>", "GetRelationalDatabaseParametersResult$nextPageToken": "<p>The token to advance to the next page of resutls from your request.</p> <p>A next page token is not returned if there are no more results to display.</p> <p>To get the next page of results, perform another <code>GetRelationalDatabaseParameters</code> request and specify the next page token using the <code>pageToken</code> parameter.</p>", "GetRelationalDatabaseSnapshotsRequest$pageToken": "<p>The token to advance to the next page of results from your request.</p> <p>To get a page token, perform an initial <code>GetRelationalDatabaseSnapshots</code> request. If your results are paginated, the response will return a next page token that you can specify as the page token in a subsequent request.</p>", "GetRelationalDatabaseSnapshotsResult$nextPageToken": "<p>The token to advance to the next page of resutls from your request.</p> <p>A next page token is not returned if there are no more results to display.</p> <p>To get the next page of results, perform another <code>GetRelationalDatabaseSnapshots</code> request and specify the next page token using the <code>pageToken</code> parameter.</p>", "GetRelationalDatabasesRequest$pageToken": "<p>The token to advance to the next page of results from your request.</p> <p>To get a page token, perform an initial <code>GetRelationalDatabases</code> request. If your results are paginated, the response will return a next page token that you can specify as the page token in a subsequent request.</p>", "GetRelationalDatabasesResult$nextPageToken": "<p>The token to advance to the next page of resutls from your request.</p> <p>A next page token is not returned if there are no more results to display.</p> <p>To get the next page of results, perform another <code>GetRelationalDatabases</code> request and specify the next page token using the <code>pageToken</code> parameter.</p>", "GetStaticIpsRequest$pageToken": "<p>The token to advance to the next page of results from your request.</p> <p>To get a page token, perform an initial <code>GetStaticIps</code> request. If your results are paginated, the response will return a next page token that you can specify as the page token in a subsequent request.</p>", "GetStaticIpsResult$nextPageToken": "<p>The token to advance to the next page of resutls from your request.</p> <p>A next page token is not returned if there are no more results to display.</p> <p>To get the next page of results, perform another <code>GetStaticIps</code> request and specify the next page token using the <code>pageToken</code> parameter.</p>", "HostKeyAttributes$algorithm": "<p>The SSH host key algorithm or the RDP certificate format.</p> <p>For SSH host keys, the algorithm may be <code>ssh-rsa</code>, <code>ecdsa-sha2-nistp256</code>, <code>ssh-ed25519</code>, etc. For RDP certificates, the algorithm is always <code>x509-cert</code>.</p>", "HostKeyAttributes$publicKey": "<p>The public SSH host key or the RDP certificate.</p>", "HostKeyAttributes$fingerprintSHA1": "<p>The SHA-1 fingerprint of the returned SSH host key or RDP certificate.</p> <ul> <li> <p>Example of an SHA-1 SSH fingerprint:</p> <p> <code>SHA1:1CHH6FaAaXjtFOsR/t83vf91SR0</code> </p> </li> <li> <p>Example of an SHA-1 RDP fingerprint:</p> <p> <code>af:34:51:fe:09:f0:e0:da:b8:4e:56:ca:60:c2:10:ff:38:06:db:45</code> </p> </li> </ul>", "HostKeyAttributes$fingerprintSHA256": "<p>The SHA-256 fingerprint of the returned SSH host key or RDP certificate.</p> <ul> <li> <p>Example of an SHA-256 SSH fingerprint:</p> <p> <code>SHA256:KTsMnRBh1IhD17HpdfsbzeGA4jOijm5tyXsMjKVbB8o</code> </p> </li> <li> <p>Example of an SHA-256 RDP fingerprint:</p> <p> <code>03:9b:36:9f:4b:de:4e:61:70:fc:7c:c9:78:e7:d2:1a:1c:25:a8:0c:91:f6:7c:e4:d6:a0:85:c8:b4:53:99:68</code> </p> </li> </ul>", "Instance$supportCode": "<p>The support code. Include this code in your email to support when you have questions about an instance or another resource in Lightsail. This code enables our support team to look up your Lightsail information more easily.</p>", "InstanceAccessDetails$certKey": "<p>For SSH access, the public key to use when accessing your instance For OpenSSH clients (e.g., command line SSH), you should save this value to <code>tempkey-cert.pub</code>.</p>", "InstanceAccessDetails$password": "<p>For RDP access, the password for your Amazon Lightsail instance. Password will be an empty string if the password for your new instance is not ready yet. When you create an instance, it can take up to 15 minutes for the instance to be ready.</p> <note> <p>If you create an instance using any key pair other than the default (<code>LightsailDefaultKeyPair</code>), <code>password</code> will always be an empty string.</p> <p>If you change the Administrator password on the instance, Lightsail will continue to return the original password value. When accessing the instance using RDP, you need to manually enter the Administrator password after changing it from the default.</p> </note>", "InstanceAccessDetails$privateKey": "<p>For SSH access, the temporary private key. For OpenSSH clients (e.g., command line SSH), you should save this value to <code>tempkey</code>).</p>", "InstanceAccessDetails$username": "<p>The user name to use when logging in to the Amazon Lightsail instance.</p>", "InstanceEntry$userData": "<p>A launch script you can create that configures a server with additional user data. For example, you might want to run <code>apt-get -y update</code>.</p> <note> <p>Depending on the machine image you choose, the command to get software on your instance varies. Amazon Linux and CentOS use <code>yum</code>, Debian and Ubuntu use <code>apt-get</code>, and FreeBSD uses <code>pkg</code>.</p> </note>", "InstanceEntry$availabilityZone": "<p>The Availability Zone for the new Amazon EC2 instance.</p>", "InstancePortInfo$accessFrom": "<p>The location from which access is allowed. For example, <code>Anywhere (0.0.0.0/0)</code>, or <code>Custom</code> if a specific IP address or range of IP addresses is allowed.</p>", "InstancePortInfo$commonName": "<p>The common name of the port information.</p>", "InstanceSnapshot$supportCode": "<p>The support code. Include this code in your email to support when you have questions about an instance or another resource in Lightsail. This code enables our support team to look up your Lightsail information more easily.</p>", "InstanceSnapshot$progress": "<p>The progress of the snapshot.</p>", "InstanceSnapshot$fromBlueprintId": "<p>The blueprint ID from which you created the snapshot (e.g., <code>os_debian_8_3</code>). A blueprint is a virtual private server (or <i>instance</i>) image used to create instances quickly.</p>", "InstanceSnapshot$fromBundleId": "<p>The bundle ID from which you created the snapshot (e.g., <code>micro_1_0</code>).</p>", "InstanceState$name": "<p>The state of the instance (e.g., <code>running</code> or <code>pending</code>).</p>", "InvalidInputException$code": null, "InvalidInputException$docs": null, "InvalidInputException$message": null, "InvalidInputException$tip": null, "KeyPair$supportCode": "<p>The support code. Include this code in your email to support when you have questions about an instance or another resource in Lightsail. This code enables our support team to look up your Lightsail information more easily.</p>", "LightsailDistribution$supportCode": "<p>The support code. Include this code in your email to support when you have questions about your Lightsail distribution. This code enables our support team to look up your Lightsail information more easily.</p>", "LightsailDistribution$status": "<p>The status of the distribution.</p>", "LightsailDistribution$domainName": "<p>The domain name of the distribution.</p>", "LightsailDistribution$bundleId": "<p>The ID of the bundle currently applied to the distribution.</p>", "LightsailDistribution$originPublicDNS": "<p>The public DNS of the origin.</p>", "LoadBalancer$supportCode": "<p>The support code. Include this code in your email to support when you have questions about your Lightsail load balancer. This code enables our support team to look up your Lightsail information more easily.</p>", "LoadBalancerConfigurationOptions$value": null, "LoadBalancerTlsCertificate$supportCode": "<p>The support code. Include this code in your email to support when you have questions about your Lightsail load balancer or SSL/TLS certificate. This code enables our support team to look up your Lightsail information more easily.</p>", "LogEvent$message": "<p>The message of the database log event.</p>", "NotFoundException$code": null, "NotFoundException$docs": null, "NotFoundException$message": null, "NotFoundException$tip": null, "Operation$operationDetails": "<p>Details about the operation (e.g., <code>Debian-1GB-Ohio-1</code>).</p>", "Operation$errorCode": "<p>The error code.</p>", "Operation$errorDetails": "<p>The error details.</p>", "OperationFailureException$code": null, "OperationFailureException$docs": null, "OperationFailureException$message": null, "OperationFailureException$tip": null, "PasswordData$ciphertext": "<p>The encrypted password. Ciphertext will be an empty string if access to your new instance is not ready yet. When you create an instance, it can take up to 15 minutes for the instance to be ready.</p> <note> <p>If you use the default key pair (<code>LightsailDefaultKeyPair</code>), the decrypted password will be available in the password field.</p> <p>If you are using a custom key pair, you need to use your own means of decryption.</p> <p>If you change the Administrator password on the instance, Lightsail will continue to return the original ciphertext value. When accessing the instance using RDP, you need to manually enter the Administrator password after changing it from the default.</p> </note>", "PendingModifiedRelationalDatabaseValues$masterUserPassword": "<p>The password for the master user of the database.</p>", "PendingModifiedRelationalDatabaseValues$engineVersion": "<p>The database engine version.</p>", "Region$continentCode": "<p>The continent code (e.g., <code>NA</code>, meaning North America).</p>", "Region$description": "<p>The description of the AWS Region (e.g., <code>This region is recommended to serve users in the eastern United States and eastern Canada</code>).</p>", "Region$displayName": "<p>The display name (e.g., <code>Ohio</code>).</p>", "RelationalDatabase$supportCode": "<p>The support code for the database. Include this code in your email to support when you have questions about a database in Lightsail. This code enables our support team to look up your Lightsail information more easily.</p>", "RelationalDatabase$masterDatabaseName": "<p>The name of the master database created when the Lightsail database resource is created.</p>", "RelationalDatabase$secondaryAvailabilityZone": "<p>Describes the secondary Availability Zone of a high availability database.</p> <p>The secondary database is used for failover support of a high availability database.</p>", "RelationalDatabase$caCertificateIdentifier": "<p>The certificate associated with the database.</p>", "RelationalDatabaseBlueprint$blueprintId": "<p>The ID for the database blueprint.</p>", "RelationalDatabaseBlueprint$engineVersion": "<p>The database engine version for the database blueprint (for example, <code>5.7.23</code>).</p>", "RelationalDatabaseBlueprint$engineDescription": "<p>The description of the database engine for the database blueprint.</p>", "RelationalDatabaseBlueprint$engineVersionDescription": "<p>The description of the database engine version for the database blueprint.</p>", "RelationalDatabaseBundle$bundleId": "<p>The ID for the database bundle.</p>", "RelationalDatabaseBundle$name": "<p>The name for the database bundle.</p>", "RelationalDatabaseEvent$message": "<p>The message of the database event.</p>", "RelationalDatabaseParameter$allowedValues": "<p>Specifies the valid range of values for the parameter.</p>", "RelationalDatabaseParameter$applyMethod": "<p>Indicates when parameter updates are applied.</p> <p>Can be <code>immediate</code> or <code>pending-reboot</code>.</p>", "RelationalDatabaseParameter$applyType": "<p>Specifies the engine-specific parameter type.</p>", "RelationalDatabaseParameter$dataType": "<p>Specifies the valid data type for the parameter.</p>", "RelationalDatabaseParameter$description": "<p>Provides a description of the parameter.</p>", "RelationalDatabaseParameter$parameterName": "<p>Specifies the name of the parameter.</p>", "RelationalDatabaseParameter$parameterValue": "<p>Specifies the value of the parameter.</p>", "RelationalDatabaseSnapshot$supportCode": "<p>The support code for the database snapshot. Include this code in your email to support when you have questions about a database snapshot in Lightsail. This code enables our support team to look up your Lightsail information more easily.</p>", "RelationalDatabaseSnapshot$fromRelationalDatabaseBundleId": "<p>The bundle ID of the database from which the database snapshot was created.</p>", "RelationalDatabaseSnapshot$fromRelationalDatabaseBlueprintId": "<p>The blueprint ID of the database from which the database snapshot was created. A blueprint describes the major engine version of a database.</p>", "ResetDistributionCacheResult$status": "<p>The status of the reset cache request.</p>", "ResourceLocation$availabilityZone": "<p>The Availability Zone. Follows the format <code>us-east-2a</code> (case-sensitive).</p>", "ResourceRecord$name": "<p>The name of the record.</p>", "ResourceRecord$type": "<p>The DNS record type.</p>", "ResourceRecord$value": "<p>The value for the DNS record.</p>", "ServiceException$code": null, "ServiceException$docs": null, "ServiceException$message": null, "ServiceException$tip": null, "StaticIp$supportCode": "<p>The support code. Include this code in your email to support when you have questions about an instance or another resource in Lightsail. This code enables our support team to look up your Lightsail information more easily.</p>", "StringList$member": null, "UnauthenticatedException$code": null, "UnauthenticatedException$docs": null, "UnauthenticatedException$message": null, "UnauthenticatedException$tip": null, "UpdateDistributionBundleRequest$bundleId": "<p>The bundle ID of the new bundle to apply to your distribution.</p> <p>Use the <code>GetDistributionBundles</code> action to get a list of distribution bundle IDs that you can specify.</p>", "UpdateRelationalDatabaseRequest$preferredBackupWindow": "<p>The daily time range during which automated backups are created for your database if automated backups are enabled.</p> <p>Constraints:</p> <ul> <li> <p>Must be in the <code>hh24:mi-hh24:mi</code> format.</p> <p>Example: <code>16:00-16:30</code> </p> </li> <li> <p>Specified in Coordinated Universal Time (UTC).</p> </li> <li> <p>Must not conflict with the preferred maintenance window.</p> </li> <li> <p>Must be at least 30 minutes.</p> </li> </ul>", "UpdateRelationalDatabaseRequest$preferredMaintenanceWindow": "<p>The weekly time range during which system maintenance can occur on your database.</p> <p>The default is a 30-minute window selected at random from an 8-hour block of time for each AWS Region, occurring on a random day of the week.</p> <p>Constraints:</p> <ul> <li> <p>Must be in the <code>ddd:hh24:mi-ddd:hh24:mi</code> format.</p> </li> <li> <p>Valid days: Mon, Tue, Wed, Thu, Fri, Sat, Sun.</p> </li> <li> <p>Must be at least 30 minutes.</p> </li> <li> <p>Specified in Coordinated Universal Time (UTC).</p> </li> <li> <p>Example: <code>Tue:17:00-<PERSON>e:17:30</code> </p> </li> </ul>", "UpdateRelationalDatabaseRequest$caCertificateIdentifier": "<p>Indicates the certificate that needs to be associated with the database.</p>"}}, "timestamp": {"base": null, "refs": {"GetDistributionMetricDataRequest$startTime": "<p>The start of the time interval for which to get metric data.</p> <p>Constraints:</p> <ul> <li> <p>Specified in Coordinated Universal Time (UTC).</p> </li> <li> <p>Specified in the Unix time format.</p> <p>For example, if you wish to use a start time of October 1, 2018, at 8 PM UTC, specify <code>1538424000</code> as the start time.</p> </li> </ul> <p>You can convert a human-friendly time to Unix time format using a converter like <a href=\"https://www.epochconverter.com/\">Epoch converter</a>.</p>", "GetDistributionMetricDataRequest$endTime": "<p>The end of the time interval for which to get metric data.</p> <p>Constraints:</p> <ul> <li> <p>Specified in Coordinated Universal Time (UTC).</p> </li> <li> <p>Specified in the Unix time format.</p> <p>For example, if you wish to use an end time of October 1, 2018, at 9 PM UTC, specify <code>1538427600</code> as the end time.</p> </li> </ul> <p>You can convert a human-friendly time to Unix time format using a converter like <a href=\"https://www.epochconverter.com/\">Epoch converter</a>.</p>", "GetInstanceMetricDataRequest$startTime": "<p>The start time of the time period.</p>", "GetInstanceMetricDataRequest$endTime": "<p>The end time of the time period.</p>", "GetLoadBalancerMetricDataRequest$startTime": "<p>The start time of the period.</p>", "GetLoadBalancerMetricDataRequest$endTime": "<p>The end time of the period.</p>", "MetricDatapoint$timestamp": "<p>The timestamp (e.g., <code>1479816991.349</code>).</p>"}}}}