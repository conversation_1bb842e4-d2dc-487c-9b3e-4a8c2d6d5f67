{"version": "2.0", "service": "<fullname>Amazon Data Lifecycle Manager</fullname> <p>With Amazon Data Lifecycle Manager, you can manage the lifecycle of your AWS resources. You create lifecycle policies, which are used to automate operations on the specified resources.</p> <p>Amazon DLM supports Amazon EBS volumes and snapshots. For information about using Amazon DLM with Amazon EBS, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/snapshot-lifecycle.html\">Automating the Amazon EBS Snapshot Lifecycle</a> in the <i>Amazon EC2 User Guide</i>.</p>", "operations": {"CreateLifecyclePolicy": "<p>Creates a policy to manage the lifecycle of the specified AWS resources. You can create up to 100 lifecycle policies.</p>", "DeleteLifecyclePolicy": "<p>Deletes the specified lifecycle policy and halts the automated operations that the policy specified.</p>", "GetLifecyclePolicies": "<p>Gets summary information about all or the specified data lifecycle policies.</p> <p>To get complete information about a policy, use <a>GetLifecyclePolicy</a>.</p>", "GetLifecyclePolicy": "<p>Gets detailed information about the specified lifecycle policy.</p>", "ListTagsForResource": "<p>Lists the tags for the specified resource.</p>", "TagResource": "<p>Adds the specified tags to the specified resource.</p>", "UntagResource": "<p>Removes the specified tags from the specified resource.</p>", "UpdateLifecyclePolicy": "<p>Updates the specified lifecycle policy.</p>"}, "shapes": {"AvailabilityZone": {"base": null, "refs": {"AvailabilityZoneList$member": null}}, "AvailabilityZoneList": {"base": null, "refs": {"FastRestoreRule$AvailabilityZones": "<p>The Availability Zones in which to enable fast snapshot restore.</p>"}}, "CmkArn": {"base": null, "refs": {"CrossRegionCopyRule$CmkArn": "<p>The Amazon Resource Name (ARN) of the AWS KMS customer master key (CMK) to use for EBS encryption. If this parameter is not specified, your AWS managed CMK for EBS is used.</p>"}}, "CopyTags": {"base": null, "refs": {"Schedule$CopyTags": "<p>Copy all user-defined tags on a source volume to snapshots of the volume created by this policy.</p>"}}, "CopyTagsNullable": {"base": null, "refs": {"CrossRegionCopyRule$CopyTags": "<p>Copy all user-defined tags from the source snapshot to the copied snapshot.</p>"}}, "Count": {"base": null, "refs": {"FastRestoreRule$Count": "<p>The number of snapshots to be enabled with fast snapshot restore.</p>", "RetainRule$Count": "<p>The number of snapshots to retain for each volume, up to a maximum of 1000.</p>"}}, "CreateLifecyclePolicyRequest": {"base": null, "refs": {}}, "CreateLifecyclePolicyResponse": {"base": null, "refs": {}}, "CreateRule": {"base": "<p>Specifies when to create snapshots of EBS volumes.</p> <p>You must specify either a Cron expression or an interval, interval unit, and start time. You cannot specify both.</p>", "refs": {"Schedule$CreateRule": "<p>The creation rule.</p>"}}, "CronExpression": {"base": null, "refs": {"CreateRule$CronExpression": "<p>The schedule, as a Cron expression. The schedule interval must be between 1 hour and 1 year. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/events/ScheduledEvents.html#CronExpressions\">Cron expressions</a> in the <i>Amazon CloudWatch User Guide</i>.</p>"}}, "CrossRegionCopyRetainRule": {"base": "<p>Specifies the retention rule for cross-Region snapshot copies.</p>", "refs": {"CrossRegionCopyRule$RetainRule": "<p>The retention rule.</p>"}}, "CrossRegionCopyRule": {"base": "<p>Specifies a rule for cross-Region snapshot copies.</p>", "refs": {"CrossRegionCopyRules$member": null}}, "CrossRegionCopyRules": {"base": null, "refs": {"Schedule$CrossRegionCopyRules": "<p>The rule for cross-Region snapshot copies.</p>"}}, "DeleteLifecyclePolicyRequest": {"base": null, "refs": {}}, "DeleteLifecyclePolicyResponse": {"base": null, "refs": {}}, "Encrypted": {"base": null, "refs": {"CrossRegionCopyRule$Encrypted": "<p>To encrypt a copy of an unencrypted snapshot if encryption by default is not enabled, enable encryption using this parameter. Copies of encrypted snapshots are encrypted, even if this parameter is false or if encryption by default is not enabled.</p>"}}, "ErrorCode": {"base": null, "refs": {"InternalServerException$Code": null, "InvalidRequestException$Code": null, "LimitExceededException$Code": null, "ResourceNotFoundException$Code": null}}, "ErrorMessage": {"base": null, "refs": {"InternalServerException$Message": null, "InvalidRequestException$Message": null, "LimitExceededException$Message": null, "ResourceNotFoundException$Message": null}}, "ExcludeBootVolume": {"base": null, "refs": {"Parameters$ExcludeBootVolume": "<p>[EBS Snapshot Management – Instance policies only] Indicates whether to exclude the root volume from snapshots created using <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/API_CreateSnapshots.html\">CreateSnapshots</a>. The default is false.</p>"}}, "ExecutionRoleArn": {"base": null, "refs": {"CreateLifecyclePolicyRequest$ExecutionRoleArn": "<p>The Amazon Resource Name (ARN) of the IAM role used to run the operations specified by the lifecycle policy.</p>", "LifecyclePolicy$ExecutionRoleArn": "<p>The Amazon Resource Name (ARN) of the IAM role used to run the operations specified by the lifecycle policy.</p>", "UpdateLifecyclePolicyRequest$ExecutionRoleArn": "<p>The Amazon Resource Name (ARN) of the IAM role used to run the operations specified by the lifecycle policy.</p>"}}, "FastRestoreRule": {"base": "<p>Specifies a rule for enabling fast snapshot restore. You can enable fast snapshot restore based on either a count or a time interval.</p>", "refs": {"Schedule$FastRestoreRule": "<p>The rule for enabling fast snapshot restore.</p>"}}, "GetLifecyclePoliciesRequest": {"base": null, "refs": {}}, "GetLifecyclePoliciesResponse": {"base": null, "refs": {}}, "GetLifecyclePolicyRequest": {"base": null, "refs": {}}, "GetLifecyclePolicyResponse": {"base": null, "refs": {}}, "GettablePolicyStateValues": {"base": null, "refs": {"GetLifecyclePoliciesRequest$State": "<p>The activation state.</p>", "LifecyclePolicy$State": "<p>The activation state of the lifecycle policy.</p>", "LifecyclePolicySummary$State": "<p>The activation state of the lifecycle policy.</p>"}}, "InternalServerException": {"base": "<p>The service failed in an unexpected way.</p>", "refs": {}}, "Interval": {"base": null, "refs": {"CreateRule$Interval": "<p>The interval between snapshots. The supported values are 1, 2, 3, 4, 6, 8, 12, and 24.</p>", "CrossRegionCopyRetainRule$Interval": "<p>The amount of time to retain each snapshot. The maximum is 100 years. This is equivalent to 1200 months, 5200 weeks, or 36500 days.</p>", "FastRestoreRule$Interval": "<p>The amount of time to enable fast snapshot restore. The maximum is 100 years. This is equivalent to 1200 months, 5200 weeks, or 36500 days.</p>", "RetainRule$Interval": "<p>The amount of time to retain each snapshot. The maximum is 100 years. This is equivalent to 1200 months, 5200 weeks, or 36500 days.</p>"}}, "IntervalUnitValues": {"base": null, "refs": {"CreateRule$IntervalUnit": "<p>The interval unit.</p>"}}, "InvalidRequestException": {"base": "<p>Bad request. The request is missing required parameters or has invalid parameters.</p>", "refs": {}}, "LifecyclePolicy": {"base": "<p>Detailed information about a lifecycle policy.</p>", "refs": {"GetLifecyclePolicyResponse$Policy": "<p>Detailed information about the lifecycle policy.</p>"}}, "LifecyclePolicySummary": {"base": "<p>Summary information about a lifecycle policy.</p>", "refs": {"LifecyclePolicySummaryList$member": null}}, "LifecyclePolicySummaryList": {"base": null, "refs": {"GetLifecyclePoliciesResponse$Policies": "<p>Summary information about the lifecycle policies.</p>"}}, "LimitExceededException": {"base": "<p>The request failed because a limit was exceeded.</p>", "refs": {}}, "ListTagsForResourceRequest": {"base": null, "refs": {}}, "ListTagsForResourceResponse": {"base": null, "refs": {}}, "Parameter": {"base": null, "refs": {"ParameterList$member": null}}, "ParameterList": {"base": null, "refs": {"InvalidRequestException$RequiredParameters": "<p>The request omitted one or more required parameters.</p>", "InvalidRequestException$MutuallyExclusiveParameters": "<p>The request included parameters that cannot be provided together.</p>"}}, "Parameters": {"base": "<p>Specifies optional parameters to add to a policy. The set of valid parameters depends on the combination of policy type and resource type.</p>", "refs": {"PolicyDetails$Parameters": "<p>A set of optional parameters for the policy. </p>"}}, "PolicyArn": {"base": null, "refs": {"LifecyclePolicy$PolicyArn": "<p>The Amazon Resource Name (ARN) of the policy.</p>", "ListTagsForResourceRequest$ResourceArn": "<p>The Amazon Resource Name (ARN) of the resource.</p>", "TagResourceRequest$ResourceArn": "<p>The Amazon Resource Name (ARN) of the resource.</p>", "UntagResourceRequest$ResourceArn": "<p>The Amazon Resource Name (ARN) of the resource.</p>"}}, "PolicyDescription": {"base": null, "refs": {"CreateLifecyclePolicyRequest$Description": "<p>A description of the lifecycle policy. The characters ^[0-9A-Za-z _-]+$ are supported.</p>", "LifecyclePolicy$Description": "<p>The description of the lifecycle policy.</p>", "LifecyclePolicySummary$Description": "<p>The description of the lifecycle policy.</p>", "UpdateLifecyclePolicyRequest$Description": "<p>A description of the lifecycle policy.</p>"}}, "PolicyDetails": {"base": "<p>Specifies the configuration of a lifecycle policy.</p>", "refs": {"CreateLifecyclePolicyRequest$PolicyDetails": "<p>The configuration details of the lifecycle policy.</p>", "LifecyclePolicy$PolicyDetails": "<p>The configuration of the lifecycle policy</p>", "UpdateLifecyclePolicyRequest$PolicyDetails": "<p>The configuration of the lifecycle policy. You cannot update the policy type or the resource type.</p>"}}, "PolicyId": {"base": null, "refs": {"CreateLifecyclePolicyResponse$PolicyId": "<p>The identifier of the lifecycle policy.</p>", "DeleteLifecyclePolicyRequest$PolicyId": "<p>The identifier of the lifecycle policy.</p>", "GetLifecyclePolicyRequest$PolicyId": "<p>The identifier of the lifecycle policy.</p>", "LifecyclePolicy$PolicyId": "<p>The identifier of the lifecycle policy.</p>", "LifecyclePolicySummary$PolicyId": "<p>The identifier of the lifecycle policy.</p>", "PolicyIdList$member": null, "UpdateLifecyclePolicyRequest$PolicyId": "<p>The identifier of the lifecycle policy.</p>"}}, "PolicyIdList": {"base": null, "refs": {"GetLifecyclePoliciesRequest$PolicyIds": "<p>The identifiers of the data lifecycle policies.</p>", "ResourceNotFoundException$ResourceIds": "<p>Value is a list of resource IDs that were not found.</p>"}}, "PolicyTypeValues": {"base": null, "refs": {"PolicyDetails$PolicyType": "<p>The valid target resource types and actions a policy can manage. The default is EBS_SNAPSHOT_MANAGEMENT.</p>"}}, "ResourceNotFoundException": {"base": "<p>A requested resource was not found.</p>", "refs": {}}, "ResourceTypeValues": {"base": null, "refs": {"ResourceTypeValuesList$member": null}}, "ResourceTypeValuesList": {"base": null, "refs": {"GetLifecyclePoliciesRequest$ResourceTypes": "<p>The resource type.</p>", "PolicyDetails$ResourceTypes": "<p>The resource type. Use VOLUME to create snapshots of individual volumes or use INSTANCE to create multi-volume snapshots from the volumes for an instance.</p>"}}, "RetainRule": {"base": "<p>Specifies the retention rule for a lifecycle policy. You can retain snapshots based on either a count or a time interval.</p>", "refs": {"Schedule$RetainRule": "<p>The retention rule.</p>"}}, "RetentionIntervalUnitValues": {"base": null, "refs": {"CrossRegionCopyRetainRule$IntervalUnit": "<p>The unit of time for time-based retention.</p>", "FastRestoreRule$IntervalUnit": "<p>The unit of time for enabling fast snapshot restore.</p>", "RetainRule$IntervalUnit": "<p>The unit of time for time-based retention.</p>"}}, "Schedule": {"base": "<p>Specifies a backup schedule.</p>", "refs": {"ScheduleList$member": null}}, "ScheduleList": {"base": null, "refs": {"PolicyDetails$Schedules": "<p>The schedule of policy-defined actions.</p>"}}, "ScheduleName": {"base": null, "refs": {"Schedule$Name": "<p>The name of the schedule.</p>"}}, "SettablePolicyStateValues": {"base": null, "refs": {"CreateLifecyclePolicyRequest$State": "<p>The desired activation state of the lifecycle policy after creation.</p>", "UpdateLifecyclePolicyRequest$State": "<p>The desired activation state of the lifecycle policy after creation.</p>"}}, "StatusMessage": {"base": null, "refs": {"LifecyclePolicy$StatusMessage": "<p>The description of the status.</p>"}}, "String": {"base": null, "refs": {"LimitExceededException$ResourceType": "<p>Value is the type of resource for which a limit was exceeded.</p>", "ResourceNotFoundException$ResourceType": "<p>Value is the type of resource that was not found.</p>", "Tag$Key": "<p>The tag key.</p>", "Tag$Value": "<p>The tag value.</p>"}}, "Tag": {"base": "<p>Specifies a tag for a resource.</p>", "refs": {"TagsToAddList$member": null, "TargetTagList$member": null, "VariableTagsList$member": null}}, "TagFilter": {"base": null, "refs": {"TagsToAddFilterList$member": null, "TargetTagsFilterList$member": null}}, "TagKey": {"base": null, "refs": {"TagKeyList$member": null, "TagMap$key": null}}, "TagKeyList": {"base": null, "refs": {"UntagResourceRequest$TagKeys": "<p>The tag keys.</p>"}}, "TagMap": {"base": null, "refs": {"CreateLifecyclePolicyRequest$Tags": "<p>The tags to apply to the lifecycle policy during creation.</p>", "LifecyclePolicy$Tags": "<p>The tags.</p>", "LifecyclePolicySummary$Tags": "<p>The tags.</p>", "ListTagsForResourceResponse$Tags": "<p>Information about the tags.</p>", "TagResourceRequest$Tags": "<p>One or more tags.</p>"}}, "TagResourceRequest": {"base": null, "refs": {}}, "TagResourceResponse": {"base": null, "refs": {}}, "TagValue": {"base": null, "refs": {"TagMap$value": null}}, "TagsToAddFilterList": {"base": null, "refs": {"GetLifecyclePoliciesRequest$TagsToAdd": "<p>The tags to add to objects created by the policy.</p> <p>Tags are strings in the format <code>key=value</code>.</p> <p>These user-defined tags are added in addition to the AWS-added lifecycle tags.</p>"}}, "TagsToAddList": {"base": null, "refs": {"Schedule$TagsToAdd": "<p>The tags to apply to policy-created resources. These user-defined tags are in addition to the AWS-added lifecycle tags.</p>"}}, "TargetRegion": {"base": null, "refs": {"CrossRegionCopyRule$TargetRegion": "<p>The target Region.</p>"}}, "TargetTagList": {"base": null, "refs": {"PolicyDetails$TargetTags": "<p>The single tag that identifies targeted resources for this policy.</p>"}}, "TargetTagsFilterList": {"base": null, "refs": {"GetLifecyclePoliciesRequest$TargetTags": "<p>The target tag for a policy.</p> <p>Tags are strings in the format <code>key=value</code>.</p>"}}, "Time": {"base": null, "refs": {"TimesList$member": null}}, "TimesList": {"base": null, "refs": {"CreateRule$Times": "<p>The time, in UTC, to start the operation. The supported format is hh:mm.</p> <p>The operation occurs within a one-hour window following the specified time. If you do not specify a time, Amazon DLM selects a time within the next 24 hours.</p>"}}, "Timestamp": {"base": null, "refs": {"LifecyclePolicy$DateCreated": "<p>The local date and time when the lifecycle policy was created.</p>", "LifecyclePolicy$DateModified": "<p>The local date and time when the lifecycle policy was last modified.</p>"}}, "UntagResourceRequest": {"base": null, "refs": {}}, "UntagResourceResponse": {"base": null, "refs": {}}, "UpdateLifecyclePolicyRequest": {"base": null, "refs": {}}, "UpdateLifecyclePolicyResponse": {"base": null, "refs": {}}, "VariableTagsList": {"base": null, "refs": {"Schedule$VariableTags": "<p>A collection of key/value pairs with values determined dynamically when the policy is executed. Keys may be any valid Amazon EC2 tag key. Values must be in one of the two following formats: <code>$(instance-id)</code> or <code>$(timestamp)</code>. Variable tags are only valid for EBS Snapshot Management – Instance policies.</p>"}}}}