// Code generated by private/model/cli/gen-api/main.go. DO NOT EDIT.

package transcribestreamingservice

import (
	"github.com/aws/aws-sdk-go/private/protocol"
)

const (

	// ErrCodeBadRequestException for service response error code
	// "BadRequestException".
	//
	// One or more arguments to the StartStreamTranscription operation was invalid.
	// For example, MediaEncoding was not set to pcm or LanguageCode was not set
	// to a valid code. Check the parameters and try your request again.
	ErrCodeBadRequestException = "BadRequestException"

	// ErrCodeConflictException for service response error code
	// "ConflictException".
	//
	// A new stream started with the same session ID. The current stream has been
	// terminated.
	ErrCodeConflictException = "ConflictException"

	// ErrCodeInternalFailureException for service response error code
	// "InternalFailureException".
	//
	// A problem occurred while processing the audio. Amazon Transcribe terminated
	// processing. Try your request again.
	ErrCodeInternalFailureException = "InternalFailureException"

	// ErrCodeLimitExceededException for service response error code
	// "LimitExceededException".
	//
	// You have exceeded the maximum number of concurrent transcription streams,
	// are starting transcription streams too quickly, or the maximum audio length
	// of 4 hours. Wait until a stream has finished processing, or break your audio
	// stream into smaller chunks and try your request again.
	ErrCodeLimitExceededException = "LimitExceededException"

	// ErrCodeServiceUnavailableException for service response error code
	// "ServiceUnavailableException".
	//
	// Service is currently unavailable. Try your request later.
	ErrCodeServiceUnavailableException = "ServiceUnavailableException"
)

var exceptionFromCode = map[string]func(protocol.ResponseMetadata) error{
	"BadRequestException":         newErrorBadRequestException,
	"ConflictException":           newErrorConflictException,
	"InternalFailureException":    newErrorInternalFailureException,
	"LimitExceededException":      newErrorLimitExceededException,
	"ServiceUnavailableException": newErrorServiceUnavailableException,
}
