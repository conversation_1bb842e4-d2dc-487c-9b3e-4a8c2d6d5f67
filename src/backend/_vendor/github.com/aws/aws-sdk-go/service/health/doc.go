// Code generated by private/model/cli/gen-api/main.go. DO NOT EDIT.

// Package health provides the client and types for making API
// requests to AWS Health APIs and Notifications.
//
// The AWS Health API provides programmatic access to the AWS Health information
// that appears in the AWS Personal Health Dashboard (https://phd.aws.amazon.com/phd/home#/).
// You can use the API operations to get information about AWS Health events
// that affect your AWS services and resources.
//
// You must have a Business or Enterprise support plan from AWS Support (http://aws.amazon.com/premiumsupport/)
// to use the AWS Health API. If you call the AWS Health API from an AWS account
// that doesn't have a Business or Enterprise support plan, you receive a SubscriptionRequiredException
// error.
//
// AWS Health has a single endpoint: health.us-east-1.amazonaws.com (HTTPS).
// Use this endpoint to call the AWS Health API operations.
//
// For authentication of requests, AWS Health uses the Signature Version 4 Signing
// Process (https://docs.aws.amazon.com/general/latest/gr/signature-version-4.html).
//
// If your AWS account is part of AWS Organizations, you can use the AWS Health
// organizational view feature. This feature provides a centralized view of
// AWS Health events across all accounts in your organization. You can aggregate
// AWS Health events in real time to identify accounts in your organization
// that are affected by an operational event or get notified of security vulnerabilities.
// Use the organizational view API operations to enable this feature and return
// event information. For more information, see Aggregating AWS Health events
// (https://docs.aws.amazon.com/health/latest/ug/aggregate-events.html) in the
// AWS Health User Guide.
//
// When you use the AWS Health API operations to return AWS Health events, see
// the following recommendations:
//
//    * Use the eventScopeCode (https://docs.aws.amazon.com/health/latest/APIReference/API_Event.html#AWSHealth-Type-Event-eventScopeCode)
//    parameter to specify whether to return AWS Health events that are public
//    or account-specific.
//
//    * Use pagination to view all events from the response. For example, if
//    you call the DescribeEventsForOrganization operation to get all events
//    in your organization, you might receive several page results. Specify
//    the nextToken in the next request to return more results.
//
// See https://docs.aws.amazon.com/goto/WebAPI/health-2016-08-04 for more information on this service.
//
// See health package documentation for more information.
// https://docs.aws.amazon.com/sdk-for-go/api/service/health/
//
// Using the Client
//
// To contact AWS Health APIs and Notifications with the SDK use the New function to create
// a new service client. With that client you can make API requests to the service.
// These clients are safe to use concurrently.
//
// See the SDK's documentation for more information on how to use the SDK.
// https://docs.aws.amazon.com/sdk-for-go/api/
//
// See aws.Config documentation for more information on configuring SDK clients.
// https://docs.aws.amazon.com/sdk-for-go/api/aws/#Config
//
// See the AWS Health APIs and Notifications client Health for more
// information on creating client for this service.
// https://docs.aws.amazon.com/sdk-for-go/api/service/health/#New
package health
