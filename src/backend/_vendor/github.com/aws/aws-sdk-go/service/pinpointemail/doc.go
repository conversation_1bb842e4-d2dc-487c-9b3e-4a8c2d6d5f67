// Code generated by private/model/cli/gen-api/main.go. DO NOT EDIT.

// Package pinpointemail provides the client and types for making API
// requests to Amazon Pinpoint Email Service.
//
// Welcome to the Amazon Pinpoint Email API Reference. This guide provides information
// about the Amazon Pinpoint Email API (version 1.0), including supported operations,
// data types, parameters, and schemas.
//
// Amazon Pinpoint (https://aws.amazon.com/pinpoint) is an AWS service that
// you can use to engage with your customers across multiple messaging channels.
// You can use Amazon Pinpoint to send email, SMS text messages, voice messages,
// and push notifications. The Amazon Pinpoint Email API provides programmatic
// access to options that are unique to the email channel and supplement the
// options provided by the Amazon Pinpoint API.
//
// If you're new to Amazon Pinpoint, you might find it helpful to also review
// the Amazon Pinpoint Developer Guide (https://docs.aws.amazon.com/pinpoint/latest/developerguide/welcome.html).
// The Amazon Pinpoint Developer Guide provides tutorials, code samples, and
// procedures that demonstrate how to use Amazon Pinpoint features programmatically
// and how to integrate Amazon Pinpoint functionality into mobile apps and other
// types of applications. The guide also provides information about key topics
// such as Amazon Pinpoint integration with other AWS services and the limits
// that apply to using the service.
//
// The Amazon Pinpoint Email API is available in several AWS Regions and it
// provides an endpoint for each of these Regions. For a list of all the Regions
// and endpoints where the API is currently available, see AWS Service Endpoints
// (https://docs.aws.amazon.com/general/latest/gr/rande.html#pinpoint_region)
// in the Amazon Web Services General Reference. To learn more about AWS Regions,
// see Managing AWS Regions (https://docs.aws.amazon.com/general/latest/gr/rande-manage.html)
// in the Amazon Web Services General Reference.
//
// In each Region, AWS maintains multiple Availability Zones. These Availability
// Zones are physically isolated from each other, but are united by private,
// low-latency, high-throughput, and highly redundant network connections. These
// Availability Zones enable us to provide very high levels of availability
// and redundancy, while also minimizing latency. To learn more about the number
// of Availability Zones that are available in each Region, see AWS Global Infrastructure
// (http://aws.amazon.com/about-aws/global-infrastructure/).
//
// See https://docs.aws.amazon.com/goto/WebAPI/pinpoint-email-2018-07-26 for more information on this service.
//
// See pinpointemail package documentation for more information.
// https://docs.aws.amazon.com/sdk-for-go/api/service/pinpointemail/
//
// Using the Client
//
// To contact Amazon Pinpoint Email Service with the SDK use the New function to create
// a new service client. With that client you can make API requests to the service.
// These clients are safe to use concurrently.
//
// See the SDK's documentation for more information on how to use the SDK.
// https://docs.aws.amazon.com/sdk-for-go/api/
//
// See aws.Config documentation for more information on configuring SDK clients.
// https://docs.aws.amazon.com/sdk-for-go/api/aws/#Config
//
// See the Amazon Pinpoint Email Service client PinpointEmail for more
// information on creating client for this service.
// https://docs.aws.amazon.com/sdk-for-go/api/service/pinpointemail/#New
package pinpointemail
