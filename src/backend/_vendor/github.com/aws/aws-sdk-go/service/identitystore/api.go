// Code generated by private/model/cli/gen-api/main.go. DO NOT EDIT.

package identitystore

import (
	"fmt"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/awsutil"
	"github.com/aws/aws-sdk-go/aws/request"
	"github.com/aws/aws-sdk-go/private/protocol"
)

const opDescribeGroup = "DescribeGroup"

// DescribeGroupRequest generates a "aws/request.Request" representing the
// client's request for the DescribeGroup operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See DescribeGroup for more information on using the DescribeGroup
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//
//    // Example sending a request using the DescribeGroupRequest method.
//    req, resp := client.DescribeGroupRequest(params)
//
//    err := req.Send()
//    if err == nil { // resp is now filled
//        fmt.Println(resp)
//    }
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/identitystore-2020-06-15/DescribeGroup
func (c *IdentityStore) DescribeGroupRequest(input *DescribeGroupInput) (req *request.Request, output *DescribeGroupOutput) {
	op := &request.Operation{
		Name:       opDescribeGroup,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &DescribeGroupInput{}
	}

	output = &DescribeGroupOutput{}
	req = c.newRequest(op, input, output)
	return
}

// DescribeGroup API operation for AWS SSO Identity Store.
//
// Retrieves the group metadata and attributes from GroupId in an identity store.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for AWS SSO Identity Store's
// API operation DescribeGroup for usage and error information.
//
// Returned Error Types:
//   * ResourceNotFoundException
//   Indicates that a requested resource is not found.
//
//   * ValidationException
//   The request failed because it contains a syntax error.
//
//   * AccessDeniedException
//   You do not have sufficient access to perform this action.
//
//   * ThrottlingException
//   Indicates that the principal has crossed the throttling limits of the API
//   operations.
//
//   * InternalServerException
//   The request processing has failed because of an unknown error, exception
//   or failure with an internal server.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/identitystore-2020-06-15/DescribeGroup
func (c *IdentityStore) DescribeGroup(input *DescribeGroupInput) (*DescribeGroupOutput, error) {
	req, out := c.DescribeGroupRequest(input)
	return out, req.Send()
}

// DescribeGroupWithContext is the same as DescribeGroup with the addition of
// the ability to pass a context and additional request options.
//
// See DescribeGroup for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *IdentityStore) DescribeGroupWithContext(ctx aws.Context, input *DescribeGroupInput, opts ...request.Option) (*DescribeGroupOutput, error) {
	req, out := c.DescribeGroupRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opDescribeUser = "DescribeUser"

// DescribeUserRequest generates a "aws/request.Request" representing the
// client's request for the DescribeUser operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See DescribeUser for more information on using the DescribeUser
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//
//    // Example sending a request using the DescribeUserRequest method.
//    req, resp := client.DescribeUserRequest(params)
//
//    err := req.Send()
//    if err == nil { // resp is now filled
//        fmt.Println(resp)
//    }
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/identitystore-2020-06-15/DescribeUser
func (c *IdentityStore) DescribeUserRequest(input *DescribeUserInput) (req *request.Request, output *DescribeUserOutput) {
	op := &request.Operation{
		Name:       opDescribeUser,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &DescribeUserInput{}
	}

	output = &DescribeUserOutput{}
	req = c.newRequest(op, input, output)
	return
}

// DescribeUser API operation for AWS SSO Identity Store.
//
// Retrieves the user metadata and attributes from UserId in an identity store.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for AWS SSO Identity Store's
// API operation DescribeUser for usage and error information.
//
// Returned Error Types:
//   * ResourceNotFoundException
//   Indicates that a requested resource is not found.
//
//   * ValidationException
//   The request failed because it contains a syntax error.
//
//   * AccessDeniedException
//   You do not have sufficient access to perform this action.
//
//   * ThrottlingException
//   Indicates that the principal has crossed the throttling limits of the API
//   operations.
//
//   * InternalServerException
//   The request processing has failed because of an unknown error, exception
//   or failure with an internal server.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/identitystore-2020-06-15/DescribeUser
func (c *IdentityStore) DescribeUser(input *DescribeUserInput) (*DescribeUserOutput, error) {
	req, out := c.DescribeUserRequest(input)
	return out, req.Send()
}

// DescribeUserWithContext is the same as DescribeUser with the addition of
// the ability to pass a context and additional request options.
//
// See DescribeUser for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *IdentityStore) DescribeUserWithContext(ctx aws.Context, input *DescribeUserInput, opts ...request.Option) (*DescribeUserOutput, error) {
	req, out := c.DescribeUserRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opListGroups = "ListGroups"

// ListGroupsRequest generates a "aws/request.Request" representing the
// client's request for the ListGroups operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See ListGroups for more information on using the ListGroups
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//
//    // Example sending a request using the ListGroupsRequest method.
//    req, resp := client.ListGroupsRequest(params)
//
//    err := req.Send()
//    if err == nil { // resp is now filled
//        fmt.Println(resp)
//    }
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/identitystore-2020-06-15/ListGroups
func (c *IdentityStore) ListGroupsRequest(input *ListGroupsInput) (req *request.Request, output *ListGroupsOutput) {
	op := &request.Operation{
		Name:       opListGroups,
		HTTPMethod: "POST",
		HTTPPath:   "/",
		Paginator: &request.Paginator{
			InputTokens:     []string{"NextToken"},
			OutputTokens:    []string{"NextToken"},
			LimitToken:      "MaxResults",
			TruncationToken: "",
		},
	}

	if input == nil {
		input = &ListGroupsInput{}
	}

	output = &ListGroupsOutput{}
	req = c.newRequest(op, input, output)
	return
}

// ListGroups API operation for AWS SSO Identity Store.
//
// Lists the attribute name and value of the group that you specified in the
// search. We only support DisplayName as a valid filter attribute path currently,
// and filter is required. This API returns minimum attributes, including GroupId
// and group DisplayName in the response.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for AWS SSO Identity Store's
// API operation ListGroups for usage and error information.
//
// Returned Error Types:
//   * ValidationException
//   The request failed because it contains a syntax error.
//
//   * AccessDeniedException
//   You do not have sufficient access to perform this action.
//
//   * ResourceNotFoundException
//   Indicates that a requested resource is not found.
//
//   * ThrottlingException
//   Indicates that the principal has crossed the throttling limits of the API
//   operations.
//
//   * InternalServerException
//   The request processing has failed because of an unknown error, exception
//   or failure with an internal server.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/identitystore-2020-06-15/ListGroups
func (c *IdentityStore) ListGroups(input *ListGroupsInput) (*ListGroupsOutput, error) {
	req, out := c.ListGroupsRequest(input)
	return out, req.Send()
}

// ListGroupsWithContext is the same as ListGroups with the addition of
// the ability to pass a context and additional request options.
//
// See ListGroups for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *IdentityStore) ListGroupsWithContext(ctx aws.Context, input *ListGroupsInput, opts ...request.Option) (*ListGroupsOutput, error) {
	req, out := c.ListGroupsRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

// ListGroupsPages iterates over the pages of a ListGroups operation,
// calling the "fn" function with the response data for each page. To stop
// iterating, return false from the fn function.
//
// See ListGroups method for more information on how to use this operation.
//
// Note: This operation can generate multiple requests to a service.
//
//    // Example iterating over at most 3 pages of a ListGroups operation.
//    pageNum := 0
//    err := client.ListGroupsPages(params,
//        func(page *identitystore.ListGroupsOutput, lastPage bool) bool {
//            pageNum++
//            fmt.Println(page)
//            return pageNum <= 3
//        })
//
func (c *IdentityStore) ListGroupsPages(input *ListGroupsInput, fn func(*ListGroupsOutput, bool) bool) error {
	return c.ListGroupsPagesWithContext(aws.BackgroundContext(), input, fn)
}

// ListGroupsPagesWithContext same as ListGroupsPages except
// it takes a Context and allows setting request options on the pages.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *IdentityStore) ListGroupsPagesWithContext(ctx aws.Context, input *ListGroupsInput, fn func(*ListGroupsOutput, bool) bool, opts ...request.Option) error {
	p := request.Pagination{
		NewRequest: func() (*request.Request, error) {
			var inCpy *ListGroupsInput
			if input != nil {
				tmp := *input
				inCpy = &tmp
			}
			req, _ := c.ListGroupsRequest(inCpy)
			req.SetContext(ctx)
			req.ApplyOptions(opts...)
			return req, nil
		},
	}

	for p.Next() {
		if !fn(p.Page().(*ListGroupsOutput), !p.HasNextPage()) {
			break
		}
	}

	return p.Err()
}

const opListUsers = "ListUsers"

// ListUsersRequest generates a "aws/request.Request" representing the
// client's request for the ListUsers operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See ListUsers for more information on using the ListUsers
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//
//    // Example sending a request using the ListUsersRequest method.
//    req, resp := client.ListUsersRequest(params)
//
//    err := req.Send()
//    if err == nil { // resp is now filled
//        fmt.Println(resp)
//    }
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/identitystore-2020-06-15/ListUsers
func (c *IdentityStore) ListUsersRequest(input *ListUsersInput) (req *request.Request, output *ListUsersOutput) {
	op := &request.Operation{
		Name:       opListUsers,
		HTTPMethod: "POST",
		HTTPPath:   "/",
		Paginator: &request.Paginator{
			InputTokens:     []string{"NextToken"},
			OutputTokens:    []string{"NextToken"},
			LimitToken:      "MaxResults",
			TruncationToken: "",
		},
	}

	if input == nil {
		input = &ListUsersInput{}
	}

	output = &ListUsersOutput{}
	req = c.newRequest(op, input, output)
	return
}

// ListUsers API operation for AWS SSO Identity Store.
//
// Lists the attribute name and value of the user that you specified in the
// search. We only support UserName as a valid filter attribute path currently,
// and filter is required. This API returns minimum attributes, including UserId
// and UserName in the response.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for AWS SSO Identity Store's
// API operation ListUsers for usage and error information.
//
// Returned Error Types:
//   * ValidationException
//   The request failed because it contains a syntax error.
//
//   * AccessDeniedException
//   You do not have sufficient access to perform this action.
//
//   * ResourceNotFoundException
//   Indicates that a requested resource is not found.
//
//   * ThrottlingException
//   Indicates that the principal has crossed the throttling limits of the API
//   operations.
//
//   * InternalServerException
//   The request processing has failed because of an unknown error, exception
//   or failure with an internal server.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/identitystore-2020-06-15/ListUsers
func (c *IdentityStore) ListUsers(input *ListUsersInput) (*ListUsersOutput, error) {
	req, out := c.ListUsersRequest(input)
	return out, req.Send()
}

// ListUsersWithContext is the same as ListUsers with the addition of
// the ability to pass a context and additional request options.
//
// See ListUsers for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *IdentityStore) ListUsersWithContext(ctx aws.Context, input *ListUsersInput, opts ...request.Option) (*ListUsersOutput, error) {
	req, out := c.ListUsersRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

// ListUsersPages iterates over the pages of a ListUsers operation,
// calling the "fn" function with the response data for each page. To stop
// iterating, return false from the fn function.
//
// See ListUsers method for more information on how to use this operation.
//
// Note: This operation can generate multiple requests to a service.
//
//    // Example iterating over at most 3 pages of a ListUsers operation.
//    pageNum := 0
//    err := client.ListUsersPages(params,
//        func(page *identitystore.ListUsersOutput, lastPage bool) bool {
//            pageNum++
//            fmt.Println(page)
//            return pageNum <= 3
//        })
//
func (c *IdentityStore) ListUsersPages(input *ListUsersInput, fn func(*ListUsersOutput, bool) bool) error {
	return c.ListUsersPagesWithContext(aws.BackgroundContext(), input, fn)
}

// ListUsersPagesWithContext same as ListUsersPages except
// it takes a Context and allows setting request options on the pages.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *IdentityStore) ListUsersPagesWithContext(ctx aws.Context, input *ListUsersInput, fn func(*ListUsersOutput, bool) bool, opts ...request.Option) error {
	p := request.Pagination{
		NewRequest: func() (*request.Request, error) {
			var inCpy *ListUsersInput
			if input != nil {
				tmp := *input
				inCpy = &tmp
			}
			req, _ := c.ListUsersRequest(inCpy)
			req.SetContext(ctx)
			req.ApplyOptions(opts...)
			return req, nil
		},
	}

	for p.Next() {
		if !fn(p.Page().(*ListUsersOutput), !p.HasNextPage()) {
			break
		}
	}

	return p.Err()
}

// You do not have sufficient access to perform this action.
type AccessDeniedException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	Message_ *string `locationName:"Message" min:"1" type:"string"`

	// The identifier for each request. This value is a globally unique ID that
	// is generated by the Identity Store service for each sent request, and is
	// then returned inside the exception if the request fails.
	RequestId *string `type:"string"`
}

// String returns the string representation
func (s AccessDeniedException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s AccessDeniedException) GoString() string {
	return s.String()
}

func newErrorAccessDeniedException(v protocol.ResponseMetadata) error {
	return &AccessDeniedException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *AccessDeniedException) Code() string {
	return "AccessDeniedException"
}

// Message returns the exception's message.
func (s *AccessDeniedException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *AccessDeniedException) OrigErr() error {
	return nil
}

func (s *AccessDeniedException) Error() string {
	return fmt.Sprintf("%s: %s\n%s", s.Code(), s.Message(), s.String())
}

// Status code returns the HTTP status code for the request's response error.
func (s *AccessDeniedException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *AccessDeniedException) RequestID() string {
	return s.RespMetadata.RequestID
}

type DescribeGroupInput struct {
	_ struct{} `type:"structure"`

	// The identifier for a group in the identity store.
	//
	// GroupId is a required field
	GroupId *string `min:"1" type:"string" required:"true"`

	// The globally unique identifier for the identity store, such as d-1234567890.
	// In this example, d- is a fixed prefix, and 1234567890 is a randomly generated
	// string which contains number and lower case letters. This value is generated
	// at the time that a new identity store is created.
	//
	// IdentityStoreId is a required field
	IdentityStoreId *string `min:"1" type:"string" required:"true"`
}

// String returns the string representation
func (s DescribeGroupInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s DescribeGroupInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *DescribeGroupInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "DescribeGroupInput"}
	if s.GroupId == nil {
		invalidParams.Add(request.NewErrParamRequired("GroupId"))
	}
	if s.GroupId != nil && len(*s.GroupId) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("GroupId", 1))
	}
	if s.IdentityStoreId == nil {
		invalidParams.Add(request.NewErrParamRequired("IdentityStoreId"))
	}
	if s.IdentityStoreId != nil && len(*s.IdentityStoreId) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("IdentityStoreId", 1))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetGroupId sets the GroupId field's value.
func (s *DescribeGroupInput) SetGroupId(v string) *DescribeGroupInput {
	s.GroupId = &v
	return s
}

// SetIdentityStoreId sets the IdentityStoreId field's value.
func (s *DescribeGroupInput) SetIdentityStoreId(v string) *DescribeGroupInput {
	s.IdentityStoreId = &v
	return s
}

type DescribeGroupOutput struct {
	_ struct{} `type:"structure"`

	// Contains the group’s display name value. The length limit is 1024 characters.
	// This value can consist of letters, accented characters, symbols, numbers,
	// punctuation, tab, new line, carriage return, space and non breaking space
	// in this attribute. The characters “<>;:%” are excluded. This value is
	// specified at the time the group is created and stored as an attribute of
	// the group object in the identity store.
	//
	// DisplayName is a required field
	DisplayName *string `min:"1" type:"string" required:"true"`

	// The identifier for a group in the identity store.
	//
	// GroupId is a required field
	GroupId *string `min:"1" type:"string" required:"true"`
}

// String returns the string representation
func (s DescribeGroupOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s DescribeGroupOutput) GoString() string {
	return s.String()
}

// SetDisplayName sets the DisplayName field's value.
func (s *DescribeGroupOutput) SetDisplayName(v string) *DescribeGroupOutput {
	s.DisplayName = &v
	return s
}

// SetGroupId sets the GroupId field's value.
func (s *DescribeGroupOutput) SetGroupId(v string) *DescribeGroupOutput {
	s.GroupId = &v
	return s
}

type DescribeUserInput struct {
	_ struct{} `type:"structure"`

	// The globally unique identifier for the identity store, such as d-1234567890.
	// In this example, d- is a fixed prefix, and 1234567890 is a randomly generated
	// string which contains number and lower case letters. This value is generated
	// at the time that a new identity store is created.
	//
	// IdentityStoreId is a required field
	IdentityStoreId *string `min:"1" type:"string" required:"true"`

	// The identifier for a user in the identity store.
	//
	// UserId is a required field
	UserId *string `min:"1" type:"string" required:"true"`
}

// String returns the string representation
func (s DescribeUserInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s DescribeUserInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *DescribeUserInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "DescribeUserInput"}
	if s.IdentityStoreId == nil {
		invalidParams.Add(request.NewErrParamRequired("IdentityStoreId"))
	}
	if s.IdentityStoreId != nil && len(*s.IdentityStoreId) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("IdentityStoreId", 1))
	}
	if s.UserId == nil {
		invalidParams.Add(request.NewErrParamRequired("UserId"))
	}
	if s.UserId != nil && len(*s.UserId) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("UserId", 1))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetIdentityStoreId sets the IdentityStoreId field's value.
func (s *DescribeUserInput) SetIdentityStoreId(v string) *DescribeUserInput {
	s.IdentityStoreId = &v
	return s
}

// SetUserId sets the UserId field's value.
func (s *DescribeUserInput) SetUserId(v string) *DescribeUserInput {
	s.UserId = &v
	return s
}

type DescribeUserOutput struct {
	_ struct{} `type:"structure"`

	// The identifier for a user in the identity store.
	//
	// UserId is a required field
	UserId *string `min:"1" type:"string" required:"true"`

	// Contains the user’s username value. The length limit is 128 characters.
	// This value can consist of letters, accented characters, symbols, numbers
	// and punctuation. The characters “<>;:%” are excluded. This value is specified
	// at the time the user is created and stored as an attribute of the user object
	// in the identity store.
	//
	// UserName is a required field
	UserName *string `min:"1" type:"string" required:"true" sensitive:"true"`
}

// String returns the string representation
func (s DescribeUserOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s DescribeUserOutput) GoString() string {
	return s.String()
}

// SetUserId sets the UserId field's value.
func (s *DescribeUserOutput) SetUserId(v string) *DescribeUserOutput {
	s.UserId = &v
	return s
}

// SetUserName sets the UserName field's value.
func (s *DescribeUserOutput) SetUserName(v string) *DescribeUserOutput {
	s.UserName = &v
	return s
}

// A query filter used by ListUsers and ListGroup. This filter object provides
// the attribute name and attribute value to search users or groups.
type Filter struct {
	_ struct{} `type:"structure"`

	// The attribute path used to specify which attribute name to search. Length
	// limit is 255 characters. For example, UserName is a valid attribute path
	// for the ListUsers API, and DisplayName is a valid attribute path for the
	// ListGroups API.
	//
	// AttributePath is a required field
	AttributePath *string `min:"1" type:"string" required:"true"`

	// Represents the data for an attribute. Each attribute value is described as
	// a name-value pair.
	//
	// AttributeValue is a required field
	AttributeValue *string `min:"1" type:"string" required:"true" sensitive:"true"`
}

// String returns the string representation
func (s Filter) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s Filter) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *Filter) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "Filter"}
	if s.AttributePath == nil {
		invalidParams.Add(request.NewErrParamRequired("AttributePath"))
	}
	if s.AttributePath != nil && len(*s.AttributePath) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("AttributePath", 1))
	}
	if s.AttributeValue == nil {
		invalidParams.Add(request.NewErrParamRequired("AttributeValue"))
	}
	if s.AttributeValue != nil && len(*s.AttributeValue) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("AttributeValue", 1))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetAttributePath sets the AttributePath field's value.
func (s *Filter) SetAttributePath(v string) *Filter {
	s.AttributePath = &v
	return s
}

// SetAttributeValue sets the AttributeValue field's value.
func (s *Filter) SetAttributeValue(v string) *Filter {
	s.AttributeValue = &v
	return s
}

// A group object, which contains a specified group’s metadata and attributes.
type Group struct {
	_ struct{} `type:"structure"`

	// Contains the group’s display name value. The length limit is 1024 characters.
	// This value can consist of letters, accented characters, symbols, numbers,
	// punctuation, tab, new line, carriage return, space and non breaking space
	// in this attribute. The characters “<>;:%” are excluded. This value is
	// specified at the time the group is created and stored as an attribute of
	// the group object in the identity store.
	//
	// DisplayName is a required field
	DisplayName *string `min:"1" type:"string" required:"true"`

	// The identifier for a group in the identity store.
	//
	// GroupId is a required field
	GroupId *string `min:"1" type:"string" required:"true"`
}

// String returns the string representation
func (s Group) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s Group) GoString() string {
	return s.String()
}

// SetDisplayName sets the DisplayName field's value.
func (s *Group) SetDisplayName(v string) *Group {
	s.DisplayName = &v
	return s
}

// SetGroupId sets the GroupId field's value.
func (s *Group) SetGroupId(v string) *Group {
	s.GroupId = &v
	return s
}

// The request processing has failed because of an unknown error, exception
// or failure with an internal server.
type InternalServerException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	Message_ *string `locationName:"Message" min:"1" type:"string"`

	// The identifier for each request. This value is a globally unique ID that
	// is generated by the Identity Store service for each sent request, and is
	// then returned inside the exception if the request fails.
	RequestId *string `type:"string"`
}

// String returns the string representation
func (s InternalServerException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s InternalServerException) GoString() string {
	return s.String()
}

func newErrorInternalServerException(v protocol.ResponseMetadata) error {
	return &InternalServerException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *InternalServerException) Code() string {
	return "InternalServerException"
}

// Message returns the exception's message.
func (s *InternalServerException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *InternalServerException) OrigErr() error {
	return nil
}

func (s *InternalServerException) Error() string {
	return fmt.Sprintf("%s: %s\n%s", s.Code(), s.Message(), s.String())
}

// Status code returns the HTTP status code for the request's response error.
func (s *InternalServerException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *InternalServerException) RequestID() string {
	return s.RespMetadata.RequestID
}

type ListGroupsInput struct {
	_ struct{} `type:"structure"`

	// A list of Filter objects, which is used in the ListUsers and ListGroups request.
	Filters []*Filter `type:"list"`

	// The globally unique identifier for the identity store, such as d-1234567890.
	// In this example, d- is a fixed prefix, and 1234567890 is a randomly generated
	// string which contains number and lower case letters. This value is generated
	// at the time that a new identity store is created.
	//
	// IdentityStoreId is a required field
	IdentityStoreId *string `min:"1" type:"string" required:"true"`

	// The maximum number of results to be returned per request, which is used in
	// the ListUsers and ListGroups request to specify how many results to return
	// in one page. The length limit is 50 characters.
	MaxResults *int64 `min:"1" type:"integer"`

	// The pagination token used for the ListUsers and ListGroups APIs. This value
	// is generated by the identity store service and is returned in the API response
	// if the total results are more than the size of one page, and when this token
	// is used in the API request to search for the next page.
	NextToken *string `min:"1" type:"string"`
}

// String returns the string representation
func (s ListGroupsInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s ListGroupsInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *ListGroupsInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "ListGroupsInput"}
	if s.IdentityStoreId == nil {
		invalidParams.Add(request.NewErrParamRequired("IdentityStoreId"))
	}
	if s.IdentityStoreId != nil && len(*s.IdentityStoreId) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("IdentityStoreId", 1))
	}
	if s.MaxResults != nil && *s.MaxResults < 1 {
		invalidParams.Add(request.NewErrParamMinValue("MaxResults", 1))
	}
	if s.NextToken != nil && len(*s.NextToken) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("NextToken", 1))
	}
	if s.Filters != nil {
		for i, v := range s.Filters {
			if v == nil {
				continue
			}
			if err := v.Validate(); err != nil {
				invalidParams.AddNested(fmt.Sprintf("%s[%v]", "Filters", i), err.(request.ErrInvalidParams))
			}
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetFilters sets the Filters field's value.
func (s *ListGroupsInput) SetFilters(v []*Filter) *ListGroupsInput {
	s.Filters = v
	return s
}

// SetIdentityStoreId sets the IdentityStoreId field's value.
func (s *ListGroupsInput) SetIdentityStoreId(v string) *ListGroupsInput {
	s.IdentityStoreId = &v
	return s
}

// SetMaxResults sets the MaxResults field's value.
func (s *ListGroupsInput) SetMaxResults(v int64) *ListGroupsInput {
	s.MaxResults = &v
	return s
}

// SetNextToken sets the NextToken field's value.
func (s *ListGroupsInput) SetNextToken(v string) *ListGroupsInput {
	s.NextToken = &v
	return s
}

type ListGroupsOutput struct {
	_ struct{} `type:"structure"`

	// A list of Group objects in the identity store.
	//
	// Groups is a required field
	Groups []*Group `type:"list" required:"true"`

	// The pagination token used for the ListUsers and ListGroups APIs. This value
	// is generated by the identity store service and is returned in the API response
	// if the total results are more than the size of one page, and when this token
	// is used in the API request to search for the next page.
	NextToken *string `min:"1" type:"string"`
}

// String returns the string representation
func (s ListGroupsOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s ListGroupsOutput) GoString() string {
	return s.String()
}

// SetGroups sets the Groups field's value.
func (s *ListGroupsOutput) SetGroups(v []*Group) *ListGroupsOutput {
	s.Groups = v
	return s
}

// SetNextToken sets the NextToken field's value.
func (s *ListGroupsOutput) SetNextToken(v string) *ListGroupsOutput {
	s.NextToken = &v
	return s
}

type ListUsersInput struct {
	_ struct{} `type:"structure"`

	// A list of Filter objects, which is used in the ListUsers and ListGroups request.
	Filters []*Filter `type:"list"`

	// The globally unique identifier for the identity store, such as d-1234567890.
	// In this example, d- is a fixed prefix, and 1234567890 is a randomly generated
	// string which contains number and lower case letters. This value is generated
	// at the time that a new identity store is created.
	//
	// IdentityStoreId is a required field
	IdentityStoreId *string `min:"1" type:"string" required:"true"`

	// The maximum number of results to be returned per request, which is used in
	// the ListUsers and ListGroups request to specify how many results to return
	// in one page. The length limit is 50 characters.
	MaxResults *int64 `min:"1" type:"integer"`

	// The pagination token used for the ListUsers and ListGroups APIs. This value
	// is generated by the identity store service and is returned in the API response
	// if the total results are more than the size of one page, and when this token
	// is used in the API request to search for the next page.
	NextToken *string `min:"1" type:"string"`
}

// String returns the string representation
func (s ListUsersInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s ListUsersInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *ListUsersInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "ListUsersInput"}
	if s.IdentityStoreId == nil {
		invalidParams.Add(request.NewErrParamRequired("IdentityStoreId"))
	}
	if s.IdentityStoreId != nil && len(*s.IdentityStoreId) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("IdentityStoreId", 1))
	}
	if s.MaxResults != nil && *s.MaxResults < 1 {
		invalidParams.Add(request.NewErrParamMinValue("MaxResults", 1))
	}
	if s.NextToken != nil && len(*s.NextToken) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("NextToken", 1))
	}
	if s.Filters != nil {
		for i, v := range s.Filters {
			if v == nil {
				continue
			}
			if err := v.Validate(); err != nil {
				invalidParams.AddNested(fmt.Sprintf("%s[%v]", "Filters", i), err.(request.ErrInvalidParams))
			}
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetFilters sets the Filters field's value.
func (s *ListUsersInput) SetFilters(v []*Filter) *ListUsersInput {
	s.Filters = v
	return s
}

// SetIdentityStoreId sets the IdentityStoreId field's value.
func (s *ListUsersInput) SetIdentityStoreId(v string) *ListUsersInput {
	s.IdentityStoreId = &v
	return s
}

// SetMaxResults sets the MaxResults field's value.
func (s *ListUsersInput) SetMaxResults(v int64) *ListUsersInput {
	s.MaxResults = &v
	return s
}

// SetNextToken sets the NextToken field's value.
func (s *ListUsersInput) SetNextToken(v string) *ListUsersInput {
	s.NextToken = &v
	return s
}

type ListUsersOutput struct {
	_ struct{} `type:"structure"`

	// The pagination token used for the ListUsers and ListGroups APIs. This value
	// is generated by the identity store service and is returned in the API response
	// if the total results are more than the size of one page, and when this token
	// is used in the API request to search for the next page.
	NextToken *string `min:"1" type:"string"`

	// A list of User objects in the identity store.
	//
	// Users is a required field
	Users []*User `type:"list" required:"true"`
}

// String returns the string representation
func (s ListUsersOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s ListUsersOutput) GoString() string {
	return s.String()
}

// SetNextToken sets the NextToken field's value.
func (s *ListUsersOutput) SetNextToken(v string) *ListUsersOutput {
	s.NextToken = &v
	return s
}

// SetUsers sets the Users field's value.
func (s *ListUsersOutput) SetUsers(v []*User) *ListUsersOutput {
	s.Users = v
	return s
}

// Indicates that a requested resource is not found.
type ResourceNotFoundException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	Message_ *string `locationName:"Message" min:"1" type:"string"`

	// The identifier for each request. This value is a globally unique ID that
	// is generated by the Identity Store service for each sent request, and is
	// then returned inside the exception if the request fails.
	RequestId *string `type:"string"`

	// The identifier for a resource in the identity store, which can be used as
	// UserId or GroupId. The format for ResourceId is either UUID or 1234567890-UUID,
	// where UUID is a randomly generated value for each resource when it is created
	// and 1234567890 represents the IdentityStoreId string value. In the case that
	// the identity store is migrated from a legacy SSO identity store, the ResourceId
	// for that identity store will be in the format of UUID. Otherwise, it will
	// be in the 1234567890-UUID format.
	ResourceId *string `min:"1" type:"string"`

	// The type of resource in the Identity Store service, which is an enum object.
	// Valid values include USER, GROUP, and IDENTITY_STORE.
	ResourceType *string `type:"string" enum:"ResourceType"`
}

// String returns the string representation
func (s ResourceNotFoundException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s ResourceNotFoundException) GoString() string {
	return s.String()
}

func newErrorResourceNotFoundException(v protocol.ResponseMetadata) error {
	return &ResourceNotFoundException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *ResourceNotFoundException) Code() string {
	return "ResourceNotFoundException"
}

// Message returns the exception's message.
func (s *ResourceNotFoundException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *ResourceNotFoundException) OrigErr() error {
	return nil
}

func (s *ResourceNotFoundException) Error() string {
	return fmt.Sprintf("%s: %s\n%s", s.Code(), s.Message(), s.String())
}

// Status code returns the HTTP status code for the request's response error.
func (s *ResourceNotFoundException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *ResourceNotFoundException) RequestID() string {
	return s.RespMetadata.RequestID
}

// Indicates that the principal has crossed the throttling limits of the API
// operations.
type ThrottlingException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	Message_ *string `locationName:"Message" min:"1" type:"string"`

	// The identifier for each request. This value is a globally unique ID that
	// is generated by the Identity Store service for each sent request, and is
	// then returned inside the exception if the request fails.
	RequestId *string `type:"string"`
}

// String returns the string representation
func (s ThrottlingException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s ThrottlingException) GoString() string {
	return s.String()
}

func newErrorThrottlingException(v protocol.ResponseMetadata) error {
	return &ThrottlingException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *ThrottlingException) Code() string {
	return "ThrottlingException"
}

// Message returns the exception's message.
func (s *ThrottlingException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *ThrottlingException) OrigErr() error {
	return nil
}

func (s *ThrottlingException) Error() string {
	return fmt.Sprintf("%s: %s\n%s", s.Code(), s.Message(), s.String())
}

// Status code returns the HTTP status code for the request's response error.
func (s *ThrottlingException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *ThrottlingException) RequestID() string {
	return s.RespMetadata.RequestID
}

// A user object, which contains a specified user’s metadata and attributes.
type User struct {
	_ struct{} `type:"structure"`

	// The identifier for a user in the identity store.
	//
	// UserId is a required field
	UserId *string `min:"1" type:"string" required:"true"`

	// Contains the user’s username value. The length limit is 128 characters.
	// This value can consist of letters, accented characters, symbols, numbers
	// and punctuation. The characters “<>;:%” are excluded. This value is specified
	// at the time the user is created and stored as an attribute of the user object
	// in the identity store.
	//
	// UserName is a required field
	UserName *string `min:"1" type:"string" required:"true" sensitive:"true"`
}

// String returns the string representation
func (s User) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s User) GoString() string {
	return s.String()
}

// SetUserId sets the UserId field's value.
func (s *User) SetUserId(v string) *User {
	s.UserId = &v
	return s
}

// SetUserName sets the UserName field's value.
func (s *User) SetUserName(v string) *User {
	s.UserName = &v
	return s
}

// The request failed because it contains a syntax error.
type ValidationException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	Message_ *string `locationName:"Message" min:"1" type:"string"`

	// The identifier for each request. This value is a globally unique ID that
	// is generated by the Identity Store service for each sent request, and is
	// then returned inside the exception if the request fails.
	RequestId *string `type:"string"`
}

// String returns the string representation
func (s ValidationException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s ValidationException) GoString() string {
	return s.String()
}

func newErrorValidationException(v protocol.ResponseMetadata) error {
	return &ValidationException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *ValidationException) Code() string {
	return "ValidationException"
}

// Message returns the exception's message.
func (s *ValidationException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *ValidationException) OrigErr() error {
	return nil
}

func (s *ValidationException) Error() string {
	return fmt.Sprintf("%s: %s\n%s", s.Code(), s.Message(), s.String())
}

// Status code returns the HTTP status code for the request's response error.
func (s *ValidationException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *ValidationException) RequestID() string {
	return s.RespMetadata.RequestID
}

const (
	// ResourceTypeGroup is a ResourceType enum value
	ResourceTypeGroup = "GROUP"

	// ResourceTypeUser is a ResourceType enum value
	ResourceTypeUser = "USER"

	// ResourceTypeIdentityStore is a ResourceType enum value
	ResourceTypeIdentityStore = "IDENTITY_STORE"
)

// ResourceType_Values returns all elements of the ResourceType enum
func ResourceType_Values() []string {
	return []string{
		ResourceTypeGroup,
		ResourceTypeUser,
		ResourceTypeIdentityStore,
	}
}
