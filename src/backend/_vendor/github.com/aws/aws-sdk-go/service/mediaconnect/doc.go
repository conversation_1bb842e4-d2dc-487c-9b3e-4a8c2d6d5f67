// Code generated by private/model/cli/gen-api/main.go. DO NOT EDIT.

// Package mediaconnect provides the client and types for making API
// requests to AWS MediaConnect.
//
// API for AWS Elemental MediaConnect
//
// See https://docs.aws.amazon.com/goto/WebAPI/mediaconnect-2018-11-14 for more information on this service.
//
// See mediaconnect package documentation for more information.
// https://docs.aws.amazon.com/sdk-for-go/api/service/mediaconnect/
//
// Using the Client
//
// To contact AWS MediaConnect with the SDK use the New function to create
// a new service client. With that client you can make API requests to the service.
// These clients are safe to use concurrently.
//
// See the SDK's documentation for more information on how to use the SDK.
// https://docs.aws.amazon.com/sdk-for-go/api/
//
// See aws.Config documentation for more information on configuring SDK clients.
// https://docs.aws.amazon.com/sdk-for-go/api/aws/#Config
//
// See the AWS MediaConnect client MediaConnect for more
// information on creating client for this service.
// https://docs.aws.amazon.com/sdk-for-go/api/service/mediaconnect/#New
package mediaconnect
