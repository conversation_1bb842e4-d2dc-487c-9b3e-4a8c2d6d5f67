// Code generated by private/model/cli/gen-api/main.go. DO NOT EDIT.

package iotevents

import (
	"fmt"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/awsutil"
	"github.com/aws/aws-sdk-go/aws/request"
	"github.com/aws/aws-sdk-go/private/protocol"
	"github.com/aws/aws-sdk-go/private/protocol/restjson"
)

const opCreateDetectorModel = "CreateDetectorModel"

// CreateDetectorModelRequest generates a "aws/request.Request" representing the
// client's request for the CreateDetectorModel operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See CreateDetectorModel for more information on using the CreateDetectorModel
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//
//    // Example sending a request using the CreateDetectorModelRequest method.
//    req, resp := client.CreateDetectorModelRequest(params)
//
//    err := req.Send()
//    if err == nil { // resp is now filled
//        fmt.Println(resp)
//    }
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/iotevents-2018-07-27/CreateDetectorModel
func (c *IoTEvents) CreateDetectorModelRequest(input *CreateDetectorModelInput) (req *request.Request, output *CreateDetectorModelOutput) {
	op := &request.Operation{
		Name:       opCreateDetectorModel,
		HTTPMethod: "POST",
		HTTPPath:   "/detector-models",
	}

	if input == nil {
		input = &CreateDetectorModelInput{}
	}

	output = &CreateDetectorModelOutput{}
	req = c.newRequest(op, input, output)
	return
}

// CreateDetectorModel API operation for AWS IoT Events.
//
// Creates a detector model.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for AWS IoT Events's
// API operation CreateDetectorModel for usage and error information.
//
// Returned Error Types:
//   * InvalidRequestException
//   The request was invalid.
//
//   * ResourceInUseException
//   The resource is in use.
//
//   * ResourceAlreadyExistsException
//   The resource already exists.
//
//   * LimitExceededException
//   A limit was exceeded.
//
//   * ThrottlingException
//   The request could not be completed due to throttling.
//
//   * InternalFailureException
//   An internal failure occurred.
//
//   * ServiceUnavailableException
//   The service is currently unavailable.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/iotevents-2018-07-27/CreateDetectorModel
func (c *IoTEvents) CreateDetectorModel(input *CreateDetectorModelInput) (*CreateDetectorModelOutput, error) {
	req, out := c.CreateDetectorModelRequest(input)
	return out, req.Send()
}

// CreateDetectorModelWithContext is the same as CreateDetectorModel with the addition of
// the ability to pass a context and additional request options.
//
// See CreateDetectorModel for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *IoTEvents) CreateDetectorModelWithContext(ctx aws.Context, input *CreateDetectorModelInput, opts ...request.Option) (*CreateDetectorModelOutput, error) {
	req, out := c.CreateDetectorModelRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opCreateInput = "CreateInput"

// CreateInputRequest generates a "aws/request.Request" representing the
// client's request for the CreateInput operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See CreateInput for more information on using the CreateInput
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//
//    // Example sending a request using the CreateInputRequest method.
//    req, resp := client.CreateInputRequest(params)
//
//    err := req.Send()
//    if err == nil { // resp is now filled
//        fmt.Println(resp)
//    }
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/iotevents-2018-07-27/CreateInput
func (c *IoTEvents) CreateInputRequest(input *CreateInputInput) (req *request.Request, output *CreateInputOutput) {
	op := &request.Operation{
		Name:       opCreateInput,
		HTTPMethod: "POST",
		HTTPPath:   "/inputs",
	}

	if input == nil {
		input = &CreateInputInput{}
	}

	output = &CreateInputOutput{}
	req = c.newRequest(op, input, output)
	return
}

// CreateInput API operation for AWS IoT Events.
//
// Creates an input.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for AWS IoT Events's
// API operation CreateInput for usage and error information.
//
// Returned Error Types:
//   * InvalidRequestException
//   The request was invalid.
//
//   * ThrottlingException
//   The request could not be completed due to throttling.
//
//   * InternalFailureException
//   An internal failure occurred.
//
//   * ServiceUnavailableException
//   The service is currently unavailable.
//
//   * ResourceAlreadyExistsException
//   The resource already exists.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/iotevents-2018-07-27/CreateInput
func (c *IoTEvents) CreateInput(input *CreateInputInput) (*CreateInputOutput, error) {
	req, out := c.CreateInputRequest(input)
	return out, req.Send()
}

// CreateInputWithContext is the same as CreateInput with the addition of
// the ability to pass a context and additional request options.
//
// See CreateInput for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *IoTEvents) CreateInputWithContext(ctx aws.Context, input *CreateInputInput, opts ...request.Option) (*CreateInputOutput, error) {
	req, out := c.CreateInputRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opDeleteDetectorModel = "DeleteDetectorModel"

// DeleteDetectorModelRequest generates a "aws/request.Request" representing the
// client's request for the DeleteDetectorModel operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See DeleteDetectorModel for more information on using the DeleteDetectorModel
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//
//    // Example sending a request using the DeleteDetectorModelRequest method.
//    req, resp := client.DeleteDetectorModelRequest(params)
//
//    err := req.Send()
//    if err == nil { // resp is now filled
//        fmt.Println(resp)
//    }
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/iotevents-2018-07-27/DeleteDetectorModel
func (c *IoTEvents) DeleteDetectorModelRequest(input *DeleteDetectorModelInput) (req *request.Request, output *DeleteDetectorModelOutput) {
	op := &request.Operation{
		Name:       opDeleteDetectorModel,
		HTTPMethod: "DELETE",
		HTTPPath:   "/detector-models/{detectorModelName}",
	}

	if input == nil {
		input = &DeleteDetectorModelInput{}
	}

	output = &DeleteDetectorModelOutput{}
	req = c.newRequest(op, input, output)
	req.Handlers.Unmarshal.Swap(restjson.UnmarshalHandler.Name, protocol.UnmarshalDiscardBodyHandler)
	return
}

// DeleteDetectorModel API operation for AWS IoT Events.
//
// Deletes a detector model. Any active instances of the detector model are
// also deleted.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for AWS IoT Events's
// API operation DeleteDetectorModel for usage and error information.
//
// Returned Error Types:
//   * InvalidRequestException
//   The request was invalid.
//
//   * ResourceInUseException
//   The resource is in use.
//
//   * ResourceNotFoundException
//   The resource was not found.
//
//   * ThrottlingException
//   The request could not be completed due to throttling.
//
//   * InternalFailureException
//   An internal failure occurred.
//
//   * ServiceUnavailableException
//   The service is currently unavailable.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/iotevents-2018-07-27/DeleteDetectorModel
func (c *IoTEvents) DeleteDetectorModel(input *DeleteDetectorModelInput) (*DeleteDetectorModelOutput, error) {
	req, out := c.DeleteDetectorModelRequest(input)
	return out, req.Send()
}

// DeleteDetectorModelWithContext is the same as DeleteDetectorModel with the addition of
// the ability to pass a context and additional request options.
//
// See DeleteDetectorModel for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *IoTEvents) DeleteDetectorModelWithContext(ctx aws.Context, input *DeleteDetectorModelInput, opts ...request.Option) (*DeleteDetectorModelOutput, error) {
	req, out := c.DeleteDetectorModelRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opDeleteInput = "DeleteInput"

// DeleteInputRequest generates a "aws/request.Request" representing the
// client's request for the DeleteInput operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See DeleteInput for more information on using the DeleteInput
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//
//    // Example sending a request using the DeleteInputRequest method.
//    req, resp := client.DeleteInputRequest(params)
//
//    err := req.Send()
//    if err == nil { // resp is now filled
//        fmt.Println(resp)
//    }
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/iotevents-2018-07-27/DeleteInput
func (c *IoTEvents) DeleteInputRequest(input *DeleteInputInput) (req *request.Request, output *DeleteInputOutput) {
	op := &request.Operation{
		Name:       opDeleteInput,
		HTTPMethod: "DELETE",
		HTTPPath:   "/inputs/{inputName}",
	}

	if input == nil {
		input = &DeleteInputInput{}
	}

	output = &DeleteInputOutput{}
	req = c.newRequest(op, input, output)
	req.Handlers.Unmarshal.Swap(restjson.UnmarshalHandler.Name, protocol.UnmarshalDiscardBodyHandler)
	return
}

// DeleteInput API operation for AWS IoT Events.
//
// Deletes an input.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for AWS IoT Events's
// API operation DeleteInput for usage and error information.
//
// Returned Error Types:
//   * InvalidRequestException
//   The request was invalid.
//
//   * ResourceNotFoundException
//   The resource was not found.
//
//   * ThrottlingException
//   The request could not be completed due to throttling.
//
//   * InternalFailureException
//   An internal failure occurred.
//
//   * ServiceUnavailableException
//   The service is currently unavailable.
//
//   * ResourceInUseException
//   The resource is in use.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/iotevents-2018-07-27/DeleteInput
func (c *IoTEvents) DeleteInput(input *DeleteInputInput) (*DeleteInputOutput, error) {
	req, out := c.DeleteInputRequest(input)
	return out, req.Send()
}

// DeleteInputWithContext is the same as DeleteInput with the addition of
// the ability to pass a context and additional request options.
//
// See DeleteInput for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *IoTEvents) DeleteInputWithContext(ctx aws.Context, input *DeleteInputInput, opts ...request.Option) (*DeleteInputOutput, error) {
	req, out := c.DeleteInputRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opDescribeDetectorModel = "DescribeDetectorModel"

// DescribeDetectorModelRequest generates a "aws/request.Request" representing the
// client's request for the DescribeDetectorModel operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See DescribeDetectorModel for more information on using the DescribeDetectorModel
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//
//    // Example sending a request using the DescribeDetectorModelRequest method.
//    req, resp := client.DescribeDetectorModelRequest(params)
//
//    err := req.Send()
//    if err == nil { // resp is now filled
//        fmt.Println(resp)
//    }
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/iotevents-2018-07-27/DescribeDetectorModel
func (c *IoTEvents) DescribeDetectorModelRequest(input *DescribeDetectorModelInput) (req *request.Request, output *DescribeDetectorModelOutput) {
	op := &request.Operation{
		Name:       opDescribeDetectorModel,
		HTTPMethod: "GET",
		HTTPPath:   "/detector-models/{detectorModelName}",
	}

	if input == nil {
		input = &DescribeDetectorModelInput{}
	}

	output = &DescribeDetectorModelOutput{}
	req = c.newRequest(op, input, output)
	return
}

// DescribeDetectorModel API operation for AWS IoT Events.
//
// Describes a detector model. If the version parameter is not specified, information
// about the latest version is returned.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for AWS IoT Events's
// API operation DescribeDetectorModel for usage and error information.
//
// Returned Error Types:
//   * InvalidRequestException
//   The request was invalid.
//
//   * ResourceNotFoundException
//   The resource was not found.
//
//   * ThrottlingException
//   The request could not be completed due to throttling.
//
//   * InternalFailureException
//   An internal failure occurred.
//
//   * ServiceUnavailableException
//   The service is currently unavailable.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/iotevents-2018-07-27/DescribeDetectorModel
func (c *IoTEvents) DescribeDetectorModel(input *DescribeDetectorModelInput) (*DescribeDetectorModelOutput, error) {
	req, out := c.DescribeDetectorModelRequest(input)
	return out, req.Send()
}

// DescribeDetectorModelWithContext is the same as DescribeDetectorModel with the addition of
// the ability to pass a context and additional request options.
//
// See DescribeDetectorModel for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *IoTEvents) DescribeDetectorModelWithContext(ctx aws.Context, input *DescribeDetectorModelInput, opts ...request.Option) (*DescribeDetectorModelOutput, error) {
	req, out := c.DescribeDetectorModelRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opDescribeInput = "DescribeInput"

// DescribeInputRequest generates a "aws/request.Request" representing the
// client's request for the DescribeInput operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See DescribeInput for more information on using the DescribeInput
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//
//    // Example sending a request using the DescribeInputRequest method.
//    req, resp := client.DescribeInputRequest(params)
//
//    err := req.Send()
//    if err == nil { // resp is now filled
//        fmt.Println(resp)
//    }
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/iotevents-2018-07-27/DescribeInput
func (c *IoTEvents) DescribeInputRequest(input *DescribeInputInput) (req *request.Request, output *DescribeInputOutput) {
	op := &request.Operation{
		Name:       opDescribeInput,
		HTTPMethod: "GET",
		HTTPPath:   "/inputs/{inputName}",
	}

	if input == nil {
		input = &DescribeInputInput{}
	}

	output = &DescribeInputOutput{}
	req = c.newRequest(op, input, output)
	return
}

// DescribeInput API operation for AWS IoT Events.
//
// Describes an input.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for AWS IoT Events's
// API operation DescribeInput for usage and error information.
//
// Returned Error Types:
//   * InvalidRequestException
//   The request was invalid.
//
//   * ResourceNotFoundException
//   The resource was not found.
//
//   * ThrottlingException
//   The request could not be completed due to throttling.
//
//   * InternalFailureException
//   An internal failure occurred.
//
//   * ServiceUnavailableException
//   The service is currently unavailable.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/iotevents-2018-07-27/DescribeInput
func (c *IoTEvents) DescribeInput(input *DescribeInputInput) (*DescribeInputOutput, error) {
	req, out := c.DescribeInputRequest(input)
	return out, req.Send()
}

// DescribeInputWithContext is the same as DescribeInput with the addition of
// the ability to pass a context and additional request options.
//
// See DescribeInput for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *IoTEvents) DescribeInputWithContext(ctx aws.Context, input *DescribeInputInput, opts ...request.Option) (*DescribeInputOutput, error) {
	req, out := c.DescribeInputRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opDescribeLoggingOptions = "DescribeLoggingOptions"

// DescribeLoggingOptionsRequest generates a "aws/request.Request" representing the
// client's request for the DescribeLoggingOptions operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See DescribeLoggingOptions for more information on using the DescribeLoggingOptions
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//
//    // Example sending a request using the DescribeLoggingOptionsRequest method.
//    req, resp := client.DescribeLoggingOptionsRequest(params)
//
//    err := req.Send()
//    if err == nil { // resp is now filled
//        fmt.Println(resp)
//    }
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/iotevents-2018-07-27/DescribeLoggingOptions
func (c *IoTEvents) DescribeLoggingOptionsRequest(input *DescribeLoggingOptionsInput) (req *request.Request, output *DescribeLoggingOptionsOutput) {
	op := &request.Operation{
		Name:       opDescribeLoggingOptions,
		HTTPMethod: "GET",
		HTTPPath:   "/logging",
	}

	if input == nil {
		input = &DescribeLoggingOptionsInput{}
	}

	output = &DescribeLoggingOptionsOutput{}
	req = c.newRequest(op, input, output)
	return
}

// DescribeLoggingOptions API operation for AWS IoT Events.
//
// Retrieves the current settings of the AWS IoT Events logging options.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for AWS IoT Events's
// API operation DescribeLoggingOptions for usage and error information.
//
// Returned Error Types:
//   * InvalidRequestException
//   The request was invalid.
//
//   * ThrottlingException
//   The request could not be completed due to throttling.
//
//   * InternalFailureException
//   An internal failure occurred.
//
//   * ResourceNotFoundException
//   The resource was not found.
//
//   * ServiceUnavailableException
//   The service is currently unavailable.
//
//   * UnsupportedOperationException
//   The requested operation is not supported.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/iotevents-2018-07-27/DescribeLoggingOptions
func (c *IoTEvents) DescribeLoggingOptions(input *DescribeLoggingOptionsInput) (*DescribeLoggingOptionsOutput, error) {
	req, out := c.DescribeLoggingOptionsRequest(input)
	return out, req.Send()
}

// DescribeLoggingOptionsWithContext is the same as DescribeLoggingOptions with the addition of
// the ability to pass a context and additional request options.
//
// See DescribeLoggingOptions for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *IoTEvents) DescribeLoggingOptionsWithContext(ctx aws.Context, input *DescribeLoggingOptionsInput, opts ...request.Option) (*DescribeLoggingOptionsOutput, error) {
	req, out := c.DescribeLoggingOptionsRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opListDetectorModelVersions = "ListDetectorModelVersions"

// ListDetectorModelVersionsRequest generates a "aws/request.Request" representing the
// client's request for the ListDetectorModelVersions operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See ListDetectorModelVersions for more information on using the ListDetectorModelVersions
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//
//    // Example sending a request using the ListDetectorModelVersionsRequest method.
//    req, resp := client.ListDetectorModelVersionsRequest(params)
//
//    err := req.Send()
//    if err == nil { // resp is now filled
//        fmt.Println(resp)
//    }
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/iotevents-2018-07-27/ListDetectorModelVersions
func (c *IoTEvents) ListDetectorModelVersionsRequest(input *ListDetectorModelVersionsInput) (req *request.Request, output *ListDetectorModelVersionsOutput) {
	op := &request.Operation{
		Name:       opListDetectorModelVersions,
		HTTPMethod: "GET",
		HTTPPath:   "/detector-models/{detectorModelName}/versions",
	}

	if input == nil {
		input = &ListDetectorModelVersionsInput{}
	}

	output = &ListDetectorModelVersionsOutput{}
	req = c.newRequest(op, input, output)
	return
}

// ListDetectorModelVersions API operation for AWS IoT Events.
//
// Lists all the versions of a detector model. Only the metadata associated
// with each detector model version is returned.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for AWS IoT Events's
// API operation ListDetectorModelVersions for usage and error information.
//
// Returned Error Types:
//   * InvalidRequestException
//   The request was invalid.
//
//   * ResourceNotFoundException
//   The resource was not found.
//
//   * ThrottlingException
//   The request could not be completed due to throttling.
//
//   * InternalFailureException
//   An internal failure occurred.
//
//   * ServiceUnavailableException
//   The service is currently unavailable.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/iotevents-2018-07-27/ListDetectorModelVersions
func (c *IoTEvents) ListDetectorModelVersions(input *ListDetectorModelVersionsInput) (*ListDetectorModelVersionsOutput, error) {
	req, out := c.ListDetectorModelVersionsRequest(input)
	return out, req.Send()
}

// ListDetectorModelVersionsWithContext is the same as ListDetectorModelVersions with the addition of
// the ability to pass a context and additional request options.
//
// See ListDetectorModelVersions for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *IoTEvents) ListDetectorModelVersionsWithContext(ctx aws.Context, input *ListDetectorModelVersionsInput, opts ...request.Option) (*ListDetectorModelVersionsOutput, error) {
	req, out := c.ListDetectorModelVersionsRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opListDetectorModels = "ListDetectorModels"

// ListDetectorModelsRequest generates a "aws/request.Request" representing the
// client's request for the ListDetectorModels operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See ListDetectorModels for more information on using the ListDetectorModels
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//
//    // Example sending a request using the ListDetectorModelsRequest method.
//    req, resp := client.ListDetectorModelsRequest(params)
//
//    err := req.Send()
//    if err == nil { // resp is now filled
//        fmt.Println(resp)
//    }
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/iotevents-2018-07-27/ListDetectorModels
func (c *IoTEvents) ListDetectorModelsRequest(input *ListDetectorModelsInput) (req *request.Request, output *ListDetectorModelsOutput) {
	op := &request.Operation{
		Name:       opListDetectorModels,
		HTTPMethod: "GET",
		HTTPPath:   "/detector-models",
	}

	if input == nil {
		input = &ListDetectorModelsInput{}
	}

	output = &ListDetectorModelsOutput{}
	req = c.newRequest(op, input, output)
	return
}

// ListDetectorModels API operation for AWS IoT Events.
//
// Lists the detector models you have created. Only the metadata associated
// with each detector model is returned.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for AWS IoT Events's
// API operation ListDetectorModels for usage and error information.
//
// Returned Error Types:
//   * InvalidRequestException
//   The request was invalid.
//
//   * ThrottlingException
//   The request could not be completed due to throttling.
//
//   * InternalFailureException
//   An internal failure occurred.
//
//   * ServiceUnavailableException
//   The service is currently unavailable.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/iotevents-2018-07-27/ListDetectorModels
func (c *IoTEvents) ListDetectorModels(input *ListDetectorModelsInput) (*ListDetectorModelsOutput, error) {
	req, out := c.ListDetectorModelsRequest(input)
	return out, req.Send()
}

// ListDetectorModelsWithContext is the same as ListDetectorModels with the addition of
// the ability to pass a context and additional request options.
//
// See ListDetectorModels for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *IoTEvents) ListDetectorModelsWithContext(ctx aws.Context, input *ListDetectorModelsInput, opts ...request.Option) (*ListDetectorModelsOutput, error) {
	req, out := c.ListDetectorModelsRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opListInputs = "ListInputs"

// ListInputsRequest generates a "aws/request.Request" representing the
// client's request for the ListInputs operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See ListInputs for more information on using the ListInputs
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//
//    // Example sending a request using the ListInputsRequest method.
//    req, resp := client.ListInputsRequest(params)
//
//    err := req.Send()
//    if err == nil { // resp is now filled
//        fmt.Println(resp)
//    }
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/iotevents-2018-07-27/ListInputs
func (c *IoTEvents) ListInputsRequest(input *ListInputsInput) (req *request.Request, output *ListInputsOutput) {
	op := &request.Operation{
		Name:       opListInputs,
		HTTPMethod: "GET",
		HTTPPath:   "/inputs",
	}

	if input == nil {
		input = &ListInputsInput{}
	}

	output = &ListInputsOutput{}
	req = c.newRequest(op, input, output)
	return
}

// ListInputs API operation for AWS IoT Events.
//
// Lists the inputs you have created.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for AWS IoT Events's
// API operation ListInputs for usage and error information.
//
// Returned Error Types:
//   * InvalidRequestException
//   The request was invalid.
//
//   * ThrottlingException
//   The request could not be completed due to throttling.
//
//   * InternalFailureException
//   An internal failure occurred.
//
//   * ServiceUnavailableException
//   The service is currently unavailable.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/iotevents-2018-07-27/ListInputs
func (c *IoTEvents) ListInputs(input *ListInputsInput) (*ListInputsOutput, error) {
	req, out := c.ListInputsRequest(input)
	return out, req.Send()
}

// ListInputsWithContext is the same as ListInputs with the addition of
// the ability to pass a context and additional request options.
//
// See ListInputs for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *IoTEvents) ListInputsWithContext(ctx aws.Context, input *ListInputsInput, opts ...request.Option) (*ListInputsOutput, error) {
	req, out := c.ListInputsRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opListTagsForResource = "ListTagsForResource"

// ListTagsForResourceRequest generates a "aws/request.Request" representing the
// client's request for the ListTagsForResource operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See ListTagsForResource for more information on using the ListTagsForResource
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//
//    // Example sending a request using the ListTagsForResourceRequest method.
//    req, resp := client.ListTagsForResourceRequest(params)
//
//    err := req.Send()
//    if err == nil { // resp is now filled
//        fmt.Println(resp)
//    }
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/iotevents-2018-07-27/ListTagsForResource
func (c *IoTEvents) ListTagsForResourceRequest(input *ListTagsForResourceInput) (req *request.Request, output *ListTagsForResourceOutput) {
	op := &request.Operation{
		Name:       opListTagsForResource,
		HTTPMethod: "GET",
		HTTPPath:   "/tags",
	}

	if input == nil {
		input = &ListTagsForResourceInput{}
	}

	output = &ListTagsForResourceOutput{}
	req = c.newRequest(op, input, output)
	return
}

// ListTagsForResource API operation for AWS IoT Events.
//
// Lists the tags (metadata) you have assigned to the resource.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for AWS IoT Events's
// API operation ListTagsForResource for usage and error information.
//
// Returned Error Types:
//   * InvalidRequestException
//   The request was invalid.
//
//   * ResourceNotFoundException
//   The resource was not found.
//
//   * ResourceInUseException
//   The resource is in use.
//
//   * ThrottlingException
//   The request could not be completed due to throttling.
//
//   * InternalFailureException
//   An internal failure occurred.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/iotevents-2018-07-27/ListTagsForResource
func (c *IoTEvents) ListTagsForResource(input *ListTagsForResourceInput) (*ListTagsForResourceOutput, error) {
	req, out := c.ListTagsForResourceRequest(input)
	return out, req.Send()
}

// ListTagsForResourceWithContext is the same as ListTagsForResource with the addition of
// the ability to pass a context and additional request options.
//
// See ListTagsForResource for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *IoTEvents) ListTagsForResourceWithContext(ctx aws.Context, input *ListTagsForResourceInput, opts ...request.Option) (*ListTagsForResourceOutput, error) {
	req, out := c.ListTagsForResourceRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opPutLoggingOptions = "PutLoggingOptions"

// PutLoggingOptionsRequest generates a "aws/request.Request" representing the
// client's request for the PutLoggingOptions operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See PutLoggingOptions for more information on using the PutLoggingOptions
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//
//    // Example sending a request using the PutLoggingOptionsRequest method.
//    req, resp := client.PutLoggingOptionsRequest(params)
//
//    err := req.Send()
//    if err == nil { // resp is now filled
//        fmt.Println(resp)
//    }
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/iotevents-2018-07-27/PutLoggingOptions
func (c *IoTEvents) PutLoggingOptionsRequest(input *PutLoggingOptionsInput) (req *request.Request, output *PutLoggingOptionsOutput) {
	op := &request.Operation{
		Name:       opPutLoggingOptions,
		HTTPMethod: "PUT",
		HTTPPath:   "/logging",
	}

	if input == nil {
		input = &PutLoggingOptionsInput{}
	}

	output = &PutLoggingOptionsOutput{}
	req = c.newRequest(op, input, output)
	req.Handlers.Unmarshal.Swap(restjson.UnmarshalHandler.Name, protocol.UnmarshalDiscardBodyHandler)
	return
}

// PutLoggingOptions API operation for AWS IoT Events.
//
// Sets or updates the AWS IoT Events logging options.
//
// If you update the value of any loggingOptions field, it takes up to one minute
// for the change to take effect. If you change the policy attached to the role
// you specified in the roleArn field (for example, to correct an invalid policy),
// it takes up to five minutes for that change to take effect.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for AWS IoT Events's
// API operation PutLoggingOptions for usage and error information.
//
// Returned Error Types:
//   * InvalidRequestException
//   The request was invalid.
//
//   * ThrottlingException
//   The request could not be completed due to throttling.
//
//   * InternalFailureException
//   An internal failure occurred.
//
//   * ServiceUnavailableException
//   The service is currently unavailable.
//
//   * UnsupportedOperationException
//   The requested operation is not supported.
//
//   * ResourceInUseException
//   The resource is in use.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/iotevents-2018-07-27/PutLoggingOptions
func (c *IoTEvents) PutLoggingOptions(input *PutLoggingOptionsInput) (*PutLoggingOptionsOutput, error) {
	req, out := c.PutLoggingOptionsRequest(input)
	return out, req.Send()
}

// PutLoggingOptionsWithContext is the same as PutLoggingOptions with the addition of
// the ability to pass a context and additional request options.
//
// See PutLoggingOptions for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *IoTEvents) PutLoggingOptionsWithContext(ctx aws.Context, input *PutLoggingOptionsInput, opts ...request.Option) (*PutLoggingOptionsOutput, error) {
	req, out := c.PutLoggingOptionsRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opTagResource = "TagResource"

// TagResourceRequest generates a "aws/request.Request" representing the
// client's request for the TagResource operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See TagResource for more information on using the TagResource
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//
//    // Example sending a request using the TagResourceRequest method.
//    req, resp := client.TagResourceRequest(params)
//
//    err := req.Send()
//    if err == nil { // resp is now filled
//        fmt.Println(resp)
//    }
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/iotevents-2018-07-27/TagResource
func (c *IoTEvents) TagResourceRequest(input *TagResourceInput) (req *request.Request, output *TagResourceOutput) {
	op := &request.Operation{
		Name:       opTagResource,
		HTTPMethod: "POST",
		HTTPPath:   "/tags",
	}

	if input == nil {
		input = &TagResourceInput{}
	}

	output = &TagResourceOutput{}
	req = c.newRequest(op, input, output)
	req.Handlers.Unmarshal.Swap(restjson.UnmarshalHandler.Name, protocol.UnmarshalDiscardBodyHandler)
	return
}

// TagResource API operation for AWS IoT Events.
//
// Adds to or modifies the tags of the given resource. Tags are metadata that
// can be used to manage a resource.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for AWS IoT Events's
// API operation TagResource for usage and error information.
//
// Returned Error Types:
//   * InvalidRequestException
//   The request was invalid.
//
//   * ResourceNotFoundException
//   The resource was not found.
//
//   * ResourceInUseException
//   The resource is in use.
//
//   * ThrottlingException
//   The request could not be completed due to throttling.
//
//   * LimitExceededException
//   A limit was exceeded.
//
//   * InternalFailureException
//   An internal failure occurred.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/iotevents-2018-07-27/TagResource
func (c *IoTEvents) TagResource(input *TagResourceInput) (*TagResourceOutput, error) {
	req, out := c.TagResourceRequest(input)
	return out, req.Send()
}

// TagResourceWithContext is the same as TagResource with the addition of
// the ability to pass a context and additional request options.
//
// See TagResource for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *IoTEvents) TagResourceWithContext(ctx aws.Context, input *TagResourceInput, opts ...request.Option) (*TagResourceOutput, error) {
	req, out := c.TagResourceRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opUntagResource = "UntagResource"

// UntagResourceRequest generates a "aws/request.Request" representing the
// client's request for the UntagResource operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See UntagResource for more information on using the UntagResource
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//
//    // Example sending a request using the UntagResourceRequest method.
//    req, resp := client.UntagResourceRequest(params)
//
//    err := req.Send()
//    if err == nil { // resp is now filled
//        fmt.Println(resp)
//    }
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/iotevents-2018-07-27/UntagResource
func (c *IoTEvents) UntagResourceRequest(input *UntagResourceInput) (req *request.Request, output *UntagResourceOutput) {
	op := &request.Operation{
		Name:       opUntagResource,
		HTTPMethod: "DELETE",
		HTTPPath:   "/tags",
	}

	if input == nil {
		input = &UntagResourceInput{}
	}

	output = &UntagResourceOutput{}
	req = c.newRequest(op, input, output)
	req.Handlers.Unmarshal.Swap(restjson.UnmarshalHandler.Name, protocol.UnmarshalDiscardBodyHandler)
	return
}

// UntagResource API operation for AWS IoT Events.
//
// Removes the given tags (metadata) from the resource.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for AWS IoT Events's
// API operation UntagResource for usage and error information.
//
// Returned Error Types:
//   * InvalidRequestException
//   The request was invalid.
//
//   * ResourceNotFoundException
//   The resource was not found.
//
//   * ResourceInUseException
//   The resource is in use.
//
//   * ThrottlingException
//   The request could not be completed due to throttling.
//
//   * InternalFailureException
//   An internal failure occurred.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/iotevents-2018-07-27/UntagResource
func (c *IoTEvents) UntagResource(input *UntagResourceInput) (*UntagResourceOutput, error) {
	req, out := c.UntagResourceRequest(input)
	return out, req.Send()
}

// UntagResourceWithContext is the same as UntagResource with the addition of
// the ability to pass a context and additional request options.
//
// See UntagResource for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *IoTEvents) UntagResourceWithContext(ctx aws.Context, input *UntagResourceInput, opts ...request.Option) (*UntagResourceOutput, error) {
	req, out := c.UntagResourceRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opUpdateDetectorModel = "UpdateDetectorModel"

// UpdateDetectorModelRequest generates a "aws/request.Request" representing the
// client's request for the UpdateDetectorModel operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See UpdateDetectorModel for more information on using the UpdateDetectorModel
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//
//    // Example sending a request using the UpdateDetectorModelRequest method.
//    req, resp := client.UpdateDetectorModelRequest(params)
//
//    err := req.Send()
//    if err == nil { // resp is now filled
//        fmt.Println(resp)
//    }
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/iotevents-2018-07-27/UpdateDetectorModel
func (c *IoTEvents) UpdateDetectorModelRequest(input *UpdateDetectorModelInput) (req *request.Request, output *UpdateDetectorModelOutput) {
	op := &request.Operation{
		Name:       opUpdateDetectorModel,
		HTTPMethod: "POST",
		HTTPPath:   "/detector-models/{detectorModelName}",
	}

	if input == nil {
		input = &UpdateDetectorModelInput{}
	}

	output = &UpdateDetectorModelOutput{}
	req = c.newRequest(op, input, output)
	return
}

// UpdateDetectorModel API operation for AWS IoT Events.
//
// Updates a detector model. Detectors (instances) spawned by the previous version
// are deleted and then re-created as new inputs arrive.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for AWS IoT Events's
// API operation UpdateDetectorModel for usage and error information.
//
// Returned Error Types:
//   * InvalidRequestException
//   The request was invalid.
//
//   * ResourceInUseException
//   The resource is in use.
//
//   * ResourceNotFoundException
//   The resource was not found.
//
//   * ThrottlingException
//   The request could not be completed due to throttling.
//
//   * InternalFailureException
//   An internal failure occurred.
//
//   * ServiceUnavailableException
//   The service is currently unavailable.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/iotevents-2018-07-27/UpdateDetectorModel
func (c *IoTEvents) UpdateDetectorModel(input *UpdateDetectorModelInput) (*UpdateDetectorModelOutput, error) {
	req, out := c.UpdateDetectorModelRequest(input)
	return out, req.Send()
}

// UpdateDetectorModelWithContext is the same as UpdateDetectorModel with the addition of
// the ability to pass a context and additional request options.
//
// See UpdateDetectorModel for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *IoTEvents) UpdateDetectorModelWithContext(ctx aws.Context, input *UpdateDetectorModelInput, opts ...request.Option) (*UpdateDetectorModelOutput, error) {
	req, out := c.UpdateDetectorModelRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opUpdateInput = "UpdateInput"

// UpdateInputRequest generates a "aws/request.Request" representing the
// client's request for the UpdateInput operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See UpdateInput for more information on using the UpdateInput
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//
//    // Example sending a request using the UpdateInputRequest method.
//    req, resp := client.UpdateInputRequest(params)
//
//    err := req.Send()
//    if err == nil { // resp is now filled
//        fmt.Println(resp)
//    }
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/iotevents-2018-07-27/UpdateInput
func (c *IoTEvents) UpdateInputRequest(input *UpdateInputInput) (req *request.Request, output *UpdateInputOutput) {
	op := &request.Operation{
		Name:       opUpdateInput,
		HTTPMethod: "PUT",
		HTTPPath:   "/inputs/{inputName}",
	}

	if input == nil {
		input = &UpdateInputInput{}
	}

	output = &UpdateInputOutput{}
	req = c.newRequest(op, input, output)
	return
}

// UpdateInput API operation for AWS IoT Events.
//
// Updates an input.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for AWS IoT Events's
// API operation UpdateInput for usage and error information.
//
// Returned Error Types:
//   * InvalidRequestException
//   The request was invalid.
//
//   * ThrottlingException
//   The request could not be completed due to throttling.
//
//   * ResourceNotFoundException
//   The resource was not found.
//
//   * InternalFailureException
//   An internal failure occurred.
//
//   * ServiceUnavailableException
//   The service is currently unavailable.
//
//   * ResourceInUseException
//   The resource is in use.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/iotevents-2018-07-27/UpdateInput
func (c *IoTEvents) UpdateInput(input *UpdateInputInput) (*UpdateInputOutput, error) {
	req, out := c.UpdateInputRequest(input)
	return out, req.Send()
}

// UpdateInputWithContext is the same as UpdateInput with the addition of
// the ability to pass a context and additional request options.
//
// See UpdateInput for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *IoTEvents) UpdateInputWithContext(ctx aws.Context, input *UpdateInputInput, opts ...request.Option) (*UpdateInputOutput, error) {
	req, out := c.UpdateInputRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

// Sends an AWS IoT Events input, passing in information about the detector
// model instance and the event that triggered the action.
type Action struct {
	_ struct{} `type:"structure"`

	// The name of the AWS IoT Events input where the data is sent.
	//
	// InputName is a required field
	InputName *string `locationName:"inputName" min:"1" type:"string" required:"true"`

	// You can configure the action payload when you send a message to an AWS IoT
	// Events input.
	Payload *Payload `locationName:"payload" type:"structure"`
}

// String returns the string representation
func (s Action) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s Action) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *Action) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "Action"}
	if s.InputName == nil {
		invalidParams.Add(request.NewErrParamRequired("InputName"))
	}
	if s.InputName != nil && len(*s.InputName) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("InputName", 1))
	}
	if s.Payload != nil {
		if err := s.Payload.Validate(); err != nil {
			invalidParams.AddNested("Payload", err.(request.ErrInvalidParams))
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetInputName sets the InputName field's value.
func (s *Action) SetInputName(v string) *Action {
	s.InputName = &v
	return s
}

// SetPayload sets the Payload field's value.
func (s *Action) SetPayload(v *Payload) *Action {
	s.Payload = v
	return s
}

// An action to be performed when the condition is TRUE.
type ActionData struct {
	_ struct{} `type:"structure"`

	// Information needed to clear the timer.
	ClearTimer *ClearTimerAction `locationName:"clearTimer" type:"structure"`

	// Writes to the DynamoDB table that you created. The default action payload
	// contains all attribute-value pairs that have the information about the detector
	// model instance and the event that triggered the action. You can also customize
	// the payload (https://docs.aws.amazon.com/iotevents/latest/apireference/API_Payload.html).
	// One column of the DynamoDB table receives all attribute-value pairs in the
	// payload that you specify. For more information, see Actions (https://docs.aws.amazon.com/iotevents/latest/developerguide/iotevents-event-actions.html)
	// in AWS IoT Events Developer Guide.
	DynamoDB *DynamoDBAction `locationName:"dynamoDB" type:"structure"`

	// Writes to the DynamoDB table that you created. The default action payload
	// contains all attribute-value pairs that have the information about the detector
	// model instance and the event that triggered the action. You can also customize
	// the payload (https://docs.aws.amazon.com/iotevents/latest/apireference/API_Payload.html).
	// A separate column of the DynamoDB table receives one attribute-value pair
	// in the payload that you specify. For more information, see Actions (https://docs.aws.amazon.com/iotevents/latest/developerguide/iotevents-event-actions.html)
	// in AWS IoT Events Developer Guide.
	DynamoDBv2 *DynamoDBv2Action `locationName:"dynamoDBv2" type:"structure"`

	// Sends information about the detector model instance and the event that triggered
	// the action to an Amazon Kinesis Data Firehose delivery stream.
	Firehose *FirehoseAction `locationName:"firehose" type:"structure"`

	// Sends AWS IoT Events input, which passes information about the detector model
	// instance and the event that triggered the action.
	IotEvents *Action `locationName:"iotEvents" type:"structure"`

	// Sends information about the detector model instance and the event that triggered
	// the action to an asset property in AWS IoT SiteWise .
	IotSiteWise *IotSiteWiseAction `locationName:"iotSiteWise" type:"structure"`

	// Publishes an MQTT message with the given topic to the AWS IoT message broker.
	IotTopicPublish *IotTopicPublishAction `locationName:"iotTopicPublish" type:"structure"`

	// Calls a Lambda function, passing in information about the detector model
	// instance and the event that triggered the action.
	Lambda *LambdaAction `locationName:"lambda" type:"structure"`

	// Information needed to reset the timer.
	ResetTimer *ResetTimerAction `locationName:"resetTimer" type:"structure"`

	// Information needed to set the timer.
	SetTimer *SetTimerAction `locationName:"setTimer" type:"structure"`

	// Sets a variable to a specified value.
	SetVariable *SetVariableAction `locationName:"setVariable" type:"structure"`

	// Sends an Amazon SNS message.
	Sns *SNSTopicPublishAction `locationName:"sns" type:"structure"`

	// Sends information about the detector model instance and the event that triggered
	// the action to an Amazon SQS queue.
	Sqs *SqsAction `locationName:"sqs" type:"structure"`
}

// String returns the string representation
func (s ActionData) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s ActionData) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *ActionData) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "ActionData"}
	if s.ClearTimer != nil {
		if err := s.ClearTimer.Validate(); err != nil {
			invalidParams.AddNested("ClearTimer", err.(request.ErrInvalidParams))
		}
	}
	if s.DynamoDB != nil {
		if err := s.DynamoDB.Validate(); err != nil {
			invalidParams.AddNested("DynamoDB", err.(request.ErrInvalidParams))
		}
	}
	if s.DynamoDBv2 != nil {
		if err := s.DynamoDBv2.Validate(); err != nil {
			invalidParams.AddNested("DynamoDBv2", err.(request.ErrInvalidParams))
		}
	}
	if s.Firehose != nil {
		if err := s.Firehose.Validate(); err != nil {
			invalidParams.AddNested("Firehose", err.(request.ErrInvalidParams))
		}
	}
	if s.IotEvents != nil {
		if err := s.IotEvents.Validate(); err != nil {
			invalidParams.AddNested("IotEvents", err.(request.ErrInvalidParams))
		}
	}
	if s.IotSiteWise != nil {
		if err := s.IotSiteWise.Validate(); err != nil {
			invalidParams.AddNested("IotSiteWise", err.(request.ErrInvalidParams))
		}
	}
	if s.IotTopicPublish != nil {
		if err := s.IotTopicPublish.Validate(); err != nil {
			invalidParams.AddNested("IotTopicPublish", err.(request.ErrInvalidParams))
		}
	}
	if s.Lambda != nil {
		if err := s.Lambda.Validate(); err != nil {
			invalidParams.AddNested("Lambda", err.(request.ErrInvalidParams))
		}
	}
	if s.ResetTimer != nil {
		if err := s.ResetTimer.Validate(); err != nil {
			invalidParams.AddNested("ResetTimer", err.(request.ErrInvalidParams))
		}
	}
	if s.SetTimer != nil {
		if err := s.SetTimer.Validate(); err != nil {
			invalidParams.AddNested("SetTimer", err.(request.ErrInvalidParams))
		}
	}
	if s.SetVariable != nil {
		if err := s.SetVariable.Validate(); err != nil {
			invalidParams.AddNested("SetVariable", err.(request.ErrInvalidParams))
		}
	}
	if s.Sns != nil {
		if err := s.Sns.Validate(); err != nil {
			invalidParams.AddNested("Sns", err.(request.ErrInvalidParams))
		}
	}
	if s.Sqs != nil {
		if err := s.Sqs.Validate(); err != nil {
			invalidParams.AddNested("Sqs", err.(request.ErrInvalidParams))
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetClearTimer sets the ClearTimer field's value.
func (s *ActionData) SetClearTimer(v *ClearTimerAction) *ActionData {
	s.ClearTimer = v
	return s
}

// SetDynamoDB sets the DynamoDB field's value.
func (s *ActionData) SetDynamoDB(v *DynamoDBAction) *ActionData {
	s.DynamoDB = v
	return s
}

// SetDynamoDBv2 sets the DynamoDBv2 field's value.
func (s *ActionData) SetDynamoDBv2(v *DynamoDBv2Action) *ActionData {
	s.DynamoDBv2 = v
	return s
}

// SetFirehose sets the Firehose field's value.
func (s *ActionData) SetFirehose(v *FirehoseAction) *ActionData {
	s.Firehose = v
	return s
}

// SetIotEvents sets the IotEvents field's value.
func (s *ActionData) SetIotEvents(v *Action) *ActionData {
	s.IotEvents = v
	return s
}

// SetIotSiteWise sets the IotSiteWise field's value.
func (s *ActionData) SetIotSiteWise(v *IotSiteWiseAction) *ActionData {
	s.IotSiteWise = v
	return s
}

// SetIotTopicPublish sets the IotTopicPublish field's value.
func (s *ActionData) SetIotTopicPublish(v *IotTopicPublishAction) *ActionData {
	s.IotTopicPublish = v
	return s
}

// SetLambda sets the Lambda field's value.
func (s *ActionData) SetLambda(v *LambdaAction) *ActionData {
	s.Lambda = v
	return s
}

// SetResetTimer sets the ResetTimer field's value.
func (s *ActionData) SetResetTimer(v *ResetTimerAction) *ActionData {
	s.ResetTimer = v
	return s
}

// SetSetTimer sets the SetTimer field's value.
func (s *ActionData) SetSetTimer(v *SetTimerAction) *ActionData {
	s.SetTimer = v
	return s
}

// SetSetVariable sets the SetVariable field's value.
func (s *ActionData) SetSetVariable(v *SetVariableAction) *ActionData {
	s.SetVariable = v
	return s
}

// SetSns sets the Sns field's value.
func (s *ActionData) SetSns(v *SNSTopicPublishAction) *ActionData {
	s.Sns = v
	return s
}

// SetSqs sets the Sqs field's value.
func (s *ActionData) SetSqs(v *SqsAction) *ActionData {
	s.Sqs = v
	return s
}

// A structure that contains timestamp information. For more information, see
// TimeInNanos (https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_TimeInNanos.html)
// in the AWS IoT SiteWise API Reference.
//
// For parameters that are string data type, you can specify the following options:
//
//    * Use a string. For example, the timeInSeconds value can be '1586400675'.
//
//    * Use an expression. For example, the timeInSeconds value can be '${$input.TemperatureInput.sensorData.timestamp/1000}'.
//    For more information, see Expressions (https://docs.aws.amazon.com/iotevents/latest/developerguide/iotevents-expressions.html)
//    in the AWS IoT Events Developer Guide.
type AssetPropertyTimestamp struct {
	_ struct{} `type:"structure"`

	// The nanosecond offset converted from timeInSeconds. The valid range is between
	// 0-999999999. You can also specify an expression.
	OffsetInNanos *string `locationName:"offsetInNanos" type:"string"`

	// The timestamp, in seconds, in the Unix epoch format. The valid range is between
	// 1-31556889864403199. You can also specify an expression.
	//
	// TimeInSeconds is a required field
	TimeInSeconds *string `locationName:"timeInSeconds" type:"string" required:"true"`
}

// String returns the string representation
func (s AssetPropertyTimestamp) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s AssetPropertyTimestamp) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *AssetPropertyTimestamp) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "AssetPropertyTimestamp"}
	if s.TimeInSeconds == nil {
		invalidParams.Add(request.NewErrParamRequired("TimeInSeconds"))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetOffsetInNanos sets the OffsetInNanos field's value.
func (s *AssetPropertyTimestamp) SetOffsetInNanos(v string) *AssetPropertyTimestamp {
	s.OffsetInNanos = &v
	return s
}

// SetTimeInSeconds sets the TimeInSeconds field's value.
func (s *AssetPropertyTimestamp) SetTimeInSeconds(v string) *AssetPropertyTimestamp {
	s.TimeInSeconds = &v
	return s
}

// A structure that contains value information. For more information, see AssetPropertyValue
// (https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_AssetPropertyValue.html)
// in the AWS IoT SiteWise API Reference.
//
// For parameters that are string data type, you can specify the following options:
//
//    * Use a string. For example, the quality value can be 'GOOD'.
//
//    * Use an expression. For example, the quality value can be $input.TemperatureInput.sensorData.quality
//    . For more information, see Expressions (https://docs.aws.amazon.com/iotevents/latest/developerguide/iotevents-expressions.html)
//    in the AWS IoT Events Developer Guide.
type AssetPropertyValue struct {
	_ struct{} `type:"structure"`

	// The quality of the asset property value. The value must be GOOD, BAD, or
	// UNCERTAIN. You can also specify an expression.
	Quality *string `locationName:"quality" type:"string"`

	// The timestamp associated with the asset property value. The default is the
	// current event time.
	Timestamp *AssetPropertyTimestamp `locationName:"timestamp" type:"structure"`

	// The value to send to an asset property.
	//
	// Value is a required field
	Value *AssetPropertyVariant `locationName:"value" type:"structure" required:"true"`
}

// String returns the string representation
func (s AssetPropertyValue) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s AssetPropertyValue) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *AssetPropertyValue) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "AssetPropertyValue"}
	if s.Value == nil {
		invalidParams.Add(request.NewErrParamRequired("Value"))
	}
	if s.Timestamp != nil {
		if err := s.Timestamp.Validate(); err != nil {
			invalidParams.AddNested("Timestamp", err.(request.ErrInvalidParams))
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetQuality sets the Quality field's value.
func (s *AssetPropertyValue) SetQuality(v string) *AssetPropertyValue {
	s.Quality = &v
	return s
}

// SetTimestamp sets the Timestamp field's value.
func (s *AssetPropertyValue) SetTimestamp(v *AssetPropertyTimestamp) *AssetPropertyValue {
	s.Timestamp = v
	return s
}

// SetValue sets the Value field's value.
func (s *AssetPropertyValue) SetValue(v *AssetPropertyVariant) *AssetPropertyValue {
	s.Value = v
	return s
}

// A structure that contains an asset property value. For more information,
// see Variant (https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_Variant.html)
// in the AWS IoT SiteWise API Reference.
//
// You must specify one of the following value types, depending on the dataType
// of the specified asset property. For more information, see AssetProperty
// (https://docs.aws.amazon.com/iot-sitewise/latest/APIReference/API_AssetProperty.html)
// in the AWS IoT SiteWise API Reference.
//
// For parameters that are string data type, you can specify the following options:
//
//    * Use a string. For example, the doubleValue value can be '47.9'.
//
//    * Use an expression. For example, the doubleValue value can be $input.TemperatureInput.sensorData.temperature.
//    For more information, see Expressions (https://docs.aws.amazon.com/iotevents/latest/developerguide/iotevents-expressions.html)
//    in the AWS IoT Events Developer Guide.
type AssetPropertyVariant struct {
	_ struct{} `type:"structure"`

	// The asset property value is a Boolean value that must be TRUE or FALSE. You
	// can also specify an expression. If you use an expression, the evaluated result
	// should be a Boolean value.
	BooleanValue *string `locationName:"booleanValue" type:"string"`

	// The asset property value is a double. You can also specify an expression.
	// If you use an expression, the evaluated result should be a double.
	DoubleValue *string `locationName:"doubleValue" type:"string"`

	// The asset property value is an integer. You can also specify an expression.
	// If you use an expression, the evaluated result should be an integer.
	IntegerValue *string `locationName:"integerValue" type:"string"`

	// The asset property value is a string. You can also specify an expression.
	// If you use an expression, the evaluated result should be a string.
	StringValue *string `locationName:"stringValue" type:"string"`
}

// String returns the string representation
func (s AssetPropertyVariant) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s AssetPropertyVariant) GoString() string {
	return s.String()
}

// SetBooleanValue sets the BooleanValue field's value.
func (s *AssetPropertyVariant) SetBooleanValue(v string) *AssetPropertyVariant {
	s.BooleanValue = &v
	return s
}

// SetDoubleValue sets the DoubleValue field's value.
func (s *AssetPropertyVariant) SetDoubleValue(v string) *AssetPropertyVariant {
	s.DoubleValue = &v
	return s
}

// SetIntegerValue sets the IntegerValue field's value.
func (s *AssetPropertyVariant) SetIntegerValue(v string) *AssetPropertyVariant {
	s.IntegerValue = &v
	return s
}

// SetStringValue sets the StringValue field's value.
func (s *AssetPropertyVariant) SetStringValue(v string) *AssetPropertyVariant {
	s.StringValue = &v
	return s
}

// The attributes from the JSON payload that are made available by the input.
// Inputs are derived from messages sent to the AWS IoT Events system using
// BatchPutMessage. Each such message contains a JSON payload. Those attributes
// (and their paired values) specified here are available for use in the condition
// expressions used by detectors.
type Attribute struct {
	_ struct{} `type:"structure"`

	// An expression that specifies an attribute-value pair in a JSON structure.
	// Use this to specify an attribute from the JSON payload that is made available
	// by the input. Inputs are derived from messages sent to AWS IoT Events (BatchPutMessage).
	// Each such message contains a JSON payload. The attribute (and its paired
	// value) specified here are available for use in the condition expressions
	// used by detectors.
	//
	// Syntax: <field-name>.<field-name>...
	//
	// JsonPath is a required field
	JsonPath *string `locationName:"jsonPath" min:"1" type:"string" required:"true"`
}

// String returns the string representation
func (s Attribute) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s Attribute) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *Attribute) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "Attribute"}
	if s.JsonPath == nil {
		invalidParams.Add(request.NewErrParamRequired("JsonPath"))
	}
	if s.JsonPath != nil && len(*s.JsonPath) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("JsonPath", 1))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetJsonPath sets the JsonPath field's value.
func (s *Attribute) SetJsonPath(v string) *Attribute {
	s.JsonPath = &v
	return s
}

// Information needed to clear the timer.
type ClearTimerAction struct {
	_ struct{} `type:"structure"`

	// The name of the timer to clear.
	//
	// TimerName is a required field
	TimerName *string `locationName:"timerName" min:"1" type:"string" required:"true"`
}

// String returns the string representation
func (s ClearTimerAction) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s ClearTimerAction) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *ClearTimerAction) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "ClearTimerAction"}
	if s.TimerName == nil {
		invalidParams.Add(request.NewErrParamRequired("TimerName"))
	}
	if s.TimerName != nil && len(*s.TimerName) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("TimerName", 1))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetTimerName sets the TimerName field's value.
func (s *ClearTimerAction) SetTimerName(v string) *ClearTimerAction {
	s.TimerName = &v
	return s
}

type CreateDetectorModelInput struct {
	_ struct{} `type:"structure"`

	// Information that defines how the detectors operate.
	//
	// DetectorModelDefinition is a required field
	DetectorModelDefinition *DetectorModelDefinition `locationName:"detectorModelDefinition" type:"structure" required:"true"`

	// A brief description of the detector model.
	DetectorModelDescription *string `locationName:"detectorModelDescription" type:"string"`

	// The name of the detector model.
	//
	// DetectorModelName is a required field
	DetectorModelName *string `locationName:"detectorModelName" min:"1" type:"string" required:"true"`

	// Information about the order in which events are evaluated and how actions
	// are executed.
	EvaluationMethod *string `locationName:"evaluationMethod" type:"string" enum:"EvaluationMethod"`

	// The input attribute key used to identify a device or system to create a detector
	// (an instance of the detector model) and then to route each input received
	// to the appropriate detector (instance). This parameter uses a JSON-path expression
	// in the message payload of each input to specify the attribute-value pair
	// that is used to identify the device associated with the input.
	Key *string `locationName:"key" min:"1" type:"string"`

	// The ARN of the role that grants permission to AWS IoT Events to perform its
	// operations.
	//
	// RoleArn is a required field
	RoleArn *string `locationName:"roleArn" min:"1" type:"string" required:"true"`

	// Metadata that can be used to manage the detector model.
	Tags []*Tag `locationName:"tags" type:"list"`
}

// String returns the string representation
func (s CreateDetectorModelInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s CreateDetectorModelInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *CreateDetectorModelInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "CreateDetectorModelInput"}
	if s.DetectorModelDefinition == nil {
		invalidParams.Add(request.NewErrParamRequired("DetectorModelDefinition"))
	}
	if s.DetectorModelName == nil {
		invalidParams.Add(request.NewErrParamRequired("DetectorModelName"))
	}
	if s.DetectorModelName != nil && len(*s.DetectorModelName) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("DetectorModelName", 1))
	}
	if s.Key != nil && len(*s.Key) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("Key", 1))
	}
	if s.RoleArn == nil {
		invalidParams.Add(request.NewErrParamRequired("RoleArn"))
	}
	if s.RoleArn != nil && len(*s.RoleArn) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("RoleArn", 1))
	}
	if s.DetectorModelDefinition != nil {
		if err := s.DetectorModelDefinition.Validate(); err != nil {
			invalidParams.AddNested("DetectorModelDefinition", err.(request.ErrInvalidParams))
		}
	}
	if s.Tags != nil {
		for i, v := range s.Tags {
			if v == nil {
				continue
			}
			if err := v.Validate(); err != nil {
				invalidParams.AddNested(fmt.Sprintf("%s[%v]", "Tags", i), err.(request.ErrInvalidParams))
			}
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetDetectorModelDefinition sets the DetectorModelDefinition field's value.
func (s *CreateDetectorModelInput) SetDetectorModelDefinition(v *DetectorModelDefinition) *CreateDetectorModelInput {
	s.DetectorModelDefinition = v
	return s
}

// SetDetectorModelDescription sets the DetectorModelDescription field's value.
func (s *CreateDetectorModelInput) SetDetectorModelDescription(v string) *CreateDetectorModelInput {
	s.DetectorModelDescription = &v
	return s
}

// SetDetectorModelName sets the DetectorModelName field's value.
func (s *CreateDetectorModelInput) SetDetectorModelName(v string) *CreateDetectorModelInput {
	s.DetectorModelName = &v
	return s
}

// SetEvaluationMethod sets the EvaluationMethod field's value.
func (s *CreateDetectorModelInput) SetEvaluationMethod(v string) *CreateDetectorModelInput {
	s.EvaluationMethod = &v
	return s
}

// SetKey sets the Key field's value.
func (s *CreateDetectorModelInput) SetKey(v string) *CreateDetectorModelInput {
	s.Key = &v
	return s
}

// SetRoleArn sets the RoleArn field's value.
func (s *CreateDetectorModelInput) SetRoleArn(v string) *CreateDetectorModelInput {
	s.RoleArn = &v
	return s
}

// SetTags sets the Tags field's value.
func (s *CreateDetectorModelInput) SetTags(v []*Tag) *CreateDetectorModelInput {
	s.Tags = v
	return s
}

type CreateDetectorModelOutput struct {
	_ struct{} `type:"structure"`

	// Information about how the detector model is configured.
	DetectorModelConfiguration *DetectorModelConfiguration `locationName:"detectorModelConfiguration" type:"structure"`
}

// String returns the string representation
func (s CreateDetectorModelOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s CreateDetectorModelOutput) GoString() string {
	return s.String()
}

// SetDetectorModelConfiguration sets the DetectorModelConfiguration field's value.
func (s *CreateDetectorModelOutput) SetDetectorModelConfiguration(v *DetectorModelConfiguration) *CreateDetectorModelOutput {
	s.DetectorModelConfiguration = v
	return s
}

type CreateInputInput struct {
	_ struct{} `type:"structure"`

	// The definition of the input.
	//
	// InputDefinition is a required field
	InputDefinition *InputDefinition `locationName:"inputDefinition" type:"structure" required:"true"`

	// A brief description of the input.
	InputDescription *string `locationName:"inputDescription" type:"string"`

	// The name you want to give to the input.
	//
	// InputName is a required field
	InputName *string `locationName:"inputName" min:"1" type:"string" required:"true"`

	// Metadata that can be used to manage the input.
	Tags []*Tag `locationName:"tags" type:"list"`
}

// String returns the string representation
func (s CreateInputInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s CreateInputInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *CreateInputInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "CreateInputInput"}
	if s.InputDefinition == nil {
		invalidParams.Add(request.NewErrParamRequired("InputDefinition"))
	}
	if s.InputName == nil {
		invalidParams.Add(request.NewErrParamRequired("InputName"))
	}
	if s.InputName != nil && len(*s.InputName) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("InputName", 1))
	}
	if s.InputDefinition != nil {
		if err := s.InputDefinition.Validate(); err != nil {
			invalidParams.AddNested("InputDefinition", err.(request.ErrInvalidParams))
		}
	}
	if s.Tags != nil {
		for i, v := range s.Tags {
			if v == nil {
				continue
			}
			if err := v.Validate(); err != nil {
				invalidParams.AddNested(fmt.Sprintf("%s[%v]", "Tags", i), err.(request.ErrInvalidParams))
			}
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetInputDefinition sets the InputDefinition field's value.
func (s *CreateInputInput) SetInputDefinition(v *InputDefinition) *CreateInputInput {
	s.InputDefinition = v
	return s
}

// SetInputDescription sets the InputDescription field's value.
func (s *CreateInputInput) SetInputDescription(v string) *CreateInputInput {
	s.InputDescription = &v
	return s
}

// SetInputName sets the InputName field's value.
func (s *CreateInputInput) SetInputName(v string) *CreateInputInput {
	s.InputName = &v
	return s
}

// SetTags sets the Tags field's value.
func (s *CreateInputInput) SetTags(v []*Tag) *CreateInputInput {
	s.Tags = v
	return s
}

type CreateInputOutput struct {
	_ struct{} `type:"structure"`

	// Information about the configuration of the input.
	InputConfiguration *InputConfiguration `locationName:"inputConfiguration" type:"structure"`
}

// String returns the string representation
func (s CreateInputOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s CreateInputOutput) GoString() string {
	return s.String()
}

// SetInputConfiguration sets the InputConfiguration field's value.
func (s *CreateInputOutput) SetInputConfiguration(v *InputConfiguration) *CreateInputOutput {
	s.InputConfiguration = v
	return s
}

type DeleteDetectorModelInput struct {
	_ struct{} `type:"structure"`

	// The name of the detector model to be deleted.
	//
	// DetectorModelName is a required field
	DetectorModelName *string `location:"uri" locationName:"detectorModelName" min:"1" type:"string" required:"true"`
}

// String returns the string representation
func (s DeleteDetectorModelInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s DeleteDetectorModelInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *DeleteDetectorModelInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "DeleteDetectorModelInput"}
	if s.DetectorModelName == nil {
		invalidParams.Add(request.NewErrParamRequired("DetectorModelName"))
	}
	if s.DetectorModelName != nil && len(*s.DetectorModelName) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("DetectorModelName", 1))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetDetectorModelName sets the DetectorModelName field's value.
func (s *DeleteDetectorModelInput) SetDetectorModelName(v string) *DeleteDetectorModelInput {
	s.DetectorModelName = &v
	return s
}

type DeleteDetectorModelOutput struct {
	_ struct{} `type:"structure"`
}

// String returns the string representation
func (s DeleteDetectorModelOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s DeleteDetectorModelOutput) GoString() string {
	return s.String()
}

type DeleteInputInput struct {
	_ struct{} `type:"structure"`

	// The name of the input to delete.
	//
	// InputName is a required field
	InputName *string `location:"uri" locationName:"inputName" min:"1" type:"string" required:"true"`
}

// String returns the string representation
func (s DeleteInputInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s DeleteInputInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *DeleteInputInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "DeleteInputInput"}
	if s.InputName == nil {
		invalidParams.Add(request.NewErrParamRequired("InputName"))
	}
	if s.InputName != nil && len(*s.InputName) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("InputName", 1))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetInputName sets the InputName field's value.
func (s *DeleteInputInput) SetInputName(v string) *DeleteInputInput {
	s.InputName = &v
	return s
}

type DeleteInputOutput struct {
	_ struct{} `type:"structure"`
}

// String returns the string representation
func (s DeleteInputOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s DeleteInputOutput) GoString() string {
	return s.String()
}

type DescribeDetectorModelInput struct {
	_ struct{} `type:"structure"`

	// The name of the detector model.
	//
	// DetectorModelName is a required field
	DetectorModelName *string `location:"uri" locationName:"detectorModelName" min:"1" type:"string" required:"true"`

	// The version of the detector model.
	DetectorModelVersion *string `location:"querystring" locationName:"version" min:"1" type:"string"`
}

// String returns the string representation
func (s DescribeDetectorModelInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s DescribeDetectorModelInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *DescribeDetectorModelInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "DescribeDetectorModelInput"}
	if s.DetectorModelName == nil {
		invalidParams.Add(request.NewErrParamRequired("DetectorModelName"))
	}
	if s.DetectorModelName != nil && len(*s.DetectorModelName) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("DetectorModelName", 1))
	}
	if s.DetectorModelVersion != nil && len(*s.DetectorModelVersion) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("DetectorModelVersion", 1))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetDetectorModelName sets the DetectorModelName field's value.
func (s *DescribeDetectorModelInput) SetDetectorModelName(v string) *DescribeDetectorModelInput {
	s.DetectorModelName = &v
	return s
}

// SetDetectorModelVersion sets the DetectorModelVersion field's value.
func (s *DescribeDetectorModelInput) SetDetectorModelVersion(v string) *DescribeDetectorModelInput {
	s.DetectorModelVersion = &v
	return s
}

type DescribeDetectorModelOutput struct {
	_ struct{} `type:"structure"`

	// Information about the detector model.
	DetectorModel *DetectorModel `locationName:"detectorModel" type:"structure"`
}

// String returns the string representation
func (s DescribeDetectorModelOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s DescribeDetectorModelOutput) GoString() string {
	return s.String()
}

// SetDetectorModel sets the DetectorModel field's value.
func (s *DescribeDetectorModelOutput) SetDetectorModel(v *DetectorModel) *DescribeDetectorModelOutput {
	s.DetectorModel = v
	return s
}

type DescribeInputInput struct {
	_ struct{} `type:"structure"`

	// The name of the input.
	//
	// InputName is a required field
	InputName *string `location:"uri" locationName:"inputName" min:"1" type:"string" required:"true"`
}

// String returns the string representation
func (s DescribeInputInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s DescribeInputInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *DescribeInputInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "DescribeInputInput"}
	if s.InputName == nil {
		invalidParams.Add(request.NewErrParamRequired("InputName"))
	}
	if s.InputName != nil && len(*s.InputName) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("InputName", 1))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetInputName sets the InputName field's value.
func (s *DescribeInputInput) SetInputName(v string) *DescribeInputInput {
	s.InputName = &v
	return s
}

type DescribeInputOutput struct {
	_ struct{} `type:"structure"`

	// Information about the input.
	Input *Input `locationName:"input" type:"structure"`
}

// String returns the string representation
func (s DescribeInputOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s DescribeInputOutput) GoString() string {
	return s.String()
}

// SetInput sets the Input field's value.
func (s *DescribeInputOutput) SetInput(v *Input) *DescribeInputOutput {
	s.Input = v
	return s
}

type DescribeLoggingOptionsInput struct {
	_ struct{} `type:"structure"`
}

// String returns the string representation
func (s DescribeLoggingOptionsInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s DescribeLoggingOptionsInput) GoString() string {
	return s.String()
}

type DescribeLoggingOptionsOutput struct {
	_ struct{} `type:"structure"`

	// The current settings of the AWS IoT Events logging options.
	LoggingOptions *LoggingOptions `locationName:"loggingOptions" type:"structure"`
}

// String returns the string representation
func (s DescribeLoggingOptionsOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s DescribeLoggingOptionsOutput) GoString() string {
	return s.String()
}

// SetLoggingOptions sets the LoggingOptions field's value.
func (s *DescribeLoggingOptionsOutput) SetLoggingOptions(v *LoggingOptions) *DescribeLoggingOptionsOutput {
	s.LoggingOptions = v
	return s
}

// The detector model and the specific detectors (instances) for which the logging
// level is given.
type DetectorDebugOption struct {
	_ struct{} `type:"structure"`

	// The name of the detector model.
	//
	// DetectorModelName is a required field
	DetectorModelName *string `locationName:"detectorModelName" min:"1" type:"string" required:"true"`

	// The value of the input attribute key used to create the detector (the instance
	// of the detector model).
	KeyValue *string `locationName:"keyValue" min:"1" type:"string"`
}

// String returns the string representation
func (s DetectorDebugOption) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s DetectorDebugOption) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *DetectorDebugOption) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "DetectorDebugOption"}
	if s.DetectorModelName == nil {
		invalidParams.Add(request.NewErrParamRequired("DetectorModelName"))
	}
	if s.DetectorModelName != nil && len(*s.DetectorModelName) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("DetectorModelName", 1))
	}
	if s.KeyValue != nil && len(*s.KeyValue) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("KeyValue", 1))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetDetectorModelName sets the DetectorModelName field's value.
func (s *DetectorDebugOption) SetDetectorModelName(v string) *DetectorDebugOption {
	s.DetectorModelName = &v
	return s
}

// SetKeyValue sets the KeyValue field's value.
func (s *DetectorDebugOption) SetKeyValue(v string) *DetectorDebugOption {
	s.KeyValue = &v
	return s
}

// Information about the detector model.
type DetectorModel struct {
	_ struct{} `type:"structure"`

	// Information about how the detector is configured.
	DetectorModelConfiguration *DetectorModelConfiguration `locationName:"detectorModelConfiguration" type:"structure"`

	// Information that defines how a detector operates.
	DetectorModelDefinition *DetectorModelDefinition `locationName:"detectorModelDefinition" type:"structure"`
}

// String returns the string representation
func (s DetectorModel) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s DetectorModel) GoString() string {
	return s.String()
}

// SetDetectorModelConfiguration sets the DetectorModelConfiguration field's value.
func (s *DetectorModel) SetDetectorModelConfiguration(v *DetectorModelConfiguration) *DetectorModel {
	s.DetectorModelConfiguration = v
	return s
}

// SetDetectorModelDefinition sets the DetectorModelDefinition field's value.
func (s *DetectorModel) SetDetectorModelDefinition(v *DetectorModelDefinition) *DetectorModel {
	s.DetectorModelDefinition = v
	return s
}

// Information about how the detector model is configured.
type DetectorModelConfiguration struct {
	_ struct{} `type:"structure"`

	// The time the detector model was created.
	CreationTime *time.Time `locationName:"creationTime" type:"timestamp"`

	// The ARN of the detector model.
	DetectorModelArn *string `locationName:"detectorModelArn" type:"string"`

	// A brief description of the detector model.
	DetectorModelDescription *string `locationName:"detectorModelDescription" type:"string"`

	// The name of the detector model.
	DetectorModelName *string `locationName:"detectorModelName" min:"1" type:"string"`

	// The version of the detector model.
	DetectorModelVersion *string `locationName:"detectorModelVersion" min:"1" type:"string"`

	// Information about the order in which events are evaluated and how actions
	// are executed.
	EvaluationMethod *string `locationName:"evaluationMethod" type:"string" enum:"EvaluationMethod"`

	// The value used to identify a detector instance. When a device or system sends
	// input, a new detector instance with a unique key value is created. AWS IoT
	// Events can continue to route input to its corresponding detector instance
	// based on this identifying information.
	//
	// This parameter uses a JSON-path expression to select the attribute-value
	// pair in the message payload that is used for identification. To route the
	// message to the correct detector instance, the device must send a message
	// payload that contains the same attribute-value.
	Key *string `locationName:"key" min:"1" type:"string"`

	// The time the detector model was last updated.
	LastUpdateTime *time.Time `locationName:"lastUpdateTime" type:"timestamp"`

	// The ARN of the role that grants permission to AWS IoT Events to perform its
	// operations.
	RoleArn *string `locationName:"roleArn" min:"1" type:"string"`

	// The status of the detector model.
	Status *string `locationName:"status" type:"string" enum:"DetectorModelVersionStatus"`
}

// String returns the string representation
func (s DetectorModelConfiguration) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s DetectorModelConfiguration) GoString() string {
	return s.String()
}

// SetCreationTime sets the CreationTime field's value.
func (s *DetectorModelConfiguration) SetCreationTime(v time.Time) *DetectorModelConfiguration {
	s.CreationTime = &v
	return s
}

// SetDetectorModelArn sets the DetectorModelArn field's value.
func (s *DetectorModelConfiguration) SetDetectorModelArn(v string) *DetectorModelConfiguration {
	s.DetectorModelArn = &v
	return s
}

// SetDetectorModelDescription sets the DetectorModelDescription field's value.
func (s *DetectorModelConfiguration) SetDetectorModelDescription(v string) *DetectorModelConfiguration {
	s.DetectorModelDescription = &v
	return s
}

// SetDetectorModelName sets the DetectorModelName field's value.
func (s *DetectorModelConfiguration) SetDetectorModelName(v string) *DetectorModelConfiguration {
	s.DetectorModelName = &v
	return s
}

// SetDetectorModelVersion sets the DetectorModelVersion field's value.
func (s *DetectorModelConfiguration) SetDetectorModelVersion(v string) *DetectorModelConfiguration {
	s.DetectorModelVersion = &v
	return s
}

// SetEvaluationMethod sets the EvaluationMethod field's value.
func (s *DetectorModelConfiguration) SetEvaluationMethod(v string) *DetectorModelConfiguration {
	s.EvaluationMethod = &v
	return s
}

// SetKey sets the Key field's value.
func (s *DetectorModelConfiguration) SetKey(v string) *DetectorModelConfiguration {
	s.Key = &v
	return s
}

// SetLastUpdateTime sets the LastUpdateTime field's value.
func (s *DetectorModelConfiguration) SetLastUpdateTime(v time.Time) *DetectorModelConfiguration {
	s.LastUpdateTime = &v
	return s
}

// SetRoleArn sets the RoleArn field's value.
func (s *DetectorModelConfiguration) SetRoleArn(v string) *DetectorModelConfiguration {
	s.RoleArn = &v
	return s
}

// SetStatus sets the Status field's value.
func (s *DetectorModelConfiguration) SetStatus(v string) *DetectorModelConfiguration {
	s.Status = &v
	return s
}

// Information that defines how a detector operates.
type DetectorModelDefinition struct {
	_ struct{} `type:"structure"`

	// The state that is entered at the creation of each detector (instance).
	//
	// InitialStateName is a required field
	InitialStateName *string `locationName:"initialStateName" min:"1" type:"string" required:"true"`

	// Information about the states of the detector.
	//
	// States is a required field
	States []*State `locationName:"states" min:"1" type:"list" required:"true"`
}

// String returns the string representation
func (s DetectorModelDefinition) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s DetectorModelDefinition) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *DetectorModelDefinition) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "DetectorModelDefinition"}
	if s.InitialStateName == nil {
		invalidParams.Add(request.NewErrParamRequired("InitialStateName"))
	}
	if s.InitialStateName != nil && len(*s.InitialStateName) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("InitialStateName", 1))
	}
	if s.States == nil {
		invalidParams.Add(request.NewErrParamRequired("States"))
	}
	if s.States != nil && len(s.States) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("States", 1))
	}
	if s.States != nil {
		for i, v := range s.States {
			if v == nil {
				continue
			}
			if err := v.Validate(); err != nil {
				invalidParams.AddNested(fmt.Sprintf("%s[%v]", "States", i), err.(request.ErrInvalidParams))
			}
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetInitialStateName sets the InitialStateName field's value.
func (s *DetectorModelDefinition) SetInitialStateName(v string) *DetectorModelDefinition {
	s.InitialStateName = &v
	return s
}

// SetStates sets the States field's value.
func (s *DetectorModelDefinition) SetStates(v []*State) *DetectorModelDefinition {
	s.States = v
	return s
}

// Information about the detector model.
type DetectorModelSummary struct {
	_ struct{} `type:"structure"`

	// The time the detector model was created.
	CreationTime *time.Time `locationName:"creationTime" type:"timestamp"`

	// A brief description of the detector model.
	DetectorModelDescription *string `locationName:"detectorModelDescription" type:"string"`

	// The name of the detector model.
	DetectorModelName *string `locationName:"detectorModelName" min:"1" type:"string"`
}

// String returns the string representation
func (s DetectorModelSummary) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s DetectorModelSummary) GoString() string {
	return s.String()
}

// SetCreationTime sets the CreationTime field's value.
func (s *DetectorModelSummary) SetCreationTime(v time.Time) *DetectorModelSummary {
	s.CreationTime = &v
	return s
}

// SetDetectorModelDescription sets the DetectorModelDescription field's value.
func (s *DetectorModelSummary) SetDetectorModelDescription(v string) *DetectorModelSummary {
	s.DetectorModelDescription = &v
	return s
}

// SetDetectorModelName sets the DetectorModelName field's value.
func (s *DetectorModelSummary) SetDetectorModelName(v string) *DetectorModelSummary {
	s.DetectorModelName = &v
	return s
}

// Information about the detector model version.
type DetectorModelVersionSummary struct {
	_ struct{} `type:"structure"`

	// The time the detector model version was created.
	CreationTime *time.Time `locationName:"creationTime" type:"timestamp"`

	// The ARN of the detector model version.
	DetectorModelArn *string `locationName:"detectorModelArn" type:"string"`

	// The name of the detector model.
	DetectorModelName *string `locationName:"detectorModelName" min:"1" type:"string"`

	// The ID of the detector model version.
	DetectorModelVersion *string `locationName:"detectorModelVersion" min:"1" type:"string"`

	// Information about the order in which events are evaluated and how actions
	// are executed.
	EvaluationMethod *string `locationName:"evaluationMethod" type:"string" enum:"EvaluationMethod"`

	// The last time the detector model version was updated.
	LastUpdateTime *time.Time `locationName:"lastUpdateTime" type:"timestamp"`

	// The ARN of the role that grants the detector model permission to perform
	// its tasks.
	RoleArn *string `locationName:"roleArn" min:"1" type:"string"`

	// The status of the detector model version.
	Status *string `locationName:"status" type:"string" enum:"DetectorModelVersionStatus"`
}

// String returns the string representation
func (s DetectorModelVersionSummary) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s DetectorModelVersionSummary) GoString() string {
	return s.String()
}

// SetCreationTime sets the CreationTime field's value.
func (s *DetectorModelVersionSummary) SetCreationTime(v time.Time) *DetectorModelVersionSummary {
	s.CreationTime = &v
	return s
}

// SetDetectorModelArn sets the DetectorModelArn field's value.
func (s *DetectorModelVersionSummary) SetDetectorModelArn(v string) *DetectorModelVersionSummary {
	s.DetectorModelArn = &v
	return s
}

// SetDetectorModelName sets the DetectorModelName field's value.
func (s *DetectorModelVersionSummary) SetDetectorModelName(v string) *DetectorModelVersionSummary {
	s.DetectorModelName = &v
	return s
}

// SetDetectorModelVersion sets the DetectorModelVersion field's value.
func (s *DetectorModelVersionSummary) SetDetectorModelVersion(v string) *DetectorModelVersionSummary {
	s.DetectorModelVersion = &v
	return s
}

// SetEvaluationMethod sets the EvaluationMethod field's value.
func (s *DetectorModelVersionSummary) SetEvaluationMethod(v string) *DetectorModelVersionSummary {
	s.EvaluationMethod = &v
	return s
}

// SetLastUpdateTime sets the LastUpdateTime field's value.
func (s *DetectorModelVersionSummary) SetLastUpdateTime(v time.Time) *DetectorModelVersionSummary {
	s.LastUpdateTime = &v
	return s
}

// SetRoleArn sets the RoleArn field's value.
func (s *DetectorModelVersionSummary) SetRoleArn(v string) *DetectorModelVersionSummary {
	s.RoleArn = &v
	return s
}

// SetStatus sets the Status field's value.
func (s *DetectorModelVersionSummary) SetStatus(v string) *DetectorModelVersionSummary {
	s.Status = &v
	return s
}

// Defines an action to write to the Amazon DynamoDB table that you created.
// The standard action payload contains all attribute-value pairs that have
// the information about the detector model instance and the event that triggered
// the action. You can also customize the payload (https://docs.aws.amazon.com/iotevents/latest/apireference/API_Payload.html).
// One column of the DynamoDB table receives all attribute-value pairs in the
// payload that you specify.
//
// The tableName and hashKeyField values must match the table name and the partition
// key of the DynamoDB table.
//
// If the DynamoDB table also has a sort key, you must specify rangeKeyField.
// The rangeKeyField value must match the sort key.
//
// The hashKeyValue and rangeKeyValue use substitution templates. These templates
// provide data at runtime. The syntax is ${sql-expression}.
//
// You can use expressions for parameters that are string data type. For more
// information, see Expressions (https://docs.aws.amazon.com/iotevents/latest/developerguide/iotevents-expressions.html)
// in the AWS IoT Events Developer Guide.
//
// If the defined payload type is a string, DynamoDBAction writes non-JSON data
// to the DynamoDB table as binary data. The DynamoDB console displays the data
// as Base64-encoded text. The payloadField is <payload-field>_raw.
type DynamoDBAction struct {
	_ struct{} `type:"structure"`

	// The name of the hash key (also called the partition key).
	//
	// HashKeyField is a required field
	HashKeyField *string `locationName:"hashKeyField" type:"string" required:"true"`

	// The data type for the hash key (also called the partition key). You can specify
	// the following values:
	//
	//    * STRING - The hash key is a string.
	//
	//    * NUMBER - The hash key is a number.
	//
	// If you don't specify hashKeyType, the default value is STRING.
	HashKeyType *string `locationName:"hashKeyType" type:"string"`

	// The value of the hash key (also called the partition key).
	//
	// HashKeyValue is a required field
	HashKeyValue *string `locationName:"hashKeyValue" type:"string" required:"true"`

	// The type of operation to perform. You can specify the following values:
	//
	//    * INSERT - Insert data as a new item into the DynamoDB table. This item
	//    uses the specified hash key as a partition key. If you specified a range
	//    key, the item uses the range key as a sort key.
	//
	//    * UPDATE - Update an existing item of the DynamoDB table with new data.
	//    This item's partition key must match the specified hash key. If you specified
	//    a range key, the range key must match the item's sort key.
	//
	//    * DELETE - Delete an existing item of the DynamoDB table. This item's
	//    partition key must match the specified hash key. If you specified a range
	//    key, the range key must match the item's sort key.
	//
	// If you don't specify this parameter, AWS IoT Events triggers the INSERT operation.
	Operation *string `locationName:"operation" type:"string"`

	// Information needed to configure the payload.
	//
	// By default, AWS IoT Events generates a standard payload in JSON for any action.
	// This action payload contains all attribute-value pairs that have the information
	// about the detector model instance and the event triggered the action. To
	// configure the action payload, you can use contentExpression.
	Payload *Payload `locationName:"payload" type:"structure"`

	// The name of the DynamoDB column that receives the action payload.
	//
	// If you don't specify this parameter, the name of the DynamoDB column is payload.
	PayloadField *string `locationName:"payloadField" type:"string"`

	// The name of the range key (also called the sort key).
	RangeKeyField *string `locationName:"rangeKeyField" type:"string"`

	// The data type for the range key (also called the sort key), You can specify
	// the following values:
	//
	//    * STRING - The range key is a string.
	//
	//    * NUMBER - The range key is number.
	//
	// If you don't specify rangeKeyField, the default value is STRING.
	RangeKeyType *string `locationName:"rangeKeyType" type:"string"`

	// The value of the range key (also called the sort key).
	RangeKeyValue *string `locationName:"rangeKeyValue" type:"string"`

	// The name of the DynamoDB table.
	//
	// TableName is a required field
	TableName *string `locationName:"tableName" type:"string" required:"true"`
}

// String returns the string representation
func (s DynamoDBAction) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s DynamoDBAction) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *DynamoDBAction) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "DynamoDBAction"}
	if s.HashKeyField == nil {
		invalidParams.Add(request.NewErrParamRequired("HashKeyField"))
	}
	if s.HashKeyValue == nil {
		invalidParams.Add(request.NewErrParamRequired("HashKeyValue"))
	}
	if s.TableName == nil {
		invalidParams.Add(request.NewErrParamRequired("TableName"))
	}
	if s.Payload != nil {
		if err := s.Payload.Validate(); err != nil {
			invalidParams.AddNested("Payload", err.(request.ErrInvalidParams))
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetHashKeyField sets the HashKeyField field's value.
func (s *DynamoDBAction) SetHashKeyField(v string) *DynamoDBAction {
	s.HashKeyField = &v
	return s
}

// SetHashKeyType sets the HashKeyType field's value.
func (s *DynamoDBAction) SetHashKeyType(v string) *DynamoDBAction {
	s.HashKeyType = &v
	return s
}

// SetHashKeyValue sets the HashKeyValue field's value.
func (s *DynamoDBAction) SetHashKeyValue(v string) *DynamoDBAction {
	s.HashKeyValue = &v
	return s
}

// SetOperation sets the Operation field's value.
func (s *DynamoDBAction) SetOperation(v string) *DynamoDBAction {
	s.Operation = &v
	return s
}

// SetPayload sets the Payload field's value.
func (s *DynamoDBAction) SetPayload(v *Payload) *DynamoDBAction {
	s.Payload = v
	return s
}

// SetPayloadField sets the PayloadField field's value.
func (s *DynamoDBAction) SetPayloadField(v string) *DynamoDBAction {
	s.PayloadField = &v
	return s
}

// SetRangeKeyField sets the RangeKeyField field's value.
func (s *DynamoDBAction) SetRangeKeyField(v string) *DynamoDBAction {
	s.RangeKeyField = &v
	return s
}

// SetRangeKeyType sets the RangeKeyType field's value.
func (s *DynamoDBAction) SetRangeKeyType(v string) *DynamoDBAction {
	s.RangeKeyType = &v
	return s
}

// SetRangeKeyValue sets the RangeKeyValue field's value.
func (s *DynamoDBAction) SetRangeKeyValue(v string) *DynamoDBAction {
	s.RangeKeyValue = &v
	return s
}

// SetTableName sets the TableName field's value.
func (s *DynamoDBAction) SetTableName(v string) *DynamoDBAction {
	s.TableName = &v
	return s
}

// Defines an action to write to the Amazon DynamoDB table that you created.
// The default action payload contains all attribute-value pairs that have the
// information about the detector model instance and the event that triggered
// the action. You can also customize the payload (https://docs.aws.amazon.com/iotevents/latest/apireference/API_Payload.html).
// A separate column of the DynamoDB table receives one attribute-value pair
// in the payload that you specify.
//
// The type value for Payload must be JSON.
//
// You can use expressions for parameters that are strings. For more information,
// see Expressions (https://docs.aws.amazon.com/iotevents/latest/developerguide/iotevents-expressions.html)
// in the AWS IoT Events Developer Guide.
type DynamoDBv2Action struct {
	_ struct{} `type:"structure"`

	// Information needed to configure the payload.
	//
	// By default, AWS IoT Events generates a standard payload in JSON for any action.
	// This action payload contains all attribute-value pairs that have the information
	// about the detector model instance and the event triggered the action. To
	// configure the action payload, you can use contentExpression.
	Payload *Payload `locationName:"payload" type:"structure"`

	// The name of the DynamoDB table.
	//
	// TableName is a required field
	TableName *string `locationName:"tableName" type:"string" required:"true"`
}

// String returns the string representation
func (s DynamoDBv2Action) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s DynamoDBv2Action) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *DynamoDBv2Action) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "DynamoDBv2Action"}
	if s.TableName == nil {
		invalidParams.Add(request.NewErrParamRequired("TableName"))
	}
	if s.Payload != nil {
		if err := s.Payload.Validate(); err != nil {
			invalidParams.AddNested("Payload", err.(request.ErrInvalidParams))
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetPayload sets the Payload field's value.
func (s *DynamoDBv2Action) SetPayload(v *Payload) *DynamoDBv2Action {
	s.Payload = v
	return s
}

// SetTableName sets the TableName field's value.
func (s *DynamoDBv2Action) SetTableName(v string) *DynamoDBv2Action {
	s.TableName = &v
	return s
}

// Specifies the actions to be performed when the condition evaluates to TRUE.
type Event struct {
	_ struct{} `type:"structure"`

	// The actions to be performed.
	Actions []*ActionData `locationName:"actions" type:"list"`

	// Optional. The Boolean expression that, when TRUE, causes the actions to be
	// performed. If not present, the actions are performed (=TRUE). If the expression
	// result is not a Boolean value, the actions are not performed (=FALSE).
	Condition *string `locationName:"condition" type:"string"`

	// The name of the event.
	//
	// EventName is a required field
	EventName *string `locationName:"eventName" type:"string" required:"true"`
}

// String returns the string representation
func (s Event) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s Event) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *Event) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "Event"}
	if s.EventName == nil {
		invalidParams.Add(request.NewErrParamRequired("EventName"))
	}
	if s.Actions != nil {
		for i, v := range s.Actions {
			if v == nil {
				continue
			}
			if err := v.Validate(); err != nil {
				invalidParams.AddNested(fmt.Sprintf("%s[%v]", "Actions", i), err.(request.ErrInvalidParams))
			}
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetActions sets the Actions field's value.
func (s *Event) SetActions(v []*ActionData) *Event {
	s.Actions = v
	return s
}

// SetCondition sets the Condition field's value.
func (s *Event) SetCondition(v string) *Event {
	s.Condition = &v
	return s
}

// SetEventName sets the EventName field's value.
func (s *Event) SetEventName(v string) *Event {
	s.EventName = &v
	return s
}

// Sends information about the detector model instance and the event that triggered
// the action to an Amazon Kinesis Data Firehose delivery stream.
type FirehoseAction struct {
	_ struct{} `type:"structure"`

	// The name of the Kinesis Data Firehose delivery stream where the data is written.
	//
	// DeliveryStreamName is a required field
	DeliveryStreamName *string `locationName:"deliveryStreamName" type:"string" required:"true"`

	// You can configure the action payload when you send a message to an Amazon
	// Kinesis Data Firehose delivery stream.
	Payload *Payload `locationName:"payload" type:"structure"`

	// A character separator that is used to separate records written to the Kinesis
	// Data Firehose delivery stream. Valid values are: '\n' (newline), '\t' (tab),
	// '\r\n' (Windows newline), ',' (comma).
	Separator *string `locationName:"separator" type:"string"`
}

// String returns the string representation
func (s FirehoseAction) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s FirehoseAction) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *FirehoseAction) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "FirehoseAction"}
	if s.DeliveryStreamName == nil {
		invalidParams.Add(request.NewErrParamRequired("DeliveryStreamName"))
	}
	if s.Payload != nil {
		if err := s.Payload.Validate(); err != nil {
			invalidParams.AddNested("Payload", err.(request.ErrInvalidParams))
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetDeliveryStreamName sets the DeliveryStreamName field's value.
func (s *FirehoseAction) SetDeliveryStreamName(v string) *FirehoseAction {
	s.DeliveryStreamName = &v
	return s
}

// SetPayload sets the Payload field's value.
func (s *FirehoseAction) SetPayload(v *Payload) *FirehoseAction {
	s.Payload = v
	return s
}

// SetSeparator sets the Separator field's value.
func (s *FirehoseAction) SetSeparator(v string) *FirehoseAction {
	s.Separator = &v
	return s
}

// Information about the input.
type Input struct {
	_ struct{} `type:"structure"`

	// Information about the configuration of an input.
	InputConfiguration *InputConfiguration `locationName:"inputConfiguration" type:"structure"`

	// The definition of the input.
	InputDefinition *InputDefinition `locationName:"inputDefinition" type:"structure"`
}

// String returns the string representation
func (s Input) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s Input) GoString() string {
	return s.String()
}

// SetInputConfiguration sets the InputConfiguration field's value.
func (s *Input) SetInputConfiguration(v *InputConfiguration) *Input {
	s.InputConfiguration = v
	return s
}

// SetInputDefinition sets the InputDefinition field's value.
func (s *Input) SetInputDefinition(v *InputDefinition) *Input {
	s.InputDefinition = v
	return s
}

// Information about the configuration of an input.
type InputConfiguration struct {
	_ struct{} `type:"structure"`

	// The time the input was created.
	//
	// CreationTime is a required field
	CreationTime *time.Time `locationName:"creationTime" type:"timestamp" required:"true"`

	// The ARN of the input.
	//
	// InputArn is a required field
	InputArn *string `locationName:"inputArn" type:"string" required:"true"`

	// A brief description of the input.
	InputDescription *string `locationName:"inputDescription" type:"string"`

	// The name of the input.
	//
	// InputName is a required field
	InputName *string `locationName:"inputName" min:"1" type:"string" required:"true"`

	// The last time the input was updated.
	//
	// LastUpdateTime is a required field
	LastUpdateTime *time.Time `locationName:"lastUpdateTime" type:"timestamp" required:"true"`

	// The status of the input.
	//
	// Status is a required field
	Status *string `locationName:"status" type:"string" required:"true" enum:"InputStatus"`
}

// String returns the string representation
func (s InputConfiguration) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s InputConfiguration) GoString() string {
	return s.String()
}

// SetCreationTime sets the CreationTime field's value.
func (s *InputConfiguration) SetCreationTime(v time.Time) *InputConfiguration {
	s.CreationTime = &v
	return s
}

// SetInputArn sets the InputArn field's value.
func (s *InputConfiguration) SetInputArn(v string) *InputConfiguration {
	s.InputArn = &v
	return s
}

// SetInputDescription sets the InputDescription field's value.
func (s *InputConfiguration) SetInputDescription(v string) *InputConfiguration {
	s.InputDescription = &v
	return s
}

// SetInputName sets the InputName field's value.
func (s *InputConfiguration) SetInputName(v string) *InputConfiguration {
	s.InputName = &v
	return s
}

// SetLastUpdateTime sets the LastUpdateTime field's value.
func (s *InputConfiguration) SetLastUpdateTime(v time.Time) *InputConfiguration {
	s.LastUpdateTime = &v
	return s
}

// SetStatus sets the Status field's value.
func (s *InputConfiguration) SetStatus(v string) *InputConfiguration {
	s.Status = &v
	return s
}

// The definition of the input.
type InputDefinition struct {
	_ struct{} `type:"structure"`

	// The attributes from the JSON payload that are made available by the input.
	// Inputs are derived from messages sent to the AWS IoT Events system using
	// BatchPutMessage. Each such message contains a JSON payload, and those attributes
	// (and their paired values) specified here are available for use in the condition
	// expressions used by detectors that monitor this input.
	//
	// Attributes is a required field
	Attributes []*Attribute `locationName:"attributes" min:"1" type:"list" required:"true"`
}

// String returns the string representation
func (s InputDefinition) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s InputDefinition) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *InputDefinition) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "InputDefinition"}
	if s.Attributes == nil {
		invalidParams.Add(request.NewErrParamRequired("Attributes"))
	}
	if s.Attributes != nil && len(s.Attributes) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("Attributes", 1))
	}
	if s.Attributes != nil {
		for i, v := range s.Attributes {
			if v == nil {
				continue
			}
			if err := v.Validate(); err != nil {
				invalidParams.AddNested(fmt.Sprintf("%s[%v]", "Attributes", i), err.(request.ErrInvalidParams))
			}
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetAttributes sets the Attributes field's value.
func (s *InputDefinition) SetAttributes(v []*Attribute) *InputDefinition {
	s.Attributes = v
	return s
}

// Information about the input.
type InputSummary struct {
	_ struct{} `type:"structure"`

	// The time the input was created.
	CreationTime *time.Time `locationName:"creationTime" type:"timestamp"`

	// The ARN of the input.
	InputArn *string `locationName:"inputArn" type:"string"`

	// A brief description of the input.
	InputDescription *string `locationName:"inputDescription" type:"string"`

	// The name of the input.
	InputName *string `locationName:"inputName" min:"1" type:"string"`

	// The last time the input was updated.
	LastUpdateTime *time.Time `locationName:"lastUpdateTime" type:"timestamp"`

	// The status of the input.
	Status *string `locationName:"status" type:"string" enum:"InputStatus"`
}

// String returns the string representation
func (s InputSummary) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s InputSummary) GoString() string {
	return s.String()
}

// SetCreationTime sets the CreationTime field's value.
func (s *InputSummary) SetCreationTime(v time.Time) *InputSummary {
	s.CreationTime = &v
	return s
}

// SetInputArn sets the InputArn field's value.
func (s *InputSummary) SetInputArn(v string) *InputSummary {
	s.InputArn = &v
	return s
}

// SetInputDescription sets the InputDescription field's value.
func (s *InputSummary) SetInputDescription(v string) *InputSummary {
	s.InputDescription = &v
	return s
}

// SetInputName sets the InputName field's value.
func (s *InputSummary) SetInputName(v string) *InputSummary {
	s.InputName = &v
	return s
}

// SetLastUpdateTime sets the LastUpdateTime field's value.
func (s *InputSummary) SetLastUpdateTime(v time.Time) *InputSummary {
	s.LastUpdateTime = &v
	return s
}

// SetStatus sets the Status field's value.
func (s *InputSummary) SetStatus(v string) *InputSummary {
	s.Status = &v
	return s
}

// An internal failure occurred.
type InternalFailureException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// The message for the exception.
	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation
func (s InternalFailureException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s InternalFailureException) GoString() string {
	return s.String()
}

func newErrorInternalFailureException(v protocol.ResponseMetadata) error {
	return &InternalFailureException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *InternalFailureException) Code() string {
	return "InternalFailureException"
}

// Message returns the exception's message.
func (s *InternalFailureException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *InternalFailureException) OrigErr() error {
	return nil
}

func (s *InternalFailureException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *InternalFailureException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *InternalFailureException) RequestID() string {
	return s.RespMetadata.RequestID
}

// The request was invalid.
type InvalidRequestException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// The message for the exception.
	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation
func (s InvalidRequestException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s InvalidRequestException) GoString() string {
	return s.String()
}

func newErrorInvalidRequestException(v protocol.ResponseMetadata) error {
	return &InvalidRequestException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *InvalidRequestException) Code() string {
	return "InvalidRequestException"
}

// Message returns the exception's message.
func (s *InvalidRequestException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *InvalidRequestException) OrigErr() error {
	return nil
}

func (s *InvalidRequestException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *InvalidRequestException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *InvalidRequestException) RequestID() string {
	return s.RespMetadata.RequestID
}

// Sends information about the detector model instance and the event that triggered
// the action to a specified asset property in AWS IoT SiteWise.
//
// You must specify either propertyAlias or both assetId and propertyId to identify
// the target asset property in AWS IoT SiteWise.
//
// For parameters that are string data type, you can specify the following options:
//
//    * Use a string. For example, the propertyAlias value can be '/company/windfarm/3/turbine/7/temperature'.
//
//    * Use an expression. For example, the propertyAlias value can be 'company/windfarm/${$input.TemperatureInput.sensorData.windfarmID}/turbine/${$input.TemperatureInput.sensorData.turbineID}/temperature'.
//    For more information, see Expressions (https://docs.aws.amazon.com/iotevents/latest/developerguide/iotevents-expressions.html)
//    in the AWS IoT Events Developer Guide.
type IotSiteWiseAction struct {
	_ struct{} `type:"structure"`

	// The ID of the asset that has the specified property. You can specify an expression.
	AssetId *string `locationName:"assetId" type:"string"`

	// A unique identifier for this entry. You can use the entry ID to track which
	// data entry causes an error in case of failure. The default is a new unique
	// identifier. You can also specify an expression.
	EntryId *string `locationName:"entryId" type:"string"`

	// The alias of the asset property. You can also specify an expression.
	PropertyAlias *string `locationName:"propertyAlias" type:"string"`

	// The ID of the asset property. You can specify an expression.
	PropertyId *string `locationName:"propertyId" type:"string"`

	// The value to send to the asset property. This value contains timestamp, quality,
	// and value (TQV) information.
	//
	// PropertyValue is a required field
	PropertyValue *AssetPropertyValue `locationName:"propertyValue" type:"structure" required:"true"`
}

// String returns the string representation
func (s IotSiteWiseAction) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s IotSiteWiseAction) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *IotSiteWiseAction) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "IotSiteWiseAction"}
	if s.PropertyValue == nil {
		invalidParams.Add(request.NewErrParamRequired("PropertyValue"))
	}
	if s.PropertyValue != nil {
		if err := s.PropertyValue.Validate(); err != nil {
			invalidParams.AddNested("PropertyValue", err.(request.ErrInvalidParams))
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetAssetId sets the AssetId field's value.
func (s *IotSiteWiseAction) SetAssetId(v string) *IotSiteWiseAction {
	s.AssetId = &v
	return s
}

// SetEntryId sets the EntryId field's value.
func (s *IotSiteWiseAction) SetEntryId(v string) *IotSiteWiseAction {
	s.EntryId = &v
	return s
}

// SetPropertyAlias sets the PropertyAlias field's value.
func (s *IotSiteWiseAction) SetPropertyAlias(v string) *IotSiteWiseAction {
	s.PropertyAlias = &v
	return s
}

// SetPropertyId sets the PropertyId field's value.
func (s *IotSiteWiseAction) SetPropertyId(v string) *IotSiteWiseAction {
	s.PropertyId = &v
	return s
}

// SetPropertyValue sets the PropertyValue field's value.
func (s *IotSiteWiseAction) SetPropertyValue(v *AssetPropertyValue) *IotSiteWiseAction {
	s.PropertyValue = v
	return s
}

// Information required to publish the MQTT message through the AWS IoT message
// broker.
type IotTopicPublishAction struct {
	_ struct{} `type:"structure"`

	// The MQTT topic of the message. You can use a string expression that includes
	// variables ($variable.<variable-name>) and input values ($input.<input-name>.<path-to-datum>)
	// as the topic string.
	//
	// MqttTopic is a required field
	MqttTopic *string `locationName:"mqttTopic" min:"1" type:"string" required:"true"`

	// You can configure the action payload when you publish a message to an AWS
	// IoT Core topic.
	Payload *Payload `locationName:"payload" type:"structure"`
}

// String returns the string representation
func (s IotTopicPublishAction) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s IotTopicPublishAction) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *IotTopicPublishAction) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "IotTopicPublishAction"}
	if s.MqttTopic == nil {
		invalidParams.Add(request.NewErrParamRequired("MqttTopic"))
	}
	if s.MqttTopic != nil && len(*s.MqttTopic) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("MqttTopic", 1))
	}
	if s.Payload != nil {
		if err := s.Payload.Validate(); err != nil {
			invalidParams.AddNested("Payload", err.(request.ErrInvalidParams))
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetMqttTopic sets the MqttTopic field's value.
func (s *IotTopicPublishAction) SetMqttTopic(v string) *IotTopicPublishAction {
	s.MqttTopic = &v
	return s
}

// SetPayload sets the Payload field's value.
func (s *IotTopicPublishAction) SetPayload(v *Payload) *IotTopicPublishAction {
	s.Payload = v
	return s
}

// Calls a Lambda function, passing in information about the detector model
// instance and the event that triggered the action.
type LambdaAction struct {
	_ struct{} `type:"structure"`

	// The ARN of the Lambda function that is executed.
	//
	// FunctionArn is a required field
	FunctionArn *string `locationName:"functionArn" min:"1" type:"string" required:"true"`

	// You can configure the action payload when you send a message to a Lambda
	// function.
	Payload *Payload `locationName:"payload" type:"structure"`
}

// String returns the string representation
func (s LambdaAction) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s LambdaAction) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *LambdaAction) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "LambdaAction"}
	if s.FunctionArn == nil {
		invalidParams.Add(request.NewErrParamRequired("FunctionArn"))
	}
	if s.FunctionArn != nil && len(*s.FunctionArn) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("FunctionArn", 1))
	}
	if s.Payload != nil {
		if err := s.Payload.Validate(); err != nil {
			invalidParams.AddNested("Payload", err.(request.ErrInvalidParams))
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetFunctionArn sets the FunctionArn field's value.
func (s *LambdaAction) SetFunctionArn(v string) *LambdaAction {
	s.FunctionArn = &v
	return s
}

// SetPayload sets the Payload field's value.
func (s *LambdaAction) SetPayload(v *Payload) *LambdaAction {
	s.Payload = v
	return s
}

// A limit was exceeded.
type LimitExceededException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// The message for the exception.
	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation
func (s LimitExceededException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s LimitExceededException) GoString() string {
	return s.String()
}

func newErrorLimitExceededException(v protocol.ResponseMetadata) error {
	return &LimitExceededException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *LimitExceededException) Code() string {
	return "LimitExceededException"
}

// Message returns the exception's message.
func (s *LimitExceededException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *LimitExceededException) OrigErr() error {
	return nil
}

func (s *LimitExceededException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *LimitExceededException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *LimitExceededException) RequestID() string {
	return s.RespMetadata.RequestID
}

type ListDetectorModelVersionsInput struct {
	_ struct{} `type:"structure"`

	// The name of the detector model whose versions are returned.
	//
	// DetectorModelName is a required field
	DetectorModelName *string `location:"uri" locationName:"detectorModelName" min:"1" type:"string" required:"true"`

	// The maximum number of results to return at one time.
	MaxResults *int64 `location:"querystring" locationName:"maxResults" min:"1" type:"integer"`

	// The token for the next set of results.
	NextToken *string `location:"querystring" locationName:"nextToken" type:"string"`
}

// String returns the string representation
func (s ListDetectorModelVersionsInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s ListDetectorModelVersionsInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *ListDetectorModelVersionsInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "ListDetectorModelVersionsInput"}
	if s.DetectorModelName == nil {
		invalidParams.Add(request.NewErrParamRequired("DetectorModelName"))
	}
	if s.DetectorModelName != nil && len(*s.DetectorModelName) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("DetectorModelName", 1))
	}
	if s.MaxResults != nil && *s.MaxResults < 1 {
		invalidParams.Add(request.NewErrParamMinValue("MaxResults", 1))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetDetectorModelName sets the DetectorModelName field's value.
func (s *ListDetectorModelVersionsInput) SetDetectorModelName(v string) *ListDetectorModelVersionsInput {
	s.DetectorModelName = &v
	return s
}

// SetMaxResults sets the MaxResults field's value.
func (s *ListDetectorModelVersionsInput) SetMaxResults(v int64) *ListDetectorModelVersionsInput {
	s.MaxResults = &v
	return s
}

// SetNextToken sets the NextToken field's value.
func (s *ListDetectorModelVersionsInput) SetNextToken(v string) *ListDetectorModelVersionsInput {
	s.NextToken = &v
	return s
}

type ListDetectorModelVersionsOutput struct {
	_ struct{} `type:"structure"`

	// Summary information about the detector model versions.
	DetectorModelVersionSummaries []*DetectorModelVersionSummary `locationName:"detectorModelVersionSummaries" type:"list"`

	// A token to retrieve the next set of results, or null if there are no additional
	// results.
	NextToken *string `locationName:"nextToken" type:"string"`
}

// String returns the string representation
func (s ListDetectorModelVersionsOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s ListDetectorModelVersionsOutput) GoString() string {
	return s.String()
}

// SetDetectorModelVersionSummaries sets the DetectorModelVersionSummaries field's value.
func (s *ListDetectorModelVersionsOutput) SetDetectorModelVersionSummaries(v []*DetectorModelVersionSummary) *ListDetectorModelVersionsOutput {
	s.DetectorModelVersionSummaries = v
	return s
}

// SetNextToken sets the NextToken field's value.
func (s *ListDetectorModelVersionsOutput) SetNextToken(v string) *ListDetectorModelVersionsOutput {
	s.NextToken = &v
	return s
}

type ListDetectorModelsInput struct {
	_ struct{} `type:"structure"`

	// The maximum number of results to return at one time.
	MaxResults *int64 `location:"querystring" locationName:"maxResults" min:"1" type:"integer"`

	// The token for the next set of results.
	NextToken *string `location:"querystring" locationName:"nextToken" type:"string"`
}

// String returns the string representation
func (s ListDetectorModelsInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s ListDetectorModelsInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *ListDetectorModelsInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "ListDetectorModelsInput"}
	if s.MaxResults != nil && *s.MaxResults < 1 {
		invalidParams.Add(request.NewErrParamMinValue("MaxResults", 1))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetMaxResults sets the MaxResults field's value.
func (s *ListDetectorModelsInput) SetMaxResults(v int64) *ListDetectorModelsInput {
	s.MaxResults = &v
	return s
}

// SetNextToken sets the NextToken field's value.
func (s *ListDetectorModelsInput) SetNextToken(v string) *ListDetectorModelsInput {
	s.NextToken = &v
	return s
}

type ListDetectorModelsOutput struct {
	_ struct{} `type:"structure"`

	// Summary information about the detector models.
	DetectorModelSummaries []*DetectorModelSummary `locationName:"detectorModelSummaries" type:"list"`

	// A token to retrieve the next set of results, or null if there are no additional
	// results.
	NextToken *string `locationName:"nextToken" type:"string"`
}

// String returns the string representation
func (s ListDetectorModelsOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s ListDetectorModelsOutput) GoString() string {
	return s.String()
}

// SetDetectorModelSummaries sets the DetectorModelSummaries field's value.
func (s *ListDetectorModelsOutput) SetDetectorModelSummaries(v []*DetectorModelSummary) *ListDetectorModelsOutput {
	s.DetectorModelSummaries = v
	return s
}

// SetNextToken sets the NextToken field's value.
func (s *ListDetectorModelsOutput) SetNextToken(v string) *ListDetectorModelsOutput {
	s.NextToken = &v
	return s
}

type ListInputsInput struct {
	_ struct{} `type:"structure"`

	// The maximum number of results to return at one time.
	MaxResults *int64 `location:"querystring" locationName:"maxResults" min:"1" type:"integer"`

	// The token for the next set of results.
	NextToken *string `location:"querystring" locationName:"nextToken" type:"string"`
}

// String returns the string representation
func (s ListInputsInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s ListInputsInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *ListInputsInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "ListInputsInput"}
	if s.MaxResults != nil && *s.MaxResults < 1 {
		invalidParams.Add(request.NewErrParamMinValue("MaxResults", 1))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetMaxResults sets the MaxResults field's value.
func (s *ListInputsInput) SetMaxResults(v int64) *ListInputsInput {
	s.MaxResults = &v
	return s
}

// SetNextToken sets the NextToken field's value.
func (s *ListInputsInput) SetNextToken(v string) *ListInputsInput {
	s.NextToken = &v
	return s
}

type ListInputsOutput struct {
	_ struct{} `type:"structure"`

	// Summary information about the inputs.
	InputSummaries []*InputSummary `locationName:"inputSummaries" type:"list"`

	// A token to retrieve the next set of results, or null if there are no additional
	// results.
	NextToken *string `locationName:"nextToken" type:"string"`
}

// String returns the string representation
func (s ListInputsOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s ListInputsOutput) GoString() string {
	return s.String()
}

// SetInputSummaries sets the InputSummaries field's value.
func (s *ListInputsOutput) SetInputSummaries(v []*InputSummary) *ListInputsOutput {
	s.InputSummaries = v
	return s
}

// SetNextToken sets the NextToken field's value.
func (s *ListInputsOutput) SetNextToken(v string) *ListInputsOutput {
	s.NextToken = &v
	return s
}

type ListTagsForResourceInput struct {
	_ struct{} `type:"structure"`

	// The ARN of the resource.
	//
	// ResourceArn is a required field
	ResourceArn *string `location:"querystring" locationName:"resourceArn" min:"1" type:"string" required:"true"`
}

// String returns the string representation
func (s ListTagsForResourceInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s ListTagsForResourceInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *ListTagsForResourceInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "ListTagsForResourceInput"}
	if s.ResourceArn == nil {
		invalidParams.Add(request.NewErrParamRequired("ResourceArn"))
	}
	if s.ResourceArn != nil && len(*s.ResourceArn) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("ResourceArn", 1))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetResourceArn sets the ResourceArn field's value.
func (s *ListTagsForResourceInput) SetResourceArn(v string) *ListTagsForResourceInput {
	s.ResourceArn = &v
	return s
}

type ListTagsForResourceOutput struct {
	_ struct{} `type:"structure"`

	// The list of tags assigned to the resource.
	Tags []*Tag `locationName:"tags" type:"list"`
}

// String returns the string representation
func (s ListTagsForResourceOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s ListTagsForResourceOutput) GoString() string {
	return s.String()
}

// SetTags sets the Tags field's value.
func (s *ListTagsForResourceOutput) SetTags(v []*Tag) *ListTagsForResourceOutput {
	s.Tags = v
	return s
}

// The values of the AWS IoT Events logging options.
type LoggingOptions struct {
	_ struct{} `type:"structure"`

	// Information that identifies those detector models and their detectors (instances)
	// for which the logging level is given.
	DetectorDebugOptions []*DetectorDebugOption `locationName:"detectorDebugOptions" min:"1" type:"list"`

	// If TRUE, logging is enabled for AWS IoT Events.
	//
	// Enabled is a required field
	Enabled *bool `locationName:"enabled" type:"boolean" required:"true"`

	// The logging level.
	//
	// Level is a required field
	Level *string `locationName:"level" type:"string" required:"true" enum:"LoggingLevel"`

	// The ARN of the role that grants permission to AWS IoT Events to perform logging.
	//
	// RoleArn is a required field
	RoleArn *string `locationName:"roleArn" min:"1" type:"string" required:"true"`
}

// String returns the string representation
func (s LoggingOptions) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s LoggingOptions) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *LoggingOptions) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "LoggingOptions"}
	if s.DetectorDebugOptions != nil && len(s.DetectorDebugOptions) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("DetectorDebugOptions", 1))
	}
	if s.Enabled == nil {
		invalidParams.Add(request.NewErrParamRequired("Enabled"))
	}
	if s.Level == nil {
		invalidParams.Add(request.NewErrParamRequired("Level"))
	}
	if s.RoleArn == nil {
		invalidParams.Add(request.NewErrParamRequired("RoleArn"))
	}
	if s.RoleArn != nil && len(*s.RoleArn) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("RoleArn", 1))
	}
	if s.DetectorDebugOptions != nil {
		for i, v := range s.DetectorDebugOptions {
			if v == nil {
				continue
			}
			if err := v.Validate(); err != nil {
				invalidParams.AddNested(fmt.Sprintf("%s[%v]", "DetectorDebugOptions", i), err.(request.ErrInvalidParams))
			}
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetDetectorDebugOptions sets the DetectorDebugOptions field's value.
func (s *LoggingOptions) SetDetectorDebugOptions(v []*DetectorDebugOption) *LoggingOptions {
	s.DetectorDebugOptions = v
	return s
}

// SetEnabled sets the Enabled field's value.
func (s *LoggingOptions) SetEnabled(v bool) *LoggingOptions {
	s.Enabled = &v
	return s
}

// SetLevel sets the Level field's value.
func (s *LoggingOptions) SetLevel(v string) *LoggingOptions {
	s.Level = &v
	return s
}

// SetRoleArn sets the RoleArn field's value.
func (s *LoggingOptions) SetRoleArn(v string) *LoggingOptions {
	s.RoleArn = &v
	return s
}

// When entering this state, perform these actions if the condition is TRUE.
type OnEnterLifecycle struct {
	_ struct{} `type:"structure"`

	// Specifies the actions that are performed when the state is entered and the
	// condition is TRUE.
	Events []*Event `locationName:"events" type:"list"`
}

// String returns the string representation
func (s OnEnterLifecycle) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s OnEnterLifecycle) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *OnEnterLifecycle) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "OnEnterLifecycle"}
	if s.Events != nil {
		for i, v := range s.Events {
			if v == nil {
				continue
			}
			if err := v.Validate(); err != nil {
				invalidParams.AddNested(fmt.Sprintf("%s[%v]", "Events", i), err.(request.ErrInvalidParams))
			}
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetEvents sets the Events field's value.
func (s *OnEnterLifecycle) SetEvents(v []*Event) *OnEnterLifecycle {
	s.Events = v
	return s
}

// When exiting this state, perform these actions if the specified condition
// is TRUE.
type OnExitLifecycle struct {
	_ struct{} `type:"structure"`

	// Specifies the actions that are performed when the state is exited and the
	// condition is TRUE.
	Events []*Event `locationName:"events" type:"list"`
}

// String returns the string representation
func (s OnExitLifecycle) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s OnExitLifecycle) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *OnExitLifecycle) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "OnExitLifecycle"}
	if s.Events != nil {
		for i, v := range s.Events {
			if v == nil {
				continue
			}
			if err := v.Validate(); err != nil {
				invalidParams.AddNested(fmt.Sprintf("%s[%v]", "Events", i), err.(request.ErrInvalidParams))
			}
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetEvents sets the Events field's value.
func (s *OnExitLifecycle) SetEvents(v []*Event) *OnExitLifecycle {
	s.Events = v
	return s
}

// Specifies the actions performed when the condition evaluates to TRUE.
type OnInputLifecycle struct {
	_ struct{} `type:"structure"`

	// Specifies the actions performed when the condition evaluates to TRUE.
	Events []*Event `locationName:"events" type:"list"`

	// Specifies the actions performed, and the next state entered, when a condition
	// evaluates to TRUE.
	TransitionEvents []*TransitionEvent `locationName:"transitionEvents" type:"list"`
}

// String returns the string representation
func (s OnInputLifecycle) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s OnInputLifecycle) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *OnInputLifecycle) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "OnInputLifecycle"}
	if s.Events != nil {
		for i, v := range s.Events {
			if v == nil {
				continue
			}
			if err := v.Validate(); err != nil {
				invalidParams.AddNested(fmt.Sprintf("%s[%v]", "Events", i), err.(request.ErrInvalidParams))
			}
		}
	}
	if s.TransitionEvents != nil {
		for i, v := range s.TransitionEvents {
			if v == nil {
				continue
			}
			if err := v.Validate(); err != nil {
				invalidParams.AddNested(fmt.Sprintf("%s[%v]", "TransitionEvents", i), err.(request.ErrInvalidParams))
			}
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetEvents sets the Events field's value.
func (s *OnInputLifecycle) SetEvents(v []*Event) *OnInputLifecycle {
	s.Events = v
	return s
}

// SetTransitionEvents sets the TransitionEvents field's value.
func (s *OnInputLifecycle) SetTransitionEvents(v []*TransitionEvent) *OnInputLifecycle {
	s.TransitionEvents = v
	return s
}

// Information needed to configure the payload.
//
// By default, AWS IoT Events generates a standard payload in JSON for any action.
// This action payload contains all attribute-value pairs that have the information
// about the detector model instance and the event triggered the action. To
// configure the action payload, you can use contentExpression.
type Payload struct {
	_ struct{} `type:"structure"`

	// The content of the payload. You can use a string expression that includes
	// quoted strings ('<string>'), variables ($variable.<variable-name>), input
	// values ($input.<input-name>.<path-to-datum>), string concatenations, and
	// quoted strings that contain ${} as the content. The recommended maximum size
	// of a content expression is 1 KB.
	//
	// ContentExpression is a required field
	ContentExpression *string `locationName:"contentExpression" min:"1" type:"string" required:"true"`

	// The value of the payload type can be either STRING or JSON.
	//
	// Type is a required field
	Type *string `locationName:"type" type:"string" required:"true" enum:"PayloadType"`
}

// String returns the string representation
func (s Payload) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s Payload) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *Payload) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "Payload"}
	if s.ContentExpression == nil {
		invalidParams.Add(request.NewErrParamRequired("ContentExpression"))
	}
	if s.ContentExpression != nil && len(*s.ContentExpression) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("ContentExpression", 1))
	}
	if s.Type == nil {
		invalidParams.Add(request.NewErrParamRequired("Type"))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetContentExpression sets the ContentExpression field's value.
func (s *Payload) SetContentExpression(v string) *Payload {
	s.ContentExpression = &v
	return s
}

// SetType sets the Type field's value.
func (s *Payload) SetType(v string) *Payload {
	s.Type = &v
	return s
}

type PutLoggingOptionsInput struct {
	_ struct{} `type:"structure"`

	// The new values of the AWS IoT Events logging options.
	//
	// LoggingOptions is a required field
	LoggingOptions *LoggingOptions `locationName:"loggingOptions" type:"structure" required:"true"`
}

// String returns the string representation
func (s PutLoggingOptionsInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s PutLoggingOptionsInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *PutLoggingOptionsInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "PutLoggingOptionsInput"}
	if s.LoggingOptions == nil {
		invalidParams.Add(request.NewErrParamRequired("LoggingOptions"))
	}
	if s.LoggingOptions != nil {
		if err := s.LoggingOptions.Validate(); err != nil {
			invalidParams.AddNested("LoggingOptions", err.(request.ErrInvalidParams))
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetLoggingOptions sets the LoggingOptions field's value.
func (s *PutLoggingOptionsInput) SetLoggingOptions(v *LoggingOptions) *PutLoggingOptionsInput {
	s.LoggingOptions = v
	return s
}

type PutLoggingOptionsOutput struct {
	_ struct{} `type:"structure"`
}

// String returns the string representation
func (s PutLoggingOptionsOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s PutLoggingOptionsOutput) GoString() string {
	return s.String()
}

// Information required to reset the timer. The timer is reset to the previously
// evaluated result of the duration. The duration expression isn't reevaluated
// when you reset the timer.
type ResetTimerAction struct {
	_ struct{} `type:"structure"`

	// The name of the timer to reset.
	//
	// TimerName is a required field
	TimerName *string `locationName:"timerName" min:"1" type:"string" required:"true"`
}

// String returns the string representation
func (s ResetTimerAction) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s ResetTimerAction) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *ResetTimerAction) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "ResetTimerAction"}
	if s.TimerName == nil {
		invalidParams.Add(request.NewErrParamRequired("TimerName"))
	}
	if s.TimerName != nil && len(*s.TimerName) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("TimerName", 1))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetTimerName sets the TimerName field's value.
func (s *ResetTimerAction) SetTimerName(v string) *ResetTimerAction {
	s.TimerName = &v
	return s
}

// The resource already exists.
type ResourceAlreadyExistsException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// The message for the exception.
	Message_ *string `locationName:"message" type:"string"`

	// The ARN of the resource.
	ResourceArn *string `locationName:"resourceArn" type:"string"`

	// The ID of the resource.
	ResourceId *string `locationName:"resourceId" type:"string"`
}

// String returns the string representation
func (s ResourceAlreadyExistsException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s ResourceAlreadyExistsException) GoString() string {
	return s.String()
}

func newErrorResourceAlreadyExistsException(v protocol.ResponseMetadata) error {
	return &ResourceAlreadyExistsException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *ResourceAlreadyExistsException) Code() string {
	return "ResourceAlreadyExistsException"
}

// Message returns the exception's message.
func (s *ResourceAlreadyExistsException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *ResourceAlreadyExistsException) OrigErr() error {
	return nil
}

func (s *ResourceAlreadyExistsException) Error() string {
	return fmt.Sprintf("%s: %s\n%s", s.Code(), s.Message(), s.String())
}

// Status code returns the HTTP status code for the request's response error.
func (s *ResourceAlreadyExistsException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *ResourceAlreadyExistsException) RequestID() string {
	return s.RespMetadata.RequestID
}

// The resource is in use.
type ResourceInUseException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// The message for the exception.
	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation
func (s ResourceInUseException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s ResourceInUseException) GoString() string {
	return s.String()
}

func newErrorResourceInUseException(v protocol.ResponseMetadata) error {
	return &ResourceInUseException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *ResourceInUseException) Code() string {
	return "ResourceInUseException"
}

// Message returns the exception's message.
func (s *ResourceInUseException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *ResourceInUseException) OrigErr() error {
	return nil
}

func (s *ResourceInUseException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *ResourceInUseException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *ResourceInUseException) RequestID() string {
	return s.RespMetadata.RequestID
}

// The resource was not found.
type ResourceNotFoundException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// The message for the exception.
	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation
func (s ResourceNotFoundException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s ResourceNotFoundException) GoString() string {
	return s.String()
}

func newErrorResourceNotFoundException(v protocol.ResponseMetadata) error {
	return &ResourceNotFoundException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *ResourceNotFoundException) Code() string {
	return "ResourceNotFoundException"
}

// Message returns the exception's message.
func (s *ResourceNotFoundException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *ResourceNotFoundException) OrigErr() error {
	return nil
}

func (s *ResourceNotFoundException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *ResourceNotFoundException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *ResourceNotFoundException) RequestID() string {
	return s.RespMetadata.RequestID
}

// Information required to publish the Amazon SNS message.
type SNSTopicPublishAction struct {
	_ struct{} `type:"structure"`

	// You can configure the action payload when you send a message as an Amazon
	// SNS push notification.
	Payload *Payload `locationName:"payload" type:"structure"`

	// The ARN of the Amazon SNS target where the message is sent.
	//
	// TargetArn is a required field
	TargetArn *string `locationName:"targetArn" min:"1" type:"string" required:"true"`
}

// String returns the string representation
func (s SNSTopicPublishAction) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s SNSTopicPublishAction) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *SNSTopicPublishAction) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "SNSTopicPublishAction"}
	if s.TargetArn == nil {
		invalidParams.Add(request.NewErrParamRequired("TargetArn"))
	}
	if s.TargetArn != nil && len(*s.TargetArn) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("TargetArn", 1))
	}
	if s.Payload != nil {
		if err := s.Payload.Validate(); err != nil {
			invalidParams.AddNested("Payload", err.(request.ErrInvalidParams))
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetPayload sets the Payload field's value.
func (s *SNSTopicPublishAction) SetPayload(v *Payload) *SNSTopicPublishAction {
	s.Payload = v
	return s
}

// SetTargetArn sets the TargetArn field's value.
func (s *SNSTopicPublishAction) SetTargetArn(v string) *SNSTopicPublishAction {
	s.TargetArn = &v
	return s
}

// The service is currently unavailable.
type ServiceUnavailableException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// The message for the exception.
	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation
func (s ServiceUnavailableException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s ServiceUnavailableException) GoString() string {
	return s.String()
}

func newErrorServiceUnavailableException(v protocol.ResponseMetadata) error {
	return &ServiceUnavailableException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *ServiceUnavailableException) Code() string {
	return "ServiceUnavailableException"
}

// Message returns the exception's message.
func (s *ServiceUnavailableException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *ServiceUnavailableException) OrigErr() error {
	return nil
}

func (s *ServiceUnavailableException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *ServiceUnavailableException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *ServiceUnavailableException) RequestID() string {
	return s.RespMetadata.RequestID
}

// Information needed to set the timer.
type SetTimerAction struct {
	_ struct{} `type:"structure"`

	// The duration of the timer, in seconds. You can use a string expression that
	// includes numbers, variables ($variable.<variable-name>), and input values
	// ($input.<input-name>.<path-to-datum>) as the duration. The range of the duration
	// is 1-31622400 seconds. To ensure accuracy, the minimum duration is 60 seconds.
	// The evaluated result of the duration is rounded down to the nearest whole
	// number.
	DurationExpression *string `locationName:"durationExpression" min:"1" type:"string"`

	// The number of seconds until the timer expires. The minimum value is 60 seconds
	// to ensure accuracy. The maximum value is 31622400 seconds.
	//
	// Deprecated: seconds is deprecated. You can use durationExpression for SetTimerAction. The value of seconds can be used as a string expression for durationExpression.
	Seconds *int64 `locationName:"seconds" min:"1" deprecated:"true" type:"integer"`

	// The name of the timer.
	//
	// TimerName is a required field
	TimerName *string `locationName:"timerName" min:"1" type:"string" required:"true"`
}

// String returns the string representation
func (s SetTimerAction) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s SetTimerAction) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *SetTimerAction) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "SetTimerAction"}
	if s.DurationExpression != nil && len(*s.DurationExpression) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("DurationExpression", 1))
	}
	if s.Seconds != nil && *s.Seconds < 1 {
		invalidParams.Add(request.NewErrParamMinValue("Seconds", 1))
	}
	if s.TimerName == nil {
		invalidParams.Add(request.NewErrParamRequired("TimerName"))
	}
	if s.TimerName != nil && len(*s.TimerName) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("TimerName", 1))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetDurationExpression sets the DurationExpression field's value.
func (s *SetTimerAction) SetDurationExpression(v string) *SetTimerAction {
	s.DurationExpression = &v
	return s
}

// SetSeconds sets the Seconds field's value.
func (s *SetTimerAction) SetSeconds(v int64) *SetTimerAction {
	s.Seconds = &v
	return s
}

// SetTimerName sets the TimerName field's value.
func (s *SetTimerAction) SetTimerName(v string) *SetTimerAction {
	s.TimerName = &v
	return s
}

// Information about the variable and its new value.
type SetVariableAction struct {
	_ struct{} `type:"structure"`

	// The new value of the variable.
	//
	// Value is a required field
	Value *string `locationName:"value" min:"1" type:"string" required:"true"`

	// The name of the variable.
	//
	// VariableName is a required field
	VariableName *string `locationName:"variableName" min:"1" type:"string" required:"true"`
}

// String returns the string representation
func (s SetVariableAction) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s SetVariableAction) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *SetVariableAction) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "SetVariableAction"}
	if s.Value == nil {
		invalidParams.Add(request.NewErrParamRequired("Value"))
	}
	if s.Value != nil && len(*s.Value) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("Value", 1))
	}
	if s.VariableName == nil {
		invalidParams.Add(request.NewErrParamRequired("VariableName"))
	}
	if s.VariableName != nil && len(*s.VariableName) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("VariableName", 1))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetValue sets the Value field's value.
func (s *SetVariableAction) SetValue(v string) *SetVariableAction {
	s.Value = &v
	return s
}

// SetVariableName sets the VariableName field's value.
func (s *SetVariableAction) SetVariableName(v string) *SetVariableAction {
	s.VariableName = &v
	return s
}

// Sends information about the detector model instance and the event that triggered
// the action to an Amazon SQS queue.
type SqsAction struct {
	_ struct{} `type:"structure"`

	// You can configure the action payload when you send a message to an Amazon
	// SQS queue.
	Payload *Payload `locationName:"payload" type:"structure"`

	// The URL of the SQS queue where the data is written.
	//
	// QueueUrl is a required field
	QueueUrl *string `locationName:"queueUrl" type:"string" required:"true"`

	// Set this to TRUE if you want the data to be base-64 encoded before it is
	// written to the queue. Otherwise, set this to FALSE.
	UseBase64 *bool `locationName:"useBase64" type:"boolean"`
}

// String returns the string representation
func (s SqsAction) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s SqsAction) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *SqsAction) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "SqsAction"}
	if s.QueueUrl == nil {
		invalidParams.Add(request.NewErrParamRequired("QueueUrl"))
	}
	if s.Payload != nil {
		if err := s.Payload.Validate(); err != nil {
			invalidParams.AddNested("Payload", err.(request.ErrInvalidParams))
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetPayload sets the Payload field's value.
func (s *SqsAction) SetPayload(v *Payload) *SqsAction {
	s.Payload = v
	return s
}

// SetQueueUrl sets the QueueUrl field's value.
func (s *SqsAction) SetQueueUrl(v string) *SqsAction {
	s.QueueUrl = &v
	return s
}

// SetUseBase64 sets the UseBase64 field's value.
func (s *SqsAction) SetUseBase64(v bool) *SqsAction {
	s.UseBase64 = &v
	return s
}

// Information that defines a state of a detector.
type State struct {
	_ struct{} `type:"structure"`

	// When entering this state, perform these actions if the condition is TRUE.
	OnEnter *OnEnterLifecycle `locationName:"onEnter" type:"structure"`

	// When exiting this state, perform these actions if the specified condition
	// is TRUE.
	OnExit *OnExitLifecycle `locationName:"onExit" type:"structure"`

	// When an input is received and the condition is TRUE, perform the specified
	// actions.
	OnInput *OnInputLifecycle `locationName:"onInput" type:"structure"`

	// The name of the state.
	//
	// StateName is a required field
	StateName *string `locationName:"stateName" min:"1" type:"string" required:"true"`
}

// String returns the string representation
func (s State) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s State) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *State) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "State"}
	if s.StateName == nil {
		invalidParams.Add(request.NewErrParamRequired("StateName"))
	}
	if s.StateName != nil && len(*s.StateName) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("StateName", 1))
	}
	if s.OnEnter != nil {
		if err := s.OnEnter.Validate(); err != nil {
			invalidParams.AddNested("OnEnter", err.(request.ErrInvalidParams))
		}
	}
	if s.OnExit != nil {
		if err := s.OnExit.Validate(); err != nil {
			invalidParams.AddNested("OnExit", err.(request.ErrInvalidParams))
		}
	}
	if s.OnInput != nil {
		if err := s.OnInput.Validate(); err != nil {
			invalidParams.AddNested("OnInput", err.(request.ErrInvalidParams))
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetOnEnter sets the OnEnter field's value.
func (s *State) SetOnEnter(v *OnEnterLifecycle) *State {
	s.OnEnter = v
	return s
}

// SetOnExit sets the OnExit field's value.
func (s *State) SetOnExit(v *OnExitLifecycle) *State {
	s.OnExit = v
	return s
}

// SetOnInput sets the OnInput field's value.
func (s *State) SetOnInput(v *OnInputLifecycle) *State {
	s.OnInput = v
	return s
}

// SetStateName sets the StateName field's value.
func (s *State) SetStateName(v string) *State {
	s.StateName = &v
	return s
}

// Metadata that can be used to manage the resource.
type Tag struct {
	_ struct{} `type:"structure"`

	// The tag's key.
	//
	// Key is a required field
	Key *string `locationName:"key" min:"1" type:"string" required:"true"`

	// The tag's value.
	//
	// Value is a required field
	Value *string `locationName:"value" type:"string" required:"true"`
}

// String returns the string representation
func (s Tag) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s Tag) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *Tag) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "Tag"}
	if s.Key == nil {
		invalidParams.Add(request.NewErrParamRequired("Key"))
	}
	if s.Key != nil && len(*s.Key) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("Key", 1))
	}
	if s.Value == nil {
		invalidParams.Add(request.NewErrParamRequired("Value"))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetKey sets the Key field's value.
func (s *Tag) SetKey(v string) *Tag {
	s.Key = &v
	return s
}

// SetValue sets the Value field's value.
func (s *Tag) SetValue(v string) *Tag {
	s.Value = &v
	return s
}

type TagResourceInput struct {
	_ struct{} `type:"structure"`

	// The ARN of the resource.
	//
	// ResourceArn is a required field
	ResourceArn *string `location:"querystring" locationName:"resourceArn" min:"1" type:"string" required:"true"`

	// The new or modified tags for the resource.
	//
	// Tags is a required field
	Tags []*Tag `locationName:"tags" type:"list" required:"true"`
}

// String returns the string representation
func (s TagResourceInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s TagResourceInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *TagResourceInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "TagResourceInput"}
	if s.ResourceArn == nil {
		invalidParams.Add(request.NewErrParamRequired("ResourceArn"))
	}
	if s.ResourceArn != nil && len(*s.ResourceArn) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("ResourceArn", 1))
	}
	if s.Tags == nil {
		invalidParams.Add(request.NewErrParamRequired("Tags"))
	}
	if s.Tags != nil {
		for i, v := range s.Tags {
			if v == nil {
				continue
			}
			if err := v.Validate(); err != nil {
				invalidParams.AddNested(fmt.Sprintf("%s[%v]", "Tags", i), err.(request.ErrInvalidParams))
			}
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetResourceArn sets the ResourceArn field's value.
func (s *TagResourceInput) SetResourceArn(v string) *TagResourceInput {
	s.ResourceArn = &v
	return s
}

// SetTags sets the Tags field's value.
func (s *TagResourceInput) SetTags(v []*Tag) *TagResourceInput {
	s.Tags = v
	return s
}

type TagResourceOutput struct {
	_ struct{} `type:"structure"`
}

// String returns the string representation
func (s TagResourceOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s TagResourceOutput) GoString() string {
	return s.String()
}

// The request could not be completed due to throttling.
type ThrottlingException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// The message for the exception.
	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation
func (s ThrottlingException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s ThrottlingException) GoString() string {
	return s.String()
}

func newErrorThrottlingException(v protocol.ResponseMetadata) error {
	return &ThrottlingException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *ThrottlingException) Code() string {
	return "ThrottlingException"
}

// Message returns the exception's message.
func (s *ThrottlingException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *ThrottlingException) OrigErr() error {
	return nil
}

func (s *ThrottlingException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *ThrottlingException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *ThrottlingException) RequestID() string {
	return s.RespMetadata.RequestID
}

// Specifies the actions performed and the next state entered when a condition
// evaluates to TRUE.
type TransitionEvent struct {
	_ struct{} `type:"structure"`

	// The actions to be performed.
	Actions []*ActionData `locationName:"actions" type:"list"`

	// Required. A Boolean expression that when TRUE causes the actions to be performed
	// and the nextState to be entered.
	//
	// Condition is a required field
	Condition *string `locationName:"condition" type:"string" required:"true"`

	// The name of the transition event.
	//
	// EventName is a required field
	EventName *string `locationName:"eventName" type:"string" required:"true"`

	// The next state to enter.
	//
	// NextState is a required field
	NextState *string `locationName:"nextState" min:"1" type:"string" required:"true"`
}

// String returns the string representation
func (s TransitionEvent) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s TransitionEvent) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *TransitionEvent) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "TransitionEvent"}
	if s.Condition == nil {
		invalidParams.Add(request.NewErrParamRequired("Condition"))
	}
	if s.EventName == nil {
		invalidParams.Add(request.NewErrParamRequired("EventName"))
	}
	if s.NextState == nil {
		invalidParams.Add(request.NewErrParamRequired("NextState"))
	}
	if s.NextState != nil && len(*s.NextState) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("NextState", 1))
	}
	if s.Actions != nil {
		for i, v := range s.Actions {
			if v == nil {
				continue
			}
			if err := v.Validate(); err != nil {
				invalidParams.AddNested(fmt.Sprintf("%s[%v]", "Actions", i), err.(request.ErrInvalidParams))
			}
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetActions sets the Actions field's value.
func (s *TransitionEvent) SetActions(v []*ActionData) *TransitionEvent {
	s.Actions = v
	return s
}

// SetCondition sets the Condition field's value.
func (s *TransitionEvent) SetCondition(v string) *TransitionEvent {
	s.Condition = &v
	return s
}

// SetEventName sets the EventName field's value.
func (s *TransitionEvent) SetEventName(v string) *TransitionEvent {
	s.EventName = &v
	return s
}

// SetNextState sets the NextState field's value.
func (s *TransitionEvent) SetNextState(v string) *TransitionEvent {
	s.NextState = &v
	return s
}

// The requested operation is not supported.
type UnsupportedOperationException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// The message for the exception.
	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation
func (s UnsupportedOperationException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s UnsupportedOperationException) GoString() string {
	return s.String()
}

func newErrorUnsupportedOperationException(v protocol.ResponseMetadata) error {
	return &UnsupportedOperationException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *UnsupportedOperationException) Code() string {
	return "UnsupportedOperationException"
}

// Message returns the exception's message.
func (s *UnsupportedOperationException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *UnsupportedOperationException) OrigErr() error {
	return nil
}

func (s *UnsupportedOperationException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *UnsupportedOperationException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *UnsupportedOperationException) RequestID() string {
	return s.RespMetadata.RequestID
}

type UntagResourceInput struct {
	_ struct{} `type:"structure"`

	// The ARN of the resource.
	//
	// ResourceArn is a required field
	ResourceArn *string `location:"querystring" locationName:"resourceArn" min:"1" type:"string" required:"true"`

	// A list of the keys of the tags to be removed from the resource.
	//
	// TagKeys is a required field
	TagKeys []*string `location:"querystring" locationName:"tagKeys" type:"list" required:"true"`
}

// String returns the string representation
func (s UntagResourceInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s UntagResourceInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *UntagResourceInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "UntagResourceInput"}
	if s.ResourceArn == nil {
		invalidParams.Add(request.NewErrParamRequired("ResourceArn"))
	}
	if s.ResourceArn != nil && len(*s.ResourceArn) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("ResourceArn", 1))
	}
	if s.TagKeys == nil {
		invalidParams.Add(request.NewErrParamRequired("TagKeys"))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetResourceArn sets the ResourceArn field's value.
func (s *UntagResourceInput) SetResourceArn(v string) *UntagResourceInput {
	s.ResourceArn = &v
	return s
}

// SetTagKeys sets the TagKeys field's value.
func (s *UntagResourceInput) SetTagKeys(v []*string) *UntagResourceInput {
	s.TagKeys = v
	return s
}

type UntagResourceOutput struct {
	_ struct{} `type:"structure"`
}

// String returns the string representation
func (s UntagResourceOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s UntagResourceOutput) GoString() string {
	return s.String()
}

type UpdateDetectorModelInput struct {
	_ struct{} `type:"structure"`

	// Information that defines how a detector operates.
	//
	// DetectorModelDefinition is a required field
	DetectorModelDefinition *DetectorModelDefinition `locationName:"detectorModelDefinition" type:"structure" required:"true"`

	// A brief description of the detector model.
	DetectorModelDescription *string `locationName:"detectorModelDescription" type:"string"`

	// The name of the detector model that is updated.
	//
	// DetectorModelName is a required field
	DetectorModelName *string `location:"uri" locationName:"detectorModelName" min:"1" type:"string" required:"true"`

	// Information about the order in which events are evaluated and how actions
	// are executed.
	EvaluationMethod *string `locationName:"evaluationMethod" type:"string" enum:"EvaluationMethod"`

	// The ARN of the role that grants permission to AWS IoT Events to perform its
	// operations.
	//
	// RoleArn is a required field
	RoleArn *string `locationName:"roleArn" min:"1" type:"string" required:"true"`
}

// String returns the string representation
func (s UpdateDetectorModelInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s UpdateDetectorModelInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *UpdateDetectorModelInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "UpdateDetectorModelInput"}
	if s.DetectorModelDefinition == nil {
		invalidParams.Add(request.NewErrParamRequired("DetectorModelDefinition"))
	}
	if s.DetectorModelName == nil {
		invalidParams.Add(request.NewErrParamRequired("DetectorModelName"))
	}
	if s.DetectorModelName != nil && len(*s.DetectorModelName) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("DetectorModelName", 1))
	}
	if s.RoleArn == nil {
		invalidParams.Add(request.NewErrParamRequired("RoleArn"))
	}
	if s.RoleArn != nil && len(*s.RoleArn) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("RoleArn", 1))
	}
	if s.DetectorModelDefinition != nil {
		if err := s.DetectorModelDefinition.Validate(); err != nil {
			invalidParams.AddNested("DetectorModelDefinition", err.(request.ErrInvalidParams))
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetDetectorModelDefinition sets the DetectorModelDefinition field's value.
func (s *UpdateDetectorModelInput) SetDetectorModelDefinition(v *DetectorModelDefinition) *UpdateDetectorModelInput {
	s.DetectorModelDefinition = v
	return s
}

// SetDetectorModelDescription sets the DetectorModelDescription field's value.
func (s *UpdateDetectorModelInput) SetDetectorModelDescription(v string) *UpdateDetectorModelInput {
	s.DetectorModelDescription = &v
	return s
}

// SetDetectorModelName sets the DetectorModelName field's value.
func (s *UpdateDetectorModelInput) SetDetectorModelName(v string) *UpdateDetectorModelInput {
	s.DetectorModelName = &v
	return s
}

// SetEvaluationMethod sets the EvaluationMethod field's value.
func (s *UpdateDetectorModelInput) SetEvaluationMethod(v string) *UpdateDetectorModelInput {
	s.EvaluationMethod = &v
	return s
}

// SetRoleArn sets the RoleArn field's value.
func (s *UpdateDetectorModelInput) SetRoleArn(v string) *UpdateDetectorModelInput {
	s.RoleArn = &v
	return s
}

type UpdateDetectorModelOutput struct {
	_ struct{} `type:"structure"`

	// Information about how the detector model is configured.
	DetectorModelConfiguration *DetectorModelConfiguration `locationName:"detectorModelConfiguration" type:"structure"`
}

// String returns the string representation
func (s UpdateDetectorModelOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s UpdateDetectorModelOutput) GoString() string {
	return s.String()
}

// SetDetectorModelConfiguration sets the DetectorModelConfiguration field's value.
func (s *UpdateDetectorModelOutput) SetDetectorModelConfiguration(v *DetectorModelConfiguration) *UpdateDetectorModelOutput {
	s.DetectorModelConfiguration = v
	return s
}

type UpdateInputInput struct {
	_ struct{} `type:"structure"`

	// The definition of the input.
	//
	// InputDefinition is a required field
	InputDefinition *InputDefinition `locationName:"inputDefinition" type:"structure" required:"true"`

	// A brief description of the input.
	InputDescription *string `locationName:"inputDescription" type:"string"`

	// The name of the input you want to update.
	//
	// InputName is a required field
	InputName *string `location:"uri" locationName:"inputName" min:"1" type:"string" required:"true"`
}

// String returns the string representation
func (s UpdateInputInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s UpdateInputInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *UpdateInputInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "UpdateInputInput"}
	if s.InputDefinition == nil {
		invalidParams.Add(request.NewErrParamRequired("InputDefinition"))
	}
	if s.InputName == nil {
		invalidParams.Add(request.NewErrParamRequired("InputName"))
	}
	if s.InputName != nil && len(*s.InputName) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("InputName", 1))
	}
	if s.InputDefinition != nil {
		if err := s.InputDefinition.Validate(); err != nil {
			invalidParams.AddNested("InputDefinition", err.(request.ErrInvalidParams))
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetInputDefinition sets the InputDefinition field's value.
func (s *UpdateInputInput) SetInputDefinition(v *InputDefinition) *UpdateInputInput {
	s.InputDefinition = v
	return s
}

// SetInputDescription sets the InputDescription field's value.
func (s *UpdateInputInput) SetInputDescription(v string) *UpdateInputInput {
	s.InputDescription = &v
	return s
}

// SetInputName sets the InputName field's value.
func (s *UpdateInputInput) SetInputName(v string) *UpdateInputInput {
	s.InputName = &v
	return s
}

type UpdateInputOutput struct {
	_ struct{} `type:"structure"`

	// Information about the configuration of the input.
	InputConfiguration *InputConfiguration `locationName:"inputConfiguration" type:"structure"`
}

// String returns the string representation
func (s UpdateInputOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation
func (s UpdateInputOutput) GoString() string {
	return s.String()
}

// SetInputConfiguration sets the InputConfiguration field's value.
func (s *UpdateInputOutput) SetInputConfiguration(v *InputConfiguration) *UpdateInputOutput {
	s.InputConfiguration = v
	return s
}

const (
	// DetectorModelVersionStatusActive is a DetectorModelVersionStatus enum value
	DetectorModelVersionStatusActive = "ACTIVE"

	// DetectorModelVersionStatusActivating is a DetectorModelVersionStatus enum value
	DetectorModelVersionStatusActivating = "ACTIVATING"

	// DetectorModelVersionStatusInactive is a DetectorModelVersionStatus enum value
	DetectorModelVersionStatusInactive = "INACTIVE"

	// DetectorModelVersionStatusDeprecated is a DetectorModelVersionStatus enum value
	DetectorModelVersionStatusDeprecated = "DEPRECATED"

	// DetectorModelVersionStatusDraft is a DetectorModelVersionStatus enum value
	DetectorModelVersionStatusDraft = "DRAFT"

	// DetectorModelVersionStatusPaused is a DetectorModelVersionStatus enum value
	DetectorModelVersionStatusPaused = "PAUSED"

	// DetectorModelVersionStatusFailed is a DetectorModelVersionStatus enum value
	DetectorModelVersionStatusFailed = "FAILED"
)

// DetectorModelVersionStatus_Values returns all elements of the DetectorModelVersionStatus enum
func DetectorModelVersionStatus_Values() []string {
	return []string{
		DetectorModelVersionStatusActive,
		DetectorModelVersionStatusActivating,
		DetectorModelVersionStatusInactive,
		DetectorModelVersionStatusDeprecated,
		DetectorModelVersionStatusDraft,
		DetectorModelVersionStatusPaused,
		DetectorModelVersionStatusFailed,
	}
}

const (
	// EvaluationMethodBatch is a EvaluationMethod enum value
	EvaluationMethodBatch = "BATCH"

	// EvaluationMethodSerial is a EvaluationMethod enum value
	EvaluationMethodSerial = "SERIAL"
)

// EvaluationMethod_Values returns all elements of the EvaluationMethod enum
func EvaluationMethod_Values() []string {
	return []string{
		EvaluationMethodBatch,
		EvaluationMethodSerial,
	}
}

const (
	// InputStatusCreating is a InputStatus enum value
	InputStatusCreating = "CREATING"

	// InputStatusUpdating is a InputStatus enum value
	InputStatusUpdating = "UPDATING"

	// InputStatusActive is a InputStatus enum value
	InputStatusActive = "ACTIVE"

	// InputStatusDeleting is a InputStatus enum value
	InputStatusDeleting = "DELETING"
)

// InputStatus_Values returns all elements of the InputStatus enum
func InputStatus_Values() []string {
	return []string{
		InputStatusCreating,
		InputStatusUpdating,
		InputStatusActive,
		InputStatusDeleting,
	}
}

const (
	// LoggingLevelError is a LoggingLevel enum value
	LoggingLevelError = "ERROR"

	// LoggingLevelInfo is a LoggingLevel enum value
	LoggingLevelInfo = "INFO"

	// LoggingLevelDebug is a LoggingLevel enum value
	LoggingLevelDebug = "DEBUG"
)

// LoggingLevel_Values returns all elements of the LoggingLevel enum
func LoggingLevel_Values() []string {
	return []string{
		LoggingLevelError,
		LoggingLevelInfo,
		LoggingLevelDebug,
	}
}

const (
	// PayloadTypeString is a PayloadType enum value
	PayloadTypeString = "STRING"

	// PayloadTypeJson is a PayloadType enum value
	PayloadTypeJson = "JSON"
)

// PayloadType_Values returns all elements of the PayloadType enum
func PayloadType_Values() []string {
	return []string{
		PayloadTypeString,
		PayloadTypeJson,
	}
}
