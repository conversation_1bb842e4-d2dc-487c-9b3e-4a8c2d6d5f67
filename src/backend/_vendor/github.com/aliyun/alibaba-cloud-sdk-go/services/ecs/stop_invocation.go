package ecs

//Licensed under the Apache License, Version 2.0 (the "License");
//you may not use this file except in compliance with the License.
//You may obtain a copy of the License at
//
//http://www.apache.org/licenses/LICENSE-2.0
//
//Unless required by applicable law or agreed to in writing, software
//distributed under the License is distributed on an "AS IS" BASIS,
//WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//See the License for the specific language governing permissions and
//limitations under the License.
//
// Code generated by Alibaba Cloud SDK Code Generator.
// Changes may cause incorrect behavior and will be lost if the code is regenerated.

import (
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/responses"
)

// StopInvocation invokes the ecs.StopInvocation API synchronously
// api document: https://help.aliyun.com/api/ecs/stopinvocation.html
func (client *Client) StopInvocation(request *StopInvocationRequest) (response *StopInvocationResponse, err error) {
	response = CreateStopInvocationResponse()
	err = client.DoAction(request, response)
	return
}

// StopInvocationWithChan invokes the ecs.StopInvocation API asynchronously
// api document: https://help.aliyun.com/api/ecs/stopinvocation.html
// asynchronous document: https://help.aliyun.com/document_detail/66220.html
func (client *Client) StopInvocationWithChan(request *StopInvocationRequest) (<-chan *StopInvocationResponse, <-chan error) {
	responseChan := make(chan *StopInvocationResponse, 1)
	errChan := make(chan error, 1)
	err := client.AddAsyncTask(func() {
		defer close(responseChan)
		defer close(errChan)
		response, err := client.StopInvocation(request)
		if err != nil {
			errChan <- err
		} else {
			responseChan <- response
		}
	})
	if err != nil {
		errChan <- err
		close(responseChan)
		close(errChan)
	}
	return responseChan, errChan
}

// StopInvocationWithCallback invokes the ecs.StopInvocation API asynchronously
// api document: https://help.aliyun.com/api/ecs/stopinvocation.html
// asynchronous document: https://help.aliyun.com/document_detail/66220.html
func (client *Client) StopInvocationWithCallback(request *StopInvocationRequest, callback func(response *StopInvocationResponse, err error)) <-chan int {
	result := make(chan int, 1)
	err := client.AddAsyncTask(func() {
		var response *StopInvocationResponse
		var err error
		defer close(result)
		response, err = client.StopInvocation(request)
		callback(response, err)
		result <- 1
	})
	if err != nil {
		defer close(result)
		callback(nil, err)
		result <- 0
	}
	return result
}

// StopInvocationRequest is the request struct for api StopInvocation
type StopInvocationRequest struct {
	*requests.RpcRequest
	ResourceOwnerId      requests.Integer `position:"Query" name:"ResourceOwnerId"`
	InvokeId             string           `position:"Query" name:"InvokeId"`
	ResourceOwnerAccount string           `position:"Query" name:"ResourceOwnerAccount"`
	OwnerAccount         string           `position:"Query" name:"OwnerAccount"`
	OwnerId              requests.Integer `position:"Query" name:"OwnerId"`
	InstanceId           *[]string        `position:"Query" name:"InstanceId"  type:"Repeated"`
}

// StopInvocationResponse is the response struct for api StopInvocation
type StopInvocationResponse struct {
	*responses.BaseResponse
	RequestId string `json:"RequestId" xml:"RequestId"`
}

// CreateStopInvocationRequest creates a request to invoke StopInvocation API
func CreateStopInvocationRequest() (request *StopInvocationRequest) {
	request = &StopInvocationRequest{
		RpcRequest: &requests.RpcRequest{},
	}
	request.InitWithApiInfo("Ecs", "2014-05-26", "StopInvocation", "ecs", "openAPI")
	return
}

// CreateStopInvocationResponse creates a response to parse from StopInvocation response
func CreateStopInvocationResponse() (response *StopInvocationResponse) {
	response = &StopInvocationResponse{
		BaseResponse: &responses.BaseResponse{},
	}
	return
}
