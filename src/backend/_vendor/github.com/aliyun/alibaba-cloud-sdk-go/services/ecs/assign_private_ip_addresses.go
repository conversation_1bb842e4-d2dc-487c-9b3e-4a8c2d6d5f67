package ecs

//Licensed under the Apache License, Version 2.0 (the "License");
//you may not use this file except in compliance with the License.
//You may obtain a copy of the License at
//
//http://www.apache.org/licenses/LICENSE-2.0
//
//Unless required by applicable law or agreed to in writing, software
//distributed under the License is distributed on an "AS IS" BASIS,
//WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//See the License for the specific language governing permissions and
//limitations under the License.
//
// Code generated by Alibaba Cloud SDK Code Generator.
// Changes may cause incorrect behavior and will be lost if the code is regenerated.

import (
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/responses"
)

// AssignPrivateIpAddresses invokes the ecs.AssignPrivateIpAddresses API synchronously
// api document: https://help.aliyun.com/api/ecs/assignprivateipaddresses.html
func (client *Client) AssignPrivateIpAddresses(request *AssignPrivateIpAddressesRequest) (response *AssignPrivateIpAddressesResponse, err error) {
	response = CreateAssignPrivateIpAddressesResponse()
	err = client.DoAction(request, response)
	return
}

// AssignPrivateIpAddressesWithChan invokes the ecs.AssignPrivateIpAddresses API asynchronously
// api document: https://help.aliyun.com/api/ecs/assignprivateipaddresses.html
// asynchronous document: https://help.aliyun.com/document_detail/66220.html
func (client *Client) AssignPrivateIpAddressesWithChan(request *AssignPrivateIpAddressesRequest) (<-chan *AssignPrivateIpAddressesResponse, <-chan error) {
	responseChan := make(chan *AssignPrivateIpAddressesResponse, 1)
	errChan := make(chan error, 1)
	err := client.AddAsyncTask(func() {
		defer close(responseChan)
		defer close(errChan)
		response, err := client.AssignPrivateIpAddresses(request)
		if err != nil {
			errChan <- err
		} else {
			responseChan <- response
		}
	})
	if err != nil {
		errChan <- err
		close(responseChan)
		close(errChan)
	}
	return responseChan, errChan
}

// AssignPrivateIpAddressesWithCallback invokes the ecs.AssignPrivateIpAddresses API asynchronously
// api document: https://help.aliyun.com/api/ecs/assignprivateipaddresses.html
// asynchronous document: https://help.aliyun.com/document_detail/66220.html
func (client *Client) AssignPrivateIpAddressesWithCallback(request *AssignPrivateIpAddressesRequest, callback func(response *AssignPrivateIpAddressesResponse, err error)) <-chan int {
	result := make(chan int, 1)
	err := client.AddAsyncTask(func() {
		var response *AssignPrivateIpAddressesResponse
		var err error
		defer close(result)
		response, err = client.AssignPrivateIpAddresses(request)
		callback(response, err)
		result <- 1
	})
	if err != nil {
		defer close(result)
		callback(nil, err)
		result <- 0
	}
	return result
}

// AssignPrivateIpAddressesRequest is the request struct for api AssignPrivateIpAddresses
type AssignPrivateIpAddressesRequest struct {
	*requests.RpcRequest
	ResourceOwnerId                requests.Integer `position:"Query" name:"ResourceOwnerId"`
	SecondaryPrivateIpAddressCount requests.Integer `position:"Query" name:"SecondaryPrivateIpAddressCount"`
	ResourceOwnerAccount           string           `position:"Query" name:"ResourceOwnerAccount"`
	OwnerAccount                   string           `position:"Query" name:"OwnerAccount"`
	OwnerId                        requests.Integer `position:"Query" name:"OwnerId"`
	PrivateIpAddress               *[]string        `position:"Query" name:"PrivateIpAddress"  type:"Repeated"`
	NetworkInterfaceId             string           `position:"Query" name:"NetworkInterfaceId"`
}

// AssignPrivateIpAddressesResponse is the response struct for api AssignPrivateIpAddresses
type AssignPrivateIpAddressesResponse struct {
	*responses.BaseResponse
	RequestId string `json:"RequestId" xml:"RequestId"`
}

// CreateAssignPrivateIpAddressesRequest creates a request to invoke AssignPrivateIpAddresses API
func CreateAssignPrivateIpAddressesRequest() (request *AssignPrivateIpAddressesRequest) {
	request = &AssignPrivateIpAddressesRequest{
		RpcRequest: &requests.RpcRequest{},
	}
	request.InitWithApiInfo("Ecs", "2014-05-26", "AssignPrivateIpAddresses", "ecs", "openAPI")
	return
}

// CreateAssignPrivateIpAddressesResponse creates a response to parse from AssignPrivateIpAddresses response
func CreateAssignPrivateIpAddressesResponse() (response *AssignPrivateIpAddressesResponse) {
	response = &AssignPrivateIpAddressesResponse{
		BaseResponse: &responses.BaseResponse{},
	}
	return
}
