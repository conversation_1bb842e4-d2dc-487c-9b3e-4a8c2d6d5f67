package ecs

//Licensed under the Apache License, Version 2.0 (the "License");
//you may not use this file except in compliance with the License.
//You may obtain a copy of the License at
//
//http://www.apache.org/licenses/LICENSE-2.0
//
//Unless required by applicable law or agreed to in writing, software
//distributed under the License is distributed on an "AS IS" BASIS,
//WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//See the License for the specific language governing permissions and
//limitations under the License.
//
// Code generated by Alibaba Cloud SDK Code Generator.
// Changes may cause incorrect behavior and will be lost if the code is regenerated.

import (
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/responses"
)

// ModifyReservedInstances invokes the ecs.ModifyReservedInstances API synchronously
// api document: https://help.aliyun.com/api/ecs/modifyreservedinstances.html
func (client *Client) ModifyReservedInstances(request *ModifyReservedInstancesRequest) (response *ModifyReservedInstancesResponse, err error) {
	response = CreateModifyReservedInstancesResponse()
	err = client.DoAction(request, response)
	return
}

// ModifyReservedInstancesWithChan invokes the ecs.ModifyReservedInstances API asynchronously
// api document: https://help.aliyun.com/api/ecs/modifyreservedinstances.html
// asynchronous document: https://help.aliyun.com/document_detail/66220.html
func (client *Client) ModifyReservedInstancesWithChan(request *ModifyReservedInstancesRequest) (<-chan *ModifyReservedInstancesResponse, <-chan error) {
	responseChan := make(chan *ModifyReservedInstancesResponse, 1)
	errChan := make(chan error, 1)
	err := client.AddAsyncTask(func() {
		defer close(responseChan)
		defer close(errChan)
		response, err := client.ModifyReservedInstances(request)
		if err != nil {
			errChan <- err
		} else {
			responseChan <- response
		}
	})
	if err != nil {
		errChan <- err
		close(responseChan)
		close(errChan)
	}
	return responseChan, errChan
}

// ModifyReservedInstancesWithCallback invokes the ecs.ModifyReservedInstances API asynchronously
// api document: https://help.aliyun.com/api/ecs/modifyreservedinstances.html
// asynchronous document: https://help.aliyun.com/document_detail/66220.html
func (client *Client) ModifyReservedInstancesWithCallback(request *ModifyReservedInstancesRequest, callback func(response *ModifyReservedInstancesResponse, err error)) <-chan int {
	result := make(chan int, 1)
	err := client.AddAsyncTask(func() {
		var response *ModifyReservedInstancesResponse
		var err error
		defer close(result)
		response, err = client.ModifyReservedInstances(request)
		callback(response, err)
		result <- 1
	})
	if err != nil {
		defer close(result)
		callback(nil, err)
		result <- 0
	}
	return result
}

// ModifyReservedInstancesRequest is the request struct for api ModifyReservedInstances
type ModifyReservedInstancesRequest struct {
	*requests.RpcRequest
	ResourceOwnerId      requests.Integer                        `position:"Query" name:"ResourceOwnerId"`
	Configuration        *[]ModifyReservedInstancesConfiguration `position:"Query" name:"Configuration"  type:"Repeated"`
	ResourceOwnerAccount string                                  `position:"Query" name:"ResourceOwnerAccount"`
	OwnerAccount         string                                  `position:"Query" name:"OwnerAccount"`
	OwnerId              requests.Integer                        `position:"Query" name:"OwnerId"`
	ReservedInstanceId   *[]string                               `position:"Query" name:"ReservedInstanceId"  type:"Repeated"`
}

// ModifyReservedInstancesConfiguration is a repeated param struct in ModifyReservedInstancesRequest
type ModifyReservedInstancesConfiguration struct {
	ZoneId               string `name:"ZoneId"`
	ReservedInstanceName string `name:"ReservedInstanceName"`
	InstanceType         string `name:"InstanceType"`
	Scope                string `name:"Scope"`
	InstanceAmount       string `name:"InstanceAmount"`
}

// ModifyReservedInstancesResponse is the response struct for api ModifyReservedInstances
type ModifyReservedInstancesResponse struct {
	*responses.BaseResponse
	RequestId              string                                          `json:"RequestId" xml:"RequestId"`
	ReservedInstanceIdSets ReservedInstanceIdSetsInModifyReservedInstances `json:"ReservedInstanceIdSets" xml:"ReservedInstanceIdSets"`
}

// CreateModifyReservedInstancesRequest creates a request to invoke ModifyReservedInstances API
func CreateModifyReservedInstancesRequest() (request *ModifyReservedInstancesRequest) {
	request = &ModifyReservedInstancesRequest{
		RpcRequest: &requests.RpcRequest{},
	}
	request.InitWithApiInfo("Ecs", "2014-05-26", "ModifyReservedInstances", "ecs", "openAPI")
	return
}

// CreateModifyReservedInstancesResponse creates a response to parse from ModifyReservedInstances response
func CreateModifyReservedInstancesResponse() (response *ModifyReservedInstancesResponse) {
	response = &ModifyReservedInstancesResponse{
		BaseResponse: &responses.BaseResponse{},
	}
	return
}
