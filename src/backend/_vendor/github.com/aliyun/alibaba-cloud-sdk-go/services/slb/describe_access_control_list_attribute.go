package slb

//Licensed under the Apache License, Version 2.0 (the "License");
//you may not use this file except in compliance with the License.
//You may obtain a copy of the License at
//
//http://www.apache.org/licenses/LICENSE-2.0
//
//Unless required by applicable law or agreed to in writing, software
//distributed under the License is distributed on an "AS IS" BASIS,
//WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//See the License for the specific language governing permissions and
//limitations under the License.
//
// Code generated by Alibaba Cloud SDK Code Generator.
// Changes may cause incorrect behavior and will be lost if the code is regenerated.

import (
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/responses"
)

// DescribeAccessControlListAttribute invokes the slb.DescribeAccessControlListAttribute API synchronously
// api document: https://help.aliyun.com/api/slb/describeaccesscontrollistattribute.html
func (client *Client) DescribeAccessControlListAttribute(request *DescribeAccessControlListAttributeRequest) (response *DescribeAccessControlListAttributeResponse, err error) {
	response = CreateDescribeAccessControlListAttributeResponse()
	err = client.DoAction(request, response)
	return
}

// DescribeAccessControlListAttributeWithChan invokes the slb.DescribeAccessControlListAttribute API asynchronously
// api document: https://help.aliyun.com/api/slb/describeaccesscontrollistattribute.html
// asynchronous document: https://help.aliyun.com/document_detail/66220.html
func (client *Client) DescribeAccessControlListAttributeWithChan(request *DescribeAccessControlListAttributeRequest) (<-chan *DescribeAccessControlListAttributeResponse, <-chan error) {
	responseChan := make(chan *DescribeAccessControlListAttributeResponse, 1)
	errChan := make(chan error, 1)
	err := client.AddAsyncTask(func() {
		defer close(responseChan)
		defer close(errChan)
		response, err := client.DescribeAccessControlListAttribute(request)
		if err != nil {
			errChan <- err
		} else {
			responseChan <- response
		}
	})
	if err != nil {
		errChan <- err
		close(responseChan)
		close(errChan)
	}
	return responseChan, errChan
}

// DescribeAccessControlListAttributeWithCallback invokes the slb.DescribeAccessControlListAttribute API asynchronously
// api document: https://help.aliyun.com/api/slb/describeaccesscontrollistattribute.html
// asynchronous document: https://help.aliyun.com/document_detail/66220.html
func (client *Client) DescribeAccessControlListAttributeWithCallback(request *DescribeAccessControlListAttributeRequest, callback func(response *DescribeAccessControlListAttributeResponse, err error)) <-chan int {
	result := make(chan int, 1)
	err := client.AddAsyncTask(func() {
		var response *DescribeAccessControlListAttributeResponse
		var err error
		defer close(result)
		response, err = client.DescribeAccessControlListAttribute(request)
		callback(response, err)
		result <- 1
	})
	if err != nil {
		defer close(result)
		callback(nil, err)
		result <- 0
	}
	return result
}

// DescribeAccessControlListAttributeRequest is the request struct for api DescribeAccessControlListAttribute
type DescribeAccessControlListAttributeRequest struct {
	*requests.RpcRequest
	AccessKeyId          string           `position:"Query" name:"access_key_id"`
	AclId                string           `position:"Query" name:"AclId"`
	ResourceOwnerId      requests.Integer `position:"Query" name:"ResourceOwnerId"`
	ResourceOwnerAccount string           `position:"Query" name:"ResourceOwnerAccount"`
	OwnerAccount         string           `position:"Query" name:"OwnerAccount"`
	AclEntryComment      string           `position:"Query" name:"AclEntryComment"`
	OwnerId              requests.Integer `position:"Query" name:"OwnerId"`
	Tags                 string           `position:"Query" name:"Tags"`
}

// DescribeAccessControlListAttributeResponse is the response struct for api DescribeAccessControlListAttribute
type DescribeAccessControlListAttributeResponse struct {
	*responses.BaseResponse
	RequestId        string           `json:"RequestId" xml:"RequestId"`
	AclId            string           `json:"AclId" xml:"AclId"`
	AclName          string           `json:"AclName" xml:"AclName"`
	AddressIPVersion string           `json:"AddressIPVersion" xml:"AddressIPVersion"`
	ResourceGroupId  string           `json:"ResourceGroupId" xml:"ResourceGroupId"`
	AclEntrys        AclEntrys        `json:"AclEntrys" xml:"AclEntrys"`
	RelatedListeners RelatedListeners `json:"RelatedListeners" xml:"RelatedListeners"`
}

// CreateDescribeAccessControlListAttributeRequest creates a request to invoke DescribeAccessControlListAttribute API
func CreateDescribeAccessControlListAttributeRequest() (request *DescribeAccessControlListAttributeRequest) {
	request = &DescribeAccessControlListAttributeRequest{
		RpcRequest: &requests.RpcRequest{},
	}
	request.InitWithApiInfo("Slb", "2014-05-15", "DescribeAccessControlListAttribute", "slb", "openAPI")
	return
}

// CreateDescribeAccessControlListAttributeResponse creates a response to parse from DescribeAccessControlListAttribute response
func CreateDescribeAccessControlListAttributeResponse() (response *DescribeAccessControlListAttributeResponse) {
	response = &DescribeAccessControlListAttributeResponse{
		BaseResponse: &responses.BaseResponse{},
	}
	return
}
