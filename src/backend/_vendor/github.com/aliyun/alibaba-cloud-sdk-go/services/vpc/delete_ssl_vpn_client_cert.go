package vpc

//Licensed under the Apache License, Version 2.0 (the "License");
//you may not use this file except in compliance with the License.
//You may obtain a copy of the License at
//
//http://www.apache.org/licenses/LICENSE-2.0
//
//Unless required by applicable law or agreed to in writing, software
//distributed under the License is distributed on an "AS IS" BASIS,
//WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//See the License for the specific language governing permissions and
//limitations under the License.
//
// Code generated by Alibaba Cloud SDK Code Generator.
// Changes may cause incorrect behavior and will be lost if the code is regenerated.

import (
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/responses"
)

// DeleteSslVpnClientCert invokes the vpc.DeleteSslVpnClientCert API synchronously
// api document: https://help.aliyun.com/api/vpc/deletesslvpnclientcert.html
func (client *Client) DeleteSslVpnClientCert(request *DeleteSslVpnClientCertRequest) (response *DeleteSslVpnClientCertResponse, err error) {
	response = CreateDeleteSslVpnClientCertResponse()
	err = client.DoAction(request, response)
	return
}

// DeleteSslVpnClientCertWithChan invokes the vpc.DeleteSslVpnClientCert API asynchronously
// api document: https://help.aliyun.com/api/vpc/deletesslvpnclientcert.html
// asynchronous document: https://help.aliyun.com/document_detail/66220.html
func (client *Client) DeleteSslVpnClientCertWithChan(request *DeleteSslVpnClientCertRequest) (<-chan *DeleteSslVpnClientCertResponse, <-chan error) {
	responseChan := make(chan *DeleteSslVpnClientCertResponse, 1)
	errChan := make(chan error, 1)
	err := client.AddAsyncTask(func() {
		defer close(responseChan)
		defer close(errChan)
		response, err := client.DeleteSslVpnClientCert(request)
		if err != nil {
			errChan <- err
		} else {
			responseChan <- response
		}
	})
	if err != nil {
		errChan <- err
		close(responseChan)
		close(errChan)
	}
	return responseChan, errChan
}

// DeleteSslVpnClientCertWithCallback invokes the vpc.DeleteSslVpnClientCert API asynchronously
// api document: https://help.aliyun.com/api/vpc/deletesslvpnclientcert.html
// asynchronous document: https://help.aliyun.com/document_detail/66220.html
func (client *Client) DeleteSslVpnClientCertWithCallback(request *DeleteSslVpnClientCertRequest, callback func(response *DeleteSslVpnClientCertResponse, err error)) <-chan int {
	result := make(chan int, 1)
	err := client.AddAsyncTask(func() {
		var response *DeleteSslVpnClientCertResponse
		var err error
		defer close(result)
		response, err = client.DeleteSslVpnClientCert(request)
		callback(response, err)
		result <- 1
	})
	if err != nil {
		defer close(result)
		callback(nil, err)
		result <- 0
	}
	return result
}

// DeleteSslVpnClientCertRequest is the request struct for api DeleteSslVpnClientCert
type DeleteSslVpnClientCertRequest struct {
	*requests.RpcRequest
	ResourceOwnerId      requests.Integer `position:"Query" name:"ResourceOwnerId"`
	ResourceOwnerAccount string           `position:"Query" name:"ResourceOwnerAccount"`
	ClientToken          string           `position:"Query" name:"ClientToken"`
	OwnerAccount         string           `position:"Query" name:"OwnerAccount"`
	OwnerId              requests.Integer `position:"Query" name:"OwnerId"`
	SslVpnClientCertId   string           `position:"Query" name:"SslVpnClientCertId"`
}

// DeleteSslVpnClientCertResponse is the response struct for api DeleteSslVpnClientCert
type DeleteSslVpnClientCertResponse struct {
	*responses.BaseResponse
	RequestId string `json:"RequestId" xml:"RequestId"`
}

// CreateDeleteSslVpnClientCertRequest creates a request to invoke DeleteSslVpnClientCert API
func CreateDeleteSslVpnClientCertRequest() (request *DeleteSslVpnClientCertRequest) {
	request = &DeleteSslVpnClientCertRequest{
		RpcRequest: &requests.RpcRequest{},
	}
	request.InitWithApiInfo("Vpc", "2016-04-28", "DeleteSslVpnClientCert", "vpc", "openAPI")
	return
}

// CreateDeleteSslVpnClientCertResponse creates a response to parse from DeleteSslVpnClientCert response
func CreateDeleteSslVpnClientCertResponse() (response *DeleteSslVpnClientCertResponse) {
	response = &DeleteSslVpnClientCertResponse{
		BaseResponse: &responses.BaseResponse{},
	}
	return
}
