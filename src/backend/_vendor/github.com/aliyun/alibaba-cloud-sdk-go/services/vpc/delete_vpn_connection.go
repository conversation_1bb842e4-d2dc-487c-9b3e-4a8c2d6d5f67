package vpc

//Licensed under the Apache License, Version 2.0 (the "License");
//you may not use this file except in compliance with the License.
//You may obtain a copy of the License at
//
//http://www.apache.org/licenses/LICENSE-2.0
//
//Unless required by applicable law or agreed to in writing, software
//distributed under the License is distributed on an "AS IS" BASIS,
//WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//See the License for the specific language governing permissions and
//limitations under the License.
//
// Code generated by Alibaba Cloud SDK Code Generator.
// Changes may cause incorrect behavior and will be lost if the code is regenerated.

import (
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/responses"
)

// DeleteVpnConnection invokes the vpc.DeleteVpnConnection API synchronously
// api document: https://help.aliyun.com/api/vpc/deletevpnconnection.html
func (client *Client) DeleteVpnConnection(request *DeleteVpnConnectionRequest) (response *DeleteVpnConnectionResponse, err error) {
	response = CreateDeleteVpnConnectionResponse()
	err = client.DoAction(request, response)
	return
}

// DeleteVpnConnectionWithChan invokes the vpc.DeleteVpnConnection API asynchronously
// api document: https://help.aliyun.com/api/vpc/deletevpnconnection.html
// asynchronous document: https://help.aliyun.com/document_detail/66220.html
func (client *Client) DeleteVpnConnectionWithChan(request *DeleteVpnConnectionRequest) (<-chan *DeleteVpnConnectionResponse, <-chan error) {
	responseChan := make(chan *DeleteVpnConnectionResponse, 1)
	errChan := make(chan error, 1)
	err := client.AddAsyncTask(func() {
		defer close(responseChan)
		defer close(errChan)
		response, err := client.DeleteVpnConnection(request)
		if err != nil {
			errChan <- err
		} else {
			responseChan <- response
		}
	})
	if err != nil {
		errChan <- err
		close(responseChan)
		close(errChan)
	}
	return responseChan, errChan
}

// DeleteVpnConnectionWithCallback invokes the vpc.DeleteVpnConnection API asynchronously
// api document: https://help.aliyun.com/api/vpc/deletevpnconnection.html
// asynchronous document: https://help.aliyun.com/document_detail/66220.html
func (client *Client) DeleteVpnConnectionWithCallback(request *DeleteVpnConnectionRequest, callback func(response *DeleteVpnConnectionResponse, err error)) <-chan int {
	result := make(chan int, 1)
	err := client.AddAsyncTask(func() {
		var response *DeleteVpnConnectionResponse
		var err error
		defer close(result)
		response, err = client.DeleteVpnConnection(request)
		callback(response, err)
		result <- 1
	})
	if err != nil {
		defer close(result)
		callback(nil, err)
		result <- 0
	}
	return result
}

// DeleteVpnConnectionRequest is the request struct for api DeleteVpnConnection
type DeleteVpnConnectionRequest struct {
	*requests.RpcRequest
	ResourceOwnerId      requests.Integer `position:"Query" name:"ResourceOwnerId"`
	ResourceOwnerAccount string           `position:"Query" name:"ResourceOwnerAccount"`
	ClientToken          string           `position:"Query" name:"ClientToken"`
	VpnConnectionId      string           `position:"Query" name:"VpnConnectionId"`
	OwnerAccount         string           `position:"Query" name:"OwnerAccount"`
	OwnerId              requests.Integer `position:"Query" name:"OwnerId"`
}

// DeleteVpnConnectionResponse is the response struct for api DeleteVpnConnection
type DeleteVpnConnectionResponse struct {
	*responses.BaseResponse
	RequestId string `json:"RequestId" xml:"RequestId"`
}

// CreateDeleteVpnConnectionRequest creates a request to invoke DeleteVpnConnection API
func CreateDeleteVpnConnectionRequest() (request *DeleteVpnConnectionRequest) {
	request = &DeleteVpnConnectionRequest{
		RpcRequest: &requests.RpcRequest{},
	}
	request.InitWithApiInfo("Vpc", "2016-04-28", "DeleteVpnConnection", "vpc", "openAPI")
	return
}

// CreateDeleteVpnConnectionResponse creates a response to parse from DeleteVpnConnection response
func CreateDeleteVpnConnectionResponse() (response *DeleteVpnConnectionResponse) {
	response = &DeleteVpnConnectionResponse{
		BaseResponse: &responses.BaseResponse{},
	}
	return
}
