package vpc

//Licensed under the Apache License, Version 2.0 (the "License");
//you may not use this file except in compliance with the License.
//You may obtain a copy of the License at
//
//http://www.apache.org/licenses/LICENSE-2.0
//
//Unless required by applicable law or agreed to in writing, software
//distributed under the License is distributed on an "AS IS" BASIS,
//WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//See the License for the specific language governing permissions and
//limitations under the License.
//
// Code generated by Alibaba Cloud SDK Code Generator.
// Changes may cause incorrect behavior and will be lost if the code is regenerated.

import (
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/responses"
)

// DescribeSslVpnClientCerts invokes the vpc.DescribeSslVpnClientCerts API synchronously
// api document: https://help.aliyun.com/api/vpc/describesslvpnclientcerts.html
func (client *Client) DescribeSslVpnClientCerts(request *DescribeSslVpnClientCertsRequest) (response *DescribeSslVpnClientCertsResponse, err error) {
	response = CreateDescribeSslVpnClientCertsResponse()
	err = client.DoAction(request, response)
	return
}

// DescribeSslVpnClientCertsWithChan invokes the vpc.DescribeSslVpnClientCerts API asynchronously
// api document: https://help.aliyun.com/api/vpc/describesslvpnclientcerts.html
// asynchronous document: https://help.aliyun.com/document_detail/66220.html
func (client *Client) DescribeSslVpnClientCertsWithChan(request *DescribeSslVpnClientCertsRequest) (<-chan *DescribeSslVpnClientCertsResponse, <-chan error) {
	responseChan := make(chan *DescribeSslVpnClientCertsResponse, 1)
	errChan := make(chan error, 1)
	err := client.AddAsyncTask(func() {
		defer close(responseChan)
		defer close(errChan)
		response, err := client.DescribeSslVpnClientCerts(request)
		if err != nil {
			errChan <- err
		} else {
			responseChan <- response
		}
	})
	if err != nil {
		errChan <- err
		close(responseChan)
		close(errChan)
	}
	return responseChan, errChan
}

// DescribeSslVpnClientCertsWithCallback invokes the vpc.DescribeSslVpnClientCerts API asynchronously
// api document: https://help.aliyun.com/api/vpc/describesslvpnclientcerts.html
// asynchronous document: https://help.aliyun.com/document_detail/66220.html
func (client *Client) DescribeSslVpnClientCertsWithCallback(request *DescribeSslVpnClientCertsRequest, callback func(response *DescribeSslVpnClientCertsResponse, err error)) <-chan int {
	result := make(chan int, 1)
	err := client.AddAsyncTask(func() {
		var response *DescribeSslVpnClientCertsResponse
		var err error
		defer close(result)
		response, err = client.DescribeSslVpnClientCerts(request)
		callback(response, err)
		result <- 1
	})
	if err != nil {
		defer close(result)
		callback(nil, err)
		result <- 0
	}
	return result
}

// DescribeSslVpnClientCertsRequest is the request struct for api DescribeSslVpnClientCerts
type DescribeSslVpnClientCertsRequest struct {
	*requests.RpcRequest
	SslVpnServerId       string           `position:"Query" name:"SslVpnServerId"`
	ResourceOwnerId      requests.Integer `position:"Query" name:"ResourceOwnerId"`
	ResourceOwnerAccount string           `position:"Query" name:"ResourceOwnerAccount"`
	OwnerAccount         string           `position:"Query" name:"OwnerAccount"`
	Name                 string           `position:"Query" name:"Name"`
	PageSize             requests.Integer `position:"Query" name:"PageSize"`
	OwnerId              requests.Integer `position:"Query" name:"OwnerId"`
	SslVpnClientCertId   string           `position:"Query" name:"SslVpnClientCertId"`
	PageNumber           requests.Integer `position:"Query" name:"PageNumber"`
}

// DescribeSslVpnClientCertsResponse is the response struct for api DescribeSslVpnClientCerts
type DescribeSslVpnClientCertsResponse struct {
	*responses.BaseResponse
	RequestId            string               `json:"RequestId" xml:"RequestId"`
	TotalCount           int                  `json:"TotalCount" xml:"TotalCount"`
	PageNumber           int                  `json:"PageNumber" xml:"PageNumber"`
	PageSize             int                  `json:"PageSize" xml:"PageSize"`
	SslVpnClientCertKeys SslVpnClientCertKeys `json:"SslVpnClientCertKeys" xml:"SslVpnClientCertKeys"`
}

// CreateDescribeSslVpnClientCertsRequest creates a request to invoke DescribeSslVpnClientCerts API
func CreateDescribeSslVpnClientCertsRequest() (request *DescribeSslVpnClientCertsRequest) {
	request = &DescribeSslVpnClientCertsRequest{
		RpcRequest: &requests.RpcRequest{},
	}
	request.InitWithApiInfo("Vpc", "2016-04-28", "DescribeSslVpnClientCerts", "vpc", "openAPI")
	return
}

// CreateDescribeSslVpnClientCertsResponse creates a response to parse from DescribeSslVpnClientCerts response
func CreateDescribeSslVpnClientCertsResponse() (response *DescribeSslVpnClientCertsResponse) {
	response = &DescribeSslVpnClientCertsResponse{
		BaseResponse: &responses.BaseResponse{},
	}
	return
}
