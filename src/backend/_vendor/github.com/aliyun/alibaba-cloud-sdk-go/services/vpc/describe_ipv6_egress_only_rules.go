package vpc

//Licensed under the Apache License, Version 2.0 (the "License");
//you may not use this file except in compliance with the License.
//You may obtain a copy of the License at
//
//http://www.apache.org/licenses/LICENSE-2.0
//
//Unless required by applicable law or agreed to in writing, software
//distributed under the License is distributed on an "AS IS" BASIS,
//WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//See the License for the specific language governing permissions and
//limitations under the License.
//
// Code generated by Alibaba Cloud SDK Code Generator.
// Changes may cause incorrect behavior and will be lost if the code is regenerated.

import (
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/responses"
)

// DescribeIpv6EgressOnlyRules invokes the vpc.DescribeIpv6EgressOnlyRules API synchronously
// api document: https://help.aliyun.com/api/vpc/describeipv6egressonlyrules.html
func (client *Client) DescribeIpv6EgressOnlyRules(request *DescribeIpv6EgressOnlyRulesRequest) (response *DescribeIpv6EgressOnlyRulesResponse, err error) {
	response = CreateDescribeIpv6EgressOnlyRulesResponse()
	err = client.DoAction(request, response)
	return
}

// DescribeIpv6EgressOnlyRulesWithChan invokes the vpc.DescribeIpv6EgressOnlyRules API asynchronously
// api document: https://help.aliyun.com/api/vpc/describeipv6egressonlyrules.html
// asynchronous document: https://help.aliyun.com/document_detail/66220.html
func (client *Client) DescribeIpv6EgressOnlyRulesWithChan(request *DescribeIpv6EgressOnlyRulesRequest) (<-chan *DescribeIpv6EgressOnlyRulesResponse, <-chan error) {
	responseChan := make(chan *DescribeIpv6EgressOnlyRulesResponse, 1)
	errChan := make(chan error, 1)
	err := client.AddAsyncTask(func() {
		defer close(responseChan)
		defer close(errChan)
		response, err := client.DescribeIpv6EgressOnlyRules(request)
		if err != nil {
			errChan <- err
		} else {
			responseChan <- response
		}
	})
	if err != nil {
		errChan <- err
		close(responseChan)
		close(errChan)
	}
	return responseChan, errChan
}

// DescribeIpv6EgressOnlyRulesWithCallback invokes the vpc.DescribeIpv6EgressOnlyRules API asynchronously
// api document: https://help.aliyun.com/api/vpc/describeipv6egressonlyrules.html
// asynchronous document: https://help.aliyun.com/document_detail/66220.html
func (client *Client) DescribeIpv6EgressOnlyRulesWithCallback(request *DescribeIpv6EgressOnlyRulesRequest, callback func(response *DescribeIpv6EgressOnlyRulesResponse, err error)) <-chan int {
	result := make(chan int, 1)
	err := client.AddAsyncTask(func() {
		var response *DescribeIpv6EgressOnlyRulesResponse
		var err error
		defer close(result)
		response, err = client.DescribeIpv6EgressOnlyRules(request)
		callback(response, err)
		result <- 1
	})
	if err != nil {
		defer close(result)
		callback(nil, err)
		result <- 0
	}
	return result
}

// DescribeIpv6EgressOnlyRulesRequest is the request struct for api DescribeIpv6EgressOnlyRules
type DescribeIpv6EgressOnlyRulesRequest struct {
	*requests.RpcRequest
	ResourceOwnerId      requests.Integer `position:"Query" name:"ResourceOwnerId"`
	PageNumber           requests.Integer `position:"Query" name:"PageNumber"`
	Ipv6EgressOnlyRuleId string           `position:"Query" name:"Ipv6EgressOnlyRuleId"`
	PageSize             requests.Integer `position:"Query" name:"PageSize"`
	InstanceType         string           `position:"Query" name:"InstanceType"`
	ResourceOwnerAccount string           `position:"Query" name:"ResourceOwnerAccount"`
	OwnerAccount         string           `position:"Query" name:"OwnerAccount"`
	OwnerId              requests.Integer `position:"Query" name:"OwnerId"`
	InstanceId           string           `position:"Query" name:"InstanceId"`
	Ipv6GatewayId        string           `position:"Query" name:"Ipv6GatewayId"`
	Name                 string           `position:"Query" name:"Name"`
}

// DescribeIpv6EgressOnlyRulesResponse is the response struct for api DescribeIpv6EgressOnlyRules
type DescribeIpv6EgressOnlyRulesResponse struct {
	*responses.BaseResponse
	RequestId           string              `json:"RequestId" xml:"RequestId"`
	TotalCount          int                 `json:"TotalCount" xml:"TotalCount"`
	PageNumber          int                 `json:"PageNumber" xml:"PageNumber"`
	PageSize            int                 `json:"PageSize" xml:"PageSize"`
	Ipv6EgressOnlyRules Ipv6EgressOnlyRules `json:"Ipv6EgressOnlyRules" xml:"Ipv6EgressOnlyRules"`
}

// CreateDescribeIpv6EgressOnlyRulesRequest creates a request to invoke DescribeIpv6EgressOnlyRules API
func CreateDescribeIpv6EgressOnlyRulesRequest() (request *DescribeIpv6EgressOnlyRulesRequest) {
	request = &DescribeIpv6EgressOnlyRulesRequest{
		RpcRequest: &requests.RpcRequest{},
	}
	request.InitWithApiInfo("Vpc", "2016-04-28", "DescribeIpv6EgressOnlyRules", "vpc", "openAPI")
	return
}

// CreateDescribeIpv6EgressOnlyRulesResponse creates a response to parse from DescribeIpv6EgressOnlyRules response
func CreateDescribeIpv6EgressOnlyRulesResponse() (response *DescribeIpv6EgressOnlyRulesResponse) {
	response = &DescribeIpv6EgressOnlyRulesResponse{
		BaseResponse: &responses.BaseResponse{},
	}
	return
}
