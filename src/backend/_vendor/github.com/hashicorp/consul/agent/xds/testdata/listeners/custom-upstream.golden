{"versionInfo": "00000001", "resources": [{"@type": "type.googleapis.com/envoy.api.v2.Listener", "name": "custom-upstream", "address": {"socketAddress": {"address": "***********", "portValue": 11111}}, "filterChains": [{"filters": [{"name": "envoy.tcp_proxy", "config": {"cluster": "random-cluster", "stat_prefix": "foo-stats"}}]}]}, {"@type": "type.googleapis.com/envoy.api.v2.Listener", "name": "prepared_query:geo-cache:************:8181", "address": {"socketAddress": {"address": "************", "portValue": 8181}}, "filterChains": [{"filters": [{"name": "envoy.tcp_proxy", "config": {"cluster": "geo-cache.default.dc1.query.11111111-2222-3333-4444-555555555555.consul", "stat_prefix": "upstream_prepared_query_geo-cache_tcp"}}]}]}, {"@type": "type.googleapis.com/envoy.api.v2.Listener", "name": "public_listener:0.0.0.0:9999", "address": {"socketAddress": {"address": "0.0.0.0", "portValue": 9999}}, "filterChains": [{"tlsContext": {"commonTlsContext": {"tlsParams": {}, "tlsCertificates": [{"certificateChain": {"inlineString": "-----<PERSON><PERSON><PERSON> CERTIFICATE-----\nMI<PERSON>jDCCAjKgAwIBAgIIC5llxGV1gB8wCgYIKoZIzj0EAwIwFDESMBAGA1UEAxMJ\nVGVzdCBDQSAyMB4XDTE5MDMyMjEzNTgyNloXDTI5MDMyMjEzNTgyNlowDjEMMAoG\nA1UEAxMDd2ViMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEADPv1RHVNRfa2VKR\nAB16b6rZnEt7tuhaxCFpQXPj7M2omb0B9Favq5E0ivpNtv1QnFhxtPd7d5k4e+T7\nSkW1TaOCAXIwggFuMA4GA1UdDwEB/wQEAwIDuDAdBgNVHSUEFjAUBggrBgEFBQcD\nAgYIKwYBBQUHAwEwDAYDVR0TAQH/BAIwADBoBgNVHQ4EYQRfN2Q6MDc6ODc6M2E6\nNDA6MTk6NDc6YzM6NWE6YzA6YmE6NjI6ZGY6YWY6NGI6ZDQ6MDU6MjU6NzY6M2Q6\nNWE6OGQ6MTY6OGQ6Njc6NWU6MmU6YTA6MzQ6N2Q6ZGM6ZmYwagYDVR0jBGMwYYBf\nZDE6MTE6MTE6YWM6MmE6YmE6OTc6YjI6M2Y6YWM6N2I6YmQ6ZGE6YmU6YjE6OGE6\nZmM6OWE6YmE6YjU6YmM6ODM6ZTc6NWU6NDE6NmY6ZjI6NzM6OTU6NTg6MGM6ZGIw\nWQYDVR0RBFIwUIZOc3BpZmZlOi8vMTExMTExMTEtMjIyMi0zMzMzLTQ0NDQtNTU1\nNTU1NTU1NTU1LmNvbnN1bC9ucy9kZWZhdWx0L2RjL2RjMS9zdmMvd2ViMAoGCCqG\nSM49BAMCA0gAMEUCIGC3TTvvjj76KMrguVyFf4tjOqaSCRie3nmHMRNNRav7AiEA\npY0heYeK9A6iOLrzqxSerkXXQyj5e9bE4VgUnxgPU6g=\n-----END CERTIFICATE-----\n"}, "privateKey": {"inlineString": "-----BEGIN EC PRIVATE KEY-----\nMH<PERSON>CAQEEIMoTkpRggp3fqZzFKh82yS4LjtJI+XY+qX/7DefHFrtdoAoGCCqGSM49\nAwEHoUQDQgAEADPv1RHVNRfa2VKRAB16b6rZnEt7tuhaxCFpQXPj7M2omb0B9Fav\nq5E0ivpNtv1QnFhxtPd7d5k4e+T7SkW1TQ==\n-----END EC PRIVATE KEY-----\n"}}], "validationContext": {"trustedCa": {"inlineString": "-----B<PERSON><PERSON> CERTIFICATE-----\nMIICXDCCAgKgAwIBAgIICpZq70Z9LyUwCgYIKoZIzj0EAwIwFDESMBAGA1UEAxMJ\nVGVzdCBDQSAyMB4XDTE5MDMyMjEzNTgyNloXDTI5MDMyMjEzNTgyNlowFDESMBAG\nA1UEAxMJVGVzdCBDQSAyMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEIhywH1gx\nAsMwuF3ukAI5YL2jFxH6Usnma1HFSfVyxbXX1/uoZEYrj8yCAtdU2yoHETyd+Zx2\nThhRLP79pYegCaOCATwwggE4MA4GA1UdDwEB/wQEAwIBhjAPBgNVHRMBAf8EBTAD\nAQH/MGgGA1UdDgRhBF9kMToxMToxMTphYzoyYTpiYTo5NzpiMjozZjphYzo3Yjpi\nZDpkYTpiZTpiMTo4YTpmYzo5YTpiYTpiNTpiYzo4MzplNzo1ZTo0MTo2ZjpmMjo3\nMzo5NTo1ODowYzpkYjBqBgNVHSMEYzBhgF9kMToxMToxMTphYzoyYTpiYTo5Nzpi\nMjozZjphYzo3YjpiZDpkYTpiZTpiMTo4YTpmYzo5YTpiYTpiNTpiYzo4MzplNzo1\nZTo0MTo2ZjpmMjo3Mzo5NTo1ODowYzpkYjA/BgNVHREEODA2hjRzcGlmZmU6Ly8x\nMTExMTExMS0yMjIyLTMzMzMtNDQ0NC01NTU1NTU1NTU1NTUuY29uc3VsMAoGCCqG\nSM49BAMCA0gAMEUCICOY0i246rQHJt8o8Oya0D5PLL1FnmsQmQqIGCi31RwnAiEA\noR5f6Ku+cig2Il8T8LJujOp2/2A72QcHZA57B13y+8o=\n-----END CERTIFICATE-----\n"}}}, "requireClientCertificate": true}, "filters": [{"name": "envoy.ext_authz", "config": {"grpc_service": {"envoy_grpc": {"cluster_name": "local_agent"}, "initial_metadata": [{"key": "x-consul-token", "value": "my-token"}]}, "stat_prefix": "connect_authz"}}, {"name": "envoy.tcp_proxy", "config": {"cluster": "local_app", "stat_prefix": "public_listener_tcp"}}]}]}], "typeUrl": "type.googleapis.com/envoy.api.v2.Listener", "nonce": "00000001"}