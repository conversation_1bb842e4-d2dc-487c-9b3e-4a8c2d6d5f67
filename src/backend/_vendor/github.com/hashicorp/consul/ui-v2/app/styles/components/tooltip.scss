@import '../base/components/tooltip/index';
%app-view h1 span[data-tooltip] {
  @extend %with-pseudo-tooltip;
  text-indent: -9000px;
  font-size: 0;
}
/* override structure min-width for the moment */
/* TODO: Clarify whether these should actually use */
/* the min-width from structure */
/* TODO: See if we can move all these to base */
%tooltip-bubble {
  min-width: 0;
}
%tooltip-below::after {
  top: calc(100% - 8px);
  bottom: auto;
  border-top: none;
  border-bottom: 18px solid $gray-500;
}
%tooltip-below::before {
  top: calc(100% + 4px);
  bottom: auto;
  /*TODO: This should probably go into base*/
  line-height: 1em;
}
%tooltip-left::before {
  right: 0;
}
%tooltip-right::before {
  left: -7px;
}
