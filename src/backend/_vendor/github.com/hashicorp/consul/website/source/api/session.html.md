---
layout: api
page_title: Session - HTTP API
sidebar_current: api-session
description: |-
  The /session endpoints create, destroy, and query sessions in Consul.
---

# Session HTTP Endpoint

The `/session` endpoints create, destroy, and query sessions in Consul.

## Create Session

This endpoint initializes a new session. Sessions must be associated with a
node and may be associated with any number of checks.

| Method | Path                         | Produces                   |
| ------ | ---------------------------- | -------------------------- |
| `PUT`  | `/session/create`            | `application/json`         |

The table below shows this endpoint's support for
[blocking queries](/api/features/blocking.html),
[consistency modes](/api/features/consistency.html),
[agent caching](/api/features/caching.html), and
[required ACLs](/api/index.html#authentication).

| Blocking Queries | Consistency Modes | Agent Caching | ACL Required    |
| ---------------- | ----------------- | ------------- | --------------- |
| `NO`             | `none`            | `none`        | `session:write` |

### Parameters

- `ns` `(string: "")` - **(Enterprise Only)** Specifies the namespace to query.
  If not provided, the namespace will be inferred from the request's ACL token,
  or will default to the `default` namespace. This is specified as part of the
  URL as a query parameter. Added in Consul 1.7.0.

- `dc` `(string: "")` - Specifies the datacenter to query. This will default to
  the datacenter of the agent being queried. This is specified as part of the
  URL as a query parameter. Using this across datacenters is not recommended.

- `LockDelay` `(string: "15s")` - Specifies the duration for the lock delay. This
  must be greater than `0`.

- `Node` `(string: "<agent>")` - Specifies the name of the node. This must refer
  to a node that is already registered.

- `Name` `(string: "")` - Specifies a human-readable name for the session.

- `Checks` `(array<string>: nil)` - specifies a list of associated health
  check IDs (commonly `CheckID` in API responses). It is highly recommended that,
  if you override this list, you include the default `serfHealth`.

- `Behavior` `(string: "release")` - Controls the behavior to take when a
  session is invalidated. Valid values are:

  - `release` - causes any locks that are held to be released
  - `delete` - causes any locks that are held to be deleted

- `TTL` `(string: "")` - Specifies the number of seconds (between 10s and
  86400s). If provided, the session is invalidated if it is not renewed before
  the TTL expires. The lowest practical TTL should be used to keep the number of
  managed sessions low. When locks are forcibly expired, such as when following
  the [leader election pattern](https://learn.hashicorp.com/consul/developer-configuration/elections) in an application,
  sessions may not be reaped for up to double this TTL, so long TTL
  values (> 1 hour) should be avoided.

### Sample Payload

```json
{
  "LockDelay": "15s",
  "Name": "my-service-lock",
  "Node": "foobar",
  "Checks": ["a", "b", "c"],
  "Behavior": "release",
  "TTL": "30s"
}
```

### Sample Request

```text
$ curl \
    --request PUT \
    --data @payload.json \
    http://127.0.0.1:8500/v1/session/create
```

### Sample Response

```javascript
{
  "ID": "adf4238a-882b-9ddc-4a9d-5b6758e4159e"
}
```

- `ID` - the ID of the created session

## Delete Session

This endpoint destroys the session with the given name. If the session UUID is
malformed, an error is returned. If the session UUID does not exist or already
expired, a 200 is still returned (the operation is idempotent).

| Method | Path                         | Produces                   |
| :----- | :--------------------------- | -------------------------- |
| `PUT`  | `/session/destroy/:uuid`     | `application/json`         |

Even though the Content-Type is `application/json`, the response is
either a literal `true` or `false`, indicating of whether the destroy was
successful.

The table below shows this endpoint's support for
[blocking queries](/api/features/blocking.html),
[consistency modes](/api/features/consistency.html),
[agent caching](/api/features/caching.html), and
[required ACLs](/api/index.html#authentication).

| Blocking Queries | Consistency Modes | Agent Caching | ACL Required    |
| ---------------- | ----------------- | ------------- | --------------- |
| `NO`             | `none`            | `none`        | `session:write` |

### Parameters

- `uuid` `(string: <required>)` - Specifies the UUID of the session to destroy.
  This is required and is specified as part of the URL path.

- `dc` `(string: "")` - Specifies the datacenter to query. This will default to
  the datacenter of the agent being queried. This is specified as part of the
  URL as a query parameter. Using this across datacenters is not recommended.
  
- `ns` `(string: "")` - **(Enterprise Only)** Specifies the namespace to query.
  If not provided, the namespace will be inferred from the request's ACL token,
  or will default to the `default` namespace. This is specified as part of the
  URL as a query parameter. Added in Consul 1.7.0.

### Sample Request

```text
$ curl \
    --request PUT \
    http://127.0.0.1:8500/v1/session/destroy/adf4238a-882b-9ddc-4a9d-5b6758e4159e
```

### Sample Response

```json
true
```

## Read Session

This endpoint returns the requested session information.

| Method | Path                         | Produces                   |
| :----- | :--------------------------- | -------------------------- |
| `GET`  | `/session/info/:uuid`        | `application/json`         |

The table below shows this endpoint's support for
[blocking queries](/api/features/blocking.html),
[consistency modes](/api/features/consistency.html),
[agent caching](/api/features/caching.html), and
[required ACLs](/api/index.html#authentication).

| Blocking Queries | Consistency Modes | Agent Caching | ACL Required   |
| ---------------- | ----------------- | ------------- | -------------- |
| `YES`            | `all`             | `none`        | `session:read` |

### Parameters

- `uuid` `(string: <required>)` - Specifies the UUID of the session to read.
  This is required and is specified as part of the URL path.

- `dc` `(string: "")` - Specifies the datacenter to query. This will default to
  the datacenter of the agent being queried. This is specified as part of the
  URL as a query parameter. Using this across datacenters is not recommended.
  
- `ns` `(string: "")` - **(Enterprise Only)** Specifies the namespace to query.
  If not provided, the namespace will be inferred from the request's ACL token,
  or will default to the `default` namespace. This is specified as part of the
  URL as a query parameter. Added in Consul 1.7.0.

### Sample Request

```text
$ curl \
    http://127.0.0.1:8500/v1/session/info/adf4238a-882b-9ddc-4a9d-5b6758e4159e
```

### Sample Response

```json
[
  {
    "ID": "adf4238a-882b-9ddc-4a9d-5b6758e4159e",
    "Name": "test-session",
    "Node": "raja-laptop-02",    
    "Checks": [
      "serfHealth"
    ],
    "LockDelay": 1.5e+10,
    "Behavior": "release",
    "TTL": "30s",
    "CreateIndex": 1086449,
    "ModifyIndex": 1086449
  }
]
```

If the session does not exist, an empty JSON list `[]` is returned.

## List Sessions for Node

This endpoint returns the active sessions for a given node.

| Method | Path                         | Produces                   |
| :----- | :--------------------------- | -------------------------- |
| `GET`  | `/session/node/:node`        | `application/json`         |

The table below shows this endpoint's support for
[blocking queries](/api/features/blocking.html),
[consistency modes](/api/features/consistency.html),
[agent caching](/api/features/caching.html), and
[required ACLs](/api/index.html#authentication).

| Blocking Queries | Consistency Modes | Agent Caching | ACL Required   |
| ---------------- | ----------------- | ------------- | -------------- |
| `YES`            | `all`             | `none`        | `session:read` |

### Parameters

- `node` `(string: <required>)` - Specifies the name or ID of the node to query.
  This is required and is specified as part of the URL path.

- `dc` `(string: "")` - Specifies the datacenter to query. This will default to
  the datacenter of the agent being queried. This is specified as part of the
  URL as a query parameter. Using this across datacenters is not recommended.
  
- `ns` `(string: "")` - **(Enterprise Only)** Specifies the namespace to query.
  If not provided, the namespace will be inferred from the request's ACL token,
  or will default to the `default` namespace. This is specified as part of the
  URL as a query parameter
  The namespace may be specified as '*' and then results will be returned for all namespaces.
  Added in Consul 1.7.0.

### Sample Request

```text
$ curl \
    http://127.0.0.1:8500/v1/session/node/node-abcd1234
```

### Sample Response

```json
[
  {
    "ID": "adf4238a-882b-9ddc-4a9d-5b6758e4159e",
    "Name": "test-session",
    "Node": "raja-laptop-02",    
    "Checks": [
      "serfHealth"
    ],
    "LockDelay": 1.5e+10,
    "Behavior": "release",
    "TTL": "30s",
    "CreateIndex": 1086449,
    "ModifyIndex": 1086449
  }
]
```

## List Sessions

This endpoint returns the list of active sessions.

| Method | Path                         | Produces                   |
| :----- | :--------------------------- | -------------------------- |
| `GET`  | `/session/list`              | `application/json`         |

The table below shows this endpoint's support for
[blocking queries](/api/features/blocking.html),
[consistency modes](/api/features/consistency.html),
[agent caching](/api/features/caching.html), and
[required ACLs](/api/index.html#authentication).

| Blocking Queries | Consistency Modes | Agent Caching | ACL Required   |
| ---------------- | ----------------- | ------------- | -------------- |
| `YES`            | `all`             | `none`        | `session:read` |

### Parameters

- `dc` `(string: "")` - Specifies the datacenter to query. This will default to
  the datacenter of the agent being queried. This is specified as part of the
  URL as a query parameter. Using this across datacenters is not recommended.
  
- `ns` `(string: "")` - **(Enterprise Only)** Specifies the namespace to query.
  If not provided, the namespace will be inferred from the request's ACL token,
  or will default to the `default` namespace.   This is specified as part of the URL as a query parameter. 
  The namespace may be specified as '*' and then results will be returned for all namespaces.
  Added in Consul 1.7.0.

### Sample Request

```text
$ curl \
    http://127.0.0.1:8500/v1/session/list
```

### Sample Response

```json
[
  {
    "ID": "adf4238a-882b-9ddc-4a9d-5b6758e4159e",
    "Name": "test-session",
    "Node": "raja-laptop-02",    
    "Checks": [
      "serfHealth"
    ],
    "LockDelay": 1.5e+10,
    "Behavior": "release",
    "TTL": "30s",
    "CreateIndex": 1086449,
    "ModifyIndex": 1086449
  }
]
```

## Renew Session

This endpoint renews the given session. This is used with sessions that have a
TTL, and it extends the expiration by the TTL.

| Method | Path                         | Produces                   |
| :----- | :--------------------------- | -------------------------- |
| `PUT`  | `/session/renew/:uuid`       | `application/json`         |

The table below shows this endpoint's support for
[blocking queries](/api/features/blocking.html),
[consistency modes](/api/features/consistency.html),
[agent caching](/api/features/caching.html), and
[required ACLs](/api/index.html#authentication).

| Blocking Queries | Consistency Modes | Agent Caching | ACL Required    |
| ---------------- | ----------------- | ------------- | --------------- |
| `NO`             | `none`            | `none`        | `session:write` |

### Parameters

- `uuid` `(string: <required>)` - Specifies the UUID of the session to renew.
  This is required and is specified as part of the URL path.

- `dc` `(string: "")` - Specifies the datacenter to query. This will default to
  the datacenter of the agent being queried. This is specified as part of the
  URL as a query parameter. Using this across datacenters is not recommended.
  
- `ns` `(string: "")` - **(Enterprise Only)** Specifies the namespace to query.
  If not provided, the namespace will be inferred from the request's ACL token,
  or will default to the `default` namespace. This is specified as part of the
  URL as a query parameter. Added in Consul 1.7.0.

### Sample Request

```text
$ curl \
    --request PUT \
    http://127.0.0.1:8500/v1/session/renew/adf4238a-882b-9ddc-4a9d-5b6758e4159e
```

### Sample Response

```json
[
  {
    "ID": "adf4238a-882b-9ddc-4a9d-5b6758e4159e",
    "Name": "test-session",
    "Node": "raja-laptop-02",    
    "Checks": [
      "serfHealth"
    ],
    "LockDelay": 1.5e+10,
    "Behavior": "release",
    "TTL": "15s",
    "CreateIndex": 1086449,
    "ModifyIndex": 1086449
  }
]
```

-> **Note:** Consul may return a TTL value higher than the one specified during session creation. This indicates the server is under high load and is requesting clients renew less often.
