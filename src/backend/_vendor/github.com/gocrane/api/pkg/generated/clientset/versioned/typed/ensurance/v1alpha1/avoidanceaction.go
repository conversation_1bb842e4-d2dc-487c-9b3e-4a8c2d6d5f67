// Code generated by client-gen. DO NOT EDIT.

package v1alpha1

import (
	"context"
	"time"

	v1alpha1 "github.com/gocrane/api/ensurance/v1alpha1"
	scheme "github.com/gocrane/api/pkg/generated/clientset/versioned/scheme"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
)

// AvoidanceActionsGetter has a method to return a AvoidanceActionInterface.
// A group's client should implement this interface.
type AvoidanceActionsGetter interface {
	AvoidanceActions() AvoidanceActionInterface
}

// AvoidanceActionInterface has methods to work with AvoidanceAction resources.
type AvoidanceActionInterface interface {
	Create(ctx context.Context, avoidanceAction *v1alpha1.AvoidanceAction, opts v1.CreateOptions) (*v1alpha1.AvoidanceAction, error)
	Update(ctx context.Context, avoidanceAction *v1alpha1.AvoidanceAction, opts v1.UpdateOptions) (*v1alpha1.AvoidanceAction, error)
	UpdateStatus(ctx context.Context, avoidanceAction *v1alpha1.AvoidanceAction, opts v1.UpdateOptions) (*v1alpha1.AvoidanceAction, error)
	Delete(ctx context.Context, name string, opts v1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error
	Get(ctx context.Context, name string, opts v1.GetOptions) (*v1alpha1.AvoidanceAction, error)
	List(ctx context.Context, opts v1.ListOptions) (*v1alpha1.AvoidanceActionList, error)
	Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha1.AvoidanceAction, err error)
	AvoidanceActionExpansion
}

// avoidanceActions implements AvoidanceActionInterface
type avoidanceActions struct {
	client rest.Interface
}

// newAvoidanceActions returns a AvoidanceActions
func newAvoidanceActions(c *EnsuranceV1alpha1Client) *avoidanceActions {
	return &avoidanceActions{
		client: c.RESTClient(),
	}
}

// Get takes name of the avoidanceAction, and returns the corresponding avoidanceAction object, and an error if there is any.
func (c *avoidanceActions) Get(ctx context.Context, name string, options v1.GetOptions) (result *v1alpha1.AvoidanceAction, err error) {
	result = &v1alpha1.AvoidanceAction{}
	err = c.client.Get().
		Resource("avoidanceactions").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(ctx).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of AvoidanceActions that match those selectors.
func (c *avoidanceActions) List(ctx context.Context, opts v1.ListOptions) (result *v1alpha1.AvoidanceActionList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1alpha1.AvoidanceActionList{}
	err = c.client.Get().
		Resource("avoidanceactions").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested avoidanceActions.
func (c *avoidanceActions) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Resource("avoidanceactions").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(ctx)
}

// Create takes the representation of a avoidanceAction and creates it.  Returns the server's representation of the avoidanceAction, and an error, if there is any.
func (c *avoidanceActions) Create(ctx context.Context, avoidanceAction *v1alpha1.AvoidanceAction, opts v1.CreateOptions) (result *v1alpha1.AvoidanceAction, err error) {
	result = &v1alpha1.AvoidanceAction{}
	err = c.client.Post().
		Resource("avoidanceactions").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(avoidanceAction).
		Do(ctx).
		Into(result)
	return
}

// Update takes the representation of a avoidanceAction and updates it. Returns the server's representation of the avoidanceAction, and an error, if there is any.
func (c *avoidanceActions) Update(ctx context.Context, avoidanceAction *v1alpha1.AvoidanceAction, opts v1.UpdateOptions) (result *v1alpha1.AvoidanceAction, err error) {
	result = &v1alpha1.AvoidanceAction{}
	err = c.client.Put().
		Resource("avoidanceactions").
		Name(avoidanceAction.Name).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(avoidanceAction).
		Do(ctx).
		Into(result)
	return
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *avoidanceActions) UpdateStatus(ctx context.Context, avoidanceAction *v1alpha1.AvoidanceAction, opts v1.UpdateOptions) (result *v1alpha1.AvoidanceAction, err error) {
	result = &v1alpha1.AvoidanceAction{}
	err = c.client.Put().
		Resource("avoidanceactions").
		Name(avoidanceAction.Name).
		SubResource("status").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(avoidanceAction).
		Do(ctx).
		Into(result)
	return
}

// Delete takes name of the avoidanceAction and deletes it. Returns an error if one occurs.
func (c *avoidanceActions) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	return c.client.Delete().
		Resource("avoidanceactions").
		Name(name).
		Body(&opts).
		Do(ctx).
		Error()
}

// DeleteCollection deletes a collection of objects.
func (c *avoidanceActions) DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error {
	var timeout time.Duration
	if listOpts.TimeoutSeconds != nil {
		timeout = time.Duration(*listOpts.TimeoutSeconds) * time.Second
	}
	return c.client.Delete().
		Resource("avoidanceactions").
		VersionedParams(&listOpts, scheme.ParameterCodec).
		Timeout(timeout).
		Body(&opts).
		Do(ctx).
		Error()
}

// Patch applies the patch and returns the patched avoidanceAction.
func (c *avoidanceActions) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha1.AvoidanceAction, err error) {
	result = &v1alpha1.AvoidanceAction{}
	err = c.client.Patch(pt).
		Resource("avoidanceactions").
		Name(name).
		SubResource(subresources...).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(data).
		Do(ctx).
		Into(result)
	return
}
