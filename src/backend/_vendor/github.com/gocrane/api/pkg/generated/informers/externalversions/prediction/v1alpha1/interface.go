// Code generated by informer-gen. DO NOT EDIT.

package v1alpha1

import (
	internalinterfaces "github.com/gocrane/api/pkg/generated/informers/externalversions/internalinterfaces"
)

// Interface provides access to all the informers in this group version.
type Interface interface {
	// ClusterNodePredictions returns a ClusterNodePredictionInformer.
	ClusterNodePredictions() ClusterNodePredictionInformer
	// TimeSeriesPredictions returns a TimeSeriesPredictionInformer.
	TimeSeriesPredictions() TimeSeriesPredictionInformer
}

type version struct {
	factory          internalinterfaces.SharedInformerFactory
	namespace        string
	tweakListOptions internalinterfaces.TweakListOptionsFunc
}

// New returns a new Interface.
func New(f internalinterfaces.SharedInformerFactory, namespace string, tweakListOptions internalinterfaces.TweakListOptionsFunc) Interface {
	return &version{factory: f, namespace: namespace, tweakListOptions: tweakListOptions}
}

// ClusterNodePredictions returns a ClusterNodePredictionInformer.
func (v *version) ClusterNodePredictions() ClusterNodePredictionInformer {
	return &clusterNodePredictionInformer{factory: v.factory, namespace: v.namespace, tweakListOptions: v.tweakListOptions}
}

// TimeSeriesPredictions returns a TimeSeriesPredictionInformer.
func (v *version) TimeSeriesPredictions() TimeSeriesPredictionInformer {
	return &timeSeriesPredictionInformer{factory: v.factory, namespace: v.namespace, tweakListOptions: v.tweakListOptions}
}
