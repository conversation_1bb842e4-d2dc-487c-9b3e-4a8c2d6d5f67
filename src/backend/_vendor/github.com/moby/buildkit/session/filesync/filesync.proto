syntax = "proto3";

package moby.filesync.v1;

option go_package = "filesync";

import "github.com/tonistiigi/fsutil/types/wire.proto";

service FileSync{
  rpc DiffCopy(stream fsutil.types.Packet) returns (stream fsutil.types.Packet);
  rpc TarStream(stream fsutil.types.Packet) returns (stream fsutil.types.Packet);
}

service FileSend{
  rpc DiffCopy(stream BytesMessage) returns (stream BytesMessage);
}


// BytesMessage contains a chunk of byte data
message BytesMessage{
	bytes data = 1;
}
