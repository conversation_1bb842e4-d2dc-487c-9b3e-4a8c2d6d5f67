// Code generated by running "go generate" in golang.org/x/text. DO NOT EDIT.

// +build go1.10

package norm

const (
	Yes = iota
	No
	Maybe
)

type formData struct {
	qc              uint8
	combinesForward bool
	decomposition   string
}

type runeData struct {
	r      rune
	ccc    uint8
	nLead  uint8
	nTrail uint8
	f      [2]formData // 0: canonical; 1: compatibility
}

func f(qc uint8, cf bool, dec string) [2]formData {
	return [2]formData{{qc, cf, dec}, {qc, cf, dec}}
}

func g(qc, qck uint8, cf, cfk bool, d, dk string) [2]formData {
	return [2]formData{{qc, cf, d}, {qck, cfk, dk}}
}

var testData = []runeData{
	{0x0, 0, 0, 0, f(Yes, false, "")},
	{0x3c, 0, 0, 0, f(Yes, true, "")},
	{0x3f, 0, 0, 0, f(Yes, false, "")},
	{0x41, 0, 0, 0, f(Yes, true, "")},
	{0x51, 0, 0, 0, f(Yes, false, "")},
	{0x52, 0, 0, 0, f(Yes, true, "")},
	{0x5b, 0, 0, 0, f(Yes, false, "")},
	{0x61, 0, 0, 0, f(Yes, true, "")},
	{0x71, 0, 0, 0, f(Yes, false, "")},
	{0x72, 0, 0, 0, f(Yes, true, "")},
	{0x7b, 0, 0, 0, f(Yes, false, "")},
	{0xa0, 0, 0, 0, g(Yes, No, false, false, "", " ")},
	{0xa1, 0, 0, 0, f(Yes, false, "")},
	{0xa8, 0, 0, 1, g(Yes, No, true, false, "", " ̈")},
	{0xa9, 0, 0, 0, f(Yes, false, "")},
	{0xaa, 0, 0, 0, g(Yes, No, false, false, "", "a")},
	{0xab, 0, 0, 0, f(Yes, false, "")},
	{0xaf, 0, 0, 1, g(Yes, No, false, false, "", " ̄")},
	{0xb0, 0, 0, 0, f(Yes, false, "")},
	{0xb2, 0, 0, 0, g(Yes, No, false, false, "", "2")},
	{0xb3, 0, 0, 0, g(Yes, No, false, false, "", "3")},
	{0xb4, 0, 0, 1, g(Yes, No, false, false, "", " ́")},
	{0xb5, 0, 0, 0, g(Yes, No, false, false, "", "μ")},
	{0xb6, 0, 0, 0, f(Yes, false, "")},
	{0xb8, 0, 0, 1, g(Yes, No, false, false, "", " ̧")},
	{0xb9, 0, 0, 0, g(Yes, No, false, false, "", "1")},
	{0xba, 0, 0, 0, g(Yes, No, false, false, "", "o")},
	{0xbb, 0, 0, 0, f(Yes, false, "")},
	{0xbc, 0, 0, 0, g(Yes, No, false, false, "", "1⁄4")},
	{0xbd, 0, 0, 0, g(Yes, No, false, false, "", "1⁄2")},
	{0xbe, 0, 0, 0, g(Yes, No, false, false, "", "3⁄4")},
	{0xbf, 0, 0, 0, f(Yes, false, "")},
	{0xc0, 0, 0, 1, f(Yes, false, "À")},
	{0xc1, 0, 0, 1, f(Yes, false, "Á")},
	{0xc2, 0, 0, 1, f(Yes, true, "Â")},
	{0xc3, 0, 0, 1, f(Yes, false, "Ã")},
	{0xc4, 0, 0, 1, f(Yes, true, "Ä")},
	{0xc5, 0, 0, 1, f(Yes, true, "Å")},
	{0xc6, 0, 0, 0, f(Yes, true, "")},
	{0xc7, 0, 0, 1, f(Yes, true, "Ç")},
	{0xc8, 0, 0, 1, f(Yes, false, "È")},
	{0xc9, 0, 0, 1, f(Yes, false, "É")},
	{0xca, 0, 0, 1, f(Yes, true, "Ê")},
	{0xcb, 0, 0, 1, f(Yes, false, "Ë")},
	{0xcc, 0, 0, 1, f(Yes, false, "Ì")},
	{0xcd, 0, 0, 1, f(Yes, false, "Í")},
	{0xce, 0, 0, 1, f(Yes, false, "Î")},
	{0xcf, 0, 0, 1, f(Yes, true, "Ï")},
	{0xd0, 0, 0, 0, f(Yes, false, "")},
	{0xd1, 0, 0, 1, f(Yes, false, "Ñ")},
	{0xd2, 0, 0, 1, f(Yes, false, "Ò")},
	{0xd3, 0, 0, 1, f(Yes, false, "Ó")},
	{0xd4, 0, 0, 1, f(Yes, true, "Ô")},
	{0xd5, 0, 0, 1, f(Yes, true, "Õ")},
	{0xd6, 0, 0, 1, f(Yes, true, "Ö")},
	{0xd7, 0, 0, 0, f(Yes, false, "")},
	{0xd8, 0, 0, 0, f(Yes, true, "")},
	{0xd9, 0, 0, 1, f(Yes, false, "Ù")},
	{0xda, 0, 0, 1, f(Yes, false, "Ú")},
	{0xdb, 0, 0, 1, f(Yes, false, "Û")},
	{0xdc, 0, 0, 1, f(Yes, true, "Ü")},
	{0xdd, 0, 0, 1, f(Yes, false, "Ý")},
	{0xde, 0, 0, 0, f(Yes, false, "")},
	{0xe0, 0, 0, 1, f(Yes, false, "à")},
	{0xe1, 0, 0, 1, f(Yes, false, "á")},
	{0xe2, 0, 0, 1, f(Yes, true, "â")},
	{0xe3, 0, 0, 1, f(Yes, false, "ã")},
	{0xe4, 0, 0, 1, f(Yes, true, "ä")},
	{0xe5, 0, 0, 1, f(Yes, true, "å")},
	{0xe6, 0, 0, 0, f(Yes, true, "")},
	{0xe7, 0, 0, 1, f(Yes, true, "ç")},
	{0xe8, 0, 0, 1, f(Yes, false, "è")},
	{0xe9, 0, 0, 1, f(Yes, false, "é")},
	{0xea, 0, 0, 1, f(Yes, true, "ê")},
	{0xeb, 0, 0, 1, f(Yes, false, "ë")},
	{0xec, 0, 0, 1, f(Yes, false, "ì")},
	{0xed, 0, 0, 1, f(Yes, false, "í")},
	{0xee, 0, 0, 1, f(Yes, false, "î")},
	{0xef, 0, 0, 1, f(Yes, true, "ï")},
	{0xf0, 0, 0, 0, f(Yes, false, "")},
	{0xf1, 0, 0, 1, f(Yes, false, "ñ")},
	{0xf2, 0, 0, 1, f(Yes, false, "ò")},
	{0xf3, 0, 0, 1, f(Yes, false, "ó")},
	{0xf4, 0, 0, 1, f(Yes, true, "ô")},
	{0xf5, 0, 0, 1, f(Yes, true, "õ")},
	{0xf6, 0, 0, 1, f(Yes, true, "ö")},
	{0xf7, 0, 0, 0, f(Yes, false, "")},
	{0xf8, 0, 0, 0, f(Yes, true, "")},
	{0xf9, 0, 0, 1, f(Yes, false, "ù")},
	{0xfa, 0, 0, 1, f(Yes, false, "ú")},
	{0xfb, 0, 0, 1, f(Yes, false, "û")},
	{0xfc, 0, 0, 1, f(Yes, true, "ü")},
	{0xfd, 0, 0, 1, f(Yes, false, "ý")},
	{0xfe, 0, 0, 0, f(Yes, false, "")},
	{0xff, 0, 0, 1, f(Yes, false, "ÿ")},
	{0x100, 0, 0, 1, f(Yes, false, "Ā")},
	{0x101, 0, 0, 1, f(Yes, false, "ā")},
	{0x102, 0, 0, 1, f(Yes, true, "Ă")},
	{0x103, 0, 0, 1, f(Yes, true, "ă")},
	{0x104, 0, 0, 1, f(Yes, false, "Ą")},
	{0x105, 0, 0, 1, f(Yes, false, "ą")},
	{0x106, 0, 0, 1, f(Yes, false, "Ć")},
	{0x107, 0, 0, 1, f(Yes, false, "ć")},
	{0x108, 0, 0, 1, f(Yes, false, "Ĉ")},
	{0x109, 0, 0, 1, f(Yes, false, "ĉ")},
	{0x10a, 0, 0, 1, f(Yes, false, "Ċ")},
	{0x10b, 0, 0, 1, f(Yes, false, "ċ")},
	{0x10c, 0, 0, 1, f(Yes, false, "Č")},
	{0x10d, 0, 0, 1, f(Yes, false, "č")},
	{0x10e, 0, 0, 1, f(Yes, false, "Ď")},
	{0x10f, 0, 0, 1, f(Yes, false, "ď")},
	{0x110, 0, 0, 0, f(Yes, false, "")},
	{0x112, 0, 0, 1, f(Yes, true, "Ē")},
	{0x113, 0, 0, 1, f(Yes, true, "ē")},
	{0x114, 0, 0, 1, f(Yes, false, "Ĕ")},
	{0x115, 0, 0, 1, f(Yes, false, "ĕ")},
	{0x116, 0, 0, 1, f(Yes, false, "Ė")},
	{0x117, 0, 0, 1, f(Yes, false, "ė")},
	{0x118, 0, 0, 1, f(Yes, false, "Ę")},
	{0x119, 0, 0, 1, f(Yes, false, "ę")},
	{0x11a, 0, 0, 1, f(Yes, false, "Ě")},
	{0x11b, 0, 0, 1, f(Yes, false, "ě")},
	{0x11c, 0, 0, 1, f(Yes, false, "Ĝ")},
	{0x11d, 0, 0, 1, f(Yes, false, "ĝ")},
	{0x11e, 0, 0, 1, f(Yes, false, "Ğ")},
	{0x11f, 0, 0, 1, f(Yes, false, "ğ")},
	{0x120, 0, 0, 1, f(Yes, false, "Ġ")},
	{0x121, 0, 0, 1, f(Yes, false, "ġ")},
	{0x122, 0, 0, 1, f(Yes, false, "Ģ")},
	{0x123, 0, 0, 1, f(Yes, false, "ģ")},
	{0x124, 0, 0, 1, f(Yes, false, "Ĥ")},
	{0x125, 0, 0, 1, f(Yes, false, "ĥ")},
	{0x126, 0, 0, 0, f(Yes, false, "")},
	{0x128, 0, 0, 1, f(Yes, false, "Ĩ")},
	{0x129, 0, 0, 1, f(Yes, false, "ĩ")},
	{0x12a, 0, 0, 1, f(Yes, false, "Ī")},
	{0x12b, 0, 0, 1, f(Yes, false, "ī")},
	{0x12c, 0, 0, 1, f(Yes, false, "Ĭ")},
	{0x12d, 0, 0, 1, f(Yes, false, "ĭ")},
	{0x12e, 0, 0, 1, f(Yes, false, "Į")},
	{0x12f, 0, 0, 1, f(Yes, false, "į")},
	{0x130, 0, 0, 1, f(Yes, false, "İ")},
	{0x131, 0, 0, 0, f(Yes, false, "")},
	{0x132, 0, 0, 0, g(Yes, No, false, false, "", "IJ")},
	{0x133, 0, 0, 0, g(Yes, No, false, false, "", "ij")},
	{0x134, 0, 0, 1, f(Yes, false, "Ĵ")},
	{0x135, 0, 0, 1, f(Yes, false, "ĵ")},
	{0x136, 0, 0, 1, f(Yes, false, "Ķ")},
	{0x137, 0, 0, 1, f(Yes, false, "ķ")},
	{0x138, 0, 0, 0, f(Yes, false, "")},
	{0x139, 0, 0, 1, f(Yes, false, "Ĺ")},
	{0x13a, 0, 0, 1, f(Yes, false, "ĺ")},
	{0x13b, 0, 0, 1, f(Yes, false, "Ļ")},
	{0x13c, 0, 0, 1, f(Yes, false, "ļ")},
	{0x13d, 0, 0, 1, f(Yes, false, "Ľ")},
	{0x13e, 0, 0, 1, f(Yes, false, "ľ")},
	{0x13f, 0, 0, 0, g(Yes, No, false, false, "", "L·")},
	{0x140, 0, 0, 0, g(Yes, No, false, false, "", "l·")},
	{0x141, 0, 0, 0, f(Yes, false, "")},
	{0x143, 0, 0, 1, f(Yes, false, "Ń")},
	{0x144, 0, 0, 1, f(Yes, false, "ń")},
	{0x145, 0, 0, 1, f(Yes, false, "Ņ")},
	{0x146, 0, 0, 1, f(Yes, false, "ņ")},
	{0x147, 0, 0, 1, f(Yes, false, "Ň")},
	{0x148, 0, 0, 1, f(Yes, false, "ň")},
	{0x149, 0, 0, 0, g(Yes, No, false, false, "", "ʼn")},
	{0x14a, 0, 0, 0, f(Yes, false, "")},
	{0x14c, 0, 0, 1, f(Yes, true, "Ō")},
	{0x14d, 0, 0, 1, f(Yes, true, "ō")},
	{0x14e, 0, 0, 1, f(Yes, false, "Ŏ")},
	{0x14f, 0, 0, 1, f(Yes, false, "ŏ")},
	{0x150, 0, 0, 1, f(Yes, false, "Ő")},
	{0x151, 0, 0, 1, f(Yes, false, "ő")},
	{0x152, 0, 0, 0, f(Yes, false, "")},
	{0x154, 0, 0, 1, f(Yes, false, "Ŕ")},
	{0x155, 0, 0, 1, f(Yes, false, "ŕ")},
	{0x156, 0, 0, 1, f(Yes, false, "Ŗ")},
	{0x157, 0, 0, 1, f(Yes, false, "ŗ")},
	{0x158, 0, 0, 1, f(Yes, false, "Ř")},
	{0x159, 0, 0, 1, f(Yes, false, "ř")},
	{0x15a, 0, 0, 1, f(Yes, true, "Ś")},
	{0x15b, 0, 0, 1, f(Yes, true, "ś")},
	{0x15c, 0, 0, 1, f(Yes, false, "Ŝ")},
	{0x15d, 0, 0, 1, f(Yes, false, "ŝ")},
	{0x15e, 0, 0, 1, f(Yes, false, "Ş")},
	{0x15f, 0, 0, 1, f(Yes, false, "ş")},
	{0x160, 0, 0, 1, f(Yes, true, "Š")},
	{0x161, 0, 0, 1, f(Yes, true, "š")},
	{0x162, 0, 0, 1, f(Yes, false, "Ţ")},
	{0x163, 0, 0, 1, f(Yes, false, "ţ")},
	{0x164, 0, 0, 1, f(Yes, false, "Ť")},
	{0x165, 0, 0, 1, f(Yes, false, "ť")},
	{0x166, 0, 0, 0, f(Yes, false, "")},
	{0x168, 0, 0, 1, f(Yes, true, "Ũ")},
	{0x169, 0, 0, 1, f(Yes, true, "ũ")},
	{0x16a, 0, 0, 1, f(Yes, true, "Ū")},
	{0x16b, 0, 0, 1, f(Yes, true, "ū")},
	{0x16c, 0, 0, 1, f(Yes, false, "Ŭ")},
	{0x16d, 0, 0, 1, f(Yes, false, "ŭ")},
	{0x16e, 0, 0, 1, f(Yes, false, "Ů")},
	{0x16f, 0, 0, 1, f(Yes, false, "ů")},
	{0x170, 0, 0, 1, f(Yes, false, "Ű")},
	{0x171, 0, 0, 1, f(Yes, false, "ű")},
	{0x172, 0, 0, 1, f(Yes, false, "Ų")},
	{0x173, 0, 0, 1, f(Yes, false, "ų")},
	{0x174, 0, 0, 1, f(Yes, false, "Ŵ")},
	{0x175, 0, 0, 1, f(Yes, false, "ŵ")},
	{0x176, 0, 0, 1, f(Yes, false, "Ŷ")},
	{0x177, 0, 0, 1, f(Yes, false, "ŷ")},
	{0x178, 0, 0, 1, f(Yes, false, "Ÿ")},
	{0x179, 0, 0, 1, f(Yes, false, "Ź")},
	{0x17a, 0, 0, 1, f(Yes, false, "ź")},
	{0x17b, 0, 0, 1, f(Yes, false, "Ż")},
	{0x17c, 0, 0, 1, f(Yes, false, "ż")},
	{0x17d, 0, 0, 1, f(Yes, false, "Ž")},
	{0x17e, 0, 0, 1, f(Yes, false, "ž")},
	{0x17f, 0, 0, 0, g(Yes, No, true, false, "", "s")},
	{0x180, 0, 0, 0, f(Yes, false, "")},
	{0x1a0, 0, 0, 1, f(Yes, true, "Ơ")},
	{0x1a1, 0, 0, 1, f(Yes, true, "ơ")},
	{0x1a2, 0, 0, 0, f(Yes, false, "")},
	{0x1af, 0, 0, 1, f(Yes, true, "Ư")},
	{0x1b0, 0, 0, 1, f(Yes, true, "ư")},
	{0x1b1, 0, 0, 0, f(Yes, false, "")},
	{0x1b7, 0, 0, 0, f(Yes, true, "")},
	{0x1b8, 0, 0, 0, f(Yes, false, "")},
	{0x1c4, 0, 0, 1, g(Yes, No, false, false, "", "DŽ")},
	{0x1c5, 0, 0, 1, g(Yes, No, false, false, "", "Dž")},
	{0x1c6, 0, 0, 1, g(Yes, No, false, false, "", "dž")},
	{0x1c7, 0, 0, 0, g(Yes, No, false, false, "", "LJ")},
	{0x1c8, 0, 0, 0, g(Yes, No, false, false, "", "Lj")},
	{0x1c9, 0, 0, 0, g(Yes, No, false, false, "", "lj")},
	{0x1ca, 0, 0, 0, g(Yes, No, false, false, "", "NJ")},
	{0x1cb, 0, 0, 0, g(Yes, No, false, false, "", "Nj")},
	{0x1cc, 0, 0, 0, g(Yes, No, false, false, "", "nj")},
	{0x1cd, 0, 0, 1, f(Yes, false, "Ǎ")},
	{0x1ce, 0, 0, 1, f(Yes, false, "ǎ")},
	{0x1cf, 0, 0, 1, f(Yes, false, "Ǐ")},
	{0x1d0, 0, 0, 1, f(Yes, false, "ǐ")},
	{0x1d1, 0, 0, 1, f(Yes, false, "Ǒ")},
	{0x1d2, 0, 0, 1, f(Yes, false, "ǒ")},
	{0x1d3, 0, 0, 1, f(Yes, false, "Ǔ")},
	{0x1d4, 0, 0, 1, f(Yes, false, "ǔ")},
	{0x1d5, 0, 0, 2, f(Yes, false, "Ǖ")},
	{0x1d6, 0, 0, 2, f(Yes, false, "ǖ")},
	{0x1d7, 0, 0, 2, f(Yes, false, "Ǘ")},
	{0x1d8, 0, 0, 2, f(Yes, false, "ǘ")},
	{0x1d9, 0, 0, 2, f(Yes, false, "Ǚ")},
	{0x1da, 0, 0, 2, f(Yes, false, "ǚ")},
	{0x1db, 0, 0, 2, f(Yes, false, "Ǜ")},
	{0x1dc, 0, 0, 2, f(Yes, false, "ǜ")},
	{0x1dd, 0, 0, 0, f(Yes, false, "")},
	{0x1de, 0, 0, 2, f(Yes, false, "Ǟ")},
	{0x1df, 0, 0, 2, f(Yes, false, "ǟ")},
	{0x1e0, 0, 0, 2, f(Yes, false, "Ǡ")},
	{0x1e1, 0, 0, 2, f(Yes, false, "ǡ")},
	{0x1e2, 0, 0, 1, f(Yes, false, "Ǣ")},
	{0x1e3, 0, 0, 1, f(Yes, false, "ǣ")},
	{0x1e4, 0, 0, 0, f(Yes, false, "")},
	{0x1e6, 0, 0, 1, f(Yes, false, "Ǧ")},
	{0x1e7, 0, 0, 1, f(Yes, false, "ǧ")},
	{0x1e8, 0, 0, 1, f(Yes, false, "Ǩ")},
	{0x1e9, 0, 0, 1, f(Yes, false, "ǩ")},
	{0x1ea, 0, 0, 1, f(Yes, true, "Ǫ")},
	{0x1eb, 0, 0, 1, f(Yes, true, "ǫ")},
	{0x1ec, 0, 0, 2, f(Yes, false, "Ǭ")},
	{0x1ed, 0, 0, 2, f(Yes, false, "ǭ")},
	{0x1ee, 0, 0, 1, f(Yes, false, "Ǯ")},
	{0x1ef, 0, 0, 1, f(Yes, false, "ǯ")},
	{0x1f0, 0, 0, 1, f(Yes, false, "ǰ")},
	{0x1f1, 0, 0, 0, g(Yes, No, false, false, "", "DZ")},
	{0x1f2, 0, 0, 0, g(Yes, No, false, false, "", "Dz")},
	{0x1f3, 0, 0, 0, g(Yes, No, false, false, "", "dz")},
	{0x1f4, 0, 0, 1, f(Yes, false, "Ǵ")},
	{0x1f5, 0, 0, 1, f(Yes, false, "ǵ")},
	{0x1f6, 0, 0, 0, f(Yes, false, "")},
	{0x1f8, 0, 0, 1, f(Yes, false, "Ǹ")},
	{0x1f9, 0, 0, 1, f(Yes, false, "ǹ")},
	{0x1fa, 0, 0, 2, f(Yes, false, "Ǻ")},
	{0x1fb, 0, 0, 2, f(Yes, false, "ǻ")},
	{0x1fc, 0, 0, 1, f(Yes, false, "Ǽ")},
	{0x1fd, 0, 0, 1, f(Yes, false, "ǽ")},
	{0x1fe, 0, 0, 1, f(Yes, false, "Ǿ")},
	{0x1ff, 0, 0, 1, f(Yes, false, "ǿ")},
	{0x200, 0, 0, 1, f(Yes, false, "Ȁ")},
	{0x201, 0, 0, 1, f(Yes, false, "ȁ")},
	{0x202, 0, 0, 1, f(Yes, false, "Ȃ")},
	{0x203, 0, 0, 1, f(Yes, false, "ȃ")},
	{0x204, 0, 0, 1, f(Yes, false, "Ȅ")},
	{0x205, 0, 0, 1, f(Yes, false, "ȅ")},
	{0x206, 0, 0, 1, f(Yes, false, "Ȇ")},
	{0x207, 0, 0, 1, f(Yes, false, "ȇ")},
	{0x208, 0, 0, 1, f(Yes, false, "Ȉ")},
	{0x209, 0, 0, 1, f(Yes, false, "ȉ")},
	{0x20a, 0, 0, 1, f(Yes, false, "Ȋ")},
	{0x20b, 0, 0, 1, f(Yes, false, "ȋ")},
	{0x20c, 0, 0, 1, f(Yes, false, "Ȍ")},
	{0x20d, 0, 0, 1, f(Yes, false, "ȍ")},
	{0x20e, 0, 0, 1, f(Yes, false, "Ȏ")},
	{0x20f, 0, 0, 1, f(Yes, false, "ȏ")},
	{0x210, 0, 0, 1, f(Yes, false, "Ȑ")},
	{0x211, 0, 0, 1, f(Yes, false, "ȑ")},
	{0x212, 0, 0, 1, f(Yes, false, "Ȓ")},
	{0x213, 0, 0, 1, f(Yes, false, "ȓ")},
	{0x214, 0, 0, 1, f(Yes, false, "Ȕ")},
	{0x215, 0, 0, 1, f(Yes, false, "ȕ")},
	{0x216, 0, 0, 1, f(Yes, false, "Ȗ")},
	{0x217, 0, 0, 1, f(Yes, false, "ȗ")},
	{0x218, 0, 0, 1, f(Yes, false, "Ș")},
	{0x219, 0, 0, 1, f(Yes, false, "ș")},
	{0x21a, 0, 0, 1, f(Yes, false, "Ț")},
	{0x21b, 0, 0, 1, f(Yes, false, "ț")},
	{0x21c, 0, 0, 0, f(Yes, false, "")},
	{0x21e, 0, 0, 1, f(Yes, false, "Ȟ")},
	{0x21f, 0, 0, 1, f(Yes, false, "ȟ")},
	{0x220, 0, 0, 0, f(Yes, false, "")},
	{0x226, 0, 0, 1, f(Yes, true, "Ȧ")},
	{0x227, 0, 0, 1, f(Yes, true, "ȧ")},
	{0x228, 0, 0, 1, f(Yes, true, "Ȩ")},
	{0x229, 0, 0, 1, f(Yes, true, "ȩ")},
	{0x22a, 0, 0, 2, f(Yes, false, "Ȫ")},
	{0x22b, 0, 0, 2, f(Yes, false, "ȫ")},
	{0x22c, 0, 0, 2, f(Yes, false, "Ȭ")},
	{0x22d, 0, 0, 2, f(Yes, false, "ȭ")},
	{0x22e, 0, 0, 1, f(Yes, true, "Ȯ")},
	{0x22f, 0, 0, 1, f(Yes, true, "ȯ")},
	{0x230, 0, 0, 2, f(Yes, false, "Ȱ")},
	{0x231, 0, 0, 2, f(Yes, false, "ȱ")},
	{0x232, 0, 0, 1, f(Yes, false, "Ȳ")},
	{0x233, 0, 0, 1, f(Yes, false, "ȳ")},
	{0x234, 0, 0, 0, f(Yes, false, "")},
	{0x292, 0, 0, 0, f(Yes, true, "")},
	{0x293, 0, 0, 0, f(Yes, false, "")},
	{0x2b0, 0, 0, 0, g(Yes, No, false, false, "", "h")},
	{0x2b1, 0, 0, 0, g(Yes, No, false, false, "", "ɦ")},
	{0x2b2, 0, 0, 0, g(Yes, No, false, false, "", "j")},
	{0x2b3, 0, 0, 0, g(Yes, No, false, false, "", "r")},
	{0x2b4, 0, 0, 0, g(Yes, No, false, false, "", "ɹ")},
	{0x2b5, 0, 0, 0, g(Yes, No, false, false, "", "ɻ")},
	{0x2b6, 0, 0, 0, g(Yes, No, false, false, "", "ʁ")},
	{0x2b7, 0, 0, 0, g(Yes, No, false, false, "", "w")},
	{0x2b8, 0, 0, 0, g(Yes, No, false, false, "", "y")},
	{0x2b9, 0, 0, 0, f(Yes, false, "")},
	{0x2d8, 0, 0, 1, g(Yes, No, false, false, "", " ̆")},
	{0x2d9, 0, 0, 1, g(Yes, No, false, false, "", " ̇")},
	{0x2da, 0, 0, 1, g(Yes, No, false, false, "", " ̊")},
	{0x2db, 0, 0, 1, g(Yes, No, false, false, "", " ̨")},
	{0x2dc, 0, 0, 1, g(Yes, No, false, false, "", " ̃")},
	{0x2dd, 0, 0, 1, g(Yes, No, false, false, "", " ̋")},
	{0x2de, 0, 0, 0, f(Yes, false, "")},
	{0x2e0, 0, 0, 0, g(Yes, No, false, false, "", "ɣ")},
	{0x2e1, 0, 0, 0, g(Yes, No, false, false, "", "l")},
	{0x2e2, 0, 0, 0, g(Yes, No, false, false, "", "s")},
	{0x2e3, 0, 0, 0, g(Yes, No, false, false, "", "x")},
	{0x2e4, 0, 0, 0, g(Yes, No, false, false, "", "ʕ")},
	{0x2e5, 0, 0, 0, f(Yes, false, "")},
	{0x300, 230, 1, 1, f(Maybe, false, "")},
	{0x305, 230, 1, 1, f(Yes, false, "")},
	{0x306, 230, 1, 1, f(Maybe, false, "")},
	{0x30d, 230, 1, 1, f(Yes, false, "")},
	{0x30f, 230, 1, 1, f(Maybe, false, "")},
	{0x310, 230, 1, 1, f(Yes, false, "")},
	{0x311, 230, 1, 1, f(Maybe, false, "")},
	{0x312, 230, 1, 1, f(Yes, false, "")},
	{0x313, 230, 1, 1, f(Maybe, false, "")},
	{0x315, 232, 1, 1, f(Yes, false, "")},
	{0x316, 220, 1, 1, f(Yes, false, "")},
	{0x31a, 232, 1, 1, f(Yes, false, "")},
	{0x31b, 216, 1, 1, f(Maybe, false, "")},
	{0x31c, 220, 1, 1, f(Yes, false, "")},
	{0x321, 202, 1, 1, f(Yes, false, "")},
	{0x323, 220, 1, 1, f(Maybe, false, "")},
	{0x327, 202, 1, 1, f(Maybe, false, "")},
	{0x329, 220, 1, 1, f(Yes, false, "")},
	{0x32d, 220, 1, 1, f(Maybe, false, "")},
	{0x32f, 220, 1, 1, f(Yes, false, "")},
	{0x330, 220, 1, 1, f(Maybe, false, "")},
	{0x332, 220, 1, 1, f(Yes, false, "")},
	{0x334, 1, 1, 1, f(Yes, false, "")},
	{0x338, 1, 1, 1, f(Maybe, false, "")},
	{0x339, 220, 1, 1, f(Yes, false, "")},
	{0x33d, 230, 1, 1, f(Yes, false, "")},
	{0x340, 230, 1, 1, f(No, false, "̀")},
	{0x341, 230, 1, 1, f(No, false, "́")},
	{0x342, 230, 1, 1, f(Maybe, false, "")},
	{0x343, 230, 1, 1, f(No, false, "̓")},
	{0x344, 230, 2, 2, f(No, false, "̈́")},
	{0x345, 240, 1, 1, f(Maybe, false, "")},
	{0x346, 230, 1, 1, f(Yes, false, "")},
	{0x347, 220, 1, 1, f(Yes, false, "")},
	{0x34a, 230, 1, 1, f(Yes, false, "")},
	{0x34d, 220, 1, 1, f(Yes, false, "")},
	{0x34f, 0, 0, 0, f(Yes, false, "")},
	{0x350, 230, 1, 1, f(Yes, false, "")},
	{0x353, 220, 1, 1, f(Yes, false, "")},
	{0x357, 230, 1, 1, f(Yes, false, "")},
	{0x358, 232, 1, 1, f(Yes, false, "")},
	{0x359, 220, 1, 1, f(Yes, false, "")},
	{0x35b, 230, 1, 1, f(Yes, false, "")},
	{0x35c, 233, 1, 1, f(Yes, false, "")},
	{0x35d, 234, 1, 1, f(Yes, false, "")},
	{0x35f, 233, 1, 1, f(Yes, false, "")},
	{0x360, 234, 1, 1, f(Yes, false, "")},
	{0x362, 233, 1, 1, f(Yes, false, "")},
	{0x363, 230, 1, 1, f(Yes, false, "")},
	{0x370, 0, 0, 0, f(Yes, false, "")},
	{0x374, 0, 0, 0, f(No, false, "ʹ")},
	{0x375, 0, 0, 0, f(Yes, false, "")},
	{0x37a, 0, 0, 1, g(Yes, No, false, false, "", " ͅ")},
	{0x37b, 0, 0, 0, f(Yes, false, "")},
	{0x37e, 0, 0, 0, f(No, false, ";")},
	{0x37f, 0, 0, 0, f(Yes, false, "")},
	{0x384, 0, 0, 1, g(Yes, No, false, false, "", " ́")},
	{0x385, 0, 0, 2, g(Yes, No, false, false, "΅", " ̈́")},
	{0x386, 0, 0, 1, f(Yes, false, "Ά")},
	{0x387, 0, 0, 0, f(No, false, "·")},
	{0x388, 0, 0, 1, f(Yes, false, "Έ")},
	{0x389, 0, 0, 1, f(Yes, false, "Ή")},
	{0x38a, 0, 0, 1, f(Yes, false, "Ί")},
	{0x38b, 0, 0, 0, f(Yes, false, "")},
	{0x38c, 0, 0, 1, f(Yes, false, "Ό")},
	{0x38d, 0, 0, 0, f(Yes, false, "")},
	{0x38e, 0, 0, 1, f(Yes, false, "Ύ")},
	{0x38f, 0, 0, 1, f(Yes, false, "Ώ")},
	{0x390, 0, 0, 2, f(Yes, false, "ΐ")},
	{0x391, 0, 0, 0, f(Yes, true, "")},
	{0x392, 0, 0, 0, f(Yes, false, "")},
	{0x395, 0, 0, 0, f(Yes, true, "")},
	{0x396, 0, 0, 0, f(Yes, false, "")},
	{0x397, 0, 0, 0, f(Yes, true, "")},
	{0x398, 0, 0, 0, f(Yes, false, "")},
	{0x399, 0, 0, 0, f(Yes, true, "")},
	{0x39a, 0, 0, 0, f(Yes, false, "")},
	{0x39f, 0, 0, 0, f(Yes, true, "")},
	{0x3a0, 0, 0, 0, f(Yes, false, "")},
	{0x3a1, 0, 0, 0, f(Yes, true, "")},
	{0x3a2, 0, 0, 0, f(Yes, false, "")},
	{0x3a5, 0, 0, 0, f(Yes, true, "")},
	{0x3a6, 0, 0, 0, f(Yes, false, "")},
	{0x3a9, 0, 0, 0, f(Yes, true, "")},
	{0x3aa, 0, 0, 1, f(Yes, false, "Ϊ")},
	{0x3ab, 0, 0, 1, f(Yes, false, "Ϋ")},
	{0x3ac, 0, 0, 1, f(Yes, true, "ά")},
	{0x3ad, 0, 0, 1, f(Yes, false, "έ")},
	{0x3ae, 0, 0, 1, f(Yes, true, "ή")},
	{0x3af, 0, 0, 1, f(Yes, false, "ί")},
	{0x3b0, 0, 0, 2, f(Yes, false, "ΰ")},
	{0x3b1, 0, 0, 0, f(Yes, true, "")},
	{0x3b2, 0, 0, 0, f(Yes, false, "")},
	{0x3b5, 0, 0, 0, f(Yes, true, "")},
	{0x3b6, 0, 0, 0, f(Yes, false, "")},
	{0x3b7, 0, 0, 0, f(Yes, true, "")},
	{0x3b8, 0, 0, 0, f(Yes, false, "")},
	{0x3b9, 0, 0, 0, f(Yes, true, "")},
	{0x3ba, 0, 0, 0, f(Yes, false, "")},
	{0x3bf, 0, 0, 0, f(Yes, true, "")},
	{0x3c0, 0, 0, 0, f(Yes, false, "")},
	{0x3c1, 0, 0, 0, f(Yes, true, "")},
	{0x3c2, 0, 0, 0, f(Yes, false, "")},
	{0x3c5, 0, 0, 0, f(Yes, true, "")},
	{0x3c6, 0, 0, 0, f(Yes, false, "")},
	{0x3c9, 0, 0, 0, f(Yes, true, "")},
	{0x3ca, 0, 0, 1, f(Yes, true, "ϊ")},
	{0x3cb, 0, 0, 1, f(Yes, true, "ϋ")},
	{0x3cc, 0, 0, 1, f(Yes, false, "ό")},
	{0x3cd, 0, 0, 1, f(Yes, false, "ύ")},
	{0x3ce, 0, 0, 1, f(Yes, true, "ώ")},
	{0x3cf, 0, 0, 0, f(Yes, false, "")},
	{0x3d0, 0, 0, 0, g(Yes, No, false, false, "", "β")},
	{0x3d1, 0, 0, 0, g(Yes, No, false, false, "", "θ")},
	{0x3d2, 0, 0, 0, g(Yes, No, true, false, "", "Υ")},
	{0x3d3, 0, 0, 1, g(Yes, No, false, false, "ϓ", "Ύ")},
	{0x3d4, 0, 0, 1, g(Yes, No, false, false, "ϔ", "Ϋ")},
	{0x3d5, 0, 0, 0, g(Yes, No, false, false, "", "φ")},
	{0x3d6, 0, 0, 0, g(Yes, No, false, false, "", "π")},
	{0x3d7, 0, 0, 0, f(Yes, false, "")},
	{0x3f0, 0, 0, 0, g(Yes, No, false, false, "", "κ")},
	{0x3f1, 0, 0, 0, g(Yes, No, false, false, "", "ρ")},
	{0x3f2, 0, 0, 0, g(Yes, No, false, false, "", "ς")},
	{0x3f3, 0, 0, 0, f(Yes, false, "")},
	{0x3f4, 0, 0, 0, g(Yes, No, false, false, "", "Θ")},
	{0x3f5, 0, 0, 0, g(Yes, No, false, false, "", "ε")},
	{0x3f6, 0, 0, 0, f(Yes, false, "")},
	{0x3f9, 0, 0, 0, g(Yes, No, false, false, "", "Σ")},
	{0x3fa, 0, 0, 0, f(Yes, false, "")},
	{0x400, 0, 0, 1, f(Yes, false, "Ѐ")},
	{0x401, 0, 0, 1, f(Yes, false, "Ё")},
	{0x402, 0, 0, 0, f(Yes, false, "")},
	{0x403, 0, 0, 1, f(Yes, false, "Ѓ")},
	{0x404, 0, 0, 0, f(Yes, false, "")},
	{0x406, 0, 0, 0, f(Yes, true, "")},
	{0x407, 0, 0, 1, f(Yes, false, "Ї")},
	{0x408, 0, 0, 0, f(Yes, false, "")},
	{0x40c, 0, 0, 1, f(Yes, false, "Ќ")},
	{0x40d, 0, 0, 1, f(Yes, false, "Ѝ")},
	{0x40e, 0, 0, 1, f(Yes, false, "Ў")},
	{0x40f, 0, 0, 0, f(Yes, false, "")},
	{0x410, 0, 0, 0, f(Yes, true, "")},
	{0x411, 0, 0, 0, f(Yes, false, "")},
	{0x413, 0, 0, 0, f(Yes, true, "")},
	{0x414, 0, 0, 0, f(Yes, false, "")},
	{0x415, 0, 0, 0, f(Yes, true, "")},
	{0x419, 0, 0, 1, f(Yes, false, "Й")},
	{0x41a, 0, 0, 0, f(Yes, true, "")},
	{0x41b, 0, 0, 0, f(Yes, false, "")},
	{0x41e, 0, 0, 0, f(Yes, true, "")},
	{0x41f, 0, 0, 0, f(Yes, false, "")},
	{0x423, 0, 0, 0, f(Yes, true, "")},
	{0x424, 0, 0, 0, f(Yes, false, "")},
	{0x427, 0, 0, 0, f(Yes, true, "")},
	{0x428, 0, 0, 0, f(Yes, false, "")},
	{0x42b, 0, 0, 0, f(Yes, true, "")},
	{0x42c, 0, 0, 0, f(Yes, false, "")},
	{0x42d, 0, 0, 0, f(Yes, true, "")},
	{0x42e, 0, 0, 0, f(Yes, false, "")},
	{0x430, 0, 0, 0, f(Yes, true, "")},
	{0x431, 0, 0, 0, f(Yes, false, "")},
	{0x433, 0, 0, 0, f(Yes, true, "")},
	{0x434, 0, 0, 0, f(Yes, false, "")},
	{0x435, 0, 0, 0, f(Yes, true, "")},
	{0x439, 0, 0, 1, f(Yes, false, "й")},
	{0x43a, 0, 0, 0, f(Yes, true, "")},
	{0x43b, 0, 0, 0, f(Yes, false, "")},
	{0x43e, 0, 0, 0, f(Yes, true, "")},
	{0x43f, 0, 0, 0, f(Yes, false, "")},
	{0x443, 0, 0, 0, f(Yes, true, "")},
	{0x444, 0, 0, 0, f(Yes, false, "")},
	{0x447, 0, 0, 0, f(Yes, true, "")},
	{0x448, 0, 0, 0, f(Yes, false, "")},
	{0x44b, 0, 0, 0, f(Yes, true, "")},
	{0x44c, 0, 0, 0, f(Yes, false, "")},
	{0x44d, 0, 0, 0, f(Yes, true, "")},
	{0x44e, 0, 0, 0, f(Yes, false, "")},
	{0x450, 0, 0, 1, f(Yes, false, "ѐ")},
	{0x451, 0, 0, 1, f(Yes, false, "ё")},
	{0x452, 0, 0, 0, f(Yes, false, "")},
	{0x453, 0, 0, 1, f(Yes, false, "ѓ")},
	{0x454, 0, 0, 0, f(Yes, false, "")},
	{0x456, 0, 0, 0, f(Yes, true, "")},
	{0x457, 0, 0, 1, f(Yes, false, "ї")},
	{0x458, 0, 0, 0, f(Yes, false, "")},
	{0x45c, 0, 0, 1, f(Yes, false, "ќ")},
	{0x45d, 0, 0, 1, f(Yes, false, "ѝ")},
	{0x45e, 0, 0, 1, f(Yes, false, "ў")},
	{0x45f, 0, 0, 0, f(Yes, false, "")},
	{0x474, 0, 0, 0, f(Yes, true, "")},
	{0x476, 0, 0, 1, f(Yes, false, "Ѷ")},
	{0x477, 0, 0, 1, f(Yes, false, "ѷ")},
	{0x478, 0, 0, 0, f(Yes, false, "")},
	{0x483, 230, 1, 1, f(Yes, false, "")},
	{0x488, 0, 0, 0, f(Yes, false, "")},
	{0x4c1, 0, 0, 1, f(Yes, false, "Ӂ")},
	{0x4c2, 0, 0, 1, f(Yes, false, "ӂ")},
	{0x4c3, 0, 0, 0, f(Yes, false, "")},
	{0x4d0, 0, 0, 1, f(Yes, false, "Ӑ")},
	{0x4d1, 0, 0, 1, f(Yes, false, "ӑ")},
	{0x4d2, 0, 0, 1, f(Yes, false, "Ӓ")},
	{0x4d3, 0, 0, 1, f(Yes, false, "ӓ")},
	{0x4d4, 0, 0, 0, f(Yes, false, "")},
	{0x4d6, 0, 0, 1, f(Yes, false, "Ӗ")},
	{0x4d7, 0, 0, 1, f(Yes, false, "ӗ")},
	{0x4d8, 0, 0, 0, f(Yes, true, "")},
	{0x4da, 0, 0, 1, f(Yes, false, "Ӛ")},
	{0x4db, 0, 0, 1, f(Yes, false, "ӛ")},
	{0x4dc, 0, 0, 1, f(Yes, false, "Ӝ")},
	{0x4dd, 0, 0, 1, f(Yes, false, "ӝ")},
	{0x4de, 0, 0, 1, f(Yes, false, "Ӟ")},
	{0x4df, 0, 0, 1, f(Yes, false, "ӟ")},
	{0x4e0, 0, 0, 0, f(Yes, false, "")},
	{0x4e2, 0, 0, 1, f(Yes, false, "Ӣ")},
	{0x4e3, 0, 0, 1, f(Yes, false, "ӣ")},
	{0x4e4, 0, 0, 1, f(Yes, false, "Ӥ")},
	{0x4e5, 0, 0, 1, f(Yes, false, "ӥ")},
	{0x4e6, 0, 0, 1, f(Yes, false, "Ӧ")},
	{0x4e7, 0, 0, 1, f(Yes, false, "ӧ")},
	{0x4e8, 0, 0, 0, f(Yes, true, "")},
	{0x4ea, 0, 0, 1, f(Yes, false, "Ӫ")},
	{0x4eb, 0, 0, 1, f(Yes, false, "ӫ")},
	{0x4ec, 0, 0, 1, f(Yes, false, "Ӭ")},
	{0x4ed, 0, 0, 1, f(Yes, false, "ӭ")},
	{0x4ee, 0, 0, 1, f(Yes, false, "Ӯ")},
	{0x4ef, 0, 0, 1, f(Yes, false, "ӯ")},
	{0x4f0, 0, 0, 1, f(Yes, false, "Ӱ")},
	{0x4f1, 0, 0, 1, f(Yes, false, "ӱ")},
	{0x4f2, 0, 0, 1, f(Yes, false, "Ӳ")},
	{0x4f3, 0, 0, 1, f(Yes, false, "ӳ")},
	{0x4f4, 0, 0, 1, f(Yes, false, "Ӵ")},
	{0x4f5, 0, 0, 1, f(Yes, false, "ӵ")},
	{0x4f6, 0, 0, 0, f(Yes, false, "")},
	{0x4f8, 0, 0, 1, f(Yes, false, "Ӹ")},
	{0x4f9, 0, 0, 1, f(Yes, false, "ӹ")},
	{0x4fa, 0, 0, 0, f(Yes, false, "")},
	{0x587, 0, 0, 0, g(Yes, No, false, false, "", "եւ")},
	{0x588, 0, 0, 0, f(Yes, false, "")},
	{0x591, 220, 1, 1, f(Yes, false, "")},
	{0x592, 230, 1, 1, f(Yes, false, "")},
	{0x596, 220, 1, 1, f(Yes, false, "")},
	{0x597, 230, 1, 1, f(Yes, false, "")},
	{0x59a, 222, 1, 1, f(Yes, false, "")},
	{0x59b, 220, 1, 1, f(Yes, false, "")},
	{0x59c, 230, 1, 1, f(Yes, false, "")},
	{0x5a2, 220, 1, 1, f(Yes, false, "")},
	{0x5a8, 230, 1, 1, f(Yes, false, "")},
	{0x5aa, 220, 1, 1, f(Yes, false, "")},
	{0x5ab, 230, 1, 1, f(Yes, false, "")},
	{0x5ad, 222, 1, 1, f(Yes, false, "")},
	{0x5ae, 228, 1, 1, f(Yes, false, "")},
	{0x5af, 230, 1, 1, f(Yes, false, "")},
	{0x5b0, 10, 1, 1, f(Yes, false, "")},
	{0x5b1, 11, 1, 1, f(Yes, false, "")},
	{0x5b2, 12, 1, 1, f(Yes, false, "")},
	{0x5b3, 13, 1, 1, f(Yes, false, "")},
	{0x5b4, 14, 1, 1, f(Yes, false, "")},
	{0x5b5, 15, 1, 1, f(Yes, false, "")},
	{0x5b6, 16, 1, 1, f(Yes, false, "")},
	{0x5b7, 17, 1, 1, f(Yes, false, "")},
	{0x5b8, 18, 1, 1, f(Yes, false, "")},
	{0x5b9, 19, 1, 1, f(Yes, false, "")},
	{0x5bb, 20, 1, 1, f(Yes, false, "")},
	{0x5bc, 21, 1, 1, f(Yes, false, "")},
	{0x5bd, 22, 1, 1, f(Yes, false, "")},
	{0x5be, 0, 0, 0, f(Yes, false, "")},
	{0x5bf, 23, 1, 1, f(Yes, false, "")},
	{0x5c0, 0, 0, 0, f(Yes, false, "")},
	{0x5c1, 24, 1, 1, f(Yes, false, "")},
	{0x5c2, 25, 1, 1, f(Yes, false, "")},
	{0x5c3, 0, 0, 0, f(Yes, false, "")},
	{0x5c4, 230, 1, 1, f(Yes, false, "")},
	{0x5c5, 220, 1, 1, f(Yes, false, "")},
	{0x5c6, 0, 0, 0, f(Yes, false, "")},
	{0x5c7, 18, 1, 1, f(Yes, false, "")},
	{0x5c8, 0, 0, 0, f(Yes, false, "")},
	{0x610, 230, 1, 1, f(Yes, false, "")},
	{0x618, 30, 1, 1, f(Yes, false, "")},
	{0x619, 31, 1, 1, f(Yes, false, "")},
	{0x61a, 32, 1, 1, f(Yes, false, "")},
	{0x61b, 0, 0, 0, f(Yes, false, "")},
	{0x622, 0, 0, 1, f(Yes, false, "آ")},
	{0x623, 0, 0, 1, f(Yes, false, "أ")},
	{0x624, 0, 0, 1, f(Yes, false, "ؤ")},
	{0x625, 0, 0, 1, f(Yes, false, "إ")},
	{0x626, 0, 0, 1, f(Yes, false, "ئ")},
	{0x627, 0, 0, 0, f(Yes, true, "")},
	{0x628, 0, 0, 0, f(Yes, false, "")},
	{0x648, 0, 0, 0, f(Yes, true, "")},
	{0x649, 0, 0, 0, f(Yes, false, "")},
	{0x64a, 0, 0, 0, f(Yes, true, "")},
	{0x64b, 27, 1, 1, f(Yes, false, "")},
	{0x64c, 28, 1, 1, f(Yes, false, "")},
	{0x64d, 29, 1, 1, f(Yes, false, "")},
	{0x64e, 30, 1, 1, f(Yes, false, "")},
	{0x64f, 31, 1, 1, f(Yes, false, "")},
	{0x650, 32, 1, 1, f(Yes, false, "")},
	{0x651, 33, 1, 1, f(Yes, false, "")},
	{0x652, 34, 1, 1, f(Yes, false, "")},
	{0x653, 230, 1, 1, f(Maybe, false, "")},
	{0x655, 220, 1, 1, f(Maybe, false, "")},
	{0x656, 220, 1, 1, f(Yes, false, "")},
	{0x657, 230, 1, 1, f(Yes, false, "")},
	{0x65c, 220, 1, 1, f(Yes, false, "")},
	{0x65d, 230, 1, 1, f(Yes, false, "")},
	{0x65f, 220, 1, 1, f(Yes, false, "")},
	{0x660, 0, 0, 0, f(Yes, false, "")},
	{0x670, 35, 1, 1, f(Yes, false, "")},
	{0x671, 0, 0, 0, f(Yes, false, "")},
	{0x675, 0, 0, 0, g(Yes, No, false, false, "", "اٴ")},
	{0x676, 0, 0, 0, g(Yes, No, false, false, "", "وٴ")},
	{0x677, 0, 0, 0, g(Yes, No, false, false, "", "ۇٴ")},
	{0x678, 0, 0, 0, g(Yes, No, false, false, "", "يٴ")},
	{0x679, 0, 0, 0, f(Yes, false, "")},
	{0x6c0, 0, 0, 1, f(Yes, false, "ۀ")},
	{0x6c1, 0, 0, 0, f(Yes, true, "")},
	{0x6c2, 0, 0, 1, f(Yes, false, "ۂ")},
	{0x6c3, 0, 0, 0, f(Yes, false, "")},
	{0x6d2, 0, 0, 0, f(Yes, true, "")},
	{0x6d3, 0, 0, 1, f(Yes, false, "ۓ")},
	{0x6d4, 0, 0, 0, f(Yes, false, "")},
	{0x6d5, 0, 0, 0, f(Yes, true, "")},
	{0x6d6, 230, 1, 1, f(Yes, false, "")},
	{0x6dd, 0, 0, 0, f(Yes, false, "")},
	{0x6df, 230, 1, 1, f(Yes, false, "")},
	{0x6e3, 220, 1, 1, f(Yes, false, "")},
	{0x6e4, 230, 1, 1, f(Yes, false, "")},
	{0x6e5, 0, 0, 0, f(Yes, false, "")},
	{0x6e7, 230, 1, 1, f(Yes, false, "")},
	{0x6e9, 0, 0, 0, f(Yes, false, "")},
	{0x6ea, 220, 1, 1, f(Yes, false, "")},
	{0x6eb, 230, 1, 1, f(Yes, false, "")},
	{0x6ed, 220, 1, 1, f(Yes, false, "")},
	{0x6ee, 0, 0, 0, f(Yes, false, "")},
	{0x711, 36, 1, 1, f(Yes, false, "")},
	{0x712, 0, 0, 0, f(Yes, false, "")},
	{0x730, 230, 1, 1, f(Yes, false, "")},
	{0x731, 220, 1, 1, f(Yes, false, "")},
	{0x732, 230, 1, 1, f(Yes, false, "")},
	{0x734, 220, 1, 1, f(Yes, false, "")},
	{0x735, 230, 1, 1, f(Yes, false, "")},
	{0x737, 220, 1, 1, f(Yes, false, "")},
	{0x73a, 230, 1, 1, f(Yes, false, "")},
	{0x73b, 220, 1, 1, f(Yes, false, "")},
	{0x73d, 230, 1, 1, f(Yes, false, "")},
	{0x73e, 220, 1, 1, f(Yes, false, "")},
	{0x73f, 230, 1, 1, f(Yes, false, "")},
	{0x742, 220, 1, 1, f(Yes, false, "")},
	{0x743, 230, 1, 1, f(Yes, false, "")},
	{0x744, 220, 1, 1, f(Yes, false, "")},
	{0x745, 230, 1, 1, f(Yes, false, "")},
	{0x746, 220, 1, 1, f(Yes, false, "")},
	{0x747, 230, 1, 1, f(Yes, false, "")},
	{0x748, 220, 1, 1, f(Yes, false, "")},
	{0x749, 230, 1, 1, f(Yes, false, "")},
	{0x74b, 0, 0, 0, f(Yes, false, "")},
	{0x7eb, 230, 1, 1, f(Yes, false, "")},
	{0x7f2, 220, 1, 1, f(Yes, false, "")},
	{0x7f3, 230, 1, 1, f(Yes, false, "")},
	{0x7f4, 0, 0, 0, f(Yes, false, "")},
	{0x816, 230, 1, 1, f(Yes, false, "")},
	{0x81a, 0, 0, 0, f(Yes, false, "")},
	{0x81b, 230, 1, 1, f(Yes, false, "")},
	{0x824, 0, 0, 0, f(Yes, false, "")},
	{0x825, 230, 1, 1, f(Yes, false, "")},
	{0x828, 0, 0, 0, f(Yes, false, "")},
	{0x829, 230, 1, 1, f(Yes, false, "")},
	{0x82e, 0, 0, 0, f(Yes, false, "")},
	{0x859, 220, 1, 1, f(Yes, false, "")},
	{0x85c, 0, 0, 0, f(Yes, false, "")},
	{0x8d4, 230, 1, 1, f(Yes, false, "")},
	{0x8e2, 0, 0, 0, f(Yes, false, "")},
	{0x8e3, 220, 1, 1, f(Yes, false, "")},
	{0x8e4, 230, 1, 1, f(Yes, false, "")},
	{0x8e6, 220, 1, 1, f(Yes, false, "")},
	{0x8e7, 230, 1, 1, f(Yes, false, "")},
	{0x8e9, 220, 1, 1, f(Yes, false, "")},
	{0x8ea, 230, 1, 1, f(Yes, false, "")},
	{0x8ed, 220, 1, 1, f(Yes, false, "")},
	{0x8f0, 27, 1, 1, f(Yes, false, "")},
	{0x8f1, 28, 1, 1, f(Yes, false, "")},
	{0x8f2, 29, 1, 1, f(Yes, false, "")},
	{0x8f3, 230, 1, 1, f(Yes, false, "")},
	{0x8f6, 220, 1, 1, f(Yes, false, "")},
	{0x8f7, 230, 1, 1, f(Yes, false, "")},
	{0x8f9, 220, 1, 1, f(Yes, false, "")},
	{0x8fb, 230, 1, 1, f(Yes, false, "")},
	{0x900, 0, 0, 0, f(Yes, false, "")},
	{0x928, 0, 0, 0, f(Yes, true, "")},
	{0x929, 0, 0, 1, f(Yes, false, "ऩ")},
	{0x92a, 0, 0, 0, f(Yes, false, "")},
	{0x930, 0, 0, 0, f(Yes, true, "")},
	{0x931, 0, 0, 1, f(Yes, false, "ऱ")},
	{0x932, 0, 0, 0, f(Yes, false, "")},
	{0x933, 0, 0, 0, f(Yes, true, "")},
	{0x934, 0, 0, 1, f(Yes, false, "ऴ")},
	{0x935, 0, 0, 0, f(Yes, false, "")},
	{0x93c, 7, 1, 1, f(Maybe, false, "")},
	{0x93d, 0, 0, 0, f(Yes, false, "")},
	{0x94d, 9, 1, 1, f(Yes, false, "")},
	{0x94e, 0, 0, 0, f(Yes, false, "")},
	{0x951, 230, 1, 1, f(Yes, false, "")},
	{0x952, 220, 1, 1, f(Yes, false, "")},
	{0x953, 230, 1, 1, f(Yes, false, "")},
	{0x955, 0, 0, 0, f(Yes, false, "")},
	{0x958, 0, 0, 1, f(No, false, "क़")},
	{0x959, 0, 0, 1, f(No, false, "ख़")},
	{0x95a, 0, 0, 1, f(No, false, "ग़")},
	{0x95b, 0, 0, 1, f(No, false, "ज़")},
	{0x95c, 0, 0, 1, f(No, false, "ड़")},
	{0x95d, 0, 0, 1, f(No, false, "ढ़")},
	{0x95e, 0, 0, 1, f(No, false, "फ़")},
	{0x95f, 0, 0, 1, f(No, false, "य़")},
	{0x960, 0, 0, 0, f(Yes, false, "")},
	{0x9bc, 7, 1, 1, f(Yes, false, "")},
	{0x9bd, 0, 0, 0, f(Yes, false, "")},
	{0x9be, 0, 1, 1, f(Maybe, false, "")},
	{0x9bf, 0, 0, 0, f(Yes, false, "")},
	{0x9c7, 0, 0, 0, f(Yes, true, "")},
	{0x9c8, 0, 0, 0, f(Yes, false, "")},
	{0x9cb, 0, 0, 1, f(Yes, false, "ো")},
	{0x9cc, 0, 0, 1, f(Yes, false, "ৌ")},
	{0x9cd, 9, 1, 1, f(Yes, false, "")},
	{0x9ce, 0, 0, 0, f(Yes, false, "")},
	{0x9d7, 0, 1, 1, f(Maybe, false, "")},
	{0x9d8, 0, 0, 0, f(Yes, false, "")},
	{0x9dc, 0, 0, 1, f(No, false, "ড়")},
	{0x9dd, 0, 0, 1, f(No, false, "ঢ়")},
	{0x9de, 0, 0, 0, f(Yes, false, "")},
	{0x9df, 0, 0, 1, f(No, false, "য়")},
	{0x9e0, 0, 0, 0, f(Yes, false, "")},
	{0xa33, 0, 0, 1, f(No, false, "ਲ਼")},
	{0xa34, 0, 0, 0, f(Yes, false, "")},
	{0xa36, 0, 0, 1, f(No, false, "ਸ਼")},
	{0xa37, 0, 0, 0, f(Yes, false, "")},
	{0xa3c, 7, 1, 1, f(Yes, false, "")},
	{0xa3d, 0, 0, 0, f(Yes, false, "")},
	{0xa4d, 9, 1, 1, f(Yes, false, "")},
	{0xa4e, 0, 0, 0, f(Yes, false, "")},
	{0xa59, 0, 0, 1, f(No, false, "ਖ਼")},
	{0xa5a, 0, 0, 1, f(No, false, "ਗ਼")},
	{0xa5b, 0, 0, 1, f(No, false, "ਜ਼")},
	{0xa5c, 0, 0, 0, f(Yes, false, "")},
	{0xa5e, 0, 0, 1, f(No, false, "ਫ਼")},
	{0xa5f, 0, 0, 0, f(Yes, false, "")},
	{0xabc, 7, 1, 1, f(Yes, false, "")},
	{0xabd, 0, 0, 0, f(Yes, false, "")},
	{0xacd, 9, 1, 1, f(Yes, false, "")},
	{0xace, 0, 0, 0, f(Yes, false, "")},
	{0xb3c, 7, 1, 1, f(Yes, false, "")},
	{0xb3d, 0, 0, 0, f(Yes, false, "")},
	{0xb3e, 0, 1, 1, f(Maybe, false, "")},
	{0xb3f, 0, 0, 0, f(Yes, false, "")},
	{0xb47, 0, 0, 0, f(Yes, true, "")},
	{0xb48, 0, 0, 1, f(Yes, false, "ୈ")},
	{0xb49, 0, 0, 0, f(Yes, false, "")},
	{0xb4b, 0, 0, 1, f(Yes, false, "ୋ")},
	{0xb4c, 0, 0, 1, f(Yes, false, "ୌ")},
	{0xb4d, 9, 1, 1, f(Yes, false, "")},
	{0xb4e, 0, 0, 0, f(Yes, false, "")},
	{0xb56, 0, 1, 1, f(Maybe, false, "")},
	{0xb58, 0, 0, 0, f(Yes, false, "")},
	{0xb5c, 0, 0, 1, f(No, false, "ଡ଼")},
	{0xb5d, 0, 0, 1, f(No, false, "ଢ଼")},
	{0xb5e, 0, 0, 0, f(Yes, false, "")},
	{0xb92, 0, 0, 0, f(Yes, true, "")},
	{0xb93, 0, 0, 0, f(Yes, false, "")},
	{0xb94, 0, 0, 1, f(Yes, false, "ஔ")},
	{0xb95, 0, 0, 0, f(Yes, false, "")},
	{0xbbe, 0, 1, 1, f(Maybe, false, "")},
	{0xbbf, 0, 0, 0, f(Yes, false, "")},
	{0xbc6, 0, 0, 0, f(Yes, true, "")},
	{0xbc8, 0, 0, 0, f(Yes, false, "")},
	{0xbca, 0, 0, 1, f(Yes, false, "ொ")},
	{0xbcb, 0, 0, 1, f(Yes, false, "ோ")},
	{0xbcc, 0, 0, 1, f(Yes, false, "ௌ")},
	{0xbcd, 9, 1, 1, f(Yes, false, "")},
	{0xbce, 0, 0, 0, f(Yes, false, "")},
	{0xbd7, 0, 1, 1, f(Maybe, false, "")},
	{0xbd8, 0, 0, 0, f(Yes, false, "")},
	{0xc46, 0, 0, 0, f(Yes, true, "")},
	{0xc47, 0, 0, 0, f(Yes, false, "")},
	{0xc48, 0, 0, 1, f(Yes, false, "ై")},
	{0xc49, 0, 0, 0, f(Yes, false, "")},
	{0xc4d, 9, 1, 1, f(Yes, false, "")},
	{0xc4e, 0, 0, 0, f(Yes, false, "")},
	{0xc55, 84, 1, 1, f(Yes, false, "")},
	{0xc56, 91, 1, 1, f(Maybe, false, "")},
	{0xc57, 0, 0, 0, f(Yes, false, "")},
	{0xcbc, 7, 1, 1, f(Yes, false, "")},
	{0xcbd, 0, 0, 0, f(Yes, false, "")},
	{0xcbf, 0, 0, 0, f(Yes, true, "")},
	{0xcc0, 0, 0, 1, f(Yes, false, "ೀ")},
	{0xcc1, 0, 0, 0, f(Yes, false, "")},
	{0xcc2, 0, 1, 1, f(Maybe, false, "")},
	{0xcc3, 0, 0, 0, f(Yes, false, "")},
	{0xcc6, 0, 0, 0, f(Yes, true, "")},
	{0xcc7, 0, 0, 1, f(Yes, false, "ೇ")},
	{0xcc8, 0, 0, 1, f(Yes, false, "ೈ")},
	{0xcc9, 0, 0, 0, f(Yes, false, "")},
	{0xcca, 0, 0, 1, f(Yes, true, "ೊ")},
	{0xccb, 0, 0, 2, f(Yes, false, "ೋ")},
	{0xccc, 0, 0, 0, f(Yes, false, "")},
	{0xccd, 9, 1, 1, f(Yes, false, "")},
	{0xcce, 0, 0, 0, f(Yes, false, "")},
	{0xcd5, 0, 1, 1, f(Maybe, false, "")},
	{0xcd7, 0, 0, 0, f(Yes, false, "")},
	{0xd3b, 9, 1, 1, f(Yes, false, "")},
	{0xd3d, 0, 0, 0, f(Yes, false, "")},
	{0xd3e, 0, 1, 1, f(Maybe, false, "")},
	{0xd3f, 0, 0, 0, f(Yes, false, "")},
	{0xd46, 0, 0, 0, f(Yes, true, "")},
	{0xd48, 0, 0, 0, f(Yes, false, "")},
	{0xd4a, 0, 0, 1, f(Yes, false, "ൊ")},
	{0xd4b, 0, 0, 1, f(Yes, false, "ോ")},
	{0xd4c, 0, 0, 1, f(Yes, false, "ൌ")},
	{0xd4d, 9, 1, 1, f(Yes, false, "")},
	{0xd4e, 0, 0, 0, f(Yes, false, "")},
	{0xd57, 0, 1, 1, f(Maybe, false, "")},
	{0xd58, 0, 0, 0, f(Yes, false, "")},
	{0xdca, 9, 1, 1, f(Maybe, false, "")},
	{0xdcb, 0, 0, 0, f(Yes, false, "")},
	{0xdcf, 0, 1, 1, f(Maybe, false, "")},
	{0xdd0, 0, 0, 0, f(Yes, false, "")},
	{0xdd9, 0, 0, 0, f(Yes, true, "")},
	{0xdda, 0, 0, 1, f(Yes, false, "ේ")},
	{0xddb, 0, 0, 0, f(Yes, false, "")},
	{0xddc, 0, 0, 1, f(Yes, true, "ො")},
	{0xddd, 0, 0, 2, f(Yes, false, "ෝ")},
	{0xdde, 0, 0, 1, f(Yes, false, "ෞ")},
	{0xddf, 0, 1, 1, f(Maybe, false, "")},
	{0xde0, 0, 0, 0, f(Yes, false, "")},
	{0xe33, 0, 0, 0, g(Yes, No, false, false, "", "ํา")},
	{0xe34, 0, 0, 0, f(Yes, false, "")},
	{0xe38, 103, 1, 1, f(Yes, false, "")},
	{0xe3a, 9, 1, 1, f(Yes, false, "")},
	{0xe3b, 0, 0, 0, f(Yes, false, "")},
	{0xe48, 107, 1, 1, f(Yes, false, "")},
	{0xe4c, 0, 0, 0, f(Yes, false, "")},
	{0xeb3, 0, 0, 0, g(Yes, No, false, false, "", "ໍາ")},
	{0xeb4, 0, 0, 0, f(Yes, false, "")},
	{0xeb8, 118, 1, 1, f(Yes, false, "")},
	{0xeba, 0, 0, 0, f(Yes, false, "")},
	{0xec8, 122, 1, 1, f(Yes, false, "")},
	{0xecc, 0, 0, 0, f(Yes, false, "")},
	{0xedc, 0, 0, 0, g(Yes, No, false, false, "", "ຫນ")},
	{0xedd, 0, 0, 0, g(Yes, No, false, false, "", "ຫມ")},
	{0xede, 0, 0, 0, f(Yes, false, "")},
	{0xf0c, 0, 0, 0, g(Yes, No, false, false, "", "་")},
	{0xf0d, 0, 0, 0, f(Yes, false, "")},
	{0xf18, 220, 1, 1, f(Yes, false, "")},
	{0xf1a, 0, 0, 0, f(Yes, false, "")},
	{0xf35, 220, 1, 1, f(Yes, false, "")},
	{0xf36, 0, 0, 0, f(Yes, false, "")},
	{0xf37, 220, 1, 1, f(Yes, false, "")},
	{0xf38, 0, 0, 0, f(Yes, false, "")},
	{0xf39, 216, 1, 1, f(Yes, false, "")},
	{0xf3a, 0, 0, 0, f(Yes, false, "")},
	{0xf43, 0, 0, 0, f(No, false, "གྷ")},
	{0xf44, 0, 0, 0, f(Yes, false, "")},
	{0xf4d, 0, 0, 0, f(No, false, "ཌྷ")},
	{0xf4e, 0, 0, 0, f(Yes, false, "")},
	{0xf52, 0, 0, 0, f(No, false, "དྷ")},
	{0xf53, 0, 0, 0, f(Yes, false, "")},
	{0xf57, 0, 0, 0, f(No, false, "བྷ")},
	{0xf58, 0, 0, 0, f(Yes, false, "")},
	{0xf5c, 0, 0, 0, f(No, false, "ཛྷ")},
	{0xf5d, 0, 0, 0, f(Yes, false, "")},
	{0xf69, 0, 0, 0, f(No, false, "ཀྵ")},
	{0xf6a, 0, 0, 0, f(Yes, false, "")},
	{0xf71, 129, 1, 1, f(Yes, false, "")},
	{0xf72, 130, 1, 1, f(Yes, false, "")},
	{0xf73, 0, 2, 2, f(No, false, "ཱི")},
	{0xf74, 132, 1, 1, f(Yes, false, "")},
	{0xf75, 0, 2, 2, f(No, false, "ཱུ")},
	{0xf76, 0, 0, 1, f(No, false, "ྲྀ")},
	{0xf77, 0, 0, 2, g(Yes, No, false, false, "", "ྲཱྀ")},
	{0xf78, 0, 0, 1, f(No, false, "ླྀ")},
	{0xf79, 0, 0, 2, g(Yes, No, false, false, "", "ླཱྀ")},
	{0xf7a, 130, 1, 1, f(Yes, false, "")},
	{0xf7e, 0, 0, 0, f(Yes, false, "")},
	{0xf80, 130, 1, 1, f(Yes, false, "")},
	{0xf81, 0, 2, 2, f(No, false, "ཱྀ")},
	{0xf82, 230, 1, 1, f(Yes, false, "")},
	{0xf84, 9, 1, 1, f(Yes, false, "")},
	{0xf85, 0, 0, 0, f(Yes, false, "")},
	{0xf86, 230, 1, 1, f(Yes, false, "")},
	{0xf88, 0, 0, 0, f(Yes, false, "")},
	{0xf93, 0, 0, 0, f(No, false, "ྒྷ")},
	{0xf94, 0, 0, 0, f(Yes, false, "")},
	{0xf9d, 0, 0, 0, f(No, false, "ྜྷ")},
	{0xf9e, 0, 0, 0, f(Yes, false, "")},
	{0xfa2, 0, 0, 0, f(No, false, "ྡྷ")},
	{0xfa3, 0, 0, 0, f(Yes, false, "")},
	{0xfa7, 0, 0, 0, f(No, false, "ྦྷ")},
	{0xfa8, 0, 0, 0, f(Yes, false, "")},
	{0xfac, 0, 0, 0, f(No, false, "ྫྷ")},
	{0xfad, 0, 0, 0, f(Yes, false, "")},
	{0xfb9, 0, 0, 0, f(No, false, "ྐྵ")},
	{0xfba, 0, 0, 0, f(Yes, false, "")},
	{0xfc6, 220, 1, 1, f(Yes, false, "")},
	{0xfc7, 0, 0, 0, f(Yes, false, "")},
	{0x1025, 0, 0, 0, f(Yes, true, "")},
	{0x1026, 0, 0, 1, f(Yes, false, "ဦ")},
	{0x1027, 0, 0, 0, f(Yes, false, "")},
	{0x102e, 0, 1, 1, f(Maybe, false, "")},
	{0x102f, 0, 0, 0, f(Yes, false, "")},
	{0x1037, 7, 1, 1, f(Yes, false, "")},
	{0x1038, 0, 0, 0, f(Yes, false, "")},
	{0x1039, 9, 1, 1, f(Yes, false, "")},
	{0x103b, 0, 0, 0, f(Yes, false, "")},
	{0x108d, 220, 1, 1, f(Yes, false, "")},
	{0x108e, 0, 0, 0, f(Yes, false, "")},
	{0x10fc, 0, 0, 0, g(Yes, No, false, false, "", "ნ")},
	{0x10fd, 0, 0, 0, f(Yes, false, "")},
	{0x1100, 0, 0, 0, f(Yes, true, "")},
	{0x1113, 0, 0, 0, f(Yes, false, "")},
	{0x1161, 0, 1, 1, f(Maybe, true, "")},
	{0x1176, 0, 0, 0, f(Yes, false, "")},
	{0x11a8, 0, 1, 1, f(Maybe, false, "")},
	{0x11c3, 0, 0, 0, f(Yes, false, "")},
	{0x135d, 230, 1, 1, f(Yes, false, "")},
	{0x1360, 0, 0, 0, f(Yes, false, "")},
	{0x1714, 9, 1, 1, f(Yes, false, "")},
	{0x1715, 0, 0, 0, f(Yes, false, "")},
	{0x1734, 9, 1, 1, f(Yes, false, "")},
	{0x1735, 0, 0, 0, f(Yes, false, "")},
	{0x17d2, 9, 1, 1, f(Yes, false, "")},
	{0x17d3, 0, 0, 0, f(Yes, false, "")},
	{0x17dd, 230, 1, 1, f(Yes, false, "")},
	{0x17de, 0, 0, 0, f(Yes, false, "")},
	{0x18a9, 228, 1, 1, f(Yes, false, "")},
	{0x18aa, 0, 0, 0, f(Yes, false, "")},
	{0x1939, 222, 1, 1, f(Yes, false, "")},
	{0x193a, 230, 1, 1, f(Yes, false, "")},
	{0x193b, 220, 1, 1, f(Yes, false, "")},
	{0x193c, 0, 0, 0, f(Yes, false, "")},
	{0x1a17, 230, 1, 1, f(Yes, false, "")},
	{0x1a18, 220, 1, 1, f(Yes, false, "")},
	{0x1a19, 0, 0, 0, f(Yes, false, "")},
	{0x1a60, 9, 1, 1, f(Yes, false, "")},
	{0x1a61, 0, 0, 0, f(Yes, false, "")},
	{0x1a75, 230, 1, 1, f(Yes, false, "")},
	{0x1a7d, 0, 0, 0, f(Yes, false, "")},
	{0x1a7f, 220, 1, 1, f(Yes, false, "")},
	{0x1a80, 0, 0, 0, f(Yes, false, "")},
	{0x1ab0, 230, 1, 1, f(Yes, false, "")},
	{0x1ab5, 220, 1, 1, f(Yes, false, "")},
	{0x1abb, 230, 1, 1, f(Yes, false, "")},
	{0x1abd, 220, 1, 1, f(Yes, false, "")},
	{0x1abe, 0, 0, 0, f(Yes, false, "")},
	{0x1b05, 0, 0, 0, f(Yes, true, "")},
	{0x1b06, 0, 0, 1, f(Yes, false, "ᬆ")},
	{0x1b07, 0, 0, 0, f(Yes, true, "")},
	{0x1b08, 0, 0, 1, f(Yes, false, "ᬈ")},
	{0x1b09, 0, 0, 0, f(Yes, true, "")},
	{0x1b0a, 0, 0, 1, f(Yes, false, "ᬊ")},
	{0x1b0b, 0, 0, 0, f(Yes, true, "")},
	{0x1b0c, 0, 0, 1, f(Yes, false, "ᬌ")},
	{0x1b0d, 0, 0, 0, f(Yes, true, "")},
	{0x1b0e, 0, 0, 1, f(Yes, false, "ᬎ")},
	{0x1b0f, 0, 0, 0, f(Yes, false, "")},
	{0x1b11, 0, 0, 0, f(Yes, true, "")},
	{0x1b12, 0, 0, 1, f(Yes, false, "ᬒ")},
	{0x1b13, 0, 0, 0, f(Yes, false, "")},
	{0x1b34, 7, 1, 1, f(Yes, false, "")},
	{0x1b35, 0, 1, 1, f(Maybe, false, "")},
	{0x1b36, 0, 0, 0, f(Yes, false, "")},
	{0x1b3a, 0, 0, 0, f(Yes, true, "")},
	{0x1b3b, 0, 0, 1, f(Yes, false, "ᬻ")},
	{0x1b3c, 0, 0, 0, f(Yes, true, "")},
	{0x1b3d, 0, 0, 1, f(Yes, false, "ᬽ")},
	{0x1b3e, 0, 0, 0, f(Yes, true, "")},
	{0x1b40, 0, 0, 1, f(Yes, false, "ᭀ")},
	{0x1b41, 0, 0, 1, f(Yes, false, "ᭁ")},
	{0x1b42, 0, 0, 0, f(Yes, true, "")},
	{0x1b43, 0, 0, 1, f(Yes, false, "ᭃ")},
	{0x1b44, 9, 1, 1, f(Yes, false, "")},
	{0x1b45, 0, 0, 0, f(Yes, false, "")},
	{0x1b6b, 230, 1, 1, f(Yes, false, "")},
	{0x1b6c, 220, 1, 1, f(Yes, false, "")},
	{0x1b6d, 230, 1, 1, f(Yes, false, "")},
	{0x1b74, 0, 0, 0, f(Yes, false, "")},
	{0x1baa, 9, 1, 1, f(Yes, false, "")},
	{0x1bac, 0, 0, 0, f(Yes, false, "")},
	{0x1be6, 7, 1, 1, f(Yes, false, "")},
	{0x1be7, 0, 0, 0, f(Yes, false, "")},
	{0x1bf2, 9, 1, 1, f(Yes, false, "")},
	{0x1bf4, 0, 0, 0, f(Yes, false, "")},
	{0x1c37, 7, 1, 1, f(Yes, false, "")},
	{0x1c38, 0, 0, 0, f(Yes, false, "")},
	{0x1cd0, 230, 1, 1, f(Yes, false, "")},
	{0x1cd3, 0, 0, 0, f(Yes, false, "")},
	{0x1cd4, 1, 1, 1, f(Yes, false, "")},
	{0x1cd5, 220, 1, 1, f(Yes, false, "")},
	{0x1cda, 230, 1, 1, f(Yes, false, "")},
	{0x1cdc, 220, 1, 1, f(Yes, false, "")},
	{0x1ce0, 230, 1, 1, f(Yes, false, "")},
	{0x1ce1, 0, 0, 0, f(Yes, false, "")},
	{0x1ce2, 1, 1, 1, f(Yes, false, "")},
	{0x1ce9, 0, 0, 0, f(Yes, false, "")},
	{0x1ced, 220, 1, 1, f(Yes, false, "")},
	{0x1cee, 0, 0, 0, f(Yes, false, "")},
	{0x1cf4, 230, 1, 1, f(Yes, false, "")},
	{0x1cf5, 0, 0, 0, f(Yes, false, "")},
	{0x1cf8, 230, 1, 1, f(Yes, false, "")},
	{0x1cfa, 0, 0, 0, f(Yes, false, "")},
	{0x1d2c, 0, 0, 0, g(Yes, No, false, false, "", "A")},
	{0x1d2d, 0, 0, 0, g(Yes, No, false, false, "", "Æ")},
	{0x1d2e, 0, 0, 0, g(Yes, No, false, false, "", "B")},
	{0x1d2f, 0, 0, 0, f(Yes, false, "")},
	{0x1d30, 0, 0, 0, g(Yes, No, false, false, "", "D")},
	{0x1d31, 0, 0, 0, g(Yes, No, false, false, "", "E")},
	{0x1d32, 0, 0, 0, g(Yes, No, false, false, "", "Ǝ")},
	{0x1d33, 0, 0, 0, g(Yes, No, false, false, "", "G")},
	{0x1d34, 0, 0, 0, g(Yes, No, false, false, "", "H")},
	{0x1d35, 0, 0, 0, g(Yes, No, false, false, "", "I")},
	{0x1d36, 0, 0, 0, g(Yes, No, false, false, "", "J")},
	{0x1d37, 0, 0, 0, g(Yes, No, false, false, "", "K")},
	{0x1d38, 0, 0, 0, g(Yes, No, false, false, "", "L")},
	{0x1d39, 0, 0, 0, g(Yes, No, false, false, "", "M")},
	{0x1d3a, 0, 0, 0, g(Yes, No, false, false, "", "N")},
	{0x1d3b, 0, 0, 0, f(Yes, false, "")},
	{0x1d3c, 0, 0, 0, g(Yes, No, false, false, "", "O")},
	{0x1d3d, 0, 0, 0, g(Yes, No, false, false, "", "Ȣ")},
	{0x1d3e, 0, 0, 0, g(Yes, No, false, false, "", "P")},
	{0x1d3f, 0, 0, 0, g(Yes, No, false, false, "", "R")},
	{0x1d40, 0, 0, 0, g(Yes, No, false, false, "", "T")},
	{0x1d41, 0, 0, 0, g(Yes, No, false, false, "", "U")},
	{0x1d42, 0, 0, 0, g(Yes, No, false, false, "", "W")},
	{0x1d43, 0, 0, 0, g(Yes, No, false, false, "", "a")},
	{0x1d44, 0, 0, 0, g(Yes, No, false, false, "", "ɐ")},
	{0x1d45, 0, 0, 0, g(Yes, No, false, false, "", "ɑ")},
	{0x1d46, 0, 0, 0, g(Yes, No, false, false, "", "ᴂ")},
	{0x1d47, 0, 0, 0, g(Yes, No, false, false, "", "b")},
	{0x1d48, 0, 0, 0, g(Yes, No, false, false, "", "d")},
	{0x1d49, 0, 0, 0, g(Yes, No, false, false, "", "e")},
	{0x1d4a, 0, 0, 0, g(Yes, No, false, false, "", "ə")},
	{0x1d4b, 0, 0, 0, g(Yes, No, false, false, "", "ɛ")},
	{0x1d4c, 0, 0, 0, g(Yes, No, false, false, "", "ɜ")},
	{0x1d4d, 0, 0, 0, g(Yes, No, false, false, "", "g")},
	{0x1d4e, 0, 0, 0, f(Yes, false, "")},
	{0x1d4f, 0, 0, 0, g(Yes, No, false, false, "", "k")},
	{0x1d50, 0, 0, 0, g(Yes, No, false, false, "", "m")},
	{0x1d51, 0, 0, 0, g(Yes, No, false, false, "", "ŋ")},
	{0x1d52, 0, 0, 0, g(Yes, No, false, false, "", "o")},
	{0x1d53, 0, 0, 0, g(Yes, No, false, false, "", "ɔ")},
	{0x1d54, 0, 0, 0, g(Yes, No, false, false, "", "ᴖ")},
	{0x1d55, 0, 0, 0, g(Yes, No, false, false, "", "ᴗ")},
	{0x1d56, 0, 0, 0, g(Yes, No, false, false, "", "p")},
	{0x1d57, 0, 0, 0, g(Yes, No, false, false, "", "t")},
	{0x1d58, 0, 0, 0, g(Yes, No, false, false, "", "u")},
	{0x1d59, 0, 0, 0, g(Yes, No, false, false, "", "ᴝ")},
	{0x1d5a, 0, 0, 0, g(Yes, No, false, false, "", "ɯ")},
	{0x1d5b, 0, 0, 0, g(Yes, No, false, false, "", "v")},
	{0x1d5c, 0, 0, 0, g(Yes, No, false, false, "", "ᴥ")},
	{0x1d5d, 0, 0, 0, g(Yes, No, false, false, "", "β")},
	{0x1d5e, 0, 0, 0, g(Yes, No, false, false, "", "γ")},
	{0x1d5f, 0, 0, 0, g(Yes, No, false, false, "", "δ")},
	{0x1d60, 0, 0, 0, g(Yes, No, false, false, "", "φ")},
	{0x1d61, 0, 0, 0, g(Yes, No, false, false, "", "χ")},
	{0x1d62, 0, 0, 0, g(Yes, No, false, false, "", "i")},
	{0x1d63, 0, 0, 0, g(Yes, No, false, false, "", "r")},
	{0x1d64, 0, 0, 0, g(Yes, No, false, false, "", "u")},
	{0x1d65, 0, 0, 0, g(Yes, No, false, false, "", "v")},
	{0x1d66, 0, 0, 0, g(Yes, No, false, false, "", "β")},
	{0x1d67, 0, 0, 0, g(Yes, No, false, false, "", "γ")},
	{0x1d68, 0, 0, 0, g(Yes, No, false, false, "", "ρ")},
	{0x1d69, 0, 0, 0, g(Yes, No, false, false, "", "φ")},
	{0x1d6a, 0, 0, 0, g(Yes, No, false, false, "", "χ")},
	{0x1d6b, 0, 0, 0, f(Yes, false, "")},
	{0x1d78, 0, 0, 0, g(Yes, No, false, false, "", "н")},
	{0x1d79, 0, 0, 0, f(Yes, false, "")},
	{0x1d9b, 0, 0, 0, g(Yes, No, false, false, "", "ɒ")},
	{0x1d9c, 0, 0, 0, g(Yes, No, false, false, "", "c")},
	{0x1d9d, 0, 0, 0, g(Yes, No, false, false, "", "ɕ")},
	{0x1d9e, 0, 0, 0, g(Yes, No, false, false, "", "ð")},
	{0x1d9f, 0, 0, 0, g(Yes, No, false, false, "", "ɜ")},
	{0x1da0, 0, 0, 0, g(Yes, No, false, false, "", "f")},
	{0x1da1, 0, 0, 0, g(Yes, No, false, false, "", "ɟ")},
	{0x1da2, 0, 0, 0, g(Yes, No, false, false, "", "ɡ")},
	{0x1da3, 0, 0, 0, g(Yes, No, false, false, "", "ɥ")},
	{0x1da4, 0, 0, 0, g(Yes, No, false, false, "", "ɨ")},
	{0x1da5, 0, 0, 0, g(Yes, No, false, false, "", "ɩ")},
	{0x1da6, 0, 0, 0, g(Yes, No, false, false, "", "ɪ")},
	{0x1da7, 0, 0, 0, g(Yes, No, false, false, "", "ᵻ")},
	{0x1da8, 0, 0, 0, g(Yes, No, false, false, "", "ʝ")},
	{0x1da9, 0, 0, 0, g(Yes, No, false, false, "", "ɭ")},
	{0x1daa, 0, 0, 0, g(Yes, No, false, false, "", "ᶅ")},
	{0x1dab, 0, 0, 0, g(Yes, No, false, false, "", "ʟ")},
	{0x1dac, 0, 0, 0, g(Yes, No, false, false, "", "ɱ")},
	{0x1dad, 0, 0, 0, g(Yes, No, false, false, "", "ɰ")},
	{0x1dae, 0, 0, 0, g(Yes, No, false, false, "", "ɲ")},
	{0x1daf, 0, 0, 0, g(Yes, No, false, false, "", "ɳ")},
	{0x1db0, 0, 0, 0, g(Yes, No, false, false, "", "ɴ")},
	{0x1db1, 0, 0, 0, g(Yes, No, false, false, "", "ɵ")},
	{0x1db2, 0, 0, 0, g(Yes, No, false, false, "", "ɸ")},
	{0x1db3, 0, 0, 0, g(Yes, No, false, false, "", "ʂ")},
	{0x1db4, 0, 0, 0, g(Yes, No, false, false, "", "ʃ")},
	{0x1db5, 0, 0, 0, g(Yes, No, false, false, "", "ƫ")},
	{0x1db6, 0, 0, 0, g(Yes, No, false, false, "", "ʉ")},
	{0x1db7, 0, 0, 0, g(Yes, No, false, false, "", "ʊ")},
	{0x1db8, 0, 0, 0, g(Yes, No, false, false, "", "ᴜ")},
	{0x1db9, 0, 0, 0, g(Yes, No, false, false, "", "ʋ")},
	{0x1dba, 0, 0, 0, g(Yes, No, false, false, "", "ʌ")},
	{0x1dbb, 0, 0, 0, g(Yes, No, false, false, "", "z")},
	{0x1dbc, 0, 0, 0, g(Yes, No, false, false, "", "ʐ")},
	{0x1dbd, 0, 0, 0, g(Yes, No, false, false, "", "ʑ")},
	{0x1dbe, 0, 0, 0, g(Yes, No, false, false, "", "ʒ")},
	{0x1dbf, 0, 0, 0, g(Yes, No, false, false, "", "θ")},
	{0x1dc0, 230, 1, 1, f(Yes, false, "")},
	{0x1dc2, 220, 1, 1, f(Yes, false, "")},
	{0x1dc3, 230, 1, 1, f(Yes, false, "")},
	{0x1dca, 220, 1, 1, f(Yes, false, "")},
	{0x1dcb, 230, 1, 1, f(Yes, false, "")},
	{0x1dcd, 234, 1, 1, f(Yes, false, "")},
	{0x1dce, 214, 1, 1, f(Yes, false, "")},
	{0x1dcf, 220, 1, 1, f(Yes, false, "")},
	{0x1dd0, 202, 1, 1, f(Yes, false, "")},
	{0x1dd1, 230, 1, 1, f(Yes, false, "")},
	{0x1df6, 232, 1, 1, f(Yes, false, "")},
	{0x1df7, 228, 1, 1, f(Yes, false, "")},
	{0x1df9, 220, 1, 1, f(Yes, false, "")},
	{0x1dfa, 0, 0, 0, f(Yes, false, "")},
	{0x1dfb, 230, 1, 1, f(Yes, false, "")},
	{0x1dfc, 233, 1, 1, f(Yes, false, "")},
	{0x1dfd, 220, 1, 1, f(Yes, false, "")},
	{0x1dfe, 230, 1, 1, f(Yes, false, "")},
	{0x1dff, 220, 1, 1, f(Yes, false, "")},
	{0x1e00, 0, 0, 1, f(Yes, false, "Ḁ")},
	{0x1e01, 0, 0, 1, f(Yes, false, "ḁ")},
	{0x1e02, 0, 0, 1, f(Yes, false, "Ḃ")},
	{0x1e03, 0, 0, 1, f(Yes, false, "ḃ")},
	{0x1e04, 0, 0, 1, f(Yes, false, "Ḅ")},
	{0x1e05, 0, 0, 1, f(Yes, false, "ḅ")},
	{0x1e06, 0, 0, 1, f(Yes, false, "Ḇ")},
	{0x1e07, 0, 0, 1, f(Yes, false, "ḇ")},
	{0x1e08, 0, 0, 2, f(Yes, false, "Ḉ")},
	{0x1e09, 0, 0, 2, f(Yes, false, "ḉ")},
	{0x1e0a, 0, 0, 1, f(Yes, false, "Ḋ")},
	{0x1e0b, 0, 0, 1, f(Yes, false, "ḋ")},
	{0x1e0c, 0, 0, 1, f(Yes, false, "Ḍ")},
	{0x1e0d, 0, 0, 1, f(Yes, false, "ḍ")},
	{0x1e0e, 0, 0, 1, f(Yes, false, "Ḏ")},
	{0x1e0f, 0, 0, 1, f(Yes, false, "ḏ")},
	{0x1e10, 0, 0, 1, f(Yes, false, "Ḑ")},
	{0x1e11, 0, 0, 1, f(Yes, false, "ḑ")},
	{0x1e12, 0, 0, 1, f(Yes, false, "Ḓ")},
	{0x1e13, 0, 0, 1, f(Yes, false, "ḓ")},
	{0x1e14, 0, 0, 2, f(Yes, false, "Ḕ")},
	{0x1e15, 0, 0, 2, f(Yes, false, "ḕ")},
	{0x1e16, 0, 0, 2, f(Yes, false, "Ḗ")},
	{0x1e17, 0, 0, 2, f(Yes, false, "ḗ")},
	{0x1e18, 0, 0, 1, f(Yes, false, "Ḙ")},
	{0x1e19, 0, 0, 1, f(Yes, false, "ḙ")},
	{0x1e1a, 0, 0, 1, f(Yes, false, "Ḛ")},
	{0x1e1b, 0, 0, 1, f(Yes, false, "ḛ")},
	{0x1e1c, 0, 0, 2, f(Yes, false, "Ḝ")},
	{0x1e1d, 0, 0, 2, f(Yes, false, "ḝ")},
	{0x1e1e, 0, 0, 1, f(Yes, false, "Ḟ")},
	{0x1e1f, 0, 0, 1, f(Yes, false, "ḟ")},
	{0x1e20, 0, 0, 1, f(Yes, false, "Ḡ")},
	{0x1e21, 0, 0, 1, f(Yes, false, "ḡ")},
	{0x1e22, 0, 0, 1, f(Yes, false, "Ḣ")},
	{0x1e23, 0, 0, 1, f(Yes, false, "ḣ")},
	{0x1e24, 0, 0, 1, f(Yes, false, "Ḥ")},
	{0x1e25, 0, 0, 1, f(Yes, false, "ḥ")},
	{0x1e26, 0, 0, 1, f(Yes, false, "Ḧ")},
	{0x1e27, 0, 0, 1, f(Yes, false, "ḧ")},
	{0x1e28, 0, 0, 1, f(Yes, false, "Ḩ")},
	{0x1e29, 0, 0, 1, f(Yes, false, "ḩ")},
	{0x1e2a, 0, 0, 1, f(Yes, false, "Ḫ")},
	{0x1e2b, 0, 0, 1, f(Yes, false, "ḫ")},
	{0x1e2c, 0, 0, 1, f(Yes, false, "Ḭ")},
	{0x1e2d, 0, 0, 1, f(Yes, false, "ḭ")},
	{0x1e2e, 0, 0, 2, f(Yes, false, "Ḯ")},
	{0x1e2f, 0, 0, 2, f(Yes, false, "ḯ")},
	{0x1e30, 0, 0, 1, f(Yes, false, "Ḱ")},
	{0x1e31, 0, 0, 1, f(Yes, false, "ḱ")},
	{0x1e32, 0, 0, 1, f(Yes, false, "Ḳ")},
	{0x1e33, 0, 0, 1, f(Yes, false, "ḳ")},
	{0x1e34, 0, 0, 1, f(Yes, false, "Ḵ")},
	{0x1e35, 0, 0, 1, f(Yes, false, "ḵ")},
	{0x1e36, 0, 0, 1, f(Yes, true, "Ḷ")},
	{0x1e37, 0, 0, 1, f(Yes, true, "ḷ")},
	{0x1e38, 0, 0, 2, f(Yes, false, "Ḹ")},
	{0x1e39, 0, 0, 2, f(Yes, false, "ḹ")},
	{0x1e3a, 0, 0, 1, f(Yes, false, "Ḻ")},
	{0x1e3b, 0, 0, 1, f(Yes, false, "ḻ")},
	{0x1e3c, 0, 0, 1, f(Yes, false, "Ḽ")},
	{0x1e3d, 0, 0, 1, f(Yes, false, "ḽ")},
	{0x1e3e, 0, 0, 1, f(Yes, false, "Ḿ")},
	{0x1e3f, 0, 0, 1, f(Yes, false, "ḿ")},
	{0x1e40, 0, 0, 1, f(Yes, false, "Ṁ")},
	{0x1e41, 0, 0, 1, f(Yes, false, "ṁ")},
	{0x1e42, 0, 0, 1, f(Yes, false, "Ṃ")},
	{0x1e43, 0, 0, 1, f(Yes, false, "ṃ")},
	{0x1e44, 0, 0, 1, f(Yes, false, "Ṅ")},
	{0x1e45, 0, 0, 1, f(Yes, false, "ṅ")},
	{0x1e46, 0, 0, 1, f(Yes, false, "Ṇ")},
	{0x1e47, 0, 0, 1, f(Yes, false, "ṇ")},
	{0x1e48, 0, 0, 1, f(Yes, false, "Ṉ")},
	{0x1e49, 0, 0, 1, f(Yes, false, "ṉ")},
	{0x1e4a, 0, 0, 1, f(Yes, false, "Ṋ")},
	{0x1e4b, 0, 0, 1, f(Yes, false, "ṋ")},
	{0x1e4c, 0, 0, 2, f(Yes, false, "Ṍ")},
	{0x1e4d, 0, 0, 2, f(Yes, false, "ṍ")},
	{0x1e4e, 0, 0, 2, f(Yes, false, "Ṏ")},
	{0x1e4f, 0, 0, 2, f(Yes, false, "ṏ")},
	{0x1e50, 0, 0, 2, f(Yes, false, "Ṑ")},
	{0x1e51, 0, 0, 2, f(Yes, false, "ṑ")},
	{0x1e52, 0, 0, 2, f(Yes, false, "Ṓ")},
	{0x1e53, 0, 0, 2, f(Yes, false, "ṓ")},
	{0x1e54, 0, 0, 1, f(Yes, false, "Ṕ")},
	{0x1e55, 0, 0, 1, f(Yes, false, "ṕ")},
	{0x1e56, 0, 0, 1, f(Yes, false, "Ṗ")},
	{0x1e57, 0, 0, 1, f(Yes, false, "ṗ")},
	{0x1e58, 0, 0, 1, f(Yes, false, "Ṙ")},
	{0x1e59, 0, 0, 1, f(Yes, false, "ṙ")},
	{0x1e5a, 0, 0, 1, f(Yes, true, "Ṛ")},
	{0x1e5b, 0, 0, 1, f(Yes, true, "ṛ")},
	{0x1e5c, 0, 0, 2, f(Yes, false, "Ṝ")},
	{0x1e5d, 0, 0, 2, f(Yes, false, "ṝ")},
	{0x1e5e, 0, 0, 1, f(Yes, false, "Ṟ")},
	{0x1e5f, 0, 0, 1, f(Yes, false, "ṟ")},
	{0x1e60, 0, 0, 1, f(Yes, false, "Ṡ")},
	{0x1e61, 0, 0, 1, f(Yes, false, "ṡ")},
	{0x1e62, 0, 0, 1, f(Yes, true, "Ṣ")},
	{0x1e63, 0, 0, 1, f(Yes, true, "ṣ")},
	{0x1e64, 0, 0, 2, f(Yes, false, "Ṥ")},
	{0x1e65, 0, 0, 2, f(Yes, false, "ṥ")},
	{0x1e66, 0, 0, 2, f(Yes, false, "Ṧ")},
	{0x1e67, 0, 0, 2, f(Yes, false, "ṧ")},
	{0x1e68, 0, 0, 2, f(Yes, false, "Ṩ")},
	{0x1e69, 0, 0, 2, f(Yes, false, "ṩ")},
	{0x1e6a, 0, 0, 1, f(Yes, false, "Ṫ")},
	{0x1e6b, 0, 0, 1, f(Yes, false, "ṫ")},
	{0x1e6c, 0, 0, 1, f(Yes, false, "Ṭ")},
	{0x1e6d, 0, 0, 1, f(Yes, false, "ṭ")},
	{0x1e6e, 0, 0, 1, f(Yes, false, "Ṯ")},
	{0x1e6f, 0, 0, 1, f(Yes, false, "ṯ")},
	{0x1e70, 0, 0, 1, f(Yes, false, "Ṱ")},
	{0x1e71, 0, 0, 1, f(Yes, false, "ṱ")},
	{0x1e72, 0, 0, 1, f(Yes, false, "Ṳ")},
	{0x1e73, 0, 0, 1, f(Yes, false, "ṳ")},
	{0x1e74, 0, 0, 1, f(Yes, false, "Ṵ")},
	{0x1e75, 0, 0, 1, f(Yes, false, "ṵ")},
	{0x1e76, 0, 0, 1, f(Yes, false, "Ṷ")},
	{0x1e77, 0, 0, 1, f(Yes, false, "ṷ")},
	{0x1e78, 0, 0, 2, f(Yes, false, "Ṹ")},
	{0x1e79, 0, 0, 2, f(Yes, false, "ṹ")},
	{0x1e7a, 0, 0, 2, f(Yes, false, "Ṻ")},
	{0x1e7b, 0, 0, 2, f(Yes, false, "ṻ")},
	{0x1e7c, 0, 0, 1, f(Yes, false, "Ṽ")},
	{0x1e7d, 0, 0, 1, f(Yes, false, "ṽ")},
	{0x1e7e, 0, 0, 1, f(Yes, false, "Ṿ")},
	{0x1e7f, 0, 0, 1, f(Yes, false, "ṿ")},
	{0x1e80, 0, 0, 1, f(Yes, false, "Ẁ")},
	{0x1e81, 0, 0, 1, f(Yes, false, "ẁ")},
	{0x1e82, 0, 0, 1, f(Yes, false, "Ẃ")},
	{0x1e83, 0, 0, 1, f(Yes, false, "ẃ")},
	{0x1e84, 0, 0, 1, f(Yes, false, "Ẅ")},
	{0x1e85, 0, 0, 1, f(Yes, false, "ẅ")},
	{0x1e86, 0, 0, 1, f(Yes, false, "Ẇ")},
	{0x1e87, 0, 0, 1, f(Yes, false, "ẇ")},
	{0x1e88, 0, 0, 1, f(Yes, false, "Ẉ")},
	{0x1e89, 0, 0, 1, f(Yes, false, "ẉ")},
	{0x1e8a, 0, 0, 1, f(Yes, false, "Ẋ")},
	{0x1e8b, 0, 0, 1, f(Yes, false, "ẋ")},
	{0x1e8c, 0, 0, 1, f(Yes, false, "Ẍ")},
	{0x1e8d, 0, 0, 1, f(Yes, false, "ẍ")},
	{0x1e8e, 0, 0, 1, f(Yes, false, "Ẏ")},
	{0x1e8f, 0, 0, 1, f(Yes, false, "ẏ")},
	{0x1e90, 0, 0, 1, f(Yes, false, "Ẑ")},
	{0x1e91, 0, 0, 1, f(Yes, false, "ẑ")},
	{0x1e92, 0, 0, 1, f(Yes, false, "Ẓ")},
	{0x1e93, 0, 0, 1, f(Yes, false, "ẓ")},
	{0x1e94, 0, 0, 1, f(Yes, false, "Ẕ")},
	{0x1e95, 0, 0, 1, f(Yes, false, "ẕ")},
	{0x1e96, 0, 0, 1, f(Yes, false, "ẖ")},
	{0x1e97, 0, 0, 1, f(Yes, false, "ẗ")},
	{0x1e98, 0, 0, 1, f(Yes, false, "ẘ")},
	{0x1e99, 0, 0, 1, f(Yes, false, "ẙ")},
	{0x1e9a, 0, 0, 0, g(Yes, No, false, false, "", "aʾ")},
	{0x1e9b, 0, 0, 1, g(Yes, No, false, false, "ẛ", "ṡ")},
	{0x1e9c, 0, 0, 0, f(Yes, false, "")},
	{0x1ea0, 0, 0, 1, f(Yes, true, "Ạ")},
	{0x1ea1, 0, 0, 1, f(Yes, true, "ạ")},
	{0x1ea2, 0, 0, 1, f(Yes, false, "Ả")},
	{0x1ea3, 0, 0, 1, f(Yes, false, "ả")},
	{0x1ea4, 0, 0, 2, f(Yes, false, "Ấ")},
	{0x1ea5, 0, 0, 2, f(Yes, false, "ấ")},
	{0x1ea6, 0, 0, 2, f(Yes, false, "Ầ")},
	{0x1ea7, 0, 0, 2, f(Yes, false, "ầ")},
	{0x1ea8, 0, 0, 2, f(Yes, false, "Ẩ")},
	{0x1ea9, 0, 0, 2, f(Yes, false, "ẩ")},
	{0x1eaa, 0, 0, 2, f(Yes, false, "Ẫ")},
	{0x1eab, 0, 0, 2, f(Yes, false, "ẫ")},
	{0x1eac, 0, 0, 2, f(Yes, false, "Ậ")},
	{0x1ead, 0, 0, 2, f(Yes, false, "ậ")},
	{0x1eae, 0, 0, 2, f(Yes, false, "Ắ")},
	{0x1eaf, 0, 0, 2, f(Yes, false, "ắ")},
	{0x1eb0, 0, 0, 2, f(Yes, false, "Ằ")},
	{0x1eb1, 0, 0, 2, f(Yes, false, "ằ")},
	{0x1eb2, 0, 0, 2, f(Yes, false, "Ẳ")},
	{0x1eb3, 0, 0, 2, f(Yes, false, "ẳ")},
	{0x1eb4, 0, 0, 2, f(Yes, false, "Ẵ")},
	{0x1eb5, 0, 0, 2, f(Yes, false, "ẵ")},
	{0x1eb6, 0, 0, 2, f(Yes, false, "Ặ")},
	{0x1eb7, 0, 0, 2, f(Yes, false, "ặ")},
	{0x1eb8, 0, 0, 1, f(Yes, true, "Ẹ")},
	{0x1eb9, 0, 0, 1, f(Yes, true, "ẹ")},
	{0x1eba, 0, 0, 1, f(Yes, false, "Ẻ")},
	{0x1ebb, 0, 0, 1, f(Yes, false, "ẻ")},
	{0x1ebc, 0, 0, 1, f(Yes, false, "Ẽ")},
	{0x1ebd, 0, 0, 1, f(Yes, false, "ẽ")},
	{0x1ebe, 0, 0, 2, f(Yes, false, "Ế")},
	{0x1ebf, 0, 0, 2, f(Yes, false, "ế")},
	{0x1ec0, 0, 0, 2, f(Yes, false, "Ề")},
	{0x1ec1, 0, 0, 2, f(Yes, false, "ề")},
	{0x1ec2, 0, 0, 2, f(Yes, false, "Ể")},
	{0x1ec3, 0, 0, 2, f(Yes, false, "ể")},
	{0x1ec4, 0, 0, 2, f(Yes, false, "Ễ")},
	{0x1ec5, 0, 0, 2, f(Yes, false, "ễ")},
	{0x1ec6, 0, 0, 2, f(Yes, false, "Ệ")},
	{0x1ec7, 0, 0, 2, f(Yes, false, "ệ")},
	{0x1ec8, 0, 0, 1, f(Yes, false, "Ỉ")},
	{0x1ec9, 0, 0, 1, f(Yes, false, "ỉ")},
	{0x1eca, 0, 0, 1, f(Yes, false, "Ị")},
	{0x1ecb, 0, 0, 1, f(Yes, false, "ị")},
	{0x1ecc, 0, 0, 1, f(Yes, true, "Ọ")},
	{0x1ecd, 0, 0, 1, f(Yes, true, "ọ")},
	{0x1ece, 0, 0, 1, f(Yes, false, "Ỏ")},
	{0x1ecf, 0, 0, 1, f(Yes, false, "ỏ")},
	{0x1ed0, 0, 0, 2, f(Yes, false, "Ố")},
	{0x1ed1, 0, 0, 2, f(Yes, false, "ố")},
	{0x1ed2, 0, 0, 2, f(Yes, false, "Ồ")},
	{0x1ed3, 0, 0, 2, f(Yes, false, "ồ")},
	{0x1ed4, 0, 0, 2, f(Yes, false, "Ổ")},
	{0x1ed5, 0, 0, 2, f(Yes, false, "ổ")},
	{0x1ed6, 0, 0, 2, f(Yes, false, "Ỗ")},
	{0x1ed7, 0, 0, 2, f(Yes, false, "ỗ")},
	{0x1ed8, 0, 0, 2, f(Yes, false, "Ộ")},
	{0x1ed9, 0, 0, 2, f(Yes, false, "ộ")},
	{0x1eda, 0, 0, 2, f(Yes, false, "Ớ")},
	{0x1edb, 0, 0, 2, f(Yes, false, "ớ")},
	{0x1edc, 0, 0, 2, f(Yes, false, "Ờ")},
	{0x1edd, 0, 0, 2, f(Yes, false, "ờ")},
	{0x1ede, 0, 0, 2, f(Yes, false, "Ở")},
	{0x1edf, 0, 0, 2, f(Yes, false, "ở")},
	{0x1ee0, 0, 0, 2, f(Yes, false, "Ỡ")},
	{0x1ee1, 0, 0, 2, f(Yes, false, "ỡ")},
	{0x1ee2, 0, 0, 2, f(Yes, false, "Ợ")},
	{0x1ee3, 0, 0, 2, f(Yes, false, "ợ")},
	{0x1ee4, 0, 0, 1, f(Yes, false, "Ụ")},
	{0x1ee5, 0, 0, 1, f(Yes, false, "ụ")},
	{0x1ee6, 0, 0, 1, f(Yes, false, "Ủ")},
	{0x1ee7, 0, 0, 1, f(Yes, false, "ủ")},
	{0x1ee8, 0, 0, 2, f(Yes, false, "Ứ")},
	{0x1ee9, 0, 0, 2, f(Yes, false, "ứ")},
	{0x1eea, 0, 0, 2, f(Yes, false, "Ừ")},
	{0x1eeb, 0, 0, 2, f(Yes, false, "ừ")},
	{0x1eec, 0, 0, 2, f(Yes, false, "Ử")},
	{0x1eed, 0, 0, 2, f(Yes, false, "ử")},
	{0x1eee, 0, 0, 2, f(Yes, false, "Ữ")},
	{0x1eef, 0, 0, 2, f(Yes, false, "ữ")},
	{0x1ef0, 0, 0, 2, f(Yes, false, "Ự")},
	{0x1ef1, 0, 0, 2, f(Yes, false, "ự")},
	{0x1ef2, 0, 0, 1, f(Yes, false, "Ỳ")},
	{0x1ef3, 0, 0, 1, f(Yes, false, "ỳ")},
	{0x1ef4, 0, 0, 1, f(Yes, false, "Ỵ")},
	{0x1ef5, 0, 0, 1, f(Yes, false, "ỵ")},
	{0x1ef6, 0, 0, 1, f(Yes, false, "Ỷ")},
	{0x1ef7, 0, 0, 1, f(Yes, false, "ỷ")},
	{0x1ef8, 0, 0, 1, f(Yes, false, "Ỹ")},
	{0x1ef9, 0, 0, 1, f(Yes, false, "ỹ")},
	{0x1efa, 0, 0, 0, f(Yes, false, "")},
	{0x1f00, 0, 0, 1, f(Yes, true, "ἀ")},
	{0x1f01, 0, 0, 1, f(Yes, true, "ἁ")},
	{0x1f02, 0, 0, 2, f(Yes, true, "ἂ")},
	{0x1f03, 0, 0, 2, f(Yes, true, "ἃ")},
	{0x1f04, 0, 0, 2, f(Yes, true, "ἄ")},
	{0x1f05, 0, 0, 2, f(Yes, true, "ἅ")},
	{0x1f06, 0, 0, 2, f(Yes, true, "ἆ")},
	{0x1f07, 0, 0, 2, f(Yes, true, "ἇ")},
	{0x1f08, 0, 0, 1, f(Yes, true, "Ἀ")},
	{0x1f09, 0, 0, 1, f(Yes, true, "Ἁ")},
	{0x1f0a, 0, 0, 2, f(Yes, true, "Ἂ")},
	{0x1f0b, 0, 0, 2, f(Yes, true, "Ἃ")},
	{0x1f0c, 0, 0, 2, f(Yes, true, "Ἄ")},
	{0x1f0d, 0, 0, 2, f(Yes, true, "Ἅ")},
	{0x1f0e, 0, 0, 2, f(Yes, true, "Ἆ")},
	{0x1f0f, 0, 0, 2, f(Yes, true, "Ἇ")},
	{0x1f10, 0, 0, 1, f(Yes, true, "ἐ")},
	{0x1f11, 0, 0, 1, f(Yes, true, "ἑ")},
	{0x1f12, 0, 0, 2, f(Yes, false, "ἒ")},
	{0x1f13, 0, 0, 2, f(Yes, false, "ἓ")},
	{0x1f14, 0, 0, 2, f(Yes, false, "ἔ")},
	{0x1f15, 0, 0, 2, f(Yes, false, "ἕ")},
	{0x1f16, 0, 0, 0, f(Yes, false, "")},
	{0x1f18, 0, 0, 1, f(Yes, true, "Ἐ")},
	{0x1f19, 0, 0, 1, f(Yes, true, "Ἑ")},
	{0x1f1a, 0, 0, 2, f(Yes, false, "Ἒ")},
	{0x1f1b, 0, 0, 2, f(Yes, false, "Ἓ")},
	{0x1f1c, 0, 0, 2, f(Yes, false, "Ἔ")},
	{0x1f1d, 0, 0, 2, f(Yes, false, "Ἕ")},
	{0x1f1e, 0, 0, 0, f(Yes, false, "")},
	{0x1f20, 0, 0, 1, f(Yes, true, "ἠ")},
	{0x1f21, 0, 0, 1, f(Yes, true, "ἡ")},
	{0x1f22, 0, 0, 2, f(Yes, true, "ἢ")},
	{0x1f23, 0, 0, 2, f(Yes, true, "ἣ")},
	{0x1f24, 0, 0, 2, f(Yes, true, "ἤ")},
	{0x1f25, 0, 0, 2, f(Yes, true, "ἥ")},
	{0x1f26, 0, 0, 2, f(Yes, true, "ἦ")},
	{0x1f27, 0, 0, 2, f(Yes, true, "ἧ")},
	{0x1f28, 0, 0, 1, f(Yes, true, "Ἠ")},
	{0x1f29, 0, 0, 1, f(Yes, true, "Ἡ")},
	{0x1f2a, 0, 0, 2, f(Yes, true, "Ἢ")},
	{0x1f2b, 0, 0, 2, f(Yes, true, "Ἣ")},
	{0x1f2c, 0, 0, 2, f(Yes, true, "Ἤ")},
	{0x1f2d, 0, 0, 2, f(Yes, true, "Ἥ")},
	{0x1f2e, 0, 0, 2, f(Yes, true, "Ἦ")},
	{0x1f2f, 0, 0, 2, f(Yes, true, "Ἧ")},
	{0x1f30, 0, 0, 1, f(Yes, true, "ἰ")},
	{0x1f31, 0, 0, 1, f(Yes, true, "ἱ")},
	{0x1f32, 0, 0, 2, f(Yes, false, "ἲ")},
	{0x1f33, 0, 0, 2, f(Yes, false, "ἳ")},
	{0x1f34, 0, 0, 2, f(Yes, false, "ἴ")},
	{0x1f35, 0, 0, 2, f(Yes, false, "ἵ")},
	{0x1f36, 0, 0, 2, f(Yes, false, "ἶ")},
	{0x1f37, 0, 0, 2, f(Yes, false, "ἷ")},
	{0x1f38, 0, 0, 1, f(Yes, true, "Ἰ")},
	{0x1f39, 0, 0, 1, f(Yes, true, "Ἱ")},
	{0x1f3a, 0, 0, 2, f(Yes, false, "Ἲ")},
	{0x1f3b, 0, 0, 2, f(Yes, false, "Ἳ")},
	{0x1f3c, 0, 0, 2, f(Yes, false, "Ἴ")},
	{0x1f3d, 0, 0, 2, f(Yes, false, "Ἵ")},
	{0x1f3e, 0, 0, 2, f(Yes, false, "Ἶ")},
	{0x1f3f, 0, 0, 2, f(Yes, false, "Ἷ")},
	{0x1f40, 0, 0, 1, f(Yes, true, "ὀ")},
	{0x1f41, 0, 0, 1, f(Yes, true, "ὁ")},
	{0x1f42, 0, 0, 2, f(Yes, false, "ὂ")},
	{0x1f43, 0, 0, 2, f(Yes, false, "ὃ")},
	{0x1f44, 0, 0, 2, f(Yes, false, "ὄ")},
	{0x1f45, 0, 0, 2, f(Yes, false, "ὅ")},
	{0x1f46, 0, 0, 0, f(Yes, false, "")},
	{0x1f48, 0, 0, 1, f(Yes, true, "Ὀ")},
	{0x1f49, 0, 0, 1, f(Yes, true, "Ὁ")},
	{0x1f4a, 0, 0, 2, f(Yes, false, "Ὂ")},
	{0x1f4b, 0, 0, 2, f(Yes, false, "Ὃ")},
	{0x1f4c, 0, 0, 2, f(Yes, false, "Ὄ")},
	{0x1f4d, 0, 0, 2, f(Yes, false, "Ὅ")},
	{0x1f4e, 0, 0, 0, f(Yes, false, "")},
	{0x1f50, 0, 0, 1, f(Yes, true, "ὐ")},
	{0x1f51, 0, 0, 1, f(Yes, true, "ὑ")},
	{0x1f52, 0, 0, 2, f(Yes, false, "ὒ")},
	{0x1f53, 0, 0, 2, f(Yes, false, "ὓ")},
	{0x1f54, 0, 0, 2, f(Yes, false, "ὔ")},
	{0x1f55, 0, 0, 2, f(Yes, false, "ὕ")},
	{0x1f56, 0, 0, 2, f(Yes, false, "ὖ")},
	{0x1f57, 0, 0, 2, f(Yes, false, "ὗ")},
	{0x1f58, 0, 0, 0, f(Yes, false, "")},
	{0x1f59, 0, 0, 1, f(Yes, true, "Ὑ")},
	{0x1f5a, 0, 0, 0, f(Yes, false, "")},
	{0x1f5b, 0, 0, 2, f(Yes, false, "Ὓ")},
	{0x1f5c, 0, 0, 0, f(Yes, false, "")},
	{0x1f5d, 0, 0, 2, f(Yes, false, "Ὕ")},
	{0x1f5e, 0, 0, 0, f(Yes, false, "")},
	{0x1f5f, 0, 0, 2, f(Yes, false, "Ὗ")},
	{0x1f60, 0, 0, 1, f(Yes, true, "ὠ")},
	{0x1f61, 0, 0, 1, f(Yes, true, "ὡ")},
	{0x1f62, 0, 0, 2, f(Yes, true, "ὢ")},
	{0x1f63, 0, 0, 2, f(Yes, true, "ὣ")},
	{0x1f64, 0, 0, 2, f(Yes, true, "ὤ")},
	{0x1f65, 0, 0, 2, f(Yes, true, "ὥ")},
	{0x1f66, 0, 0, 2, f(Yes, true, "ὦ")},
	{0x1f67, 0, 0, 2, f(Yes, true, "ὧ")},
	{0x1f68, 0, 0, 1, f(Yes, true, "Ὠ")},
	{0x1f69, 0, 0, 1, f(Yes, true, "Ὡ")},
	{0x1f6a, 0, 0, 2, f(Yes, true, "Ὢ")},
	{0x1f6b, 0, 0, 2, f(Yes, true, "Ὣ")},
	{0x1f6c, 0, 0, 2, f(Yes, true, "Ὤ")},
	{0x1f6d, 0, 0, 2, f(Yes, true, "Ὥ")},
	{0x1f6e, 0, 0, 2, f(Yes, true, "Ὦ")},
	{0x1f6f, 0, 0, 2, f(Yes, true, "Ὧ")},
	{0x1f70, 0, 0, 1, f(Yes, true, "ὰ")},
	{0x1f71, 0, 0, 1, f(No, false, "ά")},
	{0x1f72, 0, 0, 1, f(Yes, false, "ὲ")},
	{0x1f73, 0, 0, 1, f(No, false, "έ")},
	{0x1f74, 0, 0, 1, f(Yes, true, "ὴ")},
	{0x1f75, 0, 0, 1, f(No, false, "ή")},
	{0x1f76, 0, 0, 1, f(Yes, false, "ὶ")},
	{0x1f77, 0, 0, 1, f(No, false, "ί")},
	{0x1f78, 0, 0, 1, f(Yes, false, "ὸ")},
	{0x1f79, 0, 0, 1, f(No, false, "ό")},
	{0x1f7a, 0, 0, 1, f(Yes, false, "ὺ")},
	{0x1f7b, 0, 0, 1, f(No, false, "ύ")},
	{0x1f7c, 0, 0, 1, f(Yes, true, "ὼ")},
	{0x1f7d, 0, 0, 1, f(No, false, "ώ")},
	{0x1f7e, 0, 0, 0, f(Yes, false, "")},
	{0x1f80, 0, 0, 2, f(Yes, false, "ᾀ")},
	{0x1f81, 0, 0, 2, f(Yes, false, "ᾁ")},
	{0x1f82, 0, 0, 3, f(Yes, false, "ᾂ")},
	{0x1f83, 0, 0, 3, f(Yes, false, "ᾃ")},
	{0x1f84, 0, 0, 3, f(Yes, false, "ᾄ")},
	{0x1f85, 0, 0, 3, f(Yes, false, "ᾅ")},
	{0x1f86, 0, 0, 3, f(Yes, false, "ᾆ")},
	{0x1f87, 0, 0, 3, f(Yes, false, "ᾇ")},
	{0x1f88, 0, 0, 2, f(Yes, false, "ᾈ")},
	{0x1f89, 0, 0, 2, f(Yes, false, "ᾉ")},
	{0x1f8a, 0, 0, 3, f(Yes, false, "ᾊ")},
	{0x1f8b, 0, 0, 3, f(Yes, false, "ᾋ")},
	{0x1f8c, 0, 0, 3, f(Yes, false, "ᾌ")},
	{0x1f8d, 0, 0, 3, f(Yes, false, "ᾍ")},
	{0x1f8e, 0, 0, 3, f(Yes, false, "ᾎ")},
	{0x1f8f, 0, 0, 3, f(Yes, false, "ᾏ")},
	{0x1f90, 0, 0, 2, f(Yes, false, "ᾐ")},
	{0x1f91, 0, 0, 2, f(Yes, false, "ᾑ")},
	{0x1f92, 0, 0, 3, f(Yes, false, "ᾒ")},
	{0x1f93, 0, 0, 3, f(Yes, false, "ᾓ")},
	{0x1f94, 0, 0, 3, f(Yes, false, "ᾔ")},
	{0x1f95, 0, 0, 3, f(Yes, false, "ᾕ")},
	{0x1f96, 0, 0, 3, f(Yes, false, "ᾖ")},
	{0x1f97, 0, 0, 3, f(Yes, false, "ᾗ")},
	{0x1f98, 0, 0, 2, f(Yes, false, "ᾘ")},
	{0x1f99, 0, 0, 2, f(Yes, false, "ᾙ")},
	{0x1f9a, 0, 0, 3, f(Yes, false, "ᾚ")},
	{0x1f9b, 0, 0, 3, f(Yes, false, "ᾛ")},
	{0x1f9c, 0, 0, 3, f(Yes, false, "ᾜ")},
	{0x1f9d, 0, 0, 3, f(Yes, false, "ᾝ")},
	{0x1f9e, 0, 0, 3, f(Yes, false, "ᾞ")},
	{0x1f9f, 0, 0, 3, f(Yes, false, "ᾟ")},
	{0x1fa0, 0, 0, 2, f(Yes, false, "ᾠ")},
	{0x1fa1, 0, 0, 2, f(Yes, false, "ᾡ")},
	{0x1fa2, 0, 0, 3, f(Yes, false, "ᾢ")},
	{0x1fa3, 0, 0, 3, f(Yes, false, "ᾣ")},
	{0x1fa4, 0, 0, 3, f(Yes, false, "ᾤ")},
	{0x1fa5, 0, 0, 3, f(Yes, false, "ᾥ")},
	{0x1fa6, 0, 0, 3, f(Yes, false, "ᾦ")},
	{0x1fa7, 0, 0, 3, f(Yes, false, "ᾧ")},
	{0x1fa8, 0, 0, 2, f(Yes, false, "ᾨ")},
	{0x1fa9, 0, 0, 2, f(Yes, false, "ᾩ")},
	{0x1faa, 0, 0, 3, f(Yes, false, "ᾪ")},
	{0x1fab, 0, 0, 3, f(Yes, false, "ᾫ")},
	{0x1fac, 0, 0, 3, f(Yes, false, "ᾬ")},
	{0x1fad, 0, 0, 3, f(Yes, false, "ᾭ")},
	{0x1fae, 0, 0, 3, f(Yes, false, "ᾮ")},
	{0x1faf, 0, 0, 3, f(Yes, false, "ᾯ")},
	{0x1fb0, 0, 0, 1, f(Yes, false, "ᾰ")},
	{0x1fb1, 0, 0, 1, f(Yes, false, "ᾱ")},
	{0x1fb2, 0, 0, 2, f(Yes, false, "ᾲ")},
	{0x1fb3, 0, 0, 1, f(Yes, false, "ᾳ")},
	{0x1fb4, 0, 0, 2, f(Yes, false, "ᾴ")},
	{0x1fb5, 0, 0, 0, f(Yes, false, "")},
	{0x1fb6, 0, 0, 1, f(Yes, true, "ᾶ")},
	{0x1fb7, 0, 0, 2, f(Yes, false, "ᾷ")},
	{0x1fb8, 0, 0, 1, f(Yes, false, "Ᾰ")},
	{0x1fb9, 0, 0, 1, f(Yes, false, "Ᾱ")},
	{0x1fba, 0, 0, 1, f(Yes, false, "Ὰ")},
	{0x1fbb, 0, 0, 1, f(No, false, "Ά")},
	{0x1fbc, 0, 0, 1, f(Yes, false, "ᾼ")},
	{0x1fbd, 0, 0, 1, g(Yes, No, false, false, "", " ̓")},
	{0x1fbe, 0, 0, 0, f(No, false, "ι")},
	{0x1fbf, 0, 0, 1, g(Yes, No, true, false, "", " ̓")},
	{0x1fc0, 0, 0, 1, g(Yes, No, false, false, "", " ͂")},
	{0x1fc1, 0, 0, 2, g(Yes, No, false, false, "῁", " ̈͂")},
	{0x1fc2, 0, 0, 2, f(Yes, false, "ῂ")},
	{0x1fc3, 0, 0, 1, f(Yes, false, "ῃ")},
	{0x1fc4, 0, 0, 2, f(Yes, false, "ῄ")},
	{0x1fc5, 0, 0, 0, f(Yes, false, "")},
	{0x1fc6, 0, 0, 1, f(Yes, true, "ῆ")},
	{0x1fc7, 0, 0, 2, f(Yes, false, "ῇ")},
	{0x1fc8, 0, 0, 1, f(Yes, false, "Ὲ")},
	{0x1fc9, 0, 0, 1, f(No, false, "Έ")},
	{0x1fca, 0, 0, 1, f(Yes, false, "Ὴ")},
	{0x1fcb, 0, 0, 1, f(No, false, "Ή")},
	{0x1fcc, 0, 0, 1, f(Yes, false, "ῌ")},
	{0x1fcd, 0, 0, 2, g(Yes, No, false, false, "῍", " ̓̀")},
	{0x1fce, 0, 0, 2, g(Yes, No, false, false, "῎", " ̓́")},
	{0x1fcf, 0, 0, 2, g(Yes, No, false, false, "῏", " ̓͂")},
	{0x1fd0, 0, 0, 1, f(Yes, false, "ῐ")},
	{0x1fd1, 0, 0, 1, f(Yes, false, "ῑ")},
	{0x1fd2, 0, 0, 2, f(Yes, false, "ῒ")},
	{0x1fd3, 0, 0, 2, f(No, false, "ΐ")},
	{0x1fd4, 0, 0, 0, f(Yes, false, "")},
	{0x1fd6, 0, 0, 1, f(Yes, false, "ῖ")},
	{0x1fd7, 0, 0, 2, f(Yes, false, "ῗ")},
	{0x1fd8, 0, 0, 1, f(Yes, false, "Ῐ")},
	{0x1fd9, 0, 0, 1, f(Yes, false, "Ῑ")},
	{0x1fda, 0, 0, 1, f(Yes, false, "Ὶ")},
	{0x1fdb, 0, 0, 1, f(No, false, "Ί")},
	{0x1fdc, 0, 0, 0, f(Yes, false, "")},
	{0x1fdd, 0, 0, 2, g(Yes, No, false, false, "῝", " ̔̀")},
	{0x1fde, 0, 0, 2, g(Yes, No, false, false, "῞", " ̔́")},
	{0x1fdf, 0, 0, 2, g(Yes, No, false, false, "῟", " ̔͂")},
	{0x1fe0, 0, 0, 1, f(Yes, false, "ῠ")},
	{0x1fe1, 0, 0, 1, f(Yes, false, "ῡ")},
	{0x1fe2, 0, 0, 2, f(Yes, false, "ῢ")},
	{0x1fe3, 0, 0, 2, f(No, false, "ΰ")},
	{0x1fe4, 0, 0, 1, f(Yes, false, "ῤ")},
	{0x1fe5, 0, 0, 1, f(Yes, false, "ῥ")},
	{0x1fe6, 0, 0, 1, f(Yes, false, "ῦ")},
	{0x1fe7, 0, 0, 2, f(Yes, false, "ῧ")},
	{0x1fe8, 0, 0, 1, f(Yes, false, "Ῠ")},
	{0x1fe9, 0, 0, 1, f(Yes, false, "Ῡ")},
	{0x1fea, 0, 0, 1, f(Yes, false, "Ὺ")},
	{0x1feb, 0, 0, 1, f(No, false, "Ύ")},
	{0x1fec, 0, 0, 1, f(Yes, false, "Ῥ")},
	{0x1fed, 0, 0, 2, g(Yes, No, false, false, "῭", " ̈̀")},
	{0x1fee, 0, 0, 2, g(No, No, false, false, "΅", " ̈́")},
	{0x1fef, 0, 0, 0, f(No, false, "`")},
	{0x1ff0, 0, 0, 0, f(Yes, false, "")},
	{0x1ff2, 0, 0, 2, f(Yes, false, "ῲ")},
	{0x1ff3, 0, 0, 1, f(Yes, false, "ῳ")},
	{0x1ff4, 0, 0, 2, f(Yes, false, "ῴ")},
	{0x1ff5, 0, 0, 0, f(Yes, false, "")},
	{0x1ff6, 0, 0, 1, f(Yes, true, "ῶ")},
	{0x1ff7, 0, 0, 2, f(Yes, false, "ῷ")},
	{0x1ff8, 0, 0, 1, f(Yes, false, "Ὸ")},
	{0x1ff9, 0, 0, 1, f(No, false, "Ό")},
	{0x1ffa, 0, 0, 1, f(Yes, false, "Ὼ")},
	{0x1ffb, 0, 0, 1, f(No, false, "Ώ")},
	{0x1ffc, 0, 0, 1, f(Yes, false, "ῼ")},
	{0x1ffd, 0, 0, 1, g(No, No, false, false, "´", " ́")},
	{0x1ffe, 0, 0, 1, g(Yes, No, true, false, "", " ̔")},
	{0x1fff, 0, 0, 0, f(Yes, false, "")},
	{0x2000, 0, 0, 0, g(No, No, false, false, "\u2002", " ")},
	{0x2001, 0, 0, 0, g(No, No, false, false, "\u2003", " ")},
	{0x2002, 0, 0, 0, g(Yes, No, false, false, "", " ")},
	{0x200b, 0, 0, 0, f(Yes, false, "")},
	{0x2011, 0, 0, 0, g(Yes, No, false, false, "", "‐")},
	{0x2012, 0, 0, 0, f(Yes, false, "")},
	{0x2017, 0, 0, 1, g(Yes, No, false, false, "", " ̳")},
	{0x2018, 0, 0, 0, f(Yes, false, "")},
	{0x2024, 0, 0, 0, g(Yes, No, false, false, "", ".")},
	{0x2025, 0, 0, 0, g(Yes, No, false, false, "", "..")},
	{0x2026, 0, 0, 0, g(Yes, No, false, false, "", "...")},
	{0x2027, 0, 0, 0, f(Yes, false, "")},
	{0x202f, 0, 0, 0, g(Yes, No, false, false, "", " ")},
	{0x2030, 0, 0, 0, f(Yes, false, "")},
	{0x2033, 0, 0, 0, g(Yes, No, false, false, "", "′′")},
	{0x2034, 0, 0, 0, g(Yes, No, false, false, "", "′′′")},
	{0x2035, 0, 0, 0, f(Yes, false, "")},
	{0x2036, 0, 0, 0, g(Yes, No, false, false, "", "‵‵")},
	{0x2037, 0, 0, 0, g(Yes, No, false, false, "", "‵‵‵")},
	{0x2038, 0, 0, 0, f(Yes, false, "")},
	{0x203c, 0, 0, 0, g(Yes, No, false, false, "", "!!")},
	{0x203d, 0, 0, 0, f(Yes, false, "")},
	{0x203e, 0, 0, 1, g(Yes, No, false, false, "", " ̅")},
	{0x203f, 0, 0, 0, f(Yes, false, "")},
	{0x2047, 0, 0, 0, g(Yes, No, false, false, "", "??")},
	{0x2048, 0, 0, 0, g(Yes, No, false, false, "", "?!")},
	{0x2049, 0, 0, 0, g(Yes, No, false, false, "", "!?")},
	{0x204a, 0, 0, 0, f(Yes, false, "")},
	{0x2057, 0, 0, 0, g(Yes, No, false, false, "", "′′′′")},
	{0x2058, 0, 0, 0, f(Yes, false, "")},
	{0x205f, 0, 0, 0, g(Yes, No, false, false, "", " ")},
	{0x2060, 0, 0, 0, f(Yes, false, "")},
	{0x2070, 0, 0, 0, g(Yes, No, false, false, "", "0")},
	{0x2071, 0, 0, 0, g(Yes, No, false, false, "", "i")},
	{0x2072, 0, 0, 0, f(Yes, false, "")},
	{0x2074, 0, 0, 0, g(Yes, No, false, false, "", "4")},
	{0x2075, 0, 0, 0, g(Yes, No, false, false, "", "5")},
	{0x2076, 0, 0, 0, g(Yes, No, false, false, "", "6")},
	{0x2077, 0, 0, 0, g(Yes, No, false, false, "", "7")},
	{0x2078, 0, 0, 0, g(Yes, No, false, false, "", "8")},
	{0x2079, 0, 0, 0, g(Yes, No, false, false, "", "9")},
	{0x207a, 0, 0, 0, g(Yes, No, false, false, "", "+")},
	{0x207b, 0, 0, 0, g(Yes, No, false, false, "", "−")},
	{0x207c, 0, 0, 0, g(Yes, No, false, false, "", "=")},
	{0x207d, 0, 0, 0, g(Yes, No, false, false, "", "(")},
	{0x207e, 0, 0, 0, g(Yes, No, false, false, "", ")")},
	{0x207f, 0, 0, 0, g(Yes, No, false, false, "", "n")},
	{0x2080, 0, 0, 0, g(Yes, No, false, false, "", "0")},
	{0x2081, 0, 0, 0, g(Yes, No, false, false, "", "1")},
	{0x2082, 0, 0, 0, g(Yes, No, false, false, "", "2")},
	{0x2083, 0, 0, 0, g(Yes, No, false, false, "", "3")},
	{0x2084, 0, 0, 0, g(Yes, No, false, false, "", "4")},
	{0x2085, 0, 0, 0, g(Yes, No, false, false, "", "5")},
	{0x2086, 0, 0, 0, g(Yes, No, false, false, "", "6")},
	{0x2087, 0, 0, 0, g(Yes, No, false, false, "", "7")},
	{0x2088, 0, 0, 0, g(Yes, No, false, false, "", "8")},
	{0x2089, 0, 0, 0, g(Yes, No, false, false, "", "9")},
	{0x208a, 0, 0, 0, g(Yes, No, false, false, "", "+")},
	{0x208b, 0, 0, 0, g(Yes, No, false, false, "", "−")},
	{0x208c, 0, 0, 0, g(Yes, No, false, false, "", "=")},
	{0x208d, 0, 0, 0, g(Yes, No, false, false, "", "(")},
	{0x208e, 0, 0, 0, g(Yes, No, false, false, "", ")")},
	{0x208f, 0, 0, 0, f(Yes, false, "")},
	{0x2090, 0, 0, 0, g(Yes, No, false, false, "", "a")},
	{0x2091, 0, 0, 0, g(Yes, No, false, false, "", "e")},
	{0x2092, 0, 0, 0, g(Yes, No, false, false, "", "o")},
	{0x2093, 0, 0, 0, g(Yes, No, false, false, "", "x")},
	{0x2094, 0, 0, 0, g(Yes, No, false, false, "", "ə")},
	{0x2095, 0, 0, 0, g(Yes, No, false, false, "", "h")},
	{0x2096, 0, 0, 0, g(Yes, No, false, false, "", "k")},
	{0x2097, 0, 0, 0, g(Yes, No, false, false, "", "l")},
	{0x2098, 0, 0, 0, g(Yes, No, false, false, "", "m")},
	{0x2099, 0, 0, 0, g(Yes, No, false, false, "", "n")},
	{0x209a, 0, 0, 0, g(Yes, No, false, false, "", "p")},
	{0x209b, 0, 0, 0, g(Yes, No, false, false, "", "s")},
	{0x209c, 0, 0, 0, g(Yes, No, false, false, "", "t")},
	{0x209d, 0, 0, 0, f(Yes, false, "")},
	{0x20a8, 0, 0, 0, g(Yes, No, false, false, "", "Rs")},
	{0x20a9, 0, 0, 0, f(Yes, false, "")},
	{0x20d0, 230, 1, 1, f(Yes, false, "")},
	{0x20d2, 1, 1, 1, f(Yes, false, "")},
	{0x20d4, 230, 1, 1, f(Yes, false, "")},
	{0x20d8, 1, 1, 1, f(Yes, false, "")},
	{0x20db, 230, 1, 1, f(Yes, false, "")},
	{0x20dd, 0, 0, 0, f(Yes, false, "")},
	{0x20e1, 230, 1, 1, f(Yes, false, "")},
	{0x20e2, 0, 0, 0, f(Yes, false, "")},
	{0x20e5, 1, 1, 1, f(Yes, false, "")},
	{0x20e7, 230, 1, 1, f(Yes, false, "")},
	{0x20e8, 220, 1, 1, f(Yes, false, "")},
	{0x20e9, 230, 1, 1, f(Yes, false, "")},
	{0x20ea, 1, 1, 1, f(Yes, false, "")},
	{0x20ec, 220, 1, 1, f(Yes, false, "")},
	{0x20f0, 230, 1, 1, f(Yes, false, "")},
	{0x20f1, 0, 0, 0, f(Yes, false, "")},
	{0x2100, 0, 0, 0, g(Yes, No, false, false, "", "a/c")},
	{0x2101, 0, 0, 0, g(Yes, No, false, false, "", "a/s")},
	{0x2102, 0, 0, 0, g(Yes, No, false, false, "", "C")},
	{0x2103, 0, 0, 0, g(Yes, No, false, false, "", "°C")},
	{0x2104, 0, 0, 0, f(Yes, false, "")},
	{0x2105, 0, 0, 0, g(Yes, No, false, false, "", "c/o")},
	{0x2106, 0, 0, 0, g(Yes, No, false, false, "", "c/u")},
	{0x2107, 0, 0, 0, g(Yes, No, false, false, "", "Ɛ")},
	{0x2108, 0, 0, 0, f(Yes, false, "")},
	{0x2109, 0, 0, 0, g(Yes, No, false, false, "", "°F")},
	{0x210a, 0, 0, 0, g(Yes, No, false, false, "", "g")},
	{0x210b, 0, 0, 0, g(Yes, No, false, false, "", "H")},
	{0x210e, 0, 0, 0, g(Yes, No, false, false, "", "h")},
	{0x210f, 0, 0, 0, g(Yes, No, false, false, "", "ħ")},
	{0x2110, 0, 0, 0, g(Yes, No, false, false, "", "I")},
	{0x2112, 0, 0, 0, g(Yes, No, false, false, "", "L")},
	{0x2113, 0, 0, 0, g(Yes, No, false, false, "", "l")},
	{0x2114, 0, 0, 0, f(Yes, false, "")},
	{0x2115, 0, 0, 0, g(Yes, No, false, false, "", "N")},
	{0x2116, 0, 0, 0, g(Yes, No, false, false, "", "No")},
	{0x2117, 0, 0, 0, f(Yes, false, "")},
	{0x2119, 0, 0, 0, g(Yes, No, false, false, "", "P")},
	{0x211a, 0, 0, 0, g(Yes, No, false, false, "", "Q")},
	{0x211b, 0, 0, 0, g(Yes, No, false, false, "", "R")},
	{0x211e, 0, 0, 0, f(Yes, false, "")},
	{0x2120, 0, 0, 0, g(Yes, No, false, false, "", "SM")},
	{0x2121, 0, 0, 0, g(Yes, No, false, false, "", "TEL")},
	{0x2122, 0, 0, 0, g(Yes, No, false, false, "", "TM")},
	{0x2123, 0, 0, 0, f(Yes, false, "")},
	{0x2124, 0, 0, 0, g(Yes, No, false, false, "", "Z")},
	{0x2125, 0, 0, 0, f(Yes, false, "")},
	{0x2126, 0, 0, 0, f(No, false, "Ω")},
	{0x2127, 0, 0, 0, f(Yes, false, "")},
	{0x2128, 0, 0, 0, g(Yes, No, false, false, "", "Z")},
	{0x2129, 0, 0, 0, f(Yes, false, "")},
	{0x212a, 0, 0, 0, f(No, false, "K")},
	{0x212b, 0, 0, 1, f(No, false, "Å")},
	{0x212c, 0, 0, 0, g(Yes, No, false, false, "", "B")},
	{0x212d, 0, 0, 0, g(Yes, No, false, false, "", "C")},
	{0x212e, 0, 0, 0, f(Yes, false, "")},
	{0x212f, 0, 0, 0, g(Yes, No, false, false, "", "e")},
	{0x2130, 0, 0, 0, g(Yes, No, false, false, "", "E")},
	{0x2131, 0, 0, 0, g(Yes, No, false, false, "", "F")},
	{0x2132, 0, 0, 0, f(Yes, false, "")},
	{0x2133, 0, 0, 0, g(Yes, No, false, false, "", "M")},
	{0x2134, 0, 0, 0, g(Yes, No, false, false, "", "o")},
	{0x2135, 0, 0, 0, g(Yes, No, false, false, "", "א")},
	{0x2136, 0, 0, 0, g(Yes, No, false, false, "", "ב")},
	{0x2137, 0, 0, 0, g(Yes, No, false, false, "", "ג")},
	{0x2138, 0, 0, 0, g(Yes, No, false, false, "", "ד")},
	{0x2139, 0, 0, 0, g(Yes, No, false, false, "", "i")},
	{0x213a, 0, 0, 0, f(Yes, false, "")},
	{0x213b, 0, 0, 0, g(Yes, No, false, false, "", "FAX")},
	{0x213c, 0, 0, 0, g(Yes, No, false, false, "", "π")},
	{0x213d, 0, 0, 0, g(Yes, No, false, false, "", "γ")},
	{0x213e, 0, 0, 0, g(Yes, No, false, false, "", "Γ")},
	{0x213f, 0, 0, 0, g(Yes, No, false, false, "", "Π")},
	{0x2140, 0, 0, 0, g(Yes, No, false, false, "", "∑")},
	{0x2141, 0, 0, 0, f(Yes, false, "")},
	{0x2145, 0, 0, 0, g(Yes, No, false, false, "", "D")},
	{0x2146, 0, 0, 0, g(Yes, No, false, false, "", "d")},
	{0x2147, 0, 0, 0, g(Yes, No, false, false, "", "e")},
	{0x2148, 0, 0, 0, g(Yes, No, false, false, "", "i")},
	{0x2149, 0, 0, 0, g(Yes, No, false, false, "", "j")},
	{0x214a, 0, 0, 0, f(Yes, false, "")},
	{0x2150, 0, 0, 0, g(Yes, No, false, false, "", "1⁄7")},
	{0x2151, 0, 0, 0, g(Yes, No, false, false, "", "1⁄9")},
	{0x2152, 0, 0, 0, g(Yes, No, false, false, "", "1⁄10")},
	{0x2153, 0, 0, 0, g(Yes, No, false, false, "", "1⁄3")},
	{0x2154, 0, 0, 0, g(Yes, No, false, false, "", "2⁄3")},
	{0x2155, 0, 0, 0, g(Yes, No, false, false, "", "1⁄5")},
	{0x2156, 0, 0, 0, g(Yes, No, false, false, "", "2⁄5")},
	{0x2157, 0, 0, 0, g(Yes, No, false, false, "", "3⁄5")},
	{0x2158, 0, 0, 0, g(Yes, No, false, false, "", "4⁄5")},
	{0x2159, 0, 0, 0, g(Yes, No, false, false, "", "1⁄6")},
	{0x215a, 0, 0, 0, g(Yes, No, false, false, "", "5⁄6")},
	{0x215b, 0, 0, 0, g(Yes, No, false, false, "", "1⁄8")},
	{0x215c, 0, 0, 0, g(Yes, No, false, false, "", "3⁄8")},
	{0x215d, 0, 0, 0, g(Yes, No, false, false, "", "5⁄8")},
	{0x215e, 0, 0, 0, g(Yes, No, false, false, "", "7⁄8")},
	{0x215f, 0, 0, 0, g(Yes, No, false, false, "", "1⁄")},
	{0x2160, 0, 0, 0, g(Yes, No, false, false, "", "I")},
	{0x2161, 0, 0, 0, g(Yes, No, false, false, "", "II")},
	{0x2162, 0, 0, 0, g(Yes, No, false, false, "", "III")},
	{0x2163, 0, 0, 0, g(Yes, No, false, false, "", "IV")},
	{0x2164, 0, 0, 0, g(Yes, No, false, false, "", "V")},
	{0x2165, 0, 0, 0, g(Yes, No, false, false, "", "VI")},
	{0x2166, 0, 0, 0, g(Yes, No, false, false, "", "VII")},
	{0x2167, 0, 0, 0, g(Yes, No, false, false, "", "VIII")},
	{0x2168, 0, 0, 0, g(Yes, No, false, false, "", "IX")},
	{0x2169, 0, 0, 0, g(Yes, No, false, false, "", "X")},
	{0x216a, 0, 0, 0, g(Yes, No, false, false, "", "XI")},
	{0x216b, 0, 0, 0, g(Yes, No, false, false, "", "XII")},
	{0x216c, 0, 0, 0, g(Yes, No, false, false, "", "L")},
	{0x216d, 0, 0, 0, g(Yes, No, false, false, "", "C")},
	{0x216e, 0, 0, 0, g(Yes, No, false, false, "", "D")},
	{0x216f, 0, 0, 0, g(Yes, No, false, false, "", "M")},
	{0x2170, 0, 0, 0, g(Yes, No, false, false, "", "i")},
	{0x2171, 0, 0, 0, g(Yes, No, false, false, "", "ii")},
	{0x2172, 0, 0, 0, g(Yes, No, false, false, "", "iii")},
	{0x2173, 0, 0, 0, g(Yes, No, false, false, "", "iv")},
	{0x2174, 0, 0, 0, g(Yes, No, false, false, "", "v")},
	{0x2175, 0, 0, 0, g(Yes, No, false, false, "", "vi")},
	{0x2176, 0, 0, 0, g(Yes, No, false, false, "", "vii")},
	{0x2177, 0, 0, 0, g(Yes, No, false, false, "", "viii")},
	{0x2178, 0, 0, 0, g(Yes, No, false, false, "", "ix")},
	{0x2179, 0, 0, 0, g(Yes, No, false, false, "", "x")},
	{0x217a, 0, 0, 0, g(Yes, No, false, false, "", "xi")},
	{0x217b, 0, 0, 0, g(Yes, No, false, false, "", "xii")},
	{0x217c, 0, 0, 0, g(Yes, No, false, false, "", "l")},
	{0x217d, 0, 0, 0, g(Yes, No, false, false, "", "c")},
	{0x217e, 0, 0, 0, g(Yes, No, false, false, "", "d")},
	{0x217f, 0, 0, 0, g(Yes, No, false, false, "", "m")},
	{0x2180, 0, 0, 0, f(Yes, false, "")},
	{0x2189, 0, 0, 0, g(Yes, No, false, false, "", "0⁄3")},
	{0x218a, 0, 0, 0, f(Yes, false, "")},
	{0x2190, 0, 0, 0, f(Yes, true, "")},
	{0x2191, 0, 0, 0, f(Yes, false, "")},
	{0x2192, 0, 0, 0, f(Yes, true, "")},
	{0x2193, 0, 0, 0, f(Yes, false, "")},
	{0x2194, 0, 0, 0, f(Yes, true, "")},
	{0x2195, 0, 0, 0, f(Yes, false, "")},
	{0x219a, 0, 0, 1, f(Yes, false, "↚")},
	{0x219b, 0, 0, 1, f(Yes, false, "↛")},
	{0x219c, 0, 0, 0, f(Yes, false, "")},
	{0x21ae, 0, 0, 1, f(Yes, false, "↮")},
	{0x21af, 0, 0, 0, f(Yes, false, "")},
	{0x21cd, 0, 0, 1, f(Yes, false, "⇍")},
	{0x21ce, 0, 0, 1, f(Yes, false, "⇎")},
	{0x21cf, 0, 0, 1, f(Yes, false, "⇏")},
	{0x21d0, 0, 0, 0, f(Yes, true, "")},
	{0x21d1, 0, 0, 0, f(Yes, false, "")},
	{0x21d2, 0, 0, 0, f(Yes, true, "")},
	{0x21d3, 0, 0, 0, f(Yes, false, "")},
	{0x21d4, 0, 0, 0, f(Yes, true, "")},
	{0x21d5, 0, 0, 0, f(Yes, false, "")},
	{0x2203, 0, 0, 0, f(Yes, true, "")},
	{0x2204, 0, 0, 1, f(Yes, false, "∄")},
	{0x2205, 0, 0, 0, f(Yes, false, "")},
	{0x2208, 0, 0, 0, f(Yes, true, "")},
	{0x2209, 0, 0, 1, f(Yes, false, "∉")},
	{0x220a, 0, 0, 0, f(Yes, false, "")},
	{0x220b, 0, 0, 0, f(Yes, true, "")},
	{0x220c, 0, 0, 1, f(Yes, false, "∌")},
	{0x220d, 0, 0, 0, f(Yes, false, "")},
	{0x2223, 0, 0, 0, f(Yes, true, "")},
	{0x2224, 0, 0, 1, f(Yes, false, "∤")},
	{0x2225, 0, 0, 0, f(Yes, true, "")},
	{0x2226, 0, 0, 1, f(Yes, false, "∦")},
	{0x2227, 0, 0, 0, f(Yes, false, "")},
	{0x222c, 0, 0, 0, g(Yes, No, false, false, "", "∫∫")},
	{0x222d, 0, 0, 0, g(Yes, No, false, false, "", "∫∫∫")},
	{0x222e, 0, 0, 0, f(Yes, false, "")},
	{0x222f, 0, 0, 0, g(Yes, No, false, false, "", "∮∮")},
	{0x2230, 0, 0, 0, g(Yes, No, false, false, "", "∮∮∮")},
	{0x2231, 0, 0, 0, f(Yes, false, "")},
	{0x223c, 0, 0, 0, f(Yes, true, "")},
	{0x223d, 0, 0, 0, f(Yes, false, "")},
	{0x2241, 0, 0, 1, f(Yes, false, "≁")},
	{0x2242, 0, 0, 0, f(Yes, false, "")},
	{0x2243, 0, 0, 0, f(Yes, true, "")},
	{0x2244, 0, 0, 1, f(Yes, false, "≄")},
	{0x2245, 0, 0, 0, f(Yes, true, "")},
	{0x2246, 0, 0, 0, f(Yes, false, "")},
	{0x2247, 0, 0, 1, f(Yes, false, "≇")},
	{0x2248, 0, 0, 0, f(Yes, true, "")},
	{0x2249, 0, 0, 1, f(Yes, false, "≉")},
	{0x224a, 0, 0, 0, f(Yes, false, "")},
	{0x224d, 0, 0, 0, f(Yes, true, "")},
	{0x224e, 0, 0, 0, f(Yes, false, "")},
	{0x2260, 0, 0, 1, f(Yes, false, "≠")},
	{0x2261, 0, 0, 0, f(Yes, true, "")},
	{0x2262, 0, 0, 1, f(Yes, false, "≢")},
	{0x2263, 0, 0, 0, f(Yes, false, "")},
	{0x2264, 0, 0, 0, f(Yes, true, "")},
	{0x2266, 0, 0, 0, f(Yes, false, "")},
	{0x226d, 0, 0, 1, f(Yes, false, "≭")},
	{0x226e, 0, 0, 1, f(Yes, false, "≮")},
	{0x226f, 0, 0, 1, f(Yes, false, "≯")},
	{0x2270, 0, 0, 1, f(Yes, false, "≰")},
	{0x2271, 0, 0, 1, f(Yes, false, "≱")},
	{0x2272, 0, 0, 0, f(Yes, true, "")},
	{0x2274, 0, 0, 1, f(Yes, false, "≴")},
	{0x2275, 0, 0, 1, f(Yes, false, "≵")},
	{0x2276, 0, 0, 0, f(Yes, true, "")},
	{0x2278, 0, 0, 1, f(Yes, false, "≸")},
	{0x2279, 0, 0, 1, f(Yes, false, "≹")},
	{0x227a, 0, 0, 0, f(Yes, true, "")},
	{0x227e, 0, 0, 0, f(Yes, false, "")},
	{0x2280, 0, 0, 1, f(Yes, false, "⊀")},
	{0x2281, 0, 0, 1, f(Yes, false, "⊁")},
	{0x2282, 0, 0, 0, f(Yes, true, "")},
	{0x2284, 0, 0, 1, f(Yes, false, "⊄")},
	{0x2285, 0, 0, 1, f(Yes, false, "⊅")},
	{0x2286, 0, 0, 0, f(Yes, true, "")},
	{0x2288, 0, 0, 1, f(Yes, false, "⊈")},
	{0x2289, 0, 0, 1, f(Yes, false, "⊉")},
	{0x228a, 0, 0, 0, f(Yes, false, "")},
	{0x2291, 0, 0, 0, f(Yes, true, "")},
	{0x2293, 0, 0, 0, f(Yes, false, "")},
	{0x22a2, 0, 0, 0, f(Yes, true, "")},
	{0x22a3, 0, 0, 0, f(Yes, false, "")},
	{0x22a8, 0, 0, 0, f(Yes, true, "")},
	{0x22aa, 0, 0, 0, f(Yes, false, "")},
	{0x22ab, 0, 0, 0, f(Yes, true, "")},
	{0x22ac, 0, 0, 1, f(Yes, false, "⊬")},
	{0x22ad, 0, 0, 1, f(Yes, false, "⊭")},
	{0x22ae, 0, 0, 1, f(Yes, false, "⊮")},
	{0x22af, 0, 0, 1, f(Yes, false, "⊯")},
	{0x22b0, 0, 0, 0, f(Yes, false, "")},
	{0x22b2, 0, 0, 0, f(Yes, true, "")},
	{0x22b6, 0, 0, 0, f(Yes, false, "")},
	{0x22e0, 0, 0, 1, f(Yes, false, "⋠")},
	{0x22e1, 0, 0, 1, f(Yes, false, "⋡")},
	{0x22e2, 0, 0, 1, f(Yes, false, "⋢")},
	{0x22e3, 0, 0, 1, f(Yes, false, "⋣")},
	{0x22e4, 0, 0, 0, f(Yes, false, "")},
	{0x22ea, 0, 0, 1, f(Yes, false, "⋪")},
	{0x22eb, 0, 0, 1, f(Yes, false, "⋫")},
	{0x22ec, 0, 0, 1, f(Yes, false, "⋬")},
	{0x22ed, 0, 0, 1, f(Yes, false, "⋭")},
	{0x22ee, 0, 0, 0, f(Yes, false, "")},
	{0x2329, 0, 0, 0, f(No, false, "〈")},
	{0x232a, 0, 0, 0, f(No, false, "〉")},
	{0x232b, 0, 0, 0, f(Yes, false, "")},
	{0x2460, 0, 0, 0, g(Yes, No, false, false, "", "1")},
	{0x2461, 0, 0, 0, g(Yes, No, false, false, "", "2")},
	{0x2462, 0, 0, 0, g(Yes, No, false, false, "", "3")},
	{0x2463, 0, 0, 0, g(Yes, No, false, false, "", "4")},
	{0x2464, 0, 0, 0, g(Yes, No, false, false, "", "5")},
	{0x2465, 0, 0, 0, g(Yes, No, false, false, "", "6")},
	{0x2466, 0, 0, 0, g(Yes, No, false, false, "", "7")},
	{0x2467, 0, 0, 0, g(Yes, No, false, false, "", "8")},
	{0x2468, 0, 0, 0, g(Yes, No, false, false, "", "9")},
	{0x2469, 0, 0, 0, g(Yes, No, false, false, "", "10")},
	{0x246a, 0, 0, 0, g(Yes, No, false, false, "", "11")},
	{0x246b, 0, 0, 0, g(Yes, No, false, false, "", "12")},
	{0x246c, 0, 0, 0, g(Yes, No, false, false, "", "13")},
	{0x246d, 0, 0, 0, g(Yes, No, false, false, "", "14")},
	{0x246e, 0, 0, 0, g(Yes, No, false, false, "", "15")},
	{0x246f, 0, 0, 0, g(Yes, No, false, false, "", "16")},
	{0x2470, 0, 0, 0, g(Yes, No, false, false, "", "17")},
	{0x2471, 0, 0, 0, g(Yes, No, false, false, "", "18")},
	{0x2472, 0, 0, 0, g(Yes, No, false, false, "", "19")},
	{0x2473, 0, 0, 0, g(Yes, No, false, false, "", "20")},
	{0x2474, 0, 0, 0, g(Yes, No, false, false, "", "(1)")},
	{0x2475, 0, 0, 0, g(Yes, No, false, false, "", "(2)")},
	{0x2476, 0, 0, 0, g(Yes, No, false, false, "", "(3)")},
	{0x2477, 0, 0, 0, g(Yes, No, false, false, "", "(4)")},
	{0x2478, 0, 0, 0, g(Yes, No, false, false, "", "(5)")},
	{0x2479, 0, 0, 0, g(Yes, No, false, false, "", "(6)")},
	{0x247a, 0, 0, 0, g(Yes, No, false, false, "", "(7)")},
	{0x247b, 0, 0, 0, g(Yes, No, false, false, "", "(8)")},
	{0x247c, 0, 0, 0, g(Yes, No, false, false, "", "(9)")},
	{0x247d, 0, 0, 0, g(Yes, No, false, false, "", "(10)")},
	{0x247e, 0, 0, 0, g(Yes, No, false, false, "", "(11)")},
	{0x247f, 0, 0, 0, g(Yes, No, false, false, "", "(12)")},
	{0x2480, 0, 0, 0, g(Yes, No, false, false, "", "(13)")},
	{0x2481, 0, 0, 0, g(Yes, No, false, false, "", "(14)")},
	{0x2482, 0, 0, 0, g(Yes, No, false, false, "", "(15)")},
	{0x2483, 0, 0, 0, g(Yes, No, false, false, "", "(16)")},
	{0x2484, 0, 0, 0, g(Yes, No, false, false, "", "(17)")},
	{0x2485, 0, 0, 0, g(Yes, No, false, false, "", "(18)")},
	{0x2486, 0, 0, 0, g(Yes, No, false, false, "", "(19)")},
	{0x2487, 0, 0, 0, g(Yes, No, false, false, "", "(20)")},
	{0x2488, 0, 0, 0, g(Yes, No, false, false, "", "1.")},
	{0x2489, 0, 0, 0, g(Yes, No, false, false, "", "2.")},
	{0x248a, 0, 0, 0, g(Yes, No, false, false, "", "3.")},
	{0x248b, 0, 0, 0, g(Yes, No, false, false, "", "4.")},
	{0x248c, 0, 0, 0, g(Yes, No, false, false, "", "5.")},
	{0x248d, 0, 0, 0, g(Yes, No, false, false, "", "6.")},
	{0x248e, 0, 0, 0, g(Yes, No, false, false, "", "7.")},
	{0x248f, 0, 0, 0, g(Yes, No, false, false, "", "8.")},
	{0x2490, 0, 0, 0, g(Yes, No, false, false, "", "9.")},
	{0x2491, 0, 0, 0, g(Yes, No, false, false, "", "10.")},
	{0x2492, 0, 0, 0, g(Yes, No, false, false, "", "11.")},
	{0x2493, 0, 0, 0, g(Yes, No, false, false, "", "12.")},
	{0x2494, 0, 0, 0, g(Yes, No, false, false, "", "13.")},
	{0x2495, 0, 0, 0, g(Yes, No, false, false, "", "14.")},
	{0x2496, 0, 0, 0, g(Yes, No, false, false, "", "15.")},
	{0x2497, 0, 0, 0, g(Yes, No, false, false, "", "16.")},
	{0x2498, 0, 0, 0, g(Yes, No, false, false, "", "17.")},
	{0x2499, 0, 0, 0, g(Yes, No, false, false, "", "18.")},
	{0x249a, 0, 0, 0, g(Yes, No, false, false, "", "19.")},
	{0x249b, 0, 0, 0, g(Yes, No, false, false, "", "20.")},
	{0x249c, 0, 0, 0, g(Yes, No, false, false, "", "(a)")},
	{0x249d, 0, 0, 0, g(Yes, No, false, false, "", "(b)")},
	{0x249e, 0, 0, 0, g(Yes, No, false, false, "", "(c)")},
	{0x249f, 0, 0, 0, g(Yes, No, false, false, "", "(d)")},
	{0x24a0, 0, 0, 0, g(Yes, No, false, false, "", "(e)")},
	{0x24a1, 0, 0, 0, g(Yes, No, false, false, "", "(f)")},
	{0x24a2, 0, 0, 0, g(Yes, No, false, false, "", "(g)")},
	{0x24a3, 0, 0, 0, g(Yes, No, false, false, "", "(h)")},
	{0x24a4, 0, 0, 0, g(Yes, No, false, false, "", "(i)")},
	{0x24a5, 0, 0, 0, g(Yes, No, false, false, "", "(j)")},
	{0x24a6, 0, 0, 0, g(Yes, No, false, false, "", "(k)")},
	{0x24a7, 0, 0, 0, g(Yes, No, false, false, "", "(l)")},
	{0x24a8, 0, 0, 0, g(Yes, No, false, false, "", "(m)")},
	{0x24a9, 0, 0, 0, g(Yes, No, false, false, "", "(n)")},
	{0x24aa, 0, 0, 0, g(Yes, No, false, false, "", "(o)")},
	{0x24ab, 0, 0, 0, g(Yes, No, false, false, "", "(p)")},
	{0x24ac, 0, 0, 0, g(Yes, No, false, false, "", "(q)")},
	{0x24ad, 0, 0, 0, g(Yes, No, false, false, "", "(r)")},
	{0x24ae, 0, 0, 0, g(Yes, No, false, false, "", "(s)")},
	{0x24af, 0, 0, 0, g(Yes, No, false, false, "", "(t)")},
	{0x24b0, 0, 0, 0, g(Yes, No, false, false, "", "(u)")},
	{0x24b1, 0, 0, 0, g(Yes, No, false, false, "", "(v)")},
	{0x24b2, 0, 0, 0, g(Yes, No, false, false, "", "(w)")},
	{0x24b3, 0, 0, 0, g(Yes, No, false, false, "", "(x)")},
	{0x24b4, 0, 0, 0, g(Yes, No, false, false, "", "(y)")},
	{0x24b5, 0, 0, 0, g(Yes, No, false, false, "", "(z)")},
	{0x24b6, 0, 0, 0, g(Yes, No, false, false, "", "A")},
	{0x24b7, 0, 0, 0, g(Yes, No, false, false, "", "B")},
	{0x24b8, 0, 0, 0, g(Yes, No, false, false, "", "C")},
	{0x24b9, 0, 0, 0, g(Yes, No, false, false, "", "D")},
	{0x24ba, 0, 0, 0, g(Yes, No, false, false, "", "E")},
	{0x24bb, 0, 0, 0, g(Yes, No, false, false, "", "F")},
	{0x24bc, 0, 0, 0, g(Yes, No, false, false, "", "G")},
	{0x24bd, 0, 0, 0, g(Yes, No, false, false, "", "H")},
	{0x24be, 0, 0, 0, g(Yes, No, false, false, "", "I")},
	{0x24bf, 0, 0, 0, g(Yes, No, false, false, "", "J")},
	{0x24c0, 0, 0, 0, g(Yes, No, false, false, "", "K")},
	{0x24c1, 0, 0, 0, g(Yes, No, false, false, "", "L")},
	{0x24c2, 0, 0, 0, g(Yes, No, false, false, "", "M")},
	{0x24c3, 0, 0, 0, g(Yes, No, false, false, "", "N")},
	{0x24c4, 0, 0, 0, g(Yes, No, false, false, "", "O")},
	{0x24c5, 0, 0, 0, g(Yes, No, false, false, "", "P")},
	{0x24c6, 0, 0, 0, g(Yes, No, false, false, "", "Q")},
	{0x24c7, 0, 0, 0, g(Yes, No, false, false, "", "R")},
	{0x24c8, 0, 0, 0, g(Yes, No, false, false, "", "S")},
	{0x24c9, 0, 0, 0, g(Yes, No, false, false, "", "T")},
	{0x24ca, 0, 0, 0, g(Yes, No, false, false, "", "U")},
	{0x24cb, 0, 0, 0, g(Yes, No, false, false, "", "V")},
	{0x24cc, 0, 0, 0, g(Yes, No, false, false, "", "W")},
	{0x24cd, 0, 0, 0, g(Yes, No, false, false, "", "X")},
	{0x24ce, 0, 0, 0, g(Yes, No, false, false, "", "Y")},
	{0x24cf, 0, 0, 0, g(Yes, No, false, false, "", "Z")},
	{0x24d0, 0, 0, 0, g(Yes, No, false, false, "", "a")},
	{0x24d1, 0, 0, 0, g(Yes, No, false, false, "", "b")},
	{0x24d2, 0, 0, 0, g(Yes, No, false, false, "", "c")},
	{0x24d3, 0, 0, 0, g(Yes, No, false, false, "", "d")},
	{0x24d4, 0, 0, 0, g(Yes, No, false, false, "", "e")},
	{0x24d5, 0, 0, 0, g(Yes, No, false, false, "", "f")},
	{0x24d6, 0, 0, 0, g(Yes, No, false, false, "", "g")},
	{0x24d7, 0, 0, 0, g(Yes, No, false, false, "", "h")},
	{0x24d8, 0, 0, 0, g(Yes, No, false, false, "", "i")},
	{0x24d9, 0, 0, 0, g(Yes, No, false, false, "", "j")},
	{0x24da, 0, 0, 0, g(Yes, No, false, false, "", "k")},
	{0x24db, 0, 0, 0, g(Yes, No, false, false, "", "l")},
	{0x24dc, 0, 0, 0, g(Yes, No, false, false, "", "m")},
	{0x24dd, 0, 0, 0, g(Yes, No, false, false, "", "n")},
	{0x24de, 0, 0, 0, g(Yes, No, false, false, "", "o")},
	{0x24df, 0, 0, 0, g(Yes, No, false, false, "", "p")},
	{0x24e0, 0, 0, 0, g(Yes, No, false, false, "", "q")},
	{0x24e1, 0, 0, 0, g(Yes, No, false, false, "", "r")},
	{0x24e2, 0, 0, 0, g(Yes, No, false, false, "", "s")},
	{0x24e3, 0, 0, 0, g(Yes, No, false, false, "", "t")},
	{0x24e4, 0, 0, 0, g(Yes, No, false, false, "", "u")},
	{0x24e5, 0, 0, 0, g(Yes, No, false, false, "", "v")},
	{0x24e6, 0, 0, 0, g(Yes, No, false, false, "", "w")},
	{0x24e7, 0, 0, 0, g(Yes, No, false, false, "", "x")},
	{0x24e8, 0, 0, 0, g(Yes, No, false, false, "", "y")},
	{0x24e9, 0, 0, 0, g(Yes, No, false, false, "", "z")},
	{0x24ea, 0, 0, 0, g(Yes, No, false, false, "", "0")},
	{0x24eb, 0, 0, 0, f(Yes, false, "")},
	{0x2a0c, 0, 0, 0, g(Yes, No, false, false, "", "∫∫∫∫")},
	{0x2a0d, 0, 0, 0, f(Yes, false, "")},
	{0x2a74, 0, 0, 0, g(Yes, No, false, false, "", "::=")},
	{0x2a75, 0, 0, 0, g(Yes, No, false, false, "", "==")},
	{0x2a76, 0, 0, 0, g(Yes, No, false, false, "", "===")},
	{0x2a77, 0, 0, 0, f(Yes, false, "")},
	{0x2adc, 0, 0, 1, f(No, false, "⫝̸")},
	{0x2add, 0, 0, 0, f(Yes, false, "")},
	{0x2c7c, 0, 0, 0, g(Yes, No, false, false, "", "j")},
	{0x2c7d, 0, 0, 0, g(Yes, No, false, false, "", "V")},
	{0x2c7e, 0, 0, 0, f(Yes, false, "")},
	{0x2cef, 230, 1, 1, f(Yes, false, "")},
	{0x2cf2, 0, 0, 0, f(Yes, false, "")},
	{0x2d6f, 0, 0, 0, g(Yes, No, false, false, "", "ⵡ")},
	{0x2d70, 0, 0, 0, f(Yes, false, "")},
	{0x2d7f, 9, 1, 1, f(Yes, false, "")},
	{0x2d80, 0, 0, 0, f(Yes, false, "")},
	{0x2de0, 230, 1, 1, f(Yes, false, "")},
	{0x2e00, 0, 0, 0, f(Yes, false, "")},
	{0x2e9f, 0, 0, 0, g(Yes, No, false, false, "", "母")},
	{0x2ea0, 0, 0, 0, f(Yes, false, "")},
	{0x2ef3, 0, 0, 0, g(Yes, No, false, false, "", "龟")},
	{0x2ef4, 0, 0, 0, f(Yes, false, "")},
	{0x2f00, 0, 0, 0, g(Yes, No, false, false, "", "一")},
	{0x2f01, 0, 0, 0, g(Yes, No, false, false, "", "丨")},
	{0x2f02, 0, 0, 0, g(Yes, No, false, false, "", "丶")},
	{0x2f03, 0, 0, 0, g(Yes, No, false, false, "", "丿")},
	{0x2f04, 0, 0, 0, g(Yes, No, false, false, "", "乙")},
	{0x2f05, 0, 0, 0, g(Yes, No, false, false, "", "亅")},
	{0x2f06, 0, 0, 0, g(Yes, No, false, false, "", "二")},
	{0x2f07, 0, 0, 0, g(Yes, No, false, false, "", "亠")},
	{0x2f08, 0, 0, 0, g(Yes, No, false, false, "", "人")},
	{0x2f09, 0, 0, 0, g(Yes, No, false, false, "", "儿")},
	{0x2f0a, 0, 0, 0, g(Yes, No, false, false, "", "入")},
	{0x2f0b, 0, 0, 0, g(Yes, No, false, false, "", "八")},
	{0x2f0c, 0, 0, 0, g(Yes, No, false, false, "", "冂")},
	{0x2f0d, 0, 0, 0, g(Yes, No, false, false, "", "冖")},
	{0x2f0e, 0, 0, 0, g(Yes, No, false, false, "", "冫")},
	{0x2f0f, 0, 0, 0, g(Yes, No, false, false, "", "几")},
	{0x2f10, 0, 0, 0, g(Yes, No, false, false, "", "凵")},
	{0x2f11, 0, 0, 0, g(Yes, No, false, false, "", "刀")},
	{0x2f12, 0, 0, 0, g(Yes, No, false, false, "", "力")},
	{0x2f13, 0, 0, 0, g(Yes, No, false, false, "", "勹")},
	{0x2f14, 0, 0, 0, g(Yes, No, false, false, "", "匕")},
	{0x2f15, 0, 0, 0, g(Yes, No, false, false, "", "匚")},
	{0x2f16, 0, 0, 0, g(Yes, No, false, false, "", "匸")},
	{0x2f17, 0, 0, 0, g(Yes, No, false, false, "", "十")},
	{0x2f18, 0, 0, 0, g(Yes, No, false, false, "", "卜")},
	{0x2f19, 0, 0, 0, g(Yes, No, false, false, "", "卩")},
	{0x2f1a, 0, 0, 0, g(Yes, No, false, false, "", "厂")},
	{0x2f1b, 0, 0, 0, g(Yes, No, false, false, "", "厶")},
	{0x2f1c, 0, 0, 0, g(Yes, No, false, false, "", "又")},
	{0x2f1d, 0, 0, 0, g(Yes, No, false, false, "", "口")},
	{0x2f1e, 0, 0, 0, g(Yes, No, false, false, "", "囗")},
	{0x2f1f, 0, 0, 0, g(Yes, No, false, false, "", "土")},
	{0x2f20, 0, 0, 0, g(Yes, No, false, false, "", "士")},
	{0x2f21, 0, 0, 0, g(Yes, No, false, false, "", "夂")},
	{0x2f22, 0, 0, 0, g(Yes, No, false, false, "", "夊")},
	{0x2f23, 0, 0, 0, g(Yes, No, false, false, "", "夕")},
	{0x2f24, 0, 0, 0, g(Yes, No, false, false, "", "大")},
	{0x2f25, 0, 0, 0, g(Yes, No, false, false, "", "女")},
	{0x2f26, 0, 0, 0, g(Yes, No, false, false, "", "子")},
	{0x2f27, 0, 0, 0, g(Yes, No, false, false, "", "宀")},
	{0x2f28, 0, 0, 0, g(Yes, No, false, false, "", "寸")},
	{0x2f29, 0, 0, 0, g(Yes, No, false, false, "", "小")},
	{0x2f2a, 0, 0, 0, g(Yes, No, false, false, "", "尢")},
	{0x2f2b, 0, 0, 0, g(Yes, No, false, false, "", "尸")},
	{0x2f2c, 0, 0, 0, g(Yes, No, false, false, "", "屮")},
	{0x2f2d, 0, 0, 0, g(Yes, No, false, false, "", "山")},
	{0x2f2e, 0, 0, 0, g(Yes, No, false, false, "", "巛")},
	{0x2f2f, 0, 0, 0, g(Yes, No, false, false, "", "工")},
	{0x2f30, 0, 0, 0, g(Yes, No, false, false, "", "己")},
	{0x2f31, 0, 0, 0, g(Yes, No, false, false, "", "巾")},
	{0x2f32, 0, 0, 0, g(Yes, No, false, false, "", "干")},
	{0x2f33, 0, 0, 0, g(Yes, No, false, false, "", "幺")},
	{0x2f34, 0, 0, 0, g(Yes, No, false, false, "", "广")},
	{0x2f35, 0, 0, 0, g(Yes, No, false, false, "", "廴")},
	{0x2f36, 0, 0, 0, g(Yes, No, false, false, "", "廾")},
	{0x2f37, 0, 0, 0, g(Yes, No, false, false, "", "弋")},
	{0x2f38, 0, 0, 0, g(Yes, No, false, false, "", "弓")},
	{0x2f39, 0, 0, 0, g(Yes, No, false, false, "", "彐")},
	{0x2f3a, 0, 0, 0, g(Yes, No, false, false, "", "彡")},
	{0x2f3b, 0, 0, 0, g(Yes, No, false, false, "", "彳")},
	{0x2f3c, 0, 0, 0, g(Yes, No, false, false, "", "心")},
	{0x2f3d, 0, 0, 0, g(Yes, No, false, false, "", "戈")},
	{0x2f3e, 0, 0, 0, g(Yes, No, false, false, "", "戶")},
	{0x2f3f, 0, 0, 0, g(Yes, No, false, false, "", "手")},
	{0x2f40, 0, 0, 0, g(Yes, No, false, false, "", "支")},
	{0x2f41, 0, 0, 0, g(Yes, No, false, false, "", "攴")},
	{0x2f42, 0, 0, 0, g(Yes, No, false, false, "", "文")},
	{0x2f43, 0, 0, 0, g(Yes, No, false, false, "", "斗")},
	{0x2f44, 0, 0, 0, g(Yes, No, false, false, "", "斤")},
	{0x2f45, 0, 0, 0, g(Yes, No, false, false, "", "方")},
	{0x2f46, 0, 0, 0, g(Yes, No, false, false, "", "无")},
	{0x2f47, 0, 0, 0, g(Yes, No, false, false, "", "日")},
	{0x2f48, 0, 0, 0, g(Yes, No, false, false, "", "曰")},
	{0x2f49, 0, 0, 0, g(Yes, No, false, false, "", "月")},
	{0x2f4a, 0, 0, 0, g(Yes, No, false, false, "", "木")},
	{0x2f4b, 0, 0, 0, g(Yes, No, false, false, "", "欠")},
	{0x2f4c, 0, 0, 0, g(Yes, No, false, false, "", "止")},
	{0x2f4d, 0, 0, 0, g(Yes, No, false, false, "", "歹")},
	{0x2f4e, 0, 0, 0, g(Yes, No, false, false, "", "殳")},
	{0x2f4f, 0, 0, 0, g(Yes, No, false, false, "", "毋")},
	{0x2f50, 0, 0, 0, g(Yes, No, false, false, "", "比")},
	{0x2f51, 0, 0, 0, g(Yes, No, false, false, "", "毛")},
	{0x2f52, 0, 0, 0, g(Yes, No, false, false, "", "氏")},
	{0x2f53, 0, 0, 0, g(Yes, No, false, false, "", "气")},
	{0x2f54, 0, 0, 0, g(Yes, No, false, false, "", "水")},
	{0x2f55, 0, 0, 0, g(Yes, No, false, false, "", "火")},
	{0x2f56, 0, 0, 0, g(Yes, No, false, false, "", "爪")},
	{0x2f57, 0, 0, 0, g(Yes, No, false, false, "", "父")},
	{0x2f58, 0, 0, 0, g(Yes, No, false, false, "", "爻")},
	{0x2f59, 0, 0, 0, g(Yes, No, false, false, "", "爿")},
	{0x2f5a, 0, 0, 0, g(Yes, No, false, false, "", "片")},
	{0x2f5b, 0, 0, 0, g(Yes, No, false, false, "", "牙")},
	{0x2f5c, 0, 0, 0, g(Yes, No, false, false, "", "牛")},
	{0x2f5d, 0, 0, 0, g(Yes, No, false, false, "", "犬")},
	{0x2f5e, 0, 0, 0, g(Yes, No, false, false, "", "玄")},
	{0x2f5f, 0, 0, 0, g(Yes, No, false, false, "", "玉")},
	{0x2f60, 0, 0, 0, g(Yes, No, false, false, "", "瓜")},
	{0x2f61, 0, 0, 0, g(Yes, No, false, false, "", "瓦")},
	{0x2f62, 0, 0, 0, g(Yes, No, false, false, "", "甘")},
	{0x2f63, 0, 0, 0, g(Yes, No, false, false, "", "生")},
	{0x2f64, 0, 0, 0, g(Yes, No, false, false, "", "用")},
	{0x2f65, 0, 0, 0, g(Yes, No, false, false, "", "田")},
	{0x2f66, 0, 0, 0, g(Yes, No, false, false, "", "疋")},
	{0x2f67, 0, 0, 0, g(Yes, No, false, false, "", "疒")},
	{0x2f68, 0, 0, 0, g(Yes, No, false, false, "", "癶")},
	{0x2f69, 0, 0, 0, g(Yes, No, false, false, "", "白")},
	{0x2f6a, 0, 0, 0, g(Yes, No, false, false, "", "皮")},
	{0x2f6b, 0, 0, 0, g(Yes, No, false, false, "", "皿")},
	{0x2f6c, 0, 0, 0, g(Yes, No, false, false, "", "目")},
	{0x2f6d, 0, 0, 0, g(Yes, No, false, false, "", "矛")},
	{0x2f6e, 0, 0, 0, g(Yes, No, false, false, "", "矢")},
	{0x2f6f, 0, 0, 0, g(Yes, No, false, false, "", "石")},
	{0x2f70, 0, 0, 0, g(Yes, No, false, false, "", "示")},
	{0x2f71, 0, 0, 0, g(Yes, No, false, false, "", "禸")},
	{0x2f72, 0, 0, 0, g(Yes, No, false, false, "", "禾")},
	{0x2f73, 0, 0, 0, g(Yes, No, false, false, "", "穴")},
	{0x2f74, 0, 0, 0, g(Yes, No, false, false, "", "立")},
	{0x2f75, 0, 0, 0, g(Yes, No, false, false, "", "竹")},
	{0x2f76, 0, 0, 0, g(Yes, No, false, false, "", "米")},
	{0x2f77, 0, 0, 0, g(Yes, No, false, false, "", "糸")},
	{0x2f78, 0, 0, 0, g(Yes, No, false, false, "", "缶")},
	{0x2f79, 0, 0, 0, g(Yes, No, false, false, "", "网")},
	{0x2f7a, 0, 0, 0, g(Yes, No, false, false, "", "羊")},
	{0x2f7b, 0, 0, 0, g(Yes, No, false, false, "", "羽")},
	{0x2f7c, 0, 0, 0, g(Yes, No, false, false, "", "老")},
	{0x2f7d, 0, 0, 0, g(Yes, No, false, false, "", "而")},
	{0x2f7e, 0, 0, 0, g(Yes, No, false, false, "", "耒")},
	{0x2f7f, 0, 0, 0, g(Yes, No, false, false, "", "耳")},
	{0x2f80, 0, 0, 0, g(Yes, No, false, false, "", "聿")},
	{0x2f81, 0, 0, 0, g(Yes, No, false, false, "", "肉")},
	{0x2f82, 0, 0, 0, g(Yes, No, false, false, "", "臣")},
	{0x2f83, 0, 0, 0, g(Yes, No, false, false, "", "自")},
	{0x2f84, 0, 0, 0, g(Yes, No, false, false, "", "至")},
	{0x2f85, 0, 0, 0, g(Yes, No, false, false, "", "臼")},
	{0x2f86, 0, 0, 0, g(Yes, No, false, false, "", "舌")},
	{0x2f87, 0, 0, 0, g(Yes, No, false, false, "", "舛")},
	{0x2f88, 0, 0, 0, g(Yes, No, false, false, "", "舟")},
	{0x2f89, 0, 0, 0, g(Yes, No, false, false, "", "艮")},
	{0x2f8a, 0, 0, 0, g(Yes, No, false, false, "", "色")},
	{0x2f8b, 0, 0, 0, g(Yes, No, false, false, "", "艸")},
	{0x2f8c, 0, 0, 0, g(Yes, No, false, false, "", "虍")},
	{0x2f8d, 0, 0, 0, g(Yes, No, false, false, "", "虫")},
	{0x2f8e, 0, 0, 0, g(Yes, No, false, false, "", "血")},
	{0x2f8f, 0, 0, 0, g(Yes, No, false, false, "", "行")},
	{0x2f90, 0, 0, 0, g(Yes, No, false, false, "", "衣")},
	{0x2f91, 0, 0, 0, g(Yes, No, false, false, "", "襾")},
	{0x2f92, 0, 0, 0, g(Yes, No, false, false, "", "見")},
	{0x2f93, 0, 0, 0, g(Yes, No, false, false, "", "角")},
	{0x2f94, 0, 0, 0, g(Yes, No, false, false, "", "言")},
	{0x2f95, 0, 0, 0, g(Yes, No, false, false, "", "谷")},
	{0x2f96, 0, 0, 0, g(Yes, No, false, false, "", "豆")},
	{0x2f97, 0, 0, 0, g(Yes, No, false, false, "", "豕")},
	{0x2f98, 0, 0, 0, g(Yes, No, false, false, "", "豸")},
	{0x2f99, 0, 0, 0, g(Yes, No, false, false, "", "貝")},
	{0x2f9a, 0, 0, 0, g(Yes, No, false, false, "", "赤")},
	{0x2f9b, 0, 0, 0, g(Yes, No, false, false, "", "走")},
	{0x2f9c, 0, 0, 0, g(Yes, No, false, false, "", "足")},
	{0x2f9d, 0, 0, 0, g(Yes, No, false, false, "", "身")},
	{0x2f9e, 0, 0, 0, g(Yes, No, false, false, "", "車")},
	{0x2f9f, 0, 0, 0, g(Yes, No, false, false, "", "辛")},
	{0x2fa0, 0, 0, 0, g(Yes, No, false, false, "", "辰")},
	{0x2fa1, 0, 0, 0, g(Yes, No, false, false, "", "辵")},
	{0x2fa2, 0, 0, 0, g(Yes, No, false, false, "", "邑")},
	{0x2fa3, 0, 0, 0, g(Yes, No, false, false, "", "酉")},
	{0x2fa4, 0, 0, 0, g(Yes, No, false, false, "", "釆")},
	{0x2fa5, 0, 0, 0, g(Yes, No, false, false, "", "里")},
	{0x2fa6, 0, 0, 0, g(Yes, No, false, false, "", "金")},
	{0x2fa7, 0, 0, 0, g(Yes, No, false, false, "", "長")},
	{0x2fa8, 0, 0, 0, g(Yes, No, false, false, "", "門")},
	{0x2fa9, 0, 0, 0, g(Yes, No, false, false, "", "阜")},
	{0x2faa, 0, 0, 0, g(Yes, No, false, false, "", "隶")},
	{0x2fab, 0, 0, 0, g(Yes, No, false, false, "", "隹")},
	{0x2fac, 0, 0, 0, g(Yes, No, false, false, "", "雨")},
	{0x2fad, 0, 0, 0, g(Yes, No, false, false, "", "靑")},
	{0x2fae, 0, 0, 0, g(Yes, No, false, false, "", "非")},
	{0x2faf, 0, 0, 0, g(Yes, No, false, false, "", "面")},
	{0x2fb0, 0, 0, 0, g(Yes, No, false, false, "", "革")},
	{0x2fb1, 0, 0, 0, g(Yes, No, false, false, "", "韋")},
	{0x2fb2, 0, 0, 0, g(Yes, No, false, false, "", "韭")},
	{0x2fb3, 0, 0, 0, g(Yes, No, false, false, "", "音")},
	{0x2fb4, 0, 0, 0, g(Yes, No, false, false, "", "頁")},
	{0x2fb5, 0, 0, 0, g(Yes, No, false, false, "", "風")},
	{0x2fb6, 0, 0, 0, g(Yes, No, false, false, "", "飛")},
	{0x2fb7, 0, 0, 0, g(Yes, No, false, false, "", "食")},
	{0x2fb8, 0, 0, 0, g(Yes, No, false, false, "", "首")},
	{0x2fb9, 0, 0, 0, g(Yes, No, false, false, "", "香")},
	{0x2fba, 0, 0, 0, g(Yes, No, false, false, "", "馬")},
	{0x2fbb, 0, 0, 0, g(Yes, No, false, false, "", "骨")},
	{0x2fbc, 0, 0, 0, g(Yes, No, false, false, "", "高")},
	{0x2fbd, 0, 0, 0, g(Yes, No, false, false, "", "髟")},
	{0x2fbe, 0, 0, 0, g(Yes, No, false, false, "", "鬥")},
	{0x2fbf, 0, 0, 0, g(Yes, No, false, false, "", "鬯")},
	{0x2fc0, 0, 0, 0, g(Yes, No, false, false, "", "鬲")},
	{0x2fc1, 0, 0, 0, g(Yes, No, false, false, "", "鬼")},
	{0x2fc2, 0, 0, 0, g(Yes, No, false, false, "", "魚")},
	{0x2fc3, 0, 0, 0, g(Yes, No, false, false, "", "鳥")},
	{0x2fc4, 0, 0, 0, g(Yes, No, false, false, "", "鹵")},
	{0x2fc5, 0, 0, 0, g(Yes, No, false, false, "", "鹿")},
	{0x2fc6, 0, 0, 0, g(Yes, No, false, false, "", "麥")},
	{0x2fc7, 0, 0, 0, g(Yes, No, false, false, "", "麻")},
	{0x2fc8, 0, 0, 0, g(Yes, No, false, false, "", "黃")},
	{0x2fc9, 0, 0, 0, g(Yes, No, false, false, "", "黍")},
	{0x2fca, 0, 0, 0, g(Yes, No, false, false, "", "黑")},
	{0x2fcb, 0, 0, 0, g(Yes, No, false, false, "", "黹")},
	{0x2fcc, 0, 0, 0, g(Yes, No, false, false, "", "黽")},
	{0x2fcd, 0, 0, 0, g(Yes, No, false, false, "", "鼎")},
	{0x2fce, 0, 0, 0, g(Yes, No, false, false, "", "鼓")},
	{0x2fcf, 0, 0, 0, g(Yes, No, false, false, "", "鼠")},
	{0x2fd0, 0, 0, 0, g(Yes, No, false, false, "", "鼻")},
	{0x2fd1, 0, 0, 0, g(Yes, No, false, false, "", "齊")},
	{0x2fd2, 0, 0, 0, g(Yes, No, false, false, "", "齒")},
	{0x2fd3, 0, 0, 0, g(Yes, No, false, false, "", "龍")},
	{0x2fd4, 0, 0, 0, g(Yes, No, false, false, "", "龜")},
	{0x2fd5, 0, 0, 0, g(Yes, No, false, false, "", "龠")},
	{0x2fd6, 0, 0, 0, f(Yes, false, "")},
	{0x3000, 0, 0, 0, g(Yes, No, false, false, "", " ")},
	{0x3001, 0, 0, 0, f(Yes, false, "")},
	{0x302a, 218, 1, 1, f(Yes, false, "")},
	{0x302b, 228, 1, 1, f(Yes, false, "")},
	{0x302c, 232, 1, 1, f(Yes, false, "")},
	{0x302d, 222, 1, 1, f(Yes, false, "")},
	{0x302e, 224, 1, 1, f(Yes, false, "")},
	{0x3030, 0, 0, 0, f(Yes, false, "")},
	{0x3036, 0, 0, 0, g(Yes, No, false, false, "", "〒")},
	{0x3037, 0, 0, 0, f(Yes, false, "")},
	{0x3038, 0, 0, 0, g(Yes, No, false, false, "", "十")},
	{0x3039, 0, 0, 0, g(Yes, No, false, false, "", "卄")},
	{0x303a, 0, 0, 0, g(Yes, No, false, false, "", "卅")},
	{0x303b, 0, 0, 0, f(Yes, false, "")},
	{0x3046, 0, 0, 0, f(Yes, true, "")},
	{0x3047, 0, 0, 0, f(Yes, false, "")},
	{0x304b, 0, 0, 0, f(Yes, true, "")},
	{0x304c, 0, 0, 1, f(Yes, false, "が")},
	{0x304d, 0, 0, 0, f(Yes, true, "")},
	{0x304e, 0, 0, 1, f(Yes, false, "ぎ")},
	{0x304f, 0, 0, 0, f(Yes, true, "")},
	{0x3050, 0, 0, 1, f(Yes, false, "ぐ")},
	{0x3051, 0, 0, 0, f(Yes, true, "")},
	{0x3052, 0, 0, 1, f(Yes, false, "げ")},
	{0x3053, 0, 0, 0, f(Yes, true, "")},
	{0x3054, 0, 0, 1, f(Yes, false, "ご")},
	{0x3055, 0, 0, 0, f(Yes, true, "")},
	{0x3056, 0, 0, 1, f(Yes, false, "ざ")},
	{0x3057, 0, 0, 0, f(Yes, true, "")},
	{0x3058, 0, 0, 1, f(Yes, false, "じ")},
	{0x3059, 0, 0, 0, f(Yes, true, "")},
	{0x305a, 0, 0, 1, f(Yes, false, "ず")},
	{0x305b, 0, 0, 0, f(Yes, true, "")},
	{0x305c, 0, 0, 1, f(Yes, false, "ぜ")},
	{0x305d, 0, 0, 0, f(Yes, true, "")},
	{0x305e, 0, 0, 1, f(Yes, false, "ぞ")},
	{0x305f, 0, 0, 0, f(Yes, true, "")},
	{0x3060, 0, 0, 1, f(Yes, false, "だ")},
	{0x3061, 0, 0, 0, f(Yes, true, "")},
	{0x3062, 0, 0, 1, f(Yes, false, "ぢ")},
	{0x3063, 0, 0, 0, f(Yes, false, "")},
	{0x3064, 0, 0, 0, f(Yes, true, "")},
	{0x3065, 0, 0, 1, f(Yes, false, "づ")},
	{0x3066, 0, 0, 0, f(Yes, true, "")},
	{0x3067, 0, 0, 1, f(Yes, false, "で")},
	{0x3068, 0, 0, 0, f(Yes, true, "")},
	{0x3069, 0, 0, 1, f(Yes, false, "ど")},
	{0x306a, 0, 0, 0, f(Yes, false, "")},
	{0x306f, 0, 0, 0, f(Yes, true, "")},
	{0x3070, 0, 0, 1, f(Yes, false, "ば")},
	{0x3071, 0, 0, 1, f(Yes, false, "ぱ")},
	{0x3072, 0, 0, 0, f(Yes, true, "")},
	{0x3073, 0, 0, 1, f(Yes, false, "び")},
	{0x3074, 0, 0, 1, f(Yes, false, "ぴ")},
	{0x3075, 0, 0, 0, f(Yes, true, "")},
	{0x3076, 0, 0, 1, f(Yes, false, "ぶ")},
	{0x3077, 0, 0, 1, f(Yes, false, "ぷ")},
	{0x3078, 0, 0, 0, f(Yes, true, "")},
	{0x3079, 0, 0, 1, f(Yes, false, "べ")},
	{0x307a, 0, 0, 1, f(Yes, false, "ぺ")},
	{0x307b, 0, 0, 0, f(Yes, true, "")},
	{0x307c, 0, 0, 1, f(Yes, false, "ぼ")},
	{0x307d, 0, 0, 1, f(Yes, false, "ぽ")},
	{0x307e, 0, 0, 0, f(Yes, false, "")},
	{0x3094, 0, 0, 1, f(Yes, false, "ゔ")},
	{0x3095, 0, 0, 0, f(Yes, false, "")},
	{0x3099, 8, 1, 1, f(Maybe, false, "")},
	{0x309b, 0, 0, 1, g(Yes, No, false, false, "", " ゙")},
	{0x309c, 0, 0, 1, g(Yes, No, false, false, "", " ゚")},
	{0x309d, 0, 0, 0, f(Yes, true, "")},
	{0x309e, 0, 0, 1, f(Yes, false, "ゞ")},
	{0x309f, 0, 0, 0, g(Yes, No, false, false, "", "より")},
	{0x30a0, 0, 0, 0, f(Yes, false, "")},
	{0x30a6, 0, 0, 0, f(Yes, true, "")},
	{0x30a7, 0, 0, 0, f(Yes, false, "")},
	{0x30ab, 0, 0, 0, f(Yes, true, "")},
	{0x30ac, 0, 0, 1, f(Yes, false, "ガ")},
	{0x30ad, 0, 0, 0, f(Yes, true, "")},
	{0x30ae, 0, 0, 1, f(Yes, false, "ギ")},
	{0x30af, 0, 0, 0, f(Yes, true, "")},
	{0x30b0, 0, 0, 1, f(Yes, false, "グ")},
	{0x30b1, 0, 0, 0, f(Yes, true, "")},
	{0x30b2, 0, 0, 1, f(Yes, false, "ゲ")},
	{0x30b3, 0, 0, 0, f(Yes, true, "")},
	{0x30b4, 0, 0, 1, f(Yes, false, "ゴ")},
	{0x30b5, 0, 0, 0, f(Yes, true, "")},
	{0x30b6, 0, 0, 1, f(Yes, false, "ザ")},
	{0x30b7, 0, 0, 0, f(Yes, true, "")},
	{0x30b8, 0, 0, 1, f(Yes, false, "ジ")},
	{0x30b9, 0, 0, 0, f(Yes, true, "")},
	{0x30ba, 0, 0, 1, f(Yes, false, "ズ")},
	{0x30bb, 0, 0, 0, f(Yes, true, "")},
	{0x30bc, 0, 0, 1, f(Yes, false, "ゼ")},
	{0x30bd, 0, 0, 0, f(Yes, true, "")},
	{0x30be, 0, 0, 1, f(Yes, false, "ゾ")},
	{0x30bf, 0, 0, 0, f(Yes, true, "")},
	{0x30c0, 0, 0, 1, f(Yes, false, "ダ")},
	{0x30c1, 0, 0, 0, f(Yes, true, "")},
	{0x30c2, 0, 0, 1, f(Yes, false, "ヂ")},
	{0x30c3, 0, 0, 0, f(Yes, false, "")},
	{0x30c4, 0, 0, 0, f(Yes, true, "")},
	{0x30c5, 0, 0, 1, f(Yes, false, "ヅ")},
	{0x30c6, 0, 0, 0, f(Yes, true, "")},
	{0x30c7, 0, 0, 1, f(Yes, false, "デ")},
	{0x30c8, 0, 0, 0, f(Yes, true, "")},
	{0x30c9, 0, 0, 1, f(Yes, false, "ド")},
	{0x30ca, 0, 0, 0, f(Yes, false, "")},
	{0x30cf, 0, 0, 0, f(Yes, true, "")},
	{0x30d0, 0, 0, 1, f(Yes, false, "バ")},
	{0x30d1, 0, 0, 1, f(Yes, false, "パ")},
	{0x30d2, 0, 0, 0, f(Yes, true, "")},
	{0x30d3, 0, 0, 1, f(Yes, false, "ビ")},
	{0x30d4, 0, 0, 1, f(Yes, false, "ピ")},
	{0x30d5, 0, 0, 0, f(Yes, true, "")},
	{0x30d6, 0, 0, 1, f(Yes, false, "ブ")},
	{0x30d7, 0, 0, 1, f(Yes, false, "プ")},
	{0x30d8, 0, 0, 0, f(Yes, true, "")},
	{0x30d9, 0, 0, 1, f(Yes, false, "ベ")},
	{0x30da, 0, 0, 1, f(Yes, false, "ペ")},
	{0x30db, 0, 0, 0, f(Yes, true, "")},
	{0x30dc, 0, 0, 1, f(Yes, false, "ボ")},
	{0x30dd, 0, 0, 1, f(Yes, false, "ポ")},
	{0x30de, 0, 0, 0, f(Yes, false, "")},
	{0x30ef, 0, 0, 0, f(Yes, true, "")},
	{0x30f3, 0, 0, 0, f(Yes, false, "")},
	{0x30f4, 0, 0, 1, f(Yes, false, "ヴ")},
	{0x30f5, 0, 0, 0, f(Yes, false, "")},
	{0x30f7, 0, 0, 1, f(Yes, false, "ヷ")},
	{0x30f8, 0, 0, 1, f(Yes, false, "ヸ")},
	{0x30f9, 0, 0, 1, f(Yes, false, "ヹ")},
	{0x30fa, 0, 0, 1, f(Yes, false, "ヺ")},
	{0x30fb, 0, 0, 0, f(Yes, false, "")},
	{0x30fd, 0, 0, 0, f(Yes, true, "")},
	{0x30fe, 0, 0, 1, f(Yes, false, "ヾ")},
	{0x30ff, 0, 0, 0, g(Yes, No, false, false, "", "コト")},
	{0x3100, 0, 0, 0, f(Yes, false, "")},
	{0x3131, 0, 0, 0, g(Yes, No, false, false, "", "ᄀ")},
	{0x3132, 0, 0, 0, g(Yes, No, false, false, "", "ᄁ")},
	{0x3133, 0, 1, 1, g(Yes, No, false, false, "", "ᆪ")},
	{0x3134, 0, 0, 0, g(Yes, No, false, false, "", "ᄂ")},
	{0x3135, 0, 1, 1, g(Yes, No, false, false, "", "ᆬ")},
	{0x3136, 0, 1, 1, g(Yes, No, false, false, "", "ᆭ")},
	{0x3137, 0, 0, 0, g(Yes, No, false, false, "", "ᄃ")},
	{0x3138, 0, 0, 0, g(Yes, No, false, false, "", "ᄄ")},
	{0x3139, 0, 0, 0, g(Yes, No, false, false, "", "ᄅ")},
	{0x313a, 0, 1, 1, g(Yes, No, false, false, "", "ᆰ")},
	{0x313b, 0, 1, 1, g(Yes, No, false, false, "", "ᆱ")},
	{0x313c, 0, 1, 1, g(Yes, No, false, false, "", "ᆲ")},
	{0x313d, 0, 1, 1, g(Yes, No, false, false, "", "ᆳ")},
	{0x313e, 0, 1, 1, g(Yes, No, false, false, "", "ᆴ")},
	{0x313f, 0, 1, 1, g(Yes, No, false, false, "", "ᆵ")},
	{0x3140, 0, 0, 0, g(Yes, No, false, false, "", "ᄚ")},
	{0x3141, 0, 0, 0, g(Yes, No, false, false, "", "ᄆ")},
	{0x3142, 0, 0, 0, g(Yes, No, false, false, "", "ᄇ")},
	{0x3143, 0, 0, 0, g(Yes, No, false, false, "", "ᄈ")},
	{0x3144, 0, 0, 0, g(Yes, No, false, false, "", "ᄡ")},
	{0x3145, 0, 0, 0, g(Yes, No, false, false, "", "ᄉ")},
	{0x3146, 0, 0, 0, g(Yes, No, false, false, "", "ᄊ")},
	{0x3147, 0, 0, 0, g(Yes, No, false, false, "", "ᄋ")},
	{0x3148, 0, 0, 0, g(Yes, No, false, false, "", "ᄌ")},
	{0x3149, 0, 0, 0, g(Yes, No, false, false, "", "ᄍ")},
	{0x314a, 0, 0, 0, g(Yes, No, false, false, "", "ᄎ")},
	{0x314b, 0, 0, 0, g(Yes, No, false, false, "", "ᄏ")},
	{0x314c, 0, 0, 0, g(Yes, No, false, false, "", "ᄐ")},
	{0x314d, 0, 0, 0, g(Yes, No, false, false, "", "ᄑ")},
	{0x314e, 0, 0, 0, g(Yes, No, false, false, "", "ᄒ")},
	{0x314f, 0, 1, 1, g(Yes, No, false, false, "", "ᅡ")},
	{0x3150, 0, 1, 1, g(Yes, No, false, false, "", "ᅢ")},
	{0x3151, 0, 1, 1, g(Yes, No, false, false, "", "ᅣ")},
	{0x3152, 0, 1, 1, g(Yes, No, false, false, "", "ᅤ")},
	{0x3153, 0, 1, 1, g(Yes, No, false, false, "", "ᅥ")},
	{0x3154, 0, 1, 1, g(Yes, No, false, false, "", "ᅦ")},
	{0x3155, 0, 1, 1, g(Yes, No, false, false, "", "ᅧ")},
	{0x3156, 0, 1, 1, g(Yes, No, false, false, "", "ᅨ")},
	{0x3157, 0, 1, 1, g(Yes, No, false, false, "", "ᅩ")},
	{0x3158, 0, 1, 1, g(Yes, No, false, false, "", "ᅪ")},
	{0x3159, 0, 1, 1, g(Yes, No, false, false, "", "ᅫ")},
	{0x315a, 0, 1, 1, g(Yes, No, false, false, "", "ᅬ")},
	{0x315b, 0, 1, 1, g(Yes, No, false, false, "", "ᅭ")},
	{0x315c, 0, 1, 1, g(Yes, No, false, false, "", "ᅮ")},
	{0x315d, 0, 1, 1, g(Yes, No, false, false, "", "ᅯ")},
	{0x315e, 0, 1, 1, g(Yes, No, false, false, "", "ᅰ")},
	{0x315f, 0, 1, 1, g(Yes, No, false, false, "", "ᅱ")},
	{0x3160, 0, 1, 1, g(Yes, No, false, false, "", "ᅲ")},
	{0x3161, 0, 1, 1, g(Yes, No, false, false, "", "ᅳ")},
	{0x3162, 0, 1, 1, g(Yes, No, false, false, "", "ᅴ")},
	{0x3163, 0, 1, 1, g(Yes, No, false, false, "", "ᅵ")},
	{0x3164, 0, 0, 0, g(Yes, No, false, false, "", "ᅠ")},
	{0x3165, 0, 0, 0, g(Yes, No, false, false, "", "ᄔ")},
	{0x3166, 0, 0, 0, g(Yes, No, false, false, "", "ᄕ")},
	{0x3167, 0, 0, 0, g(Yes, No, false, false, "", "ᇇ")},
	{0x3168, 0, 0, 0, g(Yes, No, false, false, "", "ᇈ")},
	{0x3169, 0, 0, 0, g(Yes, No, false, false, "", "ᇌ")},
	{0x316a, 0, 0, 0, g(Yes, No, false, false, "", "ᇎ")},
	{0x316b, 0, 0, 0, g(Yes, No, false, false, "", "ᇓ")},
	{0x316c, 0, 0, 0, g(Yes, No, false, false, "", "ᇗ")},
	{0x316d, 0, 0, 0, g(Yes, No, false, false, "", "ᇙ")},
	{0x316e, 0, 0, 0, g(Yes, No, false, false, "", "ᄜ")},
	{0x316f, 0, 0, 0, g(Yes, No, false, false, "", "ᇝ")},
	{0x3170, 0, 0, 0, g(Yes, No, false, false, "", "ᇟ")},
	{0x3171, 0, 0, 0, g(Yes, No, false, false, "", "ᄝ")},
	{0x3172, 0, 0, 0, g(Yes, No, false, false, "", "ᄞ")},
	{0x3173, 0, 0, 0, g(Yes, No, false, false, "", "ᄠ")},
	{0x3174, 0, 0, 0, g(Yes, No, false, false, "", "ᄢ")},
	{0x3175, 0, 0, 0, g(Yes, No, false, false, "", "ᄣ")},
	{0x3176, 0, 0, 0, g(Yes, No, false, false, "", "ᄧ")},
	{0x3177, 0, 0, 0, g(Yes, No, false, false, "", "ᄩ")},
	{0x3178, 0, 0, 0, g(Yes, No, false, false, "", "ᄫ")},
	{0x3179, 0, 0, 0, g(Yes, No, false, false, "", "ᄬ")},
	{0x317a, 0, 0, 0, g(Yes, No, false, false, "", "ᄭ")},
	{0x317b, 0, 0, 0, g(Yes, No, false, false, "", "ᄮ")},
	{0x317c, 0, 0, 0, g(Yes, No, false, false, "", "ᄯ")},
	{0x317d, 0, 0, 0, g(Yes, No, false, false, "", "ᄲ")},
	{0x317e, 0, 0, 0, g(Yes, No, false, false, "", "ᄶ")},
	{0x317f, 0, 0, 0, g(Yes, No, false, false, "", "ᅀ")},
	{0x3180, 0, 0, 0, g(Yes, No, false, false, "", "ᅇ")},
	{0x3181, 0, 0, 0, g(Yes, No, false, false, "", "ᅌ")},
	{0x3182, 0, 0, 0, g(Yes, No, false, false, "", "ᇱ")},
	{0x3183, 0, 0, 0, g(Yes, No, false, false, "", "ᇲ")},
	{0x3184, 0, 0, 0, g(Yes, No, false, false, "", "ᅗ")},
	{0x3185, 0, 0, 0, g(Yes, No, false, false, "", "ᅘ")},
	{0x3186, 0, 0, 0, g(Yes, No, false, false, "", "ᅙ")},
	{0x3187, 0, 0, 0, g(Yes, No, false, false, "", "ᆄ")},
	{0x3188, 0, 0, 0, g(Yes, No, false, false, "", "ᆅ")},
	{0x3189, 0, 0, 0, g(Yes, No, false, false, "", "ᆈ")},
	{0x318a, 0, 0, 0, g(Yes, No, false, false, "", "ᆑ")},
	{0x318b, 0, 0, 0, g(Yes, No, false, false, "", "ᆒ")},
	{0x318c, 0, 0, 0, g(Yes, No, false, false, "", "ᆔ")},
	{0x318d, 0, 0, 0, g(Yes, No, false, false, "", "ᆞ")},
	{0x318e, 0, 0, 0, g(Yes, No, false, false, "", "ᆡ")},
	{0x318f, 0, 0, 0, f(Yes, false, "")},
	{0x3192, 0, 0, 0, g(Yes, No, false, false, "", "一")},
	{0x3193, 0, 0, 0, g(Yes, No, false, false, "", "二")},
	{0x3194, 0, 0, 0, g(Yes, No, false, false, "", "三")},
	{0x3195, 0, 0, 0, g(Yes, No, false, false, "", "四")},
	{0x3196, 0, 0, 0, g(Yes, No, false, false, "", "上")},
	{0x3197, 0, 0, 0, g(Yes, No, false, false, "", "中")},
	{0x3198, 0, 0, 0, g(Yes, No, false, false, "", "下")},
	{0x3199, 0, 0, 0, g(Yes, No, false, false, "", "甲")},
	{0x319a, 0, 0, 0, g(Yes, No, false, false, "", "乙")},
	{0x319b, 0, 0, 0, g(Yes, No, false, false, "", "丙")},
	{0x319c, 0, 0, 0, g(Yes, No, false, false, "", "丁")},
	{0x319d, 0, 0, 0, g(Yes, No, false, false, "", "天")},
	{0x319e, 0, 0, 0, g(Yes, No, false, false, "", "地")},
	{0x319f, 0, 0, 0, g(Yes, No, false, false, "", "人")},
	{0x31a0, 0, 0, 0, f(Yes, false, "")},
	{0x3200, 0, 0, 0, g(Yes, No, false, false, "", "(ᄀ)")},
	{0x3201, 0, 0, 0, g(Yes, No, false, false, "", "(ᄂ)")},
	{0x3202, 0, 0, 0, g(Yes, No, false, false, "", "(ᄃ)")},
	{0x3203, 0, 0, 0, g(Yes, No, false, false, "", "(ᄅ)")},
	{0x3204, 0, 0, 0, g(Yes, No, false, false, "", "(ᄆ)")},
	{0x3205, 0, 0, 0, g(Yes, No, false, false, "", "(ᄇ)")},
	{0x3206, 0, 0, 0, g(Yes, No, false, false, "", "(ᄉ)")},
	{0x3207, 0, 0, 0, g(Yes, No, false, false, "", "(ᄋ)")},
	{0x3208, 0, 0, 0, g(Yes, No, false, false, "", "(ᄌ)")},
	{0x3209, 0, 0, 0, g(Yes, No, false, false, "", "(ᄎ)")},
	{0x320a, 0, 0, 0, g(Yes, No, false, false, "", "(ᄏ)")},
	{0x320b, 0, 0, 0, g(Yes, No, false, false, "", "(ᄐ)")},
	{0x320c, 0, 0, 0, g(Yes, No, false, false, "", "(ᄑ)")},
	{0x320d, 0, 0, 0, g(Yes, No, false, false, "", "(ᄒ)")},
	{0x320e, 0, 0, 0, g(Yes, No, false, false, "", "(가)")},
	{0x320f, 0, 0, 0, g(Yes, No, false, false, "", "(나)")},
	{0x3210, 0, 0, 0, g(Yes, No, false, false, "", "(다)")},
	{0x3211, 0, 0, 0, g(Yes, No, false, false, "", "(라)")},
	{0x3212, 0, 0, 0, g(Yes, No, false, false, "", "(마)")},
	{0x3213, 0, 0, 0, g(Yes, No, false, false, "", "(바)")},
	{0x3214, 0, 0, 0, g(Yes, No, false, false, "", "(사)")},
	{0x3215, 0, 0, 0, g(Yes, No, false, false, "", "(아)")},
	{0x3216, 0, 0, 0, g(Yes, No, false, false, "", "(자)")},
	{0x3217, 0, 0, 0, g(Yes, No, false, false, "", "(차)")},
	{0x3218, 0, 0, 0, g(Yes, No, false, false, "", "(카)")},
	{0x3219, 0, 0, 0, g(Yes, No, false, false, "", "(타)")},
	{0x321a, 0, 0, 0, g(Yes, No, false, false, "", "(파)")},
	{0x321b, 0, 0, 0, g(Yes, No, false, false, "", "(하)")},
	{0x321c, 0, 0, 0, g(Yes, No, false, false, "", "(주)")},
	{0x321d, 0, 0, 0, g(Yes, No, false, false, "", "(오전)")},
	{0x321e, 0, 0, 0, g(Yes, No, false, false, "", "(오후)")},
	{0x321f, 0, 0, 0, f(Yes, false, "")},
	{0x3220, 0, 0, 0, g(Yes, No, false, false, "", "(一)")},
	{0x3221, 0, 0, 0, g(Yes, No, false, false, "", "(二)")},
	{0x3222, 0, 0, 0, g(Yes, No, false, false, "", "(三)")},
	{0x3223, 0, 0, 0, g(Yes, No, false, false, "", "(四)")},
	{0x3224, 0, 0, 0, g(Yes, No, false, false, "", "(五)")},
	{0x3225, 0, 0, 0, g(Yes, No, false, false, "", "(六)")},
	{0x3226, 0, 0, 0, g(Yes, No, false, false, "", "(七)")},
	{0x3227, 0, 0, 0, g(Yes, No, false, false, "", "(八)")},
	{0x3228, 0, 0, 0, g(Yes, No, false, false, "", "(九)")},
	{0x3229, 0, 0, 0, g(Yes, No, false, false, "", "(十)")},
	{0x322a, 0, 0, 0, g(Yes, No, false, false, "", "(月)")},
	{0x322b, 0, 0, 0, g(Yes, No, false, false, "", "(火)")},
	{0x322c, 0, 0, 0, g(Yes, No, false, false, "", "(水)")},
	{0x322d, 0, 0, 0, g(Yes, No, false, false, "", "(木)")},
	{0x322e, 0, 0, 0, g(Yes, No, false, false, "", "(金)")},
	{0x322f, 0, 0, 0, g(Yes, No, false, false, "", "(土)")},
	{0x3230, 0, 0, 0, g(Yes, No, false, false, "", "(日)")},
	{0x3231, 0, 0, 0, g(Yes, No, false, false, "", "(株)")},
	{0x3232, 0, 0, 0, g(Yes, No, false, false, "", "(有)")},
	{0x3233, 0, 0, 0, g(Yes, No, false, false, "", "(社)")},
	{0x3234, 0, 0, 0, g(Yes, No, false, false, "", "(名)")},
	{0x3235, 0, 0, 0, g(Yes, No, false, false, "", "(特)")},
	{0x3236, 0, 0, 0, g(Yes, No, false, false, "", "(財)")},
	{0x3237, 0, 0, 0, g(Yes, No, false, false, "", "(祝)")},
	{0x3238, 0, 0, 0, g(Yes, No, false, false, "", "(労)")},
	{0x3239, 0, 0, 0, g(Yes, No, false, false, "", "(代)")},
	{0x323a, 0, 0, 0, g(Yes, No, false, false, "", "(呼)")},
	{0x323b, 0, 0, 0, g(Yes, No, false, false, "", "(学)")},
	{0x323c, 0, 0, 0, g(Yes, No, false, false, "", "(監)")},
	{0x323d, 0, 0, 0, g(Yes, No, false, false, "", "(企)")},
	{0x323e, 0, 0, 0, g(Yes, No, false, false, "", "(資)")},
	{0x323f, 0, 0, 0, g(Yes, No, false, false, "", "(協)")},
	{0x3240, 0, 0, 0, g(Yes, No, false, false, "", "(祭)")},
	{0x3241, 0, 0, 0, g(Yes, No, false, false, "", "(休)")},
	{0x3242, 0, 0, 0, g(Yes, No, false, false, "", "(自)")},
	{0x3243, 0, 0, 0, g(Yes, No, false, false, "", "(至)")},
	{0x3244, 0, 0, 0, g(Yes, No, false, false, "", "問")},
	{0x3245, 0, 0, 0, g(Yes, No, false, false, "", "幼")},
	{0x3246, 0, 0, 0, g(Yes, No, false, false, "", "文")},
	{0x3247, 0, 0, 0, g(Yes, No, false, false, "", "箏")},
	{0x3248, 0, 0, 0, f(Yes, false, "")},
	{0x3250, 0, 0, 0, g(Yes, No, false, false, "", "PTE")},
	{0x3251, 0, 0, 0, g(Yes, No, false, false, "", "21")},
	{0x3252, 0, 0, 0, g(Yes, No, false, false, "", "22")},
	{0x3253, 0, 0, 0, g(Yes, No, false, false, "", "23")},
	{0x3254, 0, 0, 0, g(Yes, No, false, false, "", "24")},
	{0x3255, 0, 0, 0, g(Yes, No, false, false, "", "25")},
	{0x3256, 0, 0, 0, g(Yes, No, false, false, "", "26")},
	{0x3257, 0, 0, 0, g(Yes, No, false, false, "", "27")},
	{0x3258, 0, 0, 0, g(Yes, No, false, false, "", "28")},
	{0x3259, 0, 0, 0, g(Yes, No, false, false, "", "29")},
	{0x325a, 0, 0, 0, g(Yes, No, false, false, "", "30")},
	{0x325b, 0, 0, 0, g(Yes, No, false, false, "", "31")},
	{0x325c, 0, 0, 0, g(Yes, No, false, false, "", "32")},
	{0x325d, 0, 0, 0, g(Yes, No, false, false, "", "33")},
	{0x325e, 0, 0, 0, g(Yes, No, false, false, "", "34")},
	{0x325f, 0, 0, 0, g(Yes, No, false, false, "", "35")},
	{0x3260, 0, 0, 0, g(Yes, No, false, false, "", "ᄀ")},
	{0x3261, 0, 0, 0, g(Yes, No, false, false, "", "ᄂ")},
	{0x3262, 0, 0, 0, g(Yes, No, false, false, "", "ᄃ")},
	{0x3263, 0, 0, 0, g(Yes, No, false, false, "", "ᄅ")},
	{0x3264, 0, 0, 0, g(Yes, No, false, false, "", "ᄆ")},
	{0x3265, 0, 0, 0, g(Yes, No, false, false, "", "ᄇ")},
	{0x3266, 0, 0, 0, g(Yes, No, false, false, "", "ᄉ")},
	{0x3267, 0, 0, 0, g(Yes, No, false, false, "", "ᄋ")},
	{0x3268, 0, 0, 0, g(Yes, No, false, false, "", "ᄌ")},
	{0x3269, 0, 0, 0, g(Yes, No, false, false, "", "ᄎ")},
	{0x326a, 0, 0, 0, g(Yes, No, false, false, "", "ᄏ")},
	{0x326b, 0, 0, 0, g(Yes, No, false, false, "", "ᄐ")},
	{0x326c, 0, 0, 0, g(Yes, No, false, false, "", "ᄑ")},
	{0x326d, 0, 0, 0, g(Yes, No, false, false, "", "ᄒ")},
	{0x326e, 0, 0, 1, g(Yes, No, false, false, "", "가")},
	{0x326f, 0, 0, 1, g(Yes, No, false, false, "", "나")},
	{0x3270, 0, 0, 1, g(Yes, No, false, false, "", "다")},
	{0x3271, 0, 0, 1, g(Yes, No, false, false, "", "라")},
	{0x3272, 0, 0, 1, g(Yes, No, false, false, "", "마")},
	{0x3273, 0, 0, 1, g(Yes, No, false, false, "", "바")},
	{0x3274, 0, 0, 1, g(Yes, No, false, false, "", "사")},
	{0x3275, 0, 0, 1, g(Yes, No, false, false, "", "아")},
	{0x3276, 0, 0, 1, g(Yes, No, false, false, "", "자")},
	{0x3277, 0, 0, 1, g(Yes, No, false, false, "", "차")},
	{0x3278, 0, 0, 1, g(Yes, No, false, false, "", "카")},
	{0x3279, 0, 0, 1, g(Yes, No, false, false, "", "타")},
	{0x327a, 0, 0, 1, g(Yes, No, false, false, "", "파")},
	{0x327b, 0, 0, 1, g(Yes, No, false, false, "", "하")},
	{0x327c, 0, 0, 1, g(Yes, No, false, false, "", "참고")},
	{0x327d, 0, 0, 1, g(Yes, No, false, false, "", "주의")},
	{0x327e, 0, 0, 1, g(Yes, No, false, false, "", "우")},
	{0x327f, 0, 0, 0, f(Yes, false, "")},
	{0x3280, 0, 0, 0, g(Yes, No, false, false, "", "一")},
	{0x3281, 0, 0, 0, g(Yes, No, false, false, "", "二")},
	{0x3282, 0, 0, 0, g(Yes, No, false, false, "", "三")},
	{0x3283, 0, 0, 0, g(Yes, No, false, false, "", "四")},
	{0x3284, 0, 0, 0, g(Yes, No, false, false, "", "五")},
	{0x3285, 0, 0, 0, g(Yes, No, false, false, "", "六")},
	{0x3286, 0, 0, 0, g(Yes, No, false, false, "", "七")},
	{0x3287, 0, 0, 0, g(Yes, No, false, false, "", "八")},
	{0x3288, 0, 0, 0, g(Yes, No, false, false, "", "九")},
	{0x3289, 0, 0, 0, g(Yes, No, false, false, "", "十")},
	{0x328a, 0, 0, 0, g(Yes, No, false, false, "", "月")},
	{0x328b, 0, 0, 0, g(Yes, No, false, false, "", "火")},
	{0x328c, 0, 0, 0, g(Yes, No, false, false, "", "水")},
	{0x328d, 0, 0, 0, g(Yes, No, false, false, "", "木")},
	{0x328e, 0, 0, 0, g(Yes, No, false, false, "", "金")},
	{0x328f, 0, 0, 0, g(Yes, No, false, false, "", "土")},
	{0x3290, 0, 0, 0, g(Yes, No, false, false, "", "日")},
	{0x3291, 0, 0, 0, g(Yes, No, false, false, "", "株")},
	{0x3292, 0, 0, 0, g(Yes, No, false, false, "", "有")},
	{0x3293, 0, 0, 0, g(Yes, No, false, false, "", "社")},
	{0x3294, 0, 0, 0, g(Yes, No, false, false, "", "名")},
	{0x3295, 0, 0, 0, g(Yes, No, false, false, "", "特")},
	{0x3296, 0, 0, 0, g(Yes, No, false, false, "", "財")},
	{0x3297, 0, 0, 0, g(Yes, No, false, false, "", "祝")},
	{0x3298, 0, 0, 0, g(Yes, No, false, false, "", "労")},
	{0x3299, 0, 0, 0, g(Yes, No, false, false, "", "秘")},
	{0x329a, 0, 0, 0, g(Yes, No, false, false, "", "男")},
	{0x329b, 0, 0, 0, g(Yes, No, false, false, "", "女")},
	{0x329c, 0, 0, 0, g(Yes, No, false, false, "", "適")},
	{0x329d, 0, 0, 0, g(Yes, No, false, false, "", "優")},
	{0x329e, 0, 0, 0, g(Yes, No, false, false, "", "印")},
	{0x329f, 0, 0, 0, g(Yes, No, false, false, "", "注")},
	{0x32a0, 0, 0, 0, g(Yes, No, false, false, "", "項")},
	{0x32a1, 0, 0, 0, g(Yes, No, false, false, "", "休")},
	{0x32a2, 0, 0, 0, g(Yes, No, false, false, "", "写")},
	{0x32a3, 0, 0, 0, g(Yes, No, false, false, "", "正")},
	{0x32a4, 0, 0, 0, g(Yes, No, false, false, "", "上")},
	{0x32a5, 0, 0, 0, g(Yes, No, false, false, "", "中")},
	{0x32a6, 0, 0, 0, g(Yes, No, false, false, "", "下")},
	{0x32a7, 0, 0, 0, g(Yes, No, false, false, "", "左")},
	{0x32a8, 0, 0, 0, g(Yes, No, false, false, "", "右")},
	{0x32a9, 0, 0, 0, g(Yes, No, false, false, "", "医")},
	{0x32aa, 0, 0, 0, g(Yes, No, false, false, "", "宗")},
	{0x32ab, 0, 0, 0, g(Yes, No, false, false, "", "学")},
	{0x32ac, 0, 0, 0, g(Yes, No, false, false, "", "監")},
	{0x32ad, 0, 0, 0, g(Yes, No, false, false, "", "企")},
	{0x32ae, 0, 0, 0, g(Yes, No, false, false, "", "資")},
	{0x32af, 0, 0, 0, g(Yes, No, false, false, "", "協")},
	{0x32b0, 0, 0, 0, g(Yes, No, false, false, "", "夜")},
	{0x32b1, 0, 0, 0, g(Yes, No, false, false, "", "36")},
	{0x32b2, 0, 0, 0, g(Yes, No, false, false, "", "37")},
	{0x32b3, 0, 0, 0, g(Yes, No, false, false, "", "38")},
	{0x32b4, 0, 0, 0, g(Yes, No, false, false, "", "39")},
	{0x32b5, 0, 0, 0, g(Yes, No, false, false, "", "40")},
	{0x32b6, 0, 0, 0, g(Yes, No, false, false, "", "41")},
	{0x32b7, 0, 0, 0, g(Yes, No, false, false, "", "42")},
	{0x32b8, 0, 0, 0, g(Yes, No, false, false, "", "43")},
	{0x32b9, 0, 0, 0, g(Yes, No, false, false, "", "44")},
	{0x32ba, 0, 0, 0, g(Yes, No, false, false, "", "45")},
	{0x32bb, 0, 0, 0, g(Yes, No, false, false, "", "46")},
	{0x32bc, 0, 0, 0, g(Yes, No, false, false, "", "47")},
	{0x32bd, 0, 0, 0, g(Yes, No, false, false, "", "48")},
	{0x32be, 0, 0, 0, g(Yes, No, false, false, "", "49")},
	{0x32bf, 0, 0, 0, g(Yes, No, false, false, "", "50")},
	{0x32c0, 0, 0, 0, g(Yes, No, false, false, "", "1月")},
	{0x32c1, 0, 0, 0, g(Yes, No, false, false, "", "2月")},
	{0x32c2, 0, 0, 0, g(Yes, No, false, false, "", "3月")},
	{0x32c3, 0, 0, 0, g(Yes, No, false, false, "", "4月")},
	{0x32c4, 0, 0, 0, g(Yes, No, false, false, "", "5月")},
	{0x32c5, 0, 0, 0, g(Yes, No, false, false, "", "6月")},
	{0x32c6, 0, 0, 0, g(Yes, No, false, false, "", "7月")},
	{0x32c7, 0, 0, 0, g(Yes, No, false, false, "", "8月")},
	{0x32c8, 0, 0, 0, g(Yes, No, false, false, "", "9月")},
	{0x32c9, 0, 0, 0, g(Yes, No, false, false, "", "10月")},
	{0x32ca, 0, 0, 0, g(Yes, No, false, false, "", "11月")},
	{0x32cb, 0, 0, 0, g(Yes, No, false, false, "", "12月")},
	{0x32cc, 0, 0, 0, g(Yes, No, false, false, "", "Hg")},
	{0x32cd, 0, 0, 0, g(Yes, No, false, false, "", "erg")},
	{0x32ce, 0, 0, 0, g(Yes, No, false, false, "", "eV")},
	{0x32cf, 0, 0, 0, g(Yes, No, false, false, "", "LTD")},
	{0x32d0, 0, 0, 0, g(Yes, No, false, false, "", "ア")},
	{0x32d1, 0, 0, 0, g(Yes, No, false, false, "", "イ")},
	{0x32d2, 0, 0, 0, g(Yes, No, false, false, "", "ウ")},
	{0x32d3, 0, 0, 0, g(Yes, No, false, false, "", "エ")},
	{0x32d4, 0, 0, 0, g(Yes, No, false, false, "", "オ")},
	{0x32d5, 0, 0, 0, g(Yes, No, false, false, "", "カ")},
	{0x32d6, 0, 0, 0, g(Yes, No, false, false, "", "キ")},
	{0x32d7, 0, 0, 0, g(Yes, No, false, false, "", "ク")},
	{0x32d8, 0, 0, 0, g(Yes, No, false, false, "", "ケ")},
	{0x32d9, 0, 0, 0, g(Yes, No, false, false, "", "コ")},
	{0x32da, 0, 0, 0, g(Yes, No, false, false, "", "サ")},
	{0x32db, 0, 0, 0, g(Yes, No, false, false, "", "シ")},
	{0x32dc, 0, 0, 0, g(Yes, No, false, false, "", "ス")},
	{0x32dd, 0, 0, 0, g(Yes, No, false, false, "", "セ")},
	{0x32de, 0, 0, 0, g(Yes, No, false, false, "", "ソ")},
	{0x32df, 0, 0, 0, g(Yes, No, false, false, "", "タ")},
	{0x32e0, 0, 0, 0, g(Yes, No, false, false, "", "チ")},
	{0x32e1, 0, 0, 0, g(Yes, No, false, false, "", "ツ")},
	{0x32e2, 0, 0, 0, g(Yes, No, false, false, "", "テ")},
	{0x32e3, 0, 0, 0, g(Yes, No, false, false, "", "ト")},
	{0x32e4, 0, 0, 0, g(Yes, No, false, false, "", "ナ")},
	{0x32e5, 0, 0, 0, g(Yes, No, false, false, "", "ニ")},
	{0x32e6, 0, 0, 0, g(Yes, No, false, false, "", "ヌ")},
	{0x32e7, 0, 0, 0, g(Yes, No, false, false, "", "ネ")},
	{0x32e8, 0, 0, 0, g(Yes, No, false, false, "", "ノ")},
	{0x32e9, 0, 0, 0, g(Yes, No, false, false, "", "ハ")},
	{0x32ea, 0, 0, 0, g(Yes, No, false, false, "", "ヒ")},
	{0x32eb, 0, 0, 0, g(Yes, No, false, false, "", "フ")},
	{0x32ec, 0, 0, 0, g(Yes, No, false, false, "", "ヘ")},
	{0x32ed, 0, 0, 0, g(Yes, No, false, false, "", "ホ")},
	{0x32ee, 0, 0, 0, g(Yes, No, false, false, "", "マ")},
	{0x32ef, 0, 0, 0, g(Yes, No, false, false, "", "ミ")},
	{0x32f0, 0, 0, 0, g(Yes, No, false, false, "", "ム")},
	{0x32f1, 0, 0, 0, g(Yes, No, false, false, "", "メ")},
	{0x32f2, 0, 0, 0, g(Yes, No, false, false, "", "モ")},
	{0x32f3, 0, 0, 0, g(Yes, No, false, false, "", "ヤ")},
	{0x32f4, 0, 0, 0, g(Yes, No, false, false, "", "ユ")},
	{0x32f5, 0, 0, 0, g(Yes, No, false, false, "", "ヨ")},
	{0x32f6, 0, 0, 0, g(Yes, No, false, false, "", "ラ")},
	{0x32f7, 0, 0, 0, g(Yes, No, false, false, "", "リ")},
	{0x32f8, 0, 0, 0, g(Yes, No, false, false, "", "ル")},
	{0x32f9, 0, 0, 0, g(Yes, No, false, false, "", "レ")},
	{0x32fa, 0, 0, 0, g(Yes, No, false, false, "", "ロ")},
	{0x32fb, 0, 0, 0, g(Yes, No, false, false, "", "ワ")},
	{0x32fc, 0, 0, 0, g(Yes, No, false, false, "", "ヰ")},
	{0x32fd, 0, 0, 0, g(Yes, No, false, false, "", "ヱ")},
	{0x32fe, 0, 0, 0, g(Yes, No, false, false, "", "ヲ")},
	{0x32ff, 0, 0, 0, f(Yes, false, "")},
	{0x3300, 0, 0, 0, g(Yes, No, false, false, "", "アパート")},
	{0x3301, 0, 0, 0, g(Yes, No, false, false, "", "アルファ")},
	{0x3302, 0, 0, 0, g(Yes, No, false, false, "", "アンペア")},
	{0x3303, 0, 0, 0, g(Yes, No, false, false, "", "アール")},
	{0x3304, 0, 0, 1, g(Yes, No, false, false, "", "イニング")},
	{0x3305, 0, 0, 0, g(Yes, No, false, false, "", "インチ")},
	{0x3306, 0, 0, 0, g(Yes, No, false, false, "", "ウォン")},
	{0x3307, 0, 0, 1, g(Yes, No, false, false, "", "エスクード")},
	{0x3308, 0, 0, 0, g(Yes, No, false, false, "", "エーカー")},
	{0x3309, 0, 0, 0, g(Yes, No, false, false, "", "オンス")},
	{0x330a, 0, 0, 0, g(Yes, No, false, false, "", "オーム")},
	{0x330b, 0, 0, 0, g(Yes, No, false, false, "", "カイリ")},
	{0x330c, 0, 0, 0, g(Yes, No, false, false, "", "カラット")},
	{0x330d, 0, 0, 0, g(Yes, No, false, false, "", "カロリー")},
	{0x330e, 0, 0, 0, g(Yes, No, false, false, "", "ガロン")},
	{0x330f, 0, 0, 0, g(Yes, No, false, false, "", "ガンマ")},
	{0x3310, 0, 0, 1, g(Yes, No, false, false, "", "ギガ")},
	{0x3311, 0, 0, 0, g(Yes, No, false, false, "", "ギニー")},
	{0x3312, 0, 0, 0, g(Yes, No, false, false, "", "キュリー")},
	{0x3313, 0, 0, 0, g(Yes, No, false, false, "", "ギルダー")},
	{0x3314, 0, 0, 0, g(Yes, No, false, false, "", "キロ")},
	{0x3315, 0, 0, 0, g(Yes, No, false, false, "", "キログラム")},
	{0x3316, 0, 0, 0, g(Yes, No, false, false, "", "キロメートル")},
	{0x3317, 0, 0, 0, g(Yes, No, false, false, "", "キロワット")},
	{0x3318, 0, 0, 0, g(Yes, No, false, false, "", "グラム")},
	{0x3319, 0, 0, 0, g(Yes, No, false, false, "", "グラムトン")},
	{0x331a, 0, 0, 0, g(Yes, No, false, false, "", "クルゼイロ")},
	{0x331b, 0, 0, 0, g(Yes, No, false, false, "", "クローネ")},
	{0x331c, 0, 0, 0, g(Yes, No, false, false, "", "ケース")},
	{0x331d, 0, 0, 0, g(Yes, No, false, false, "", "コルナ")},
	{0x331e, 0, 0, 1, g(Yes, No, false, false, "", "コーポ")},
	{0x331f, 0, 0, 0, g(Yes, No, false, false, "", "サイクル")},
	{0x3320, 0, 0, 0, g(Yes, No, false, false, "", "サンチーム")},
	{0x3321, 0, 0, 1, g(Yes, No, false, false, "", "シリング")},
	{0x3322, 0, 0, 0, g(Yes, No, false, false, "", "センチ")},
	{0x3323, 0, 0, 0, g(Yes, No, false, false, "", "セント")},
	{0x3324, 0, 0, 0, g(Yes, No, false, false, "", "ダース")},
	{0x3325, 0, 0, 0, g(Yes, No, false, false, "", "デシ")},
	{0x3326, 0, 0, 0, g(Yes, No, false, false, "", "ドル")},
	{0x3327, 0, 0, 0, g(Yes, No, false, false, "", "トン")},
	{0x3328, 0, 0, 0, g(Yes, No, false, false, "", "ナノ")},
	{0x3329, 0, 0, 0, g(Yes, No, false, false, "", "ノット")},
	{0x332a, 0, 0, 0, g(Yes, No, false, false, "", "ハイツ")},
	{0x332b, 0, 0, 0, g(Yes, No, false, false, "", "パーセント")},
	{0x332c, 0, 0, 0, g(Yes, No, false, false, "", "パーツ")},
	{0x332d, 0, 0, 0, g(Yes, No, false, false, "", "バーレル")},
	{0x332e, 0, 0, 0, g(Yes, No, false, false, "", "ピアストル")},
	{0x332f, 0, 0, 0, g(Yes, No, false, false, "", "ピクル")},
	{0x3330, 0, 0, 0, g(Yes, No, false, false, "", "ピコ")},
	{0x3331, 0, 0, 0, g(Yes, No, false, false, "", "ビル")},
	{0x3332, 0, 0, 1, g(Yes, No, false, false, "", "ファラッド")},
	{0x3333, 0, 0, 0, g(Yes, No, false, false, "", "フィート")},
	{0x3334, 0, 0, 0, g(Yes, No, false, false, "", "ブッシェル")},
	{0x3335, 0, 0, 0, g(Yes, No, false, false, "", "フラン")},
	{0x3336, 0, 0, 0, g(Yes, No, false, false, "", "ヘクタール")},
	{0x3337, 0, 0, 0, g(Yes, No, false, false, "", "ペソ")},
	{0x3338, 0, 0, 0, g(Yes, No, false, false, "", "ペニヒ")},
	{0x3339, 0, 0, 0, g(Yes, No, false, false, "", "ヘルツ")},
	{0x333a, 0, 0, 0, g(Yes, No, false, false, "", "ペンス")},
	{0x333b, 0, 0, 1, g(Yes, No, false, false, "", "ページ")},
	{0x333c, 0, 0, 0, g(Yes, No, false, false, "", "ベータ")},
	{0x333d, 0, 0, 0, g(Yes, No, false, false, "", "ポイント")},
	{0x333e, 0, 0, 0, g(Yes, No, false, false, "", "ボルト")},
	{0x333f, 0, 0, 0, g(Yes, No, false, false, "", "ホン")},
	{0x3340, 0, 0, 1, g(Yes, No, false, false, "", "ポンド")},
	{0x3341, 0, 0, 0, g(Yes, No, false, false, "", "ホール")},
	{0x3342, 0, 0, 0, g(Yes, No, false, false, "", "ホーン")},
	{0x3343, 0, 0, 0, g(Yes, No, false, false, "", "マイクロ")},
	{0x3344, 0, 0, 0, g(Yes, No, false, false, "", "マイル")},
	{0x3345, 0, 0, 0, g(Yes, No, false, false, "", "マッハ")},
	{0x3346, 0, 0, 0, g(Yes, No, false, false, "", "マルク")},
	{0x3347, 0, 0, 0, g(Yes, No, false, false, "", "マンション")},
	{0x3348, 0, 0, 0, g(Yes, No, false, false, "", "ミクロン")},
	{0x3349, 0, 0, 0, g(Yes, No, false, false, "", "ミリ")},
	{0x334a, 0, 0, 0, g(Yes, No, false, false, "", "ミリバール")},
	{0x334b, 0, 0, 1, g(Yes, No, false, false, "", "メガ")},
	{0x334c, 0, 0, 0, g(Yes, No, false, false, "", "メガトン")},
	{0x334d, 0, 0, 0, g(Yes, No, false, false, "", "メートル")},
	{0x334e, 0, 0, 1, g(Yes, No, false, false, "", "ヤード")},
	{0x334f, 0, 0, 0, g(Yes, No, false, false, "", "ヤール")},
	{0x3350, 0, 0, 0, g(Yes, No, false, false, "", "ユアン")},
	{0x3351, 0, 0, 0, g(Yes, No, false, false, "", "リットル")},
	{0x3352, 0, 0, 0, g(Yes, No, false, false, "", "リラ")},
	{0x3353, 0, 0, 0, g(Yes, No, false, false, "", "ルピー")},
	{0x3354, 0, 0, 0, g(Yes, No, false, false, "", "ルーブル")},
	{0x3355, 0, 0, 0, g(Yes, No, false, false, "", "レム")},
	{0x3356, 0, 0, 0, g(Yes, No, false, false, "", "レントゲン")},
	{0x3357, 0, 0, 0, g(Yes, No, false, false, "", "ワット")},
	{0x3358, 0, 0, 0, g(Yes, No, false, false, "", "0点")},
	{0x3359, 0, 0, 0, g(Yes, No, false, false, "", "1点")},
	{0x335a, 0, 0, 0, g(Yes, No, false, false, "", "2点")},
	{0x335b, 0, 0, 0, g(Yes, No, false, false, "", "3点")},
	{0x335c, 0, 0, 0, g(Yes, No, false, false, "", "4点")},
	{0x335d, 0, 0, 0, g(Yes, No, false, false, "", "5点")},
	{0x335e, 0, 0, 0, g(Yes, No, false, false, "", "6点")},
	{0x335f, 0, 0, 0, g(Yes, No, false, false, "", "7点")},
	{0x3360, 0, 0, 0, g(Yes, No, false, false, "", "8点")},
	{0x3361, 0, 0, 0, g(Yes, No, false, false, "", "9点")},
	{0x3362, 0, 0, 0, g(Yes, No, false, false, "", "10点")},
	{0x3363, 0, 0, 0, g(Yes, No, false, false, "", "11点")},
	{0x3364, 0, 0, 0, g(Yes, No, false, false, "", "12点")},
	{0x3365, 0, 0, 0, g(Yes, No, false, false, "", "13点")},
	{0x3366, 0, 0, 0, g(Yes, No, false, false, "", "14点")},
	{0x3367, 0, 0, 0, g(Yes, No, false, false, "", "15点")},
	{0x3368, 0, 0, 0, g(Yes, No, false, false, "", "16点")},
	{0x3369, 0, 0, 0, g(Yes, No, false, false, "", "17点")},
	{0x336a, 0, 0, 0, g(Yes, No, false, false, "", "18点")},
	{0x336b, 0, 0, 0, g(Yes, No, false, false, "", "19点")},
	{0x336c, 0, 0, 0, g(Yes, No, false, false, "", "20点")},
	{0x336d, 0, 0, 0, g(Yes, No, false, false, "", "21点")},
	{0x336e, 0, 0, 0, g(Yes, No, false, false, "", "22点")},
	{0x336f, 0, 0, 0, g(Yes, No, false, false, "", "23点")},
	{0x3370, 0, 0, 0, g(Yes, No, false, false, "", "24点")},
	{0x3371, 0, 0, 0, g(Yes, No, false, false, "", "hPa")},
	{0x3372, 0, 0, 0, g(Yes, No, false, false, "", "da")},
	{0x3373, 0, 0, 0, g(Yes, No, false, false, "", "AU")},
	{0x3374, 0, 0, 0, g(Yes, No, false, false, "", "bar")},
	{0x3375, 0, 0, 0, g(Yes, No, false, false, "", "oV")},
	{0x3376, 0, 0, 0, g(Yes, No, false, false, "", "pc")},
	{0x3377, 0, 0, 0, g(Yes, No, false, false, "", "dm")},
	{0x3378, 0, 0, 0, g(Yes, No, false, false, "", "dm2")},
	{0x3379, 0, 0, 0, g(Yes, No, false, false, "", "dm3")},
	{0x337a, 0, 0, 0, g(Yes, No, false, false, "", "IU")},
	{0x337b, 0, 0, 0, g(Yes, No, false, false, "", "平成")},
	{0x337c, 0, 0, 0, g(Yes, No, false, false, "", "昭和")},
	{0x337d, 0, 0, 0, g(Yes, No, false, false, "", "大正")},
	{0x337e, 0, 0, 0, g(Yes, No, false, false, "", "明治")},
	{0x337f, 0, 0, 0, g(Yes, No, false, false, "", "株式会社")},
	{0x3380, 0, 0, 0, g(Yes, No, false, false, "", "pA")},
	{0x3381, 0, 0, 0, g(Yes, No, false, false, "", "nA")},
	{0x3382, 0, 0, 0, g(Yes, No, false, false, "", "μA")},
	{0x3383, 0, 0, 0, g(Yes, No, false, false, "", "mA")},
	{0x3384, 0, 0, 0, g(Yes, No, false, false, "", "kA")},
	{0x3385, 0, 0, 0, g(Yes, No, false, false, "", "KB")},
	{0x3386, 0, 0, 0, g(Yes, No, false, false, "", "MB")},
	{0x3387, 0, 0, 0, g(Yes, No, false, false, "", "GB")},
	{0x3388, 0, 0, 0, g(Yes, No, false, false, "", "cal")},
	{0x3389, 0, 0, 0, g(Yes, No, false, false, "", "kcal")},
	{0x338a, 0, 0, 0, g(Yes, No, false, false, "", "pF")},
	{0x338b, 0, 0, 0, g(Yes, No, false, false, "", "nF")},
	{0x338c, 0, 0, 0, g(Yes, No, false, false, "", "μF")},
	{0x338d, 0, 0, 0, g(Yes, No, false, false, "", "μg")},
	{0x338e, 0, 0, 0, g(Yes, No, false, false, "", "mg")},
	{0x338f, 0, 0, 0, g(Yes, No, false, false, "", "kg")},
	{0x3390, 0, 0, 0, g(Yes, No, false, false, "", "Hz")},
	{0x3391, 0, 0, 0, g(Yes, No, false, false, "", "kHz")},
	{0x3392, 0, 0, 0, g(Yes, No, false, false, "", "MHz")},
	{0x3393, 0, 0, 0, g(Yes, No, false, false, "", "GHz")},
	{0x3394, 0, 0, 0, g(Yes, No, false, false, "", "THz")},
	{0x3395, 0, 0, 0, g(Yes, No, false, false, "", "μl")},
	{0x3396, 0, 0, 0, g(Yes, No, false, false, "", "ml")},
	{0x3397, 0, 0, 0, g(Yes, No, false, false, "", "dl")},
	{0x3398, 0, 0, 0, g(Yes, No, false, false, "", "kl")},
	{0x3399, 0, 0, 0, g(Yes, No, false, false, "", "fm")},
	{0x339a, 0, 0, 0, g(Yes, No, false, false, "", "nm")},
	{0x339b, 0, 0, 0, g(Yes, No, false, false, "", "μm")},
	{0x339c, 0, 0, 0, g(Yes, No, false, false, "", "mm")},
	{0x339d, 0, 0, 0, g(Yes, No, false, false, "", "cm")},
	{0x339e, 0, 0, 0, g(Yes, No, false, false, "", "km")},
	{0x339f, 0, 0, 0, g(Yes, No, false, false, "", "mm2")},
	{0x33a0, 0, 0, 0, g(Yes, No, false, false, "", "cm2")},
	{0x33a1, 0, 0, 0, g(Yes, No, false, false, "", "m2")},
	{0x33a2, 0, 0, 0, g(Yes, No, false, false, "", "km2")},
	{0x33a3, 0, 0, 0, g(Yes, No, false, false, "", "mm3")},
	{0x33a4, 0, 0, 0, g(Yes, No, false, false, "", "cm3")},
	{0x33a5, 0, 0, 0, g(Yes, No, false, false, "", "m3")},
	{0x33a6, 0, 0, 0, g(Yes, No, false, false, "", "km3")},
	{0x33a7, 0, 0, 0, g(Yes, No, false, false, "", "m∕s")},
	{0x33a8, 0, 0, 0, g(Yes, No, false, false, "", "m∕s2")},
	{0x33a9, 0, 0, 0, g(Yes, No, false, false, "", "Pa")},
	{0x33aa, 0, 0, 0, g(Yes, No, false, false, "", "kPa")},
	{0x33ab, 0, 0, 0, g(Yes, No, false, false, "", "MPa")},
	{0x33ac, 0, 0, 0, g(Yes, No, false, false, "", "GPa")},
	{0x33ad, 0, 0, 0, g(Yes, No, false, false, "", "rad")},
	{0x33ae, 0, 0, 0, g(Yes, No, false, false, "", "rad∕s")},
	{0x33af, 0, 0, 0, g(Yes, No, false, false, "", "rad∕s2")},
	{0x33b0, 0, 0, 0, g(Yes, No, false, false, "", "ps")},
	{0x33b1, 0, 0, 0, g(Yes, No, false, false, "", "ns")},
	{0x33b2, 0, 0, 0, g(Yes, No, false, false, "", "μs")},
	{0x33b3, 0, 0, 0, g(Yes, No, false, false, "", "ms")},
	{0x33b4, 0, 0, 0, g(Yes, No, false, false, "", "pV")},
	{0x33b5, 0, 0, 0, g(Yes, No, false, false, "", "nV")},
	{0x33b6, 0, 0, 0, g(Yes, No, false, false, "", "μV")},
	{0x33b7, 0, 0, 0, g(Yes, No, false, false, "", "mV")},
	{0x33b8, 0, 0, 0, g(Yes, No, false, false, "", "kV")},
	{0x33b9, 0, 0, 0, g(Yes, No, false, false, "", "MV")},
	{0x33ba, 0, 0, 0, g(Yes, No, false, false, "", "pW")},
	{0x33bb, 0, 0, 0, g(Yes, No, false, false, "", "nW")},
	{0x33bc, 0, 0, 0, g(Yes, No, false, false, "", "μW")},
	{0x33bd, 0, 0, 0, g(Yes, No, false, false, "", "mW")},
	{0x33be, 0, 0, 0, g(Yes, No, false, false, "", "kW")},
	{0x33bf, 0, 0, 0, g(Yes, No, false, false, "", "MW")},
	{0x33c0, 0, 0, 0, g(Yes, No, false, false, "", "kΩ")},
	{0x33c1, 0, 0, 0, g(Yes, No, false, false, "", "MΩ")},
	{0x33c2, 0, 0, 0, g(Yes, No, false, false, "", "a.m.")},
	{0x33c3, 0, 0, 0, g(Yes, No, false, false, "", "Bq")},
	{0x33c4, 0, 0, 0, g(Yes, No, false, false, "", "cc")},
	{0x33c5, 0, 0, 0, g(Yes, No, false, false, "", "cd")},
	{0x33c6, 0, 0, 0, g(Yes, No, false, false, "", "C∕kg")},
	{0x33c7, 0, 0, 0, g(Yes, No, false, false, "", "Co.")},
	{0x33c8, 0, 0, 0, g(Yes, No, false, false, "", "dB")},
	{0x33c9, 0, 0, 0, g(Yes, No, false, false, "", "Gy")},
	{0x33ca, 0, 0, 0, g(Yes, No, false, false, "", "ha")},
	{0x33cb, 0, 0, 0, g(Yes, No, false, false, "", "HP")},
	{0x33cc, 0, 0, 0, g(Yes, No, false, false, "", "in")},
	{0x33cd, 0, 0, 0, g(Yes, No, false, false, "", "KK")},
	{0x33ce, 0, 0, 0, g(Yes, No, false, false, "", "KM")},
	{0x33cf, 0, 0, 0, g(Yes, No, false, false, "", "kt")},
	{0x33d0, 0, 0, 0, g(Yes, No, false, false, "", "lm")},
	{0x33d1, 0, 0, 0, g(Yes, No, false, false, "", "ln")},
	{0x33d2, 0, 0, 0, g(Yes, No, false, false, "", "log")},
	{0x33d3, 0, 0, 0, g(Yes, No, false, false, "", "lx")},
	{0x33d4, 0, 0, 0, g(Yes, No, false, false, "", "mb")},
	{0x33d5, 0, 0, 0, g(Yes, No, false, false, "", "mil")},
	{0x33d6, 0, 0, 0, g(Yes, No, false, false, "", "mol")},
	{0x33d7, 0, 0, 0, g(Yes, No, false, false, "", "PH")},
	{0x33d8, 0, 0, 0, g(Yes, No, false, false, "", "p.m.")},
	{0x33d9, 0, 0, 0, g(Yes, No, false, false, "", "PPM")},
	{0x33da, 0, 0, 0, g(Yes, No, false, false, "", "PR")},
	{0x33db, 0, 0, 0, g(Yes, No, false, false, "", "sr")},
	{0x33dc, 0, 0, 0, g(Yes, No, false, false, "", "Sv")},
	{0x33dd, 0, 0, 0, g(Yes, No, false, false, "", "Wb")},
	{0x33de, 0, 0, 0, g(Yes, No, false, false, "", "V∕m")},
	{0x33df, 0, 0, 0, g(Yes, No, false, false, "", "A∕m")},
	{0x33e0, 0, 0, 0, g(Yes, No, false, false, "", "1日")},
	{0x33e1, 0, 0, 0, g(Yes, No, false, false, "", "2日")},
	{0x33e2, 0, 0, 0, g(Yes, No, false, false, "", "3日")},
	{0x33e3, 0, 0, 0, g(Yes, No, false, false, "", "4日")},
	{0x33e4, 0, 0, 0, g(Yes, No, false, false, "", "5日")},
	{0x33e5, 0, 0, 0, g(Yes, No, false, false, "", "6日")},
	{0x33e6, 0, 0, 0, g(Yes, No, false, false, "", "7日")},
	{0x33e7, 0, 0, 0, g(Yes, No, false, false, "", "8日")},
	{0x33e8, 0, 0, 0, g(Yes, No, false, false, "", "9日")},
	{0x33e9, 0, 0, 0, g(Yes, No, false, false, "", "10日")},
	{0x33ea, 0, 0, 0, g(Yes, No, false, false, "", "11日")},
	{0x33eb, 0, 0, 0, g(Yes, No, false, false, "", "12日")},
	{0x33ec, 0, 0, 0, g(Yes, No, false, false, "", "13日")},
	{0x33ed, 0, 0, 0, g(Yes, No, false, false, "", "14日")},
	{0x33ee, 0, 0, 0, g(Yes, No, false, false, "", "15日")},
	{0x33ef, 0, 0, 0, g(Yes, No, false, false, "", "16日")},
	{0x33f0, 0, 0, 0, g(Yes, No, false, false, "", "17日")},
	{0x33f1, 0, 0, 0, g(Yes, No, false, false, "", "18日")},
	{0x33f2, 0, 0, 0, g(Yes, No, false, false, "", "19日")},
	{0x33f3, 0, 0, 0, g(Yes, No, false, false, "", "20日")},
	{0x33f4, 0, 0, 0, g(Yes, No, false, false, "", "21日")},
	{0x33f5, 0, 0, 0, g(Yes, No, false, false, "", "22日")},
	{0x33f6, 0, 0, 0, g(Yes, No, false, false, "", "23日")},
	{0x33f7, 0, 0, 0, g(Yes, No, false, false, "", "24日")},
	{0x33f8, 0, 0, 0, g(Yes, No, false, false, "", "25日")},
	{0x33f9, 0, 0, 0, g(Yes, No, false, false, "", "26日")},
	{0x33fa, 0, 0, 0, g(Yes, No, false, false, "", "27日")},
	{0x33fb, 0, 0, 0, g(Yes, No, false, false, "", "28日")},
	{0x33fc, 0, 0, 0, g(Yes, No, false, false, "", "29日")},
	{0x33fd, 0, 0, 0, g(Yes, No, false, false, "", "30日")},
	{0x33fe, 0, 0, 0, g(Yes, No, false, false, "", "31日")},
	{0x33ff, 0, 0, 0, g(Yes, No, false, false, "", "gal")},
	{0x3400, 0, 0, 0, f(Yes, false, "")},
	{0xa66f, 230, 1, 1, f(Yes, false, "")},
	{0xa670, 0, 0, 0, f(Yes, false, "")},
	{0xa674, 230, 1, 1, f(Yes, false, "")},
	{0xa67e, 0, 0, 0, f(Yes, false, "")},
	{0xa69c, 0, 0, 0, g(Yes, No, false, false, "", "ъ")},
	{0xa69d, 0, 0, 0, g(Yes, No, false, false, "", "ь")},
	{0xa69e, 230, 1, 1, f(Yes, false, "")},
	{0xa6a0, 0, 0, 0, f(Yes, false, "")},
	{0xa6f0, 230, 1, 1, f(Yes, false, "")},
	{0xa6f2, 0, 0, 0, f(Yes, false, "")},
	{0xa770, 0, 0, 0, g(Yes, No, false, false, "", "ꝯ")},
	{0xa771, 0, 0, 0, f(Yes, false, "")},
	{0xa7f8, 0, 0, 0, g(Yes, No, false, false, "", "Ħ")},
	{0xa7f9, 0, 0, 0, g(Yes, No, false, false, "", "œ")},
	{0xa7fa, 0, 0, 0, f(Yes, false, "")},
	{0xa806, 9, 1, 1, f(Yes, false, "")},
	{0xa807, 0, 0, 0, f(Yes, false, "")},
	{0xa8c4, 9, 1, 1, f(Yes, false, "")},
	{0xa8c5, 0, 0, 0, f(Yes, false, "")},
	{0xa8e0, 230, 1, 1, f(Yes, false, "")},
	{0xa8f2, 0, 0, 0, f(Yes, false, "")},
	{0xa92b, 220, 1, 1, f(Yes, false, "")},
	{0xa92e, 0, 0, 0, f(Yes, false, "")},
	{0xa953, 9, 1, 1, f(Yes, false, "")},
	{0xa954, 0, 0, 0, f(Yes, false, "")},
	{0xa9b3, 7, 1, 1, f(Yes, false, "")},
	{0xa9b4, 0, 0, 0, f(Yes, false, "")},
	{0xa9c0, 9, 1, 1, f(Yes, false, "")},
	{0xa9c1, 0, 0, 0, f(Yes, false, "")},
	{0xaab0, 230, 1, 1, f(Yes, false, "")},
	{0xaab1, 0, 0, 0, f(Yes, false, "")},
	{0xaab2, 230, 1, 1, f(Yes, false, "")},
	{0xaab4, 220, 1, 1, f(Yes, false, "")},
	{0xaab5, 0, 0, 0, f(Yes, false, "")},
	{0xaab7, 230, 1, 1, f(Yes, false, "")},
	{0xaab9, 0, 0, 0, f(Yes, false, "")},
	{0xaabe, 230, 1, 1, f(Yes, false, "")},
	{0xaac0, 0, 0, 0, f(Yes, false, "")},
	{0xaac1, 230, 1, 1, f(Yes, false, "")},
	{0xaac2, 0, 0, 0, f(Yes, false, "")},
	{0xaaf6, 9, 1, 1, f(Yes, false, "")},
	{0xaaf7, 0, 0, 0, f(Yes, false, "")},
	{0xab5c, 0, 0, 0, g(Yes, No, false, false, "", "ꜧ")},
	{0xab5d, 0, 0, 0, g(Yes, No, false, false, "", "ꬷ")},
	{0xab5e, 0, 0, 0, g(Yes, No, false, false, "", "ɫ")},
	{0xab5f, 0, 0, 0, g(Yes, No, false, false, "", "ꭒ")},
	{0xab60, 0, 0, 0, f(Yes, false, "")},
	{0xabed, 9, 1, 1, f(Yes, false, "")},
	{0xabee, 0, 0, 0, f(Yes, false, "")},
	{0xac00, 0, 0, 1, f(Yes, true, "")},
	{0xac01, 0, 0, 2, f(Yes, false, "")},
	{0xac1c, 0, 0, 1, f(Yes, true, "")},
	{0xac1d, 0, 0, 2, f(Yes, false, "")},
	{0xac38, 0, 0, 1, f(Yes, true, "")},
	{0xac39, 0, 0, 2, f(Yes, false, "")},
	{0xac54, 0, 0, 1, f(Yes, true, "")},
	{0xac55, 0, 0, 2, f(Yes, false, "")},
	{0xac70, 0, 0, 1, f(Yes, true, "")},
	{0xac71, 0, 0, 2, f(Yes, false, "")},
	{0xac8c, 0, 0, 1, f(Yes, true, "")},
	{0xac8d, 0, 0, 2, f(Yes, false, "")},
	{0xaca8, 0, 0, 1, f(Yes, true, "")},
	{0xaca9, 0, 0, 2, f(Yes, false, "")},
	{0xacc4, 0, 0, 1, f(Yes, true, "")},
	{0xacc5, 0, 0, 2, f(Yes, false, "")},
	{0xace0, 0, 0, 1, f(Yes, true, "")},
	{0xace1, 0, 0, 2, f(Yes, false, "")},
	{0xacfc, 0, 0, 1, f(Yes, true, "")},
	{0xacfd, 0, 0, 2, f(Yes, false, "")},
	{0xad18, 0, 0, 1, f(Yes, true, "")},
	{0xad19, 0, 0, 2, f(Yes, false, "")},
	{0xad34, 0, 0, 1, f(Yes, true, "")},
	{0xad35, 0, 0, 2, f(Yes, false, "")},
	{0xad50, 0, 0, 1, f(Yes, true, "")},
	{0xad51, 0, 0, 2, f(Yes, false, "")},
	{0xad6c, 0, 0, 1, f(Yes, true, "")},
	{0xad6d, 0, 0, 2, f(Yes, false, "")},
	{0xad88, 0, 0, 1, f(Yes, true, "")},
	{0xad89, 0, 0, 2, f(Yes, false, "")},
	{0xada4, 0, 0, 1, f(Yes, true, "")},
	{0xada5, 0, 0, 2, f(Yes, false, "")},
	{0xadc0, 0, 0, 1, f(Yes, true, "")},
	{0xadc1, 0, 0, 2, f(Yes, false, "")},
	{0xaddc, 0, 0, 1, f(Yes, true, "")},
	{0xaddd, 0, 0, 2, f(Yes, false, "")},
	{0xadf8, 0, 0, 1, f(Yes, true, "")},
	{0xadf9, 0, 0, 2, f(Yes, false, "")},
	{0xae14, 0, 0, 1, f(Yes, true, "")},
	{0xae15, 0, 0, 2, f(Yes, false, "")},
	{0xae30, 0, 0, 1, f(Yes, true, "")},
	{0xae31, 0, 0, 2, f(Yes, false, "")},
	{0xae4c, 0, 0, 1, f(Yes, true, "")},
	{0xae4d, 0, 0, 2, f(Yes, false, "")},
	{0xae68, 0, 0, 1, f(Yes, true, "")},
	{0xae69, 0, 0, 2, f(Yes, false, "")},
	{0xae84, 0, 0, 1, f(Yes, true, "")},
	{0xae85, 0, 0, 2, f(Yes, false, "")},
	{0xaea0, 0, 0, 1, f(Yes, true, "")},
	{0xaea1, 0, 0, 2, f(Yes, false, "")},
	{0xaebc, 0, 0, 1, f(Yes, true, "")},
	{0xaebd, 0, 0, 2, f(Yes, false, "")},
	{0xaed8, 0, 0, 1, f(Yes, true, "")},
	{0xaed9, 0, 0, 2, f(Yes, false, "")},
	{0xaef4, 0, 0, 1, f(Yes, true, "")},
	{0xaef5, 0, 0, 2, f(Yes, false, "")},
	{0xaf10, 0, 0, 1, f(Yes, true, "")},
	{0xaf11, 0, 0, 2, f(Yes, false, "")},
	{0xaf2c, 0, 0, 1, f(Yes, true, "")},
	{0xaf2d, 0, 0, 2, f(Yes, false, "")},
	{0xaf48, 0, 0, 1, f(Yes, true, "")},
	{0xaf49, 0, 0, 2, f(Yes, false, "")},
	{0xaf64, 0, 0, 1, f(Yes, true, "")},
	{0xaf65, 0, 0, 2, f(Yes, false, "")},
	{0xaf80, 0, 0, 1, f(Yes, true, "")},
	{0xaf81, 0, 0, 2, f(Yes, false, "")},
	{0xaf9c, 0, 0, 1, f(Yes, true, "")},
	{0xaf9d, 0, 0, 2, f(Yes, false, "")},
	{0xafb8, 0, 0, 1, f(Yes, true, "")},
	{0xafb9, 0, 0, 2, f(Yes, false, "")},
	{0xafd4, 0, 0, 1, f(Yes, true, "")},
	{0xafd5, 0, 0, 2, f(Yes, false, "")},
	{0xaff0, 0, 0, 1, f(Yes, true, "")},
	{0xaff1, 0, 0, 2, f(Yes, false, "")},
	{0xb00c, 0, 0, 1, f(Yes, true, "")},
	{0xb00d, 0, 0, 2, f(Yes, false, "")},
	{0xb028, 0, 0, 1, f(Yes, true, "")},
	{0xb029, 0, 0, 2, f(Yes, false, "")},
	{0xb044, 0, 0, 1, f(Yes, true, "")},
	{0xb045, 0, 0, 2, f(Yes, false, "")},
	{0xb060, 0, 0, 1, f(Yes, true, "")},
	{0xb061, 0, 0, 2, f(Yes, false, "")},
	{0xb07c, 0, 0, 1, f(Yes, true, "")},
	{0xb07d, 0, 0, 2, f(Yes, false, "")},
	{0xb098, 0, 0, 1, f(Yes, true, "")},
	{0xb099, 0, 0, 2, f(Yes, false, "")},
	{0xb0b4, 0, 0, 1, f(Yes, true, "")},
	{0xb0b5, 0, 0, 2, f(Yes, false, "")},
	{0xb0d0, 0, 0, 1, f(Yes, true, "")},
	{0xb0d1, 0, 0, 2, f(Yes, false, "")},
	{0xb0ec, 0, 0, 1, f(Yes, true, "")},
	{0xb0ed, 0, 0, 2, f(Yes, false, "")},
	{0xb108, 0, 0, 1, f(Yes, true, "")},
	{0xb109, 0, 0, 2, f(Yes, false, "")},
	{0xb124, 0, 0, 1, f(Yes, true, "")},
	{0xb125, 0, 0, 2, f(Yes, false, "")},
	{0xb140, 0, 0, 1, f(Yes, true, "")},
	{0xb141, 0, 0, 2, f(Yes, false, "")},
	{0xb15c, 0, 0, 1, f(Yes, true, "")},
	{0xb15d, 0, 0, 2, f(Yes, false, "")},
	{0xb178, 0, 0, 1, f(Yes, true, "")},
	{0xb179, 0, 0, 2, f(Yes, false, "")},
	{0xb194, 0, 0, 1, f(Yes, true, "")},
	{0xb195, 0, 0, 2, f(Yes, false, "")},
	{0xb1b0, 0, 0, 1, f(Yes, true, "")},
	{0xb1b1, 0, 0, 2, f(Yes, false, "")},
	{0xb1cc, 0, 0, 1, f(Yes, true, "")},
	{0xb1cd, 0, 0, 2, f(Yes, false, "")},
	{0xb1e8, 0, 0, 1, f(Yes, true, "")},
	{0xb1e9, 0, 0, 2, f(Yes, false, "")},
	{0xb204, 0, 0, 1, f(Yes, true, "")},
	{0xb205, 0, 0, 2, f(Yes, false, "")},
	{0xb220, 0, 0, 1, f(Yes, true, "")},
	{0xb221, 0, 0, 2, f(Yes, false, "")},
	{0xb23c, 0, 0, 1, f(Yes, true, "")},
	{0xb23d, 0, 0, 2, f(Yes, false, "")},
	{0xb258, 0, 0, 1, f(Yes, true, "")},
	{0xb259, 0, 0, 2, f(Yes, false, "")},
	{0xb274, 0, 0, 1, f(Yes, true, "")},
	{0xb275, 0, 0, 2, f(Yes, false, "")},
	{0xb290, 0, 0, 1, f(Yes, true, "")},
	{0xb291, 0, 0, 2, f(Yes, false, "")},
	{0xb2ac, 0, 0, 1, f(Yes, true, "")},
	{0xb2ad, 0, 0, 2, f(Yes, false, "")},
	{0xb2c8, 0, 0, 1, f(Yes, true, "")},
	{0xb2c9, 0, 0, 2, f(Yes, false, "")},
	{0xb2e4, 0, 0, 1, f(Yes, true, "")},
	{0xb2e5, 0, 0, 2, f(Yes, false, "")},
	{0xb300, 0, 0, 1, f(Yes, true, "")},
	{0xb301, 0, 0, 2, f(Yes, false, "")},
	{0xb31c, 0, 0, 1, f(Yes, true, "")},
	{0xb31d, 0, 0, 2, f(Yes, false, "")},
	{0xb338, 0, 0, 1, f(Yes, true, "")},
	{0xb339, 0, 0, 2, f(Yes, false, "")},
	{0xb354, 0, 0, 1, f(Yes, true, "")},
	{0xb355, 0, 0, 2, f(Yes, false, "")},
	{0xb370, 0, 0, 1, f(Yes, true, "")},
	{0xb371, 0, 0, 2, f(Yes, false, "")},
	{0xb38c, 0, 0, 1, f(Yes, true, "")},
	{0xb38d, 0, 0, 2, f(Yes, false, "")},
	{0xb3a8, 0, 0, 1, f(Yes, true, "")},
	{0xb3a9, 0, 0, 2, f(Yes, false, "")},
	{0xb3c4, 0, 0, 1, f(Yes, true, "")},
	{0xb3c5, 0, 0, 2, f(Yes, false, "")},
	{0xb3e0, 0, 0, 1, f(Yes, true, "")},
	{0xb3e1, 0, 0, 2, f(Yes, false, "")},
	{0xb3fc, 0, 0, 1, f(Yes, true, "")},
	{0xb3fd, 0, 0, 2, f(Yes, false, "")},
	{0xb418, 0, 0, 1, f(Yes, true, "")},
	{0xb419, 0, 0, 2, f(Yes, false, "")},
	{0xb434, 0, 0, 1, f(Yes, true, "")},
	{0xb435, 0, 0, 2, f(Yes, false, "")},
	{0xb450, 0, 0, 1, f(Yes, true, "")},
	{0xb451, 0, 0, 2, f(Yes, false, "")},
	{0xb46c, 0, 0, 1, f(Yes, true, "")},
	{0xb46d, 0, 0, 2, f(Yes, false, "")},
	{0xb488, 0, 0, 1, f(Yes, true, "")},
	{0xb489, 0, 0, 2, f(Yes, false, "")},
	{0xb4a4, 0, 0, 1, f(Yes, true, "")},
	{0xb4a5, 0, 0, 2, f(Yes, false, "")},
	{0xb4c0, 0, 0, 1, f(Yes, true, "")},
	{0xb4c1, 0, 0, 2, f(Yes, false, "")},
	{0xb4dc, 0, 0, 1, f(Yes, true, "")},
	{0xb4dd, 0, 0, 2, f(Yes, false, "")},
	{0xb4f8, 0, 0, 1, f(Yes, true, "")},
	{0xb4f9, 0, 0, 2, f(Yes, false, "")},
	{0xb514, 0, 0, 1, f(Yes, true, "")},
	{0xb515, 0, 0, 2, f(Yes, false, "")},
	{0xb530, 0, 0, 1, f(Yes, true, "")},
	{0xb531, 0, 0, 2, f(Yes, false, "")},
	{0xb54c, 0, 0, 1, f(Yes, true, "")},
	{0xb54d, 0, 0, 2, f(Yes, false, "")},
	{0xb568, 0, 0, 1, f(Yes, true, "")},
	{0xb569, 0, 0, 2, f(Yes, false, "")},
	{0xb584, 0, 0, 1, f(Yes, true, "")},
	{0xb585, 0, 0, 2, f(Yes, false, "")},
	{0xb5a0, 0, 0, 1, f(Yes, true, "")},
	{0xb5a1, 0, 0, 2, f(Yes, false, "")},
	{0xb5bc, 0, 0, 1, f(Yes, true, "")},
	{0xb5bd, 0, 0, 2, f(Yes, false, "")},
	{0xb5d8, 0, 0, 1, f(Yes, true, "")},
	{0xb5d9, 0, 0, 2, f(Yes, false, "")},
	{0xb5f4, 0, 0, 1, f(Yes, true, "")},
	{0xb5f5, 0, 0, 2, f(Yes, false, "")},
	{0xb610, 0, 0, 1, f(Yes, true, "")},
	{0xb611, 0, 0, 2, f(Yes, false, "")},
	{0xb62c, 0, 0, 1, f(Yes, true, "")},
	{0xb62d, 0, 0, 2, f(Yes, false, "")},
	{0xb648, 0, 0, 1, f(Yes, true, "")},
	{0xb649, 0, 0, 2, f(Yes, false, "")},
	{0xb664, 0, 0, 1, f(Yes, true, "")},
	{0xb665, 0, 0, 2, f(Yes, false, "")},
	{0xb680, 0, 0, 1, f(Yes, true, "")},
	{0xb681, 0, 0, 2, f(Yes, false, "")},
	{0xb69c, 0, 0, 1, f(Yes, true, "")},
	{0xb69d, 0, 0, 2, f(Yes, false, "")},
	{0xb6b8, 0, 0, 1, f(Yes, true, "")},
	{0xb6b9, 0, 0, 2, f(Yes, false, "")},
	{0xb6d4, 0, 0, 1, f(Yes, true, "")},
	{0xb6d5, 0, 0, 2, f(Yes, false, "")},
	{0xb6f0, 0, 0, 1, f(Yes, true, "")},
	{0xb6f1, 0, 0, 2, f(Yes, false, "")},
	{0xb70c, 0, 0, 1, f(Yes, true, "")},
	{0xb70d, 0, 0, 2, f(Yes, false, "")},
	{0xb728, 0, 0, 1, f(Yes, true, "")},
	{0xb729, 0, 0, 2, f(Yes, false, "")},
	{0xb744, 0, 0, 1, f(Yes, true, "")},
	{0xb745, 0, 0, 2, f(Yes, false, "")},
	{0xb760, 0, 0, 1, f(Yes, true, "")},
	{0xb761, 0, 0, 2, f(Yes, false, "")},
	{0xb77c, 0, 0, 1, f(Yes, true, "")},
	{0xb77d, 0, 0, 2, f(Yes, false, "")},
	{0xb798, 0, 0, 1, f(Yes, true, "")},
	{0xb799, 0, 0, 2, f(Yes, false, "")},
	{0xb7b4, 0, 0, 1, f(Yes, true, "")},
	{0xb7b5, 0, 0, 2, f(Yes, false, "")},
	{0xb7d0, 0, 0, 1, f(Yes, true, "")},
	{0xb7d1, 0, 0, 2, f(Yes, false, "")},
	{0xb7ec, 0, 0, 1, f(Yes, true, "")},
	{0xb7ed, 0, 0, 2, f(Yes, false, "")},
	{0xb808, 0, 0, 1, f(Yes, true, "")},
	{0xb809, 0, 0, 2, f(Yes, false, "")},
	{0xb824, 0, 0, 1, f(Yes, true, "")},
	{0xb825, 0, 0, 2, f(Yes, false, "")},
	{0xb840, 0, 0, 1, f(Yes, true, "")},
	{0xb841, 0, 0, 2, f(Yes, false, "")},
	{0xb85c, 0, 0, 1, f(Yes, true, "")},
	{0xb85d, 0, 0, 2, f(Yes, false, "")},
	{0xb878, 0, 0, 1, f(Yes, true, "")},
	{0xb879, 0, 0, 2, f(Yes, false, "")},
	{0xb894, 0, 0, 1, f(Yes, true, "")},
	{0xb895, 0, 0, 2, f(Yes, false, "")},
	{0xb8b0, 0, 0, 1, f(Yes, true, "")},
	{0xb8b1, 0, 0, 2, f(Yes, false, "")},
	{0xb8cc, 0, 0, 1, f(Yes, true, "")},
	{0xb8cd, 0, 0, 2, f(Yes, false, "")},
	{0xb8e8, 0, 0, 1, f(Yes, true, "")},
	{0xb8e9, 0, 0, 2, f(Yes, false, "")},
	{0xb904, 0, 0, 1, f(Yes, true, "")},
	{0xb905, 0, 0, 2, f(Yes, false, "")},
	{0xb920, 0, 0, 1, f(Yes, true, "")},
	{0xb921, 0, 0, 2, f(Yes, false, "")},
	{0xb93c, 0, 0, 1, f(Yes, true, "")},
	{0xb93d, 0, 0, 2, f(Yes, false, "")},
	{0xb958, 0, 0, 1, f(Yes, true, "")},
	{0xb959, 0, 0, 2, f(Yes, false, "")},
	{0xb974, 0, 0, 1, f(Yes, true, "")},
	{0xb975, 0, 0, 2, f(Yes, false, "")},
	{0xb990, 0, 0, 1, f(Yes, true, "")},
	{0xb991, 0, 0, 2, f(Yes, false, "")},
	{0xb9ac, 0, 0, 1, f(Yes, true, "")},
	{0xb9ad, 0, 0, 2, f(Yes, false, "")},
	{0xb9c8, 0, 0, 1, f(Yes, true, "")},
	{0xb9c9, 0, 0, 2, f(Yes, false, "")},
	{0xb9e4, 0, 0, 1, f(Yes, true, "")},
	{0xb9e5, 0, 0, 2, f(Yes, false, "")},
	{0xba00, 0, 0, 1, f(Yes, true, "")},
	{0xba01, 0, 0, 2, f(Yes, false, "")},
	{0xba1c, 0, 0, 1, f(Yes, true, "")},
	{0xba1d, 0, 0, 2, f(Yes, false, "")},
	{0xba38, 0, 0, 1, f(Yes, true, "")},
	{0xba39, 0, 0, 2, f(Yes, false, "")},
	{0xba54, 0, 0, 1, f(Yes, true, "")},
	{0xba55, 0, 0, 2, f(Yes, false, "")},
	{0xba70, 0, 0, 1, f(Yes, true, "")},
	{0xba71, 0, 0, 2, f(Yes, false, "")},
	{0xba8c, 0, 0, 1, f(Yes, true, "")},
	{0xba8d, 0, 0, 2, f(Yes, false, "")},
	{0xbaa8, 0, 0, 1, f(Yes, true, "")},
	{0xbaa9, 0, 0, 2, f(Yes, false, "")},
	{0xbac4, 0, 0, 1, f(Yes, true, "")},
	{0xbac5, 0, 0, 2, f(Yes, false, "")},
	{0xbae0, 0, 0, 1, f(Yes, true, "")},
	{0xbae1, 0, 0, 2, f(Yes, false, "")},
	{0xbafc, 0, 0, 1, f(Yes, true, "")},
	{0xbafd, 0, 0, 2, f(Yes, false, "")},
	{0xbb18, 0, 0, 1, f(Yes, true, "")},
	{0xbb19, 0, 0, 2, f(Yes, false, "")},
	{0xbb34, 0, 0, 1, f(Yes, true, "")},
	{0xbb35, 0, 0, 2, f(Yes, false, "")},
	{0xbb50, 0, 0, 1, f(Yes, true, "")},
	{0xbb51, 0, 0, 2, f(Yes, false, "")},
	{0xbb6c, 0, 0, 1, f(Yes, true, "")},
	{0xbb6d, 0, 0, 2, f(Yes, false, "")},
	{0xbb88, 0, 0, 1, f(Yes, true, "")},
	{0xbb89, 0, 0, 2, f(Yes, false, "")},
	{0xbba4, 0, 0, 1, f(Yes, true, "")},
	{0xbba5, 0, 0, 2, f(Yes, false, "")},
	{0xbbc0, 0, 0, 1, f(Yes, true, "")},
	{0xbbc1, 0, 0, 2, f(Yes, false, "")},
	{0xbbdc, 0, 0, 1, f(Yes, true, "")},
	{0xbbdd, 0, 0, 2, f(Yes, false, "")},
	{0xbbf8, 0, 0, 1, f(Yes, true, "")},
	{0xbbf9, 0, 0, 2, f(Yes, false, "")},
	{0xbc14, 0, 0, 1, f(Yes, true, "")},
	{0xbc15, 0, 0, 2, f(Yes, false, "")},
	{0xbc30, 0, 0, 1, f(Yes, true, "")},
	{0xbc31, 0, 0, 2, f(Yes, false, "")},
	{0xbc4c, 0, 0, 1, f(Yes, true, "")},
	{0xbc4d, 0, 0, 2, f(Yes, false, "")},
	{0xbc68, 0, 0, 1, f(Yes, true, "")},
	{0xbc69, 0, 0, 2, f(Yes, false, "")},
	{0xbc84, 0, 0, 1, f(Yes, true, "")},
	{0xbc85, 0, 0, 2, f(Yes, false, "")},
	{0xbca0, 0, 0, 1, f(Yes, true, "")},
	{0xbca1, 0, 0, 2, f(Yes, false, "")},
	{0xbcbc, 0, 0, 1, f(Yes, true, "")},
	{0xbcbd, 0, 0, 2, f(Yes, false, "")},
	{0xbcd8, 0, 0, 1, f(Yes, true, "")},
	{0xbcd9, 0, 0, 2, f(Yes, false, "")},
	{0xbcf4, 0, 0, 1, f(Yes, true, "")},
	{0xbcf5, 0, 0, 2, f(Yes, false, "")},
	{0xbd10, 0, 0, 1, f(Yes, true, "")},
	{0xbd11, 0, 0, 2, f(Yes, false, "")},
	{0xbd2c, 0, 0, 1, f(Yes, true, "")},
	{0xbd2d, 0, 0, 2, f(Yes, false, "")},
	{0xbd48, 0, 0, 1, f(Yes, true, "")},
	{0xbd49, 0, 0, 2, f(Yes, false, "")},
	{0xbd64, 0, 0, 1, f(Yes, true, "")},
	{0xbd65, 0, 0, 2, f(Yes, false, "")},
	{0xbd80, 0, 0, 1, f(Yes, true, "")},
	{0xbd81, 0, 0, 2, f(Yes, false, "")},
	{0xbd9c, 0, 0, 1, f(Yes, true, "")},
	{0xbd9d, 0, 0, 2, f(Yes, false, "")},
	{0xbdb8, 0, 0, 1, f(Yes, true, "")},
	{0xbdb9, 0, 0, 2, f(Yes, false, "")},
	{0xbdd4, 0, 0, 1, f(Yes, true, "")},
	{0xbdd5, 0, 0, 2, f(Yes, false, "")},
	{0xbdf0, 0, 0, 1, f(Yes, true, "")},
	{0xbdf1, 0, 0, 2, f(Yes, false, "")},
	{0xbe0c, 0, 0, 1, f(Yes, true, "")},
	{0xbe0d, 0, 0, 2, f(Yes, false, "")},
	{0xbe28, 0, 0, 1, f(Yes, true, "")},
	{0xbe29, 0, 0, 2, f(Yes, false, "")},
	{0xbe44, 0, 0, 1, f(Yes, true, "")},
	{0xbe45, 0, 0, 2, f(Yes, false, "")},
	{0xbe60, 0, 0, 1, f(Yes, true, "")},
	{0xbe61, 0, 0, 2, f(Yes, false, "")},
	{0xbe7c, 0, 0, 1, f(Yes, true, "")},
	{0xbe7d, 0, 0, 2, f(Yes, false, "")},
	{0xbe98, 0, 0, 1, f(Yes, true, "")},
	{0xbe99, 0, 0, 2, f(Yes, false, "")},
	{0xbeb4, 0, 0, 1, f(Yes, true, "")},
	{0xbeb5, 0, 0, 2, f(Yes, false, "")},
	{0xbed0, 0, 0, 1, f(Yes, true, "")},
	{0xbed1, 0, 0, 2, f(Yes, false, "")},
	{0xbeec, 0, 0, 1, f(Yes, true, "")},
	{0xbeed, 0, 0, 2, f(Yes, false, "")},
	{0xbf08, 0, 0, 1, f(Yes, true, "")},
	{0xbf09, 0, 0, 2, f(Yes, false, "")},
	{0xbf24, 0, 0, 1, f(Yes, true, "")},
	{0xbf25, 0, 0, 2, f(Yes, false, "")},
	{0xbf40, 0, 0, 1, f(Yes, true, "")},
	{0xbf41, 0, 0, 2, f(Yes, false, "")},
	{0xbf5c, 0, 0, 1, f(Yes, true, "")},
	{0xbf5d, 0, 0, 2, f(Yes, false, "")},
	{0xbf78, 0, 0, 1, f(Yes, true, "")},
	{0xbf79, 0, 0, 2, f(Yes, false, "")},
	{0xbf94, 0, 0, 1, f(Yes, true, "")},
	{0xbf95, 0, 0, 2, f(Yes, false, "")},
	{0xbfb0, 0, 0, 1, f(Yes, true, "")},
	{0xbfb1, 0, 0, 2, f(Yes, false, "")},
	{0xbfcc, 0, 0, 1, f(Yes, true, "")},
	{0xbfcd, 0, 0, 2, f(Yes, false, "")},
	{0xbfe8, 0, 0, 1, f(Yes, true, "")},
	{0xbfe9, 0, 0, 2, f(Yes, false, "")},
	{0xc004, 0, 0, 1, f(Yes, true, "")},
	{0xc005, 0, 0, 2, f(Yes, false, "")},
	{0xc020, 0, 0, 1, f(Yes, true, "")},
	{0xc021, 0, 0, 2, f(Yes, false, "")},
	{0xc03c, 0, 0, 1, f(Yes, true, "")},
	{0xc03d, 0, 0, 2, f(Yes, false, "")},
	{0xc058, 0, 0, 1, f(Yes, true, "")},
	{0xc059, 0, 0, 2, f(Yes, false, "")},
	{0xc074, 0, 0, 1, f(Yes, true, "")},
	{0xc075, 0, 0, 2, f(Yes, false, "")},
	{0xc090, 0, 0, 1, f(Yes, true, "")},
	{0xc091, 0, 0, 2, f(Yes, false, "")},
	{0xc0ac, 0, 0, 1, f(Yes, true, "")},
	{0xc0ad, 0, 0, 2, f(Yes, false, "")},
	{0xc0c8, 0, 0, 1, f(Yes, true, "")},
	{0xc0c9, 0, 0, 2, f(Yes, false, "")},
	{0xc0e4, 0, 0, 1, f(Yes, true, "")},
	{0xc0e5, 0, 0, 2, f(Yes, false, "")},
	{0xc100, 0, 0, 1, f(Yes, true, "")},
	{0xc101, 0, 0, 2, f(Yes, false, "")},
	{0xc11c, 0, 0, 1, f(Yes, true, "")},
	{0xc11d, 0, 0, 2, f(Yes, false, "")},
	{0xc138, 0, 0, 1, f(Yes, true, "")},
	{0xc139, 0, 0, 2, f(Yes, false, "")},
	{0xc154, 0, 0, 1, f(Yes, true, "")},
	{0xc155, 0, 0, 2, f(Yes, false, "")},
	{0xc170, 0, 0, 1, f(Yes, true, "")},
	{0xc171, 0, 0, 2, f(Yes, false, "")},
	{0xc18c, 0, 0, 1, f(Yes, true, "")},
	{0xc18d, 0, 0, 2, f(Yes, false, "")},
	{0xc1a8, 0, 0, 1, f(Yes, true, "")},
	{0xc1a9, 0, 0, 2, f(Yes, false, "")},
	{0xc1c4, 0, 0, 1, f(Yes, true, "")},
	{0xc1c5, 0, 0, 2, f(Yes, false, "")},
	{0xc1e0, 0, 0, 1, f(Yes, true, "")},
	{0xc1e1, 0, 0, 2, f(Yes, false, "")},
	{0xc1fc, 0, 0, 1, f(Yes, true, "")},
	{0xc1fd, 0, 0, 2, f(Yes, false, "")},
	{0xc218, 0, 0, 1, f(Yes, true, "")},
	{0xc219, 0, 0, 2, f(Yes, false, "")},
	{0xc234, 0, 0, 1, f(Yes, true, "")},
	{0xc235, 0, 0, 2, f(Yes, false, "")},
	{0xc250, 0, 0, 1, f(Yes, true, "")},
	{0xc251, 0, 0, 2, f(Yes, false, "")},
	{0xc26c, 0, 0, 1, f(Yes, true, "")},
	{0xc26d, 0, 0, 2, f(Yes, false, "")},
	{0xc288, 0, 0, 1, f(Yes, true, "")},
	{0xc289, 0, 0, 2, f(Yes, false, "")},
	{0xc2a4, 0, 0, 1, f(Yes, true, "")},
	{0xc2a5, 0, 0, 2, f(Yes, false, "")},
	{0xc2c0, 0, 0, 1, f(Yes, true, "")},
	{0xc2c1, 0, 0, 2, f(Yes, false, "")},
	{0xc2dc, 0, 0, 1, f(Yes, true, "")},
	{0xc2dd, 0, 0, 2, f(Yes, false, "")},
	{0xc2f8, 0, 0, 1, f(Yes, true, "")},
	{0xc2f9, 0, 0, 2, f(Yes, false, "")},
	{0xc314, 0, 0, 1, f(Yes, true, "")},
	{0xc315, 0, 0, 2, f(Yes, false, "")},
	{0xc330, 0, 0, 1, f(Yes, true, "")},
	{0xc331, 0, 0, 2, f(Yes, false, "")},
	{0xc34c, 0, 0, 1, f(Yes, true, "")},
	{0xc34d, 0, 0, 2, f(Yes, false, "")},
	{0xc368, 0, 0, 1, f(Yes, true, "")},
	{0xc369, 0, 0, 2, f(Yes, false, "")},
	{0xc384, 0, 0, 1, f(Yes, true, "")},
	{0xc385, 0, 0, 2, f(Yes, false, "")},
	{0xc3a0, 0, 0, 1, f(Yes, true, "")},
	{0xc3a1, 0, 0, 2, f(Yes, false, "")},
	{0xc3bc, 0, 0, 1, f(Yes, true, "")},
	{0xc3bd, 0, 0, 2, f(Yes, false, "")},
	{0xc3d8, 0, 0, 1, f(Yes, true, "")},
	{0xc3d9, 0, 0, 2, f(Yes, false, "")},
	{0xc3f4, 0, 0, 1, f(Yes, true, "")},
	{0xc3f5, 0, 0, 2, f(Yes, false, "")},
	{0xc410, 0, 0, 1, f(Yes, true, "")},
	{0xc411, 0, 0, 2, f(Yes, false, "")},
	{0xc42c, 0, 0, 1, f(Yes, true, "")},
	{0xc42d, 0, 0, 2, f(Yes, false, "")},
	{0xc448, 0, 0, 1, f(Yes, true, "")},
	{0xc449, 0, 0, 2, f(Yes, false, "")},
	{0xc464, 0, 0, 1, f(Yes, true, "")},
	{0xc465, 0, 0, 2, f(Yes, false, "")},
	{0xc480, 0, 0, 1, f(Yes, true, "")},
	{0xc481, 0, 0, 2, f(Yes, false, "")},
	{0xc49c, 0, 0, 1, f(Yes, true, "")},
	{0xc49d, 0, 0, 2, f(Yes, false, "")},
	{0xc4b8, 0, 0, 1, f(Yes, true, "")},
	{0xc4b9, 0, 0, 2, f(Yes, false, "")},
	{0xc4d4, 0, 0, 1, f(Yes, true, "")},
	{0xc4d5, 0, 0, 2, f(Yes, false, "")},
	{0xc4f0, 0, 0, 1, f(Yes, true, "")},
	{0xc4f1, 0, 0, 2, f(Yes, false, "")},
	{0xc50c, 0, 0, 1, f(Yes, true, "")},
	{0xc50d, 0, 0, 2, f(Yes, false, "")},
	{0xc528, 0, 0, 1, f(Yes, true, "")},
	{0xc529, 0, 0, 2, f(Yes, false, "")},
	{0xc544, 0, 0, 1, f(Yes, true, "")},
	{0xc545, 0, 0, 2, f(Yes, false, "")},
	{0xc560, 0, 0, 1, f(Yes, true, "")},
	{0xc561, 0, 0, 2, f(Yes, false, "")},
	{0xc57c, 0, 0, 1, f(Yes, true, "")},
	{0xc57d, 0, 0, 2, f(Yes, false, "")},
	{0xc598, 0, 0, 1, f(Yes, true, "")},
	{0xc599, 0, 0, 2, f(Yes, false, "")},
	{0xc5b4, 0, 0, 1, f(Yes, true, "")},
	{0xc5b5, 0, 0, 2, f(Yes, false, "")},
	{0xc5d0, 0, 0, 1, f(Yes, true, "")},
	{0xc5d1, 0, 0, 2, f(Yes, false, "")},
	{0xc5ec, 0, 0, 1, f(Yes, true, "")},
	{0xc5ed, 0, 0, 2, f(Yes, false, "")},
	{0xc608, 0, 0, 1, f(Yes, true, "")},
	{0xc609, 0, 0, 2, f(Yes, false, "")},
	{0xc624, 0, 0, 1, f(Yes, true, "")},
	{0xc625, 0, 0, 2, f(Yes, false, "")},
	{0xc640, 0, 0, 1, f(Yes, true, "")},
	{0xc641, 0, 0, 2, f(Yes, false, "")},
	{0xc65c, 0, 0, 1, f(Yes, true, "")},
	{0xc65d, 0, 0, 2, f(Yes, false, "")},
	{0xc678, 0, 0, 1, f(Yes, true, "")},
	{0xc679, 0, 0, 2, f(Yes, false, "")},
	{0xc694, 0, 0, 1, f(Yes, true, "")},
	{0xc695, 0, 0, 2, f(Yes, false, "")},
	{0xc6b0, 0, 0, 1, f(Yes, true, "")},
	{0xc6b1, 0, 0, 2, f(Yes, false, "")},
	{0xc6cc, 0, 0, 1, f(Yes, true, "")},
	{0xc6cd, 0, 0, 2, f(Yes, false, "")},
	{0xc6e8, 0, 0, 1, f(Yes, true, "")},
	{0xc6e9, 0, 0, 2, f(Yes, false, "")},
	{0xc704, 0, 0, 1, f(Yes, true, "")},
	{0xc705, 0, 0, 2, f(Yes, false, "")},
	{0xc720, 0, 0, 1, f(Yes, true, "")},
	{0xc721, 0, 0, 2, f(Yes, false, "")},
	{0xc73c, 0, 0, 1, f(Yes, true, "")},
	{0xc73d, 0, 0, 2, f(Yes, false, "")},
	{0xc758, 0, 0, 1, f(Yes, true, "")},
	{0xc759, 0, 0, 2, f(Yes, false, "")},
	{0xc774, 0, 0, 1, f(Yes, true, "")},
	{0xc775, 0, 0, 2, f(Yes, false, "")},
	{0xc790, 0, 0, 1, f(Yes, true, "")},
	{0xc791, 0, 0, 2, f(Yes, false, "")},
	{0xc7ac, 0, 0, 1, f(Yes, true, "")},
	{0xc7ad, 0, 0, 2, f(Yes, false, "")},
	{0xc7c8, 0, 0, 1, f(Yes, true, "")},
	{0xc7c9, 0, 0, 2, f(Yes, false, "")},
	{0xc7e4, 0, 0, 1, f(Yes, true, "")},
	{0xc7e5, 0, 0, 2, f(Yes, false, "")},
	{0xc800, 0, 0, 1, f(Yes, true, "")},
	{0xc801, 0, 0, 2, f(Yes, false, "")},
	{0xc81c, 0, 0, 1, f(Yes, true, "")},
	{0xc81d, 0, 0, 2, f(Yes, false, "")},
	{0xc838, 0, 0, 1, f(Yes, true, "")},
	{0xc839, 0, 0, 2, f(Yes, false, "")},
	{0xc854, 0, 0, 1, f(Yes, true, "")},
	{0xc855, 0, 0, 2, f(Yes, false, "")},
	{0xc870, 0, 0, 1, f(Yes, true, "")},
	{0xc871, 0, 0, 2, f(Yes, false, "")},
	{0xc88c, 0, 0, 1, f(Yes, true, "")},
	{0xc88d, 0, 0, 2, f(Yes, false, "")},
	{0xc8a8, 0, 0, 1, f(Yes, true, "")},
	{0xc8a9, 0, 0, 2, f(Yes, false, "")},
	{0xc8c4, 0, 0, 1, f(Yes, true, "")},
	{0xc8c5, 0, 0, 2, f(Yes, false, "")},
	{0xc8e0, 0, 0, 1, f(Yes, true, "")},
	{0xc8e1, 0, 0, 2, f(Yes, false, "")},
	{0xc8fc, 0, 0, 1, f(Yes, true, "")},
	{0xc8fd, 0, 0, 2, f(Yes, false, "")},
	{0xc918, 0, 0, 1, f(Yes, true, "")},
	{0xc919, 0, 0, 2, f(Yes, false, "")},
	{0xc934, 0, 0, 1, f(Yes, true, "")},
	{0xc935, 0, 0, 2, f(Yes, false, "")},
	{0xc950, 0, 0, 1, f(Yes, true, "")},
	{0xc951, 0, 0, 2, f(Yes, false, "")},
	{0xc96c, 0, 0, 1, f(Yes, true, "")},
	{0xc96d, 0, 0, 2, f(Yes, false, "")},
	{0xc988, 0, 0, 1, f(Yes, true, "")},
	{0xc989, 0, 0, 2, f(Yes, false, "")},
	{0xc9a4, 0, 0, 1, f(Yes, true, "")},
	{0xc9a5, 0, 0, 2, f(Yes, false, "")},
	{0xc9c0, 0, 0, 1, f(Yes, true, "")},
	{0xc9c1, 0, 0, 2, f(Yes, false, "")},
	{0xc9dc, 0, 0, 1, f(Yes, true, "")},
	{0xc9dd, 0, 0, 2, f(Yes, false, "")},
	{0xc9f8, 0, 0, 1, f(Yes, true, "")},
	{0xc9f9, 0, 0, 2, f(Yes, false, "")},
	{0xca14, 0, 0, 1, f(Yes, true, "")},
	{0xca15, 0, 0, 2, f(Yes, false, "")},
	{0xca30, 0, 0, 1, f(Yes, true, "")},
	{0xca31, 0, 0, 2, f(Yes, false, "")},
	{0xca4c, 0, 0, 1, f(Yes, true, "")},
	{0xca4d, 0, 0, 2, f(Yes, false, "")},
	{0xca68, 0, 0, 1, f(Yes, true, "")},
	{0xca69, 0, 0, 2, f(Yes, false, "")},
	{0xca84, 0, 0, 1, f(Yes, true, "")},
	{0xca85, 0, 0, 2, f(Yes, false, "")},
	{0xcaa0, 0, 0, 1, f(Yes, true, "")},
	{0xcaa1, 0, 0, 2, f(Yes, false, "")},
	{0xcabc, 0, 0, 1, f(Yes, true, "")},
	{0xcabd, 0, 0, 2, f(Yes, false, "")},
	{0xcad8, 0, 0, 1, f(Yes, true, "")},
	{0xcad9, 0, 0, 2, f(Yes, false, "")},
	{0xcaf4, 0, 0, 1, f(Yes, true, "")},
	{0xcaf5, 0, 0, 2, f(Yes, false, "")},
	{0xcb10, 0, 0, 1, f(Yes, true, "")},
	{0xcb11, 0, 0, 2, f(Yes, false, "")},
	{0xcb2c, 0, 0, 1, f(Yes, true, "")},
	{0xcb2d, 0, 0, 2, f(Yes, false, "")},
	{0xcb48, 0, 0, 1, f(Yes, true, "")},
	{0xcb49, 0, 0, 2, f(Yes, false, "")},
	{0xcb64, 0, 0, 1, f(Yes, true, "")},
	{0xcb65, 0, 0, 2, f(Yes, false, "")},
	{0xcb80, 0, 0, 1, f(Yes, true, "")},
	{0xcb81, 0, 0, 2, f(Yes, false, "")},
	{0xcb9c, 0, 0, 1, f(Yes, true, "")},
	{0xcb9d, 0, 0, 2, f(Yes, false, "")},
	{0xcbb8, 0, 0, 1, f(Yes, true, "")},
	{0xcbb9, 0, 0, 2, f(Yes, false, "")},
	{0xcbd4, 0, 0, 1, f(Yes, true, "")},
	{0xcbd5, 0, 0, 2, f(Yes, false, "")},
	{0xcbf0, 0, 0, 1, f(Yes, true, "")},
	{0xcbf1, 0, 0, 2, f(Yes, false, "")},
	{0xcc0c, 0, 0, 1, f(Yes, true, "")},
	{0xcc0d, 0, 0, 2, f(Yes, false, "")},
	{0xcc28, 0, 0, 1, f(Yes, true, "")},
	{0xcc29, 0, 0, 2, f(Yes, false, "")},
	{0xcc44, 0, 0, 1, f(Yes, true, "")},
	{0xcc45, 0, 0, 2, f(Yes, false, "")},
	{0xcc60, 0, 0, 1, f(Yes, true, "")},
	{0xcc61, 0, 0, 2, f(Yes, false, "")},
	{0xcc7c, 0, 0, 1, f(Yes, true, "")},
	{0xcc7d, 0, 0, 2, f(Yes, false, "")},
	{0xcc98, 0, 0, 1, f(Yes, true, "")},
	{0xcc99, 0, 0, 2, f(Yes, false, "")},
	{0xccb4, 0, 0, 1, f(Yes, true, "")},
	{0xccb5, 0, 0, 2, f(Yes, false, "")},
	{0xccd0, 0, 0, 1, f(Yes, true, "")},
	{0xccd1, 0, 0, 2, f(Yes, false, "")},
	{0xccec, 0, 0, 1, f(Yes, true, "")},
	{0xcced, 0, 0, 2, f(Yes, false, "")},
	{0xcd08, 0, 0, 1, f(Yes, true, "")},
	{0xcd09, 0, 0, 2, f(Yes, false, "")},
	{0xcd24, 0, 0, 1, f(Yes, true, "")},
	{0xcd25, 0, 0, 2, f(Yes, false, "")},
	{0xcd40, 0, 0, 1, f(Yes, true, "")},
	{0xcd41, 0, 0, 2, f(Yes, false, "")},
	{0xcd5c, 0, 0, 1, f(Yes, true, "")},
	{0xcd5d, 0, 0, 2, f(Yes, false, "")},
	{0xcd78, 0, 0, 1, f(Yes, true, "")},
	{0xcd79, 0, 0, 2, f(Yes, false, "")},
	{0xcd94, 0, 0, 1, f(Yes, true, "")},
	{0xcd95, 0, 0, 2, f(Yes, false, "")},
	{0xcdb0, 0, 0, 1, f(Yes, true, "")},
	{0xcdb1, 0, 0, 2, f(Yes, false, "")},
	{0xcdcc, 0, 0, 1, f(Yes, true, "")},
	{0xcdcd, 0, 0, 2, f(Yes, false, "")},
	{0xcde8, 0, 0, 1, f(Yes, true, "")},
	{0xcde9, 0, 0, 2, f(Yes, false, "")},
	{0xce04, 0, 0, 1, f(Yes, true, "")},
	{0xce05, 0, 0, 2, f(Yes, false, "")},
	{0xce20, 0, 0, 1, f(Yes, true, "")},
	{0xce21, 0, 0, 2, f(Yes, false, "")},
	{0xce3c, 0, 0, 1, f(Yes, true, "")},
	{0xce3d, 0, 0, 2, f(Yes, false, "")},
	{0xce58, 0, 0, 1, f(Yes, true, "")},
	{0xce59, 0, 0, 2, f(Yes, false, "")},
	{0xce74, 0, 0, 1, f(Yes, true, "")},
	{0xce75, 0, 0, 2, f(Yes, false, "")},
	{0xce90, 0, 0, 1, f(Yes, true, "")},
	{0xce91, 0, 0, 2, f(Yes, false, "")},
	{0xceac, 0, 0, 1, f(Yes, true, "")},
	{0xcead, 0, 0, 2, f(Yes, false, "")},
	{0xcec8, 0, 0, 1, f(Yes, true, "")},
	{0xcec9, 0, 0, 2, f(Yes, false, "")},
	{0xcee4, 0, 0, 1, f(Yes, true, "")},
	{0xcee5, 0, 0, 2, f(Yes, false, "")},
	{0xcf00, 0, 0, 1, f(Yes, true, "")},
	{0xcf01, 0, 0, 2, f(Yes, false, "")},
	{0xcf1c, 0, 0, 1, f(Yes, true, "")},
	{0xcf1d, 0, 0, 2, f(Yes, false, "")},
	{0xcf38, 0, 0, 1, f(Yes, true, "")},
	{0xcf39, 0, 0, 2, f(Yes, false, "")},
	{0xcf54, 0, 0, 1, f(Yes, true, "")},
	{0xcf55, 0, 0, 2, f(Yes, false, "")},
	{0xcf70, 0, 0, 1, f(Yes, true, "")},
	{0xcf71, 0, 0, 2, f(Yes, false, "")},
	{0xcf8c, 0, 0, 1, f(Yes, true, "")},
	{0xcf8d, 0, 0, 2, f(Yes, false, "")},
	{0xcfa8, 0, 0, 1, f(Yes, true, "")},
	{0xcfa9, 0, 0, 2, f(Yes, false, "")},
	{0xcfc4, 0, 0, 1, f(Yes, true, "")},
	{0xcfc5, 0, 0, 2, f(Yes, false, "")},
	{0xcfe0, 0, 0, 1, f(Yes, true, "")},
	{0xcfe1, 0, 0, 2, f(Yes, false, "")},
	{0xcffc, 0, 0, 1, f(Yes, true, "")},
	{0xcffd, 0, 0, 2, f(Yes, false, "")},
	{0xd018, 0, 0, 1, f(Yes, true, "")},
	{0xd019, 0, 0, 2, f(Yes, false, "")},
	{0xd034, 0, 0, 1, f(Yes, true, "")},
	{0xd035, 0, 0, 2, f(Yes, false, "")},
	{0xd050, 0, 0, 1, f(Yes, true, "")},
	{0xd051, 0, 0, 2, f(Yes, false, "")},
	{0xd06c, 0, 0, 1, f(Yes, true, "")},
	{0xd06d, 0, 0, 2, f(Yes, false, "")},
	{0xd088, 0, 0, 1, f(Yes, true, "")},
	{0xd089, 0, 0, 2, f(Yes, false, "")},
	{0xd0a4, 0, 0, 1, f(Yes, true, "")},
	{0xd0a5, 0, 0, 2, f(Yes, false, "")},
	{0xd0c0, 0, 0, 1, f(Yes, true, "")},
	{0xd0c1, 0, 0, 2, f(Yes, false, "")},
	{0xd0dc, 0, 0, 1, f(Yes, true, "")},
	{0xd0dd, 0, 0, 2, f(Yes, false, "")},
	{0xd0f8, 0, 0, 1, f(Yes, true, "")},
	{0xd0f9, 0, 0, 2, f(Yes, false, "")},
	{0xd114, 0, 0, 1, f(Yes, true, "")},
	{0xd115, 0, 0, 2, f(Yes, false, "")},
	{0xd130, 0, 0, 1, f(Yes, true, "")},
	{0xd131, 0, 0, 2, f(Yes, false, "")},
	{0xd14c, 0, 0, 1, f(Yes, true, "")},
	{0xd14d, 0, 0, 2, f(Yes, false, "")},
	{0xd168, 0, 0, 1, f(Yes, true, "")},
	{0xd169, 0, 0, 2, f(Yes, false, "")},
	{0xd184, 0, 0, 1, f(Yes, true, "")},
	{0xd185, 0, 0, 2, f(Yes, false, "")},
	{0xd1a0, 0, 0, 1, f(Yes, true, "")},
	{0xd1a1, 0, 0, 2, f(Yes, false, "")},
	{0xd1bc, 0, 0, 1, f(Yes, true, "")},
	{0xd1bd, 0, 0, 2, f(Yes, false, "")},
	{0xd1d8, 0, 0, 1, f(Yes, true, "")},
	{0xd1d9, 0, 0, 2, f(Yes, false, "")},
	{0xd1f4, 0, 0, 1, f(Yes, true, "")},
	{0xd1f5, 0, 0, 2, f(Yes, false, "")},
	{0xd210, 0, 0, 1, f(Yes, true, "")},
	{0xd211, 0, 0, 2, f(Yes, false, "")},
	{0xd22c, 0, 0, 1, f(Yes, true, "")},
	{0xd22d, 0, 0, 2, f(Yes, false, "")},
	{0xd248, 0, 0, 1, f(Yes, true, "")},
	{0xd249, 0, 0, 2, f(Yes, false, "")},
	{0xd264, 0, 0, 1, f(Yes, true, "")},
	{0xd265, 0, 0, 2, f(Yes, false, "")},
	{0xd280, 0, 0, 1, f(Yes, true, "")},
	{0xd281, 0, 0, 2, f(Yes, false, "")},
	{0xd29c, 0, 0, 1, f(Yes, true, "")},
	{0xd29d, 0, 0, 2, f(Yes, false, "")},
	{0xd2b8, 0, 0, 1, f(Yes, true, "")},
	{0xd2b9, 0, 0, 2, f(Yes, false, "")},
	{0xd2d4, 0, 0, 1, f(Yes, true, "")},
	{0xd2d5, 0, 0, 2, f(Yes, false, "")},
	{0xd2f0, 0, 0, 1, f(Yes, true, "")},
	{0xd2f1, 0, 0, 2, f(Yes, false, "")},
	{0xd30c, 0, 0, 1, f(Yes, true, "")},
	{0xd30d, 0, 0, 2, f(Yes, false, "")},
	{0xd328, 0, 0, 1, f(Yes, true, "")},
	{0xd329, 0, 0, 2, f(Yes, false, "")},
	{0xd344, 0, 0, 1, f(Yes, true, "")},
	{0xd345, 0, 0, 2, f(Yes, false, "")},
	{0xd360, 0, 0, 1, f(Yes, true, "")},
	{0xd361, 0, 0, 2, f(Yes, false, "")},
	{0xd37c, 0, 0, 1, f(Yes, true, "")},
	{0xd37d, 0, 0, 2, f(Yes, false, "")},
	{0xd398, 0, 0, 1, f(Yes, true, "")},
	{0xd399, 0, 0, 2, f(Yes, false, "")},
	{0xd3b4, 0, 0, 1, f(Yes, true, "")},
	{0xd3b5, 0, 0, 2, f(Yes, false, "")},
	{0xd3d0, 0, 0, 1, f(Yes, true, "")},
	{0xd3d1, 0, 0, 2, f(Yes, false, "")},
	{0xd3ec, 0, 0, 1, f(Yes, true, "")},
	{0xd3ed, 0, 0, 2, f(Yes, false, "")},
	{0xd408, 0, 0, 1, f(Yes, true, "")},
	{0xd409, 0, 0, 2, f(Yes, false, "")},
	{0xd424, 0, 0, 1, f(Yes, true, "")},
	{0xd425, 0, 0, 2, f(Yes, false, "")},
	{0xd440, 0, 0, 1, f(Yes, true, "")},
	{0xd441, 0, 0, 2, f(Yes, false, "")},
	{0xd45c, 0, 0, 1, f(Yes, true, "")},
	{0xd45d, 0, 0, 2, f(Yes, false, "")},
	{0xd478, 0, 0, 1, f(Yes, true, "")},
	{0xd479, 0, 0, 2, f(Yes, false, "")},
	{0xd494, 0, 0, 1, f(Yes, true, "")},
	{0xd495, 0, 0, 2, f(Yes, false, "")},
	{0xd4b0, 0, 0, 1, f(Yes, true, "")},
	{0xd4b1, 0, 0, 2, f(Yes, false, "")},
	{0xd4cc, 0, 0, 1, f(Yes, true, "")},
	{0xd4cd, 0, 0, 2, f(Yes, false, "")},
	{0xd4e8, 0, 0, 1, f(Yes, true, "")},
	{0xd4e9, 0, 0, 2, f(Yes, false, "")},
	{0xd504, 0, 0, 1, f(Yes, true, "")},
	{0xd505, 0, 0, 2, f(Yes, false, "")},
	{0xd520, 0, 0, 1, f(Yes, true, "")},
	{0xd521, 0, 0, 2, f(Yes, false, "")},
	{0xd53c, 0, 0, 1, f(Yes, true, "")},
	{0xd53d, 0, 0, 2, f(Yes, false, "")},
	{0xd558, 0, 0, 1, f(Yes, true, "")},
	{0xd559, 0, 0, 2, f(Yes, false, "")},
	{0xd574, 0, 0, 1, f(Yes, true, "")},
	{0xd575, 0, 0, 2, f(Yes, false, "")},
	{0xd590, 0, 0, 1, f(Yes, true, "")},
	{0xd591, 0, 0, 2, f(Yes, false, "")},
	{0xd5ac, 0, 0, 1, f(Yes, true, "")},
	{0xd5ad, 0, 0, 2, f(Yes, false, "")},
	{0xd5c8, 0, 0, 1, f(Yes, true, "")},
	{0xd5c9, 0, 0, 2, f(Yes, false, "")},
	{0xd5e4, 0, 0, 1, f(Yes, true, "")},
	{0xd5e5, 0, 0, 2, f(Yes, false, "")},
	{0xd600, 0, 0, 1, f(Yes, true, "")},
	{0xd601, 0, 0, 2, f(Yes, false, "")},
	{0xd61c, 0, 0, 1, f(Yes, true, "")},
	{0xd61d, 0, 0, 2, f(Yes, false, "")},
	{0xd638, 0, 0, 1, f(Yes, true, "")},
	{0xd639, 0, 0, 2, f(Yes, false, "")},
	{0xd654, 0, 0, 1, f(Yes, true, "")},
	{0xd655, 0, 0, 2, f(Yes, false, "")},
	{0xd670, 0, 0, 1, f(Yes, true, "")},
	{0xd671, 0, 0, 2, f(Yes, false, "")},
	{0xd68c, 0, 0, 1, f(Yes, true, "")},
	{0xd68d, 0, 0, 2, f(Yes, false, "")},
	{0xd6a8, 0, 0, 1, f(Yes, true, "")},
	{0xd6a9, 0, 0, 2, f(Yes, false, "")},
	{0xd6c4, 0, 0, 1, f(Yes, true, "")},
	{0xd6c5, 0, 0, 2, f(Yes, false, "")},
	{0xd6e0, 0, 0, 1, f(Yes, true, "")},
	{0xd6e1, 0, 0, 2, f(Yes, false, "")},
	{0xd6fc, 0, 0, 1, f(Yes, true, "")},
	{0xd6fd, 0, 0, 2, f(Yes, false, "")},
	{0xd718, 0, 0, 1, f(Yes, true, "")},
	{0xd719, 0, 0, 2, f(Yes, false, "")},
	{0xd734, 0, 0, 1, f(Yes, true, "")},
	{0xd735, 0, 0, 2, f(Yes, false, "")},
	{0xd750, 0, 0, 1, f(Yes, true, "")},
	{0xd751, 0, 0, 2, f(Yes, false, "")},
	{0xd76c, 0, 0, 1, f(Yes, true, "")},
	{0xd76d, 0, 0, 2, f(Yes, false, "")},
	{0xd788, 0, 0, 1, f(Yes, true, "")},
	{0xd789, 0, 0, 2, f(Yes, false, "")},
	{0xd7a4, 0, 0, 0, f(Yes, false, "")},
	{0xf900, 0, 0, 0, f(No, false, "豈")},
	{0xf901, 0, 0, 0, f(No, false, "更")},
	{0xf902, 0, 0, 0, f(No, false, "車")},
	{0xf903, 0, 0, 0, f(No, false, "賈")},
	{0xf904, 0, 0, 0, f(No, false, "滑")},
	{0xf905, 0, 0, 0, f(No, false, "串")},
	{0xf906, 0, 0, 0, f(No, false, "句")},
	{0xf907, 0, 0, 0, f(No, false, "龜")},
	{0xf909, 0, 0, 0, f(No, false, "契")},
	{0xf90a, 0, 0, 0, f(No, false, "金")},
	{0xf90b, 0, 0, 0, f(No, false, "喇")},
	{0xf90c, 0, 0, 0, f(No, false, "奈")},
	{0xf90d, 0, 0, 0, f(No, false, "懶")},
	{0xf90e, 0, 0, 0, f(No, false, "癩")},
	{0xf90f, 0, 0, 0, f(No, false, "羅")},
	{0xf910, 0, 0, 0, f(No, false, "蘿")},
	{0xf911, 0, 0, 0, f(No, false, "螺")},
	{0xf912, 0, 0, 0, f(No, false, "裸")},
	{0xf913, 0, 0, 0, f(No, false, "邏")},
	{0xf914, 0, 0, 0, f(No, false, "樂")},
	{0xf915, 0, 0, 0, f(No, false, "洛")},
	{0xf916, 0, 0, 0, f(No, false, "烙")},
	{0xf917, 0, 0, 0, f(No, false, "珞")},
	{0xf918, 0, 0, 0, f(No, false, "落")},
	{0xf919, 0, 0, 0, f(No, false, "酪")},
	{0xf91a, 0, 0, 0, f(No, false, "駱")},
	{0xf91b, 0, 0, 0, f(No, false, "亂")},
	{0xf91c, 0, 0, 0, f(No, false, "卵")},
	{0xf91d, 0, 0, 0, f(No, false, "欄")},
	{0xf91e, 0, 0, 0, f(No, false, "爛")},
	{0xf91f, 0, 0, 0, f(No, false, "蘭")},
	{0xf920, 0, 0, 0, f(No, false, "鸞")},
	{0xf921, 0, 0, 0, f(No, false, "嵐")},
	{0xf922, 0, 0, 0, f(No, false, "濫")},
	{0xf923, 0, 0, 0, f(No, false, "藍")},
	{0xf924, 0, 0, 0, f(No, false, "襤")},
	{0xf925, 0, 0, 0, f(No, false, "拉")},
	{0xf926, 0, 0, 0, f(No, false, "臘")},
	{0xf927, 0, 0, 0, f(No, false, "蠟")},
	{0xf928, 0, 0, 0, f(No, false, "廊")},
	{0xf929, 0, 0, 0, f(No, false, "朗")},
	{0xf92a, 0, 0, 0, f(No, false, "浪")},
	{0xf92b, 0, 0, 0, f(No, false, "狼")},
	{0xf92c, 0, 0, 0, f(No, false, "郎")},
	{0xf92d, 0, 0, 0, f(No, false, "來")},
	{0xf92e, 0, 0, 0, f(No, false, "冷")},
	{0xf92f, 0, 0, 0, f(No, false, "勞")},
	{0xf930, 0, 0, 0, f(No, false, "擄")},
	{0xf931, 0, 0, 0, f(No, false, "櫓")},
	{0xf932, 0, 0, 0, f(No, false, "爐")},
	{0xf933, 0, 0, 0, f(No, false, "盧")},
	{0xf934, 0, 0, 0, f(No, false, "老")},
	{0xf935, 0, 0, 0, f(No, false, "蘆")},
	{0xf936, 0, 0, 0, f(No, false, "虜")},
	{0xf937, 0, 0, 0, f(No, false, "路")},
	{0xf938, 0, 0, 0, f(No, false, "露")},
	{0xf939, 0, 0, 0, f(No, false, "魯")},
	{0xf93a, 0, 0, 0, f(No, false, "鷺")},
	{0xf93b, 0, 0, 0, f(No, false, "碌")},
	{0xf93c, 0, 0, 0, f(No, false, "祿")},
	{0xf93d, 0, 0, 0, f(No, false, "綠")},
	{0xf93e, 0, 0, 0, f(No, false, "菉")},
	{0xf93f, 0, 0, 0, f(No, false, "錄")},
	{0xf940, 0, 0, 0, f(No, false, "鹿")},
	{0xf941, 0, 0, 0, f(No, false, "論")},
	{0xf942, 0, 0, 0, f(No, false, "壟")},
	{0xf943, 0, 0, 0, f(No, false, "弄")},
	{0xf944, 0, 0, 0, f(No, false, "籠")},
	{0xf945, 0, 0, 0, f(No, false, "聾")},
	{0xf946, 0, 0, 0, f(No, false, "牢")},
	{0xf947, 0, 0, 0, f(No, false, "磊")},
	{0xf948, 0, 0, 0, f(No, false, "賂")},
	{0xf949, 0, 0, 0, f(No, false, "雷")},
	{0xf94a, 0, 0, 0, f(No, false, "壘")},
	{0xf94b, 0, 0, 0, f(No, false, "屢")},
	{0xf94c, 0, 0, 0, f(No, false, "樓")},
	{0xf94d, 0, 0, 0, f(No, false, "淚")},
	{0xf94e, 0, 0, 0, f(No, false, "漏")},
	{0xf94f, 0, 0, 0, f(No, false, "累")},
	{0xf950, 0, 0, 0, f(No, false, "縷")},
	{0xf951, 0, 0, 0, f(No, false, "陋")},
	{0xf952, 0, 0, 0, f(No, false, "勒")},
	{0xf953, 0, 0, 0, f(No, false, "肋")},
	{0xf954, 0, 0, 0, f(No, false, "凜")},
	{0xf955, 0, 0, 0, f(No, false, "凌")},
	{0xf956, 0, 0, 0, f(No, false, "稜")},
	{0xf957, 0, 0, 0, f(No, false, "綾")},
	{0xf958, 0, 0, 0, f(No, false, "菱")},
	{0xf959, 0, 0, 0, f(No, false, "陵")},
	{0xf95a, 0, 0, 0, f(No, false, "讀")},
	{0xf95b, 0, 0, 0, f(No, false, "拏")},
	{0xf95c, 0, 0, 0, f(No, false, "樂")},
	{0xf95d, 0, 0, 0, f(No, false, "諾")},
	{0xf95e, 0, 0, 0, f(No, false, "丹")},
	{0xf95f, 0, 0, 0, f(No, false, "寧")},
	{0xf960, 0, 0, 0, f(No, false, "怒")},
	{0xf961, 0, 0, 0, f(No, false, "率")},
	{0xf962, 0, 0, 0, f(No, false, "異")},
	{0xf963, 0, 0, 0, f(No, false, "北")},
	{0xf964, 0, 0, 0, f(No, false, "磻")},
	{0xf965, 0, 0, 0, f(No, false, "便")},
	{0xf966, 0, 0, 0, f(No, false, "復")},
	{0xf967, 0, 0, 0, f(No, false, "不")},
	{0xf968, 0, 0, 0, f(No, false, "泌")},
	{0xf969, 0, 0, 0, f(No, false, "數")},
	{0xf96a, 0, 0, 0, f(No, false, "索")},
	{0xf96b, 0, 0, 0, f(No, false, "參")},
	{0xf96c, 0, 0, 0, f(No, false, "塞")},
	{0xf96d, 0, 0, 0, f(No, false, "省")},
	{0xf96e, 0, 0, 0, f(No, false, "葉")},
	{0xf96f, 0, 0, 0, f(No, false, "說")},
	{0xf970, 0, 0, 0, f(No, false, "殺")},
	{0xf971, 0, 0, 0, f(No, false, "辰")},
	{0xf972, 0, 0, 0, f(No, false, "沈")},
	{0xf973, 0, 0, 0, f(No, false, "拾")},
	{0xf974, 0, 0, 0, f(No, false, "若")},
	{0xf975, 0, 0, 0, f(No, false, "掠")},
	{0xf976, 0, 0, 0, f(No, false, "略")},
	{0xf977, 0, 0, 0, f(No, false, "亮")},
	{0xf978, 0, 0, 0, f(No, false, "兩")},
	{0xf979, 0, 0, 0, f(No, false, "凉")},
	{0xf97a, 0, 0, 0, f(No, false, "梁")},
	{0xf97b, 0, 0, 0, f(No, false, "糧")},
	{0xf97c, 0, 0, 0, f(No, false, "良")},
	{0xf97d, 0, 0, 0, f(No, false, "諒")},
	{0xf97e, 0, 0, 0, f(No, false, "量")},
	{0xf97f, 0, 0, 0, f(No, false, "勵")},
	{0xf980, 0, 0, 0, f(No, false, "呂")},
	{0xf981, 0, 0, 0, f(No, false, "女")},
	{0xf982, 0, 0, 0, f(No, false, "廬")},
	{0xf983, 0, 0, 0, f(No, false, "旅")},
	{0xf984, 0, 0, 0, f(No, false, "濾")},
	{0xf985, 0, 0, 0, f(No, false, "礪")},
	{0xf986, 0, 0, 0, f(No, false, "閭")},
	{0xf987, 0, 0, 0, f(No, false, "驪")},
	{0xf988, 0, 0, 0, f(No, false, "麗")},
	{0xf989, 0, 0, 0, f(No, false, "黎")},
	{0xf98a, 0, 0, 0, f(No, false, "力")},
	{0xf98b, 0, 0, 0, f(No, false, "曆")},
	{0xf98c, 0, 0, 0, f(No, false, "歷")},
	{0xf98d, 0, 0, 0, f(No, false, "轢")},
	{0xf98e, 0, 0, 0, f(No, false, "年")},
	{0xf98f, 0, 0, 0, f(No, false, "憐")},
	{0xf990, 0, 0, 0, f(No, false, "戀")},
	{0xf991, 0, 0, 0, f(No, false, "撚")},
	{0xf992, 0, 0, 0, f(No, false, "漣")},
	{0xf993, 0, 0, 0, f(No, false, "煉")},
	{0xf994, 0, 0, 0, f(No, false, "璉")},
	{0xf995, 0, 0, 0, f(No, false, "秊")},
	{0xf996, 0, 0, 0, f(No, false, "練")},
	{0xf997, 0, 0, 0, f(No, false, "聯")},
	{0xf998, 0, 0, 0, f(No, false, "輦")},
	{0xf999, 0, 0, 0, f(No, false, "蓮")},
	{0xf99a, 0, 0, 0, f(No, false, "連")},
	{0xf99b, 0, 0, 0, f(No, false, "鍊")},
	{0xf99c, 0, 0, 0, f(No, false, "列")},
	{0xf99d, 0, 0, 0, f(No, false, "劣")},
	{0xf99e, 0, 0, 0, f(No, false, "咽")},
	{0xf99f, 0, 0, 0, f(No, false, "烈")},
	{0xf9a0, 0, 0, 0, f(No, false, "裂")},
	{0xf9a1, 0, 0, 0, f(No, false, "說")},
	{0xf9a2, 0, 0, 0, f(No, false, "廉")},
	{0xf9a3, 0, 0, 0, f(No, false, "念")},
	{0xf9a4, 0, 0, 0, f(No, false, "捻")},
	{0xf9a5, 0, 0, 0, f(No, false, "殮")},
	{0xf9a6, 0, 0, 0, f(No, false, "簾")},
	{0xf9a7, 0, 0, 0, f(No, false, "獵")},
	{0xf9a8, 0, 0, 0, f(No, false, "令")},
	{0xf9a9, 0, 0, 0, f(No, false, "囹")},
	{0xf9aa, 0, 0, 0, f(No, false, "寧")},
	{0xf9ab, 0, 0, 0, f(No, false, "嶺")},
	{0xf9ac, 0, 0, 0, f(No, false, "怜")},
	{0xf9ad, 0, 0, 0, f(No, false, "玲")},
	{0xf9ae, 0, 0, 0, f(No, false, "瑩")},
	{0xf9af, 0, 0, 0, f(No, false, "羚")},
	{0xf9b0, 0, 0, 0, f(No, false, "聆")},
	{0xf9b1, 0, 0, 0, f(No, false, "鈴")},
	{0xf9b2, 0, 0, 0, f(No, false, "零")},
	{0xf9b3, 0, 0, 0, f(No, false, "靈")},
	{0xf9b4, 0, 0, 0, f(No, false, "領")},
	{0xf9b5, 0, 0, 0, f(No, false, "例")},
	{0xf9b6, 0, 0, 0, f(No, false, "禮")},
	{0xf9b7, 0, 0, 0, f(No, false, "醴")},
	{0xf9b8, 0, 0, 0, f(No, false, "隸")},
	{0xf9b9, 0, 0, 0, f(No, false, "惡")},
	{0xf9ba, 0, 0, 0, f(No, false, "了")},
	{0xf9bb, 0, 0, 0, f(No, false, "僚")},
	{0xf9bc, 0, 0, 0, f(No, false, "寮")},
	{0xf9bd, 0, 0, 0, f(No, false, "尿")},
	{0xf9be, 0, 0, 0, f(No, false, "料")},
	{0xf9bf, 0, 0, 0, f(No, false, "樂")},
	{0xf9c0, 0, 0, 0, f(No, false, "燎")},
	{0xf9c1, 0, 0, 0, f(No, false, "療")},
	{0xf9c2, 0, 0, 0, f(No, false, "蓼")},
	{0xf9c3, 0, 0, 0, f(No, false, "遼")},
	{0xf9c4, 0, 0, 0, f(No, false, "龍")},
	{0xf9c5, 0, 0, 0, f(No, false, "暈")},
	{0xf9c6, 0, 0, 0, f(No, false, "阮")},
	{0xf9c7, 0, 0, 0, f(No, false, "劉")},
	{0xf9c8, 0, 0, 0, f(No, false, "杻")},
	{0xf9c9, 0, 0, 0, f(No, false, "柳")},
	{0xf9ca, 0, 0, 0, f(No, false, "流")},
	{0xf9cb, 0, 0, 0, f(No, false, "溜")},
	{0xf9cc, 0, 0, 0, f(No, false, "琉")},
	{0xf9cd, 0, 0, 0, f(No, false, "留")},
	{0xf9ce, 0, 0, 0, f(No, false, "硫")},
	{0xf9cf, 0, 0, 0, f(No, false, "紐")},
	{0xf9d0, 0, 0, 0, f(No, false, "類")},
	{0xf9d1, 0, 0, 0, f(No, false, "六")},
	{0xf9d2, 0, 0, 0, f(No, false, "戮")},
	{0xf9d3, 0, 0, 0, f(No, false, "陸")},
	{0xf9d4, 0, 0, 0, f(No, false, "倫")},
	{0xf9d5, 0, 0, 0, f(No, false, "崙")},
	{0xf9d6, 0, 0, 0, f(No, false, "淪")},
	{0xf9d7, 0, 0, 0, f(No, false, "輪")},
	{0xf9d8, 0, 0, 0, f(No, false, "律")},
	{0xf9d9, 0, 0, 0, f(No, false, "慄")},
	{0xf9da, 0, 0, 0, f(No, false, "栗")},
	{0xf9db, 0, 0, 0, f(No, false, "率")},
	{0xf9dc, 0, 0, 0, f(No, false, "隆")},
	{0xf9dd, 0, 0, 0, f(No, false, "利")},
	{0xf9de, 0, 0, 0, f(No, false, "吏")},
	{0xf9df, 0, 0, 0, f(No, false, "履")},
	{0xf9e0, 0, 0, 0, f(No, false, "易")},
	{0xf9e1, 0, 0, 0, f(No, false, "李")},
	{0xf9e2, 0, 0, 0, f(No, false, "梨")},
	{0xf9e3, 0, 0, 0, f(No, false, "泥")},
	{0xf9e4, 0, 0, 0, f(No, false, "理")},
	{0xf9e5, 0, 0, 0, f(No, false, "痢")},
	{0xf9e6, 0, 0, 0, f(No, false, "罹")},
	{0xf9e7, 0, 0, 0, f(No, false, "裏")},
	{0xf9e8, 0, 0, 0, f(No, false, "裡")},
	{0xf9e9, 0, 0, 0, f(No, false, "里")},
	{0xf9ea, 0, 0, 0, f(No, false, "離")},
	{0xf9eb, 0, 0, 0, f(No, false, "匿")},
	{0xf9ec, 0, 0, 0, f(No, false, "溺")},
	{0xf9ed, 0, 0, 0, f(No, false, "吝")},
	{0xf9ee, 0, 0, 0, f(No, false, "燐")},
	{0xf9ef, 0, 0, 0, f(No, false, "璘")},
	{0xf9f0, 0, 0, 0, f(No, false, "藺")},
	{0xf9f1, 0, 0, 0, f(No, false, "隣")},
	{0xf9f2, 0, 0, 0, f(No, false, "鱗")},
	{0xf9f3, 0, 0, 0, f(No, false, "麟")},
	{0xf9f4, 0, 0, 0, f(No, false, "林")},
	{0xf9f5, 0, 0, 0, f(No, false, "淋")},
	{0xf9f6, 0, 0, 0, f(No, false, "臨")},
	{0xf9f7, 0, 0, 0, f(No, false, "立")},
	{0xf9f8, 0, 0, 0, f(No, false, "笠")},
	{0xf9f9, 0, 0, 0, f(No, false, "粒")},
	{0xf9fa, 0, 0, 0, f(No, false, "狀")},
	{0xf9fb, 0, 0, 0, f(No, false, "炙")},
	{0xf9fc, 0, 0, 0, f(No, false, "識")},
	{0xf9fd, 0, 0, 0, f(No, false, "什")},
	{0xf9fe, 0, 0, 0, f(No, false, "茶")},
	{0xf9ff, 0, 0, 0, f(No, false, "刺")},
	{0xfa00, 0, 0, 0, f(No, false, "切")},
	{0xfa01, 0, 0, 0, f(No, false, "度")},
	{0xfa02, 0, 0, 0, f(No, false, "拓")},
	{0xfa03, 0, 0, 0, f(No, false, "糖")},
	{0xfa04, 0, 0, 0, f(No, false, "宅")},
	{0xfa05, 0, 0, 0, f(No, false, "洞")},
	{0xfa06, 0, 0, 0, f(No, false, "暴")},
	{0xfa07, 0, 0, 0, f(No, false, "輻")},
	{0xfa08, 0, 0, 0, f(No, false, "行")},
	{0xfa09, 0, 0, 0, f(No, false, "降")},
	{0xfa0a, 0, 0, 0, f(No, false, "見")},
	{0xfa0b, 0, 0, 0, f(No, false, "廓")},
	{0xfa0c, 0, 0, 0, f(No, false, "兀")},
	{0xfa0d, 0, 0, 0, f(No, false, "嗀")},
	{0xfa0e, 0, 0, 0, f(Yes, false, "")},
	{0xfa10, 0, 0, 0, f(No, false, "塚")},
	{0xfa11, 0, 0, 0, f(Yes, false, "")},
	{0xfa12, 0, 0, 0, f(No, false, "晴")},
	{0xfa13, 0, 0, 0, f(Yes, false, "")},
	{0xfa15, 0, 0, 0, f(No, false, "凞")},
	{0xfa16, 0, 0, 0, f(No, false, "猪")},
	{0xfa17, 0, 0, 0, f(No, false, "益")},
	{0xfa18, 0, 0, 0, f(No, false, "礼")},
	{0xfa19, 0, 0, 0, f(No, false, "神")},
	{0xfa1a, 0, 0, 0, f(No, false, "祥")},
	{0xfa1b, 0, 0, 0, f(No, false, "福")},
	{0xfa1c, 0, 0, 0, f(No, false, "靖")},
	{0xfa1d, 0, 0, 0, f(No, false, "精")},
	{0xfa1e, 0, 0, 0, f(No, false, "羽")},
	{0xfa1f, 0, 0, 0, f(Yes, false, "")},
	{0xfa20, 0, 0, 0, f(No, false, "蘒")},
	{0xfa21, 0, 0, 0, f(Yes, false, "")},
	{0xfa22, 0, 0, 0, f(No, false, "諸")},
	{0xfa23, 0, 0, 0, f(Yes, false, "")},
	{0xfa25, 0, 0, 0, f(No, false, "逸")},
	{0xfa26, 0, 0, 0, f(No, false, "都")},
	{0xfa27, 0, 0, 0, f(Yes, false, "")},
	{0xfa2a, 0, 0, 0, f(No, false, "飯")},
	{0xfa2b, 0, 0, 0, f(No, false, "飼")},
	{0xfa2c, 0, 0, 0, f(No, false, "館")},
	{0xfa2d, 0, 0, 0, f(No, false, "鶴")},
	{0xfa2e, 0, 0, 0, f(No, false, "郞")},
	{0xfa2f, 0, 0, 0, f(No, false, "隷")},
	{0xfa30, 0, 0, 0, f(No, false, "侮")},
	{0xfa31, 0, 0, 0, f(No, false, "僧")},
	{0xfa32, 0, 0, 0, f(No, false, "免")},
	{0xfa33, 0, 0, 0, f(No, false, "勉")},
	{0xfa34, 0, 0, 0, f(No, false, "勤")},
	{0xfa35, 0, 0, 0, f(No, false, "卑")},
	{0xfa36, 0, 0, 0, f(No, false, "喝")},
	{0xfa37, 0, 0, 0, f(No, false, "嘆")},
	{0xfa38, 0, 0, 0, f(No, false, "器")},
	{0xfa39, 0, 0, 0, f(No, false, "塀")},
	{0xfa3a, 0, 0, 0, f(No, false, "墨")},
	{0xfa3b, 0, 0, 0, f(No, false, "層")},
	{0xfa3c, 0, 0, 0, f(No, false, "屮")},
	{0xfa3d, 0, 0, 0, f(No, false, "悔")},
	{0xfa3e, 0, 0, 0, f(No, false, "慨")},
	{0xfa3f, 0, 0, 0, f(No, false, "憎")},
	{0xfa40, 0, 0, 0, f(No, false, "懲")},
	{0xfa41, 0, 0, 0, f(No, false, "敏")},
	{0xfa42, 0, 0, 0, f(No, false, "既")},
	{0xfa43, 0, 0, 0, f(No, false, "暑")},
	{0xfa44, 0, 0, 0, f(No, false, "梅")},
	{0xfa45, 0, 0, 0, f(No, false, "海")},
	{0xfa46, 0, 0, 0, f(No, false, "渚")},
	{0xfa47, 0, 0, 0, f(No, false, "漢")},
	{0xfa48, 0, 0, 0, f(No, false, "煮")},
	{0xfa49, 0, 0, 0, f(No, false, "爫")},
	{0xfa4a, 0, 0, 0, f(No, false, "琢")},
	{0xfa4b, 0, 0, 0, f(No, false, "碑")},
	{0xfa4c, 0, 0, 0, f(No, false, "社")},
	{0xfa4d, 0, 0, 0, f(No, false, "祉")},
	{0xfa4e, 0, 0, 0, f(No, false, "祈")},
	{0xfa4f, 0, 0, 0, f(No, false, "祐")},
	{0xfa50, 0, 0, 0, f(No, false, "祖")},
	{0xfa51, 0, 0, 0, f(No, false, "祝")},
	{0xfa52, 0, 0, 0, f(No, false, "禍")},
	{0xfa53, 0, 0, 0, f(No, false, "禎")},
	{0xfa54, 0, 0, 0, f(No, false, "穀")},
	{0xfa55, 0, 0, 0, f(No, false, "突")},
	{0xfa56, 0, 0, 0, f(No, false, "節")},
	{0xfa57, 0, 0, 0, f(No, false, "練")},
	{0xfa58, 0, 0, 0, f(No, false, "縉")},
	{0xfa59, 0, 0, 0, f(No, false, "繁")},
	{0xfa5a, 0, 0, 0, f(No, false, "署")},
	{0xfa5b, 0, 0, 0, f(No, false, "者")},
	{0xfa5c, 0, 0, 0, f(No, false, "臭")},
	{0xfa5d, 0, 0, 0, f(No, false, "艹")},
	{0xfa5f, 0, 0, 0, f(No, false, "著")},
	{0xfa60, 0, 0, 0, f(No, false, "褐")},
	{0xfa61, 0, 0, 0, f(No, false, "視")},
	{0xfa62, 0, 0, 0, f(No, false, "謁")},
	{0xfa63, 0, 0, 0, f(No, false, "謹")},
	{0xfa64, 0, 0, 0, f(No, false, "賓")},
	{0xfa65, 0, 0, 0, f(No, false, "贈")},
	{0xfa66, 0, 0, 0, f(No, false, "辶")},
	{0xfa67, 0, 0, 0, f(No, false, "逸")},
	{0xfa68, 0, 0, 0, f(No, false, "難")},
	{0xfa69, 0, 0, 0, f(No, false, "響")},
	{0xfa6a, 0, 0, 0, f(No, false, "頻")},
	{0xfa6b, 0, 0, 0, f(No, false, "恵")},
	{0xfa6c, 0, 0, 0, f(No, false, "𤋮")},
	{0xfa6d, 0, 0, 0, f(No, false, "舘")},
	{0xfa6e, 0, 0, 0, f(Yes, false, "")},
	{0xfa70, 0, 0, 0, f(No, false, "並")},
	{0xfa71, 0, 0, 0, f(No, false, "况")},
	{0xfa72, 0, 0, 0, f(No, false, "全")},
	{0xfa73, 0, 0, 0, f(No, false, "侀")},
	{0xfa74, 0, 0, 0, f(No, false, "充")},
	{0xfa75, 0, 0, 0, f(No, false, "冀")},
	{0xfa76, 0, 0, 0, f(No, false, "勇")},
	{0xfa77, 0, 0, 0, f(No, false, "勺")},
	{0xfa78, 0, 0, 0, f(No, false, "喝")},
	{0xfa79, 0, 0, 0, f(No, false, "啕")},
	{0xfa7a, 0, 0, 0, f(No, false, "喙")},
	{0xfa7b, 0, 0, 0, f(No, false, "嗢")},
	{0xfa7c, 0, 0, 0, f(No, false, "塚")},
	{0xfa7d, 0, 0, 0, f(No, false, "墳")},
	{0xfa7e, 0, 0, 0, f(No, false, "奄")},
	{0xfa7f, 0, 0, 0, f(No, false, "奔")},
	{0xfa80, 0, 0, 0, f(No, false, "婢")},
	{0xfa81, 0, 0, 0, f(No, false, "嬨")},
	{0xfa82, 0, 0, 0, f(No, false, "廒")},
	{0xfa83, 0, 0, 0, f(No, false, "廙")},
	{0xfa84, 0, 0, 0, f(No, false, "彩")},
	{0xfa85, 0, 0, 0, f(No, false, "徭")},
	{0xfa86, 0, 0, 0, f(No, false, "惘")},
	{0xfa87, 0, 0, 0, f(No, false, "慎")},
	{0xfa88, 0, 0, 0, f(No, false, "愈")},
	{0xfa89, 0, 0, 0, f(No, false, "憎")},
	{0xfa8a, 0, 0, 0, f(No, false, "慠")},
	{0xfa8b, 0, 0, 0, f(No, false, "懲")},
	{0xfa8c, 0, 0, 0, f(No, false, "戴")},
	{0xfa8d, 0, 0, 0, f(No, false, "揄")},
	{0xfa8e, 0, 0, 0, f(No, false, "搜")},
	{0xfa8f, 0, 0, 0, f(No, false, "摒")},
	{0xfa90, 0, 0, 0, f(No, false, "敖")},
	{0xfa91, 0, 0, 0, f(No, false, "晴")},
	{0xfa92, 0, 0, 0, f(No, false, "朗")},
	{0xfa93, 0, 0, 0, f(No, false, "望")},
	{0xfa94, 0, 0, 0, f(No, false, "杖")},
	{0xfa95, 0, 0, 0, f(No, false, "歹")},
	{0xfa96, 0, 0, 0, f(No, false, "殺")},
	{0xfa97, 0, 0, 0, f(No, false, "流")},
	{0xfa98, 0, 0, 0, f(No, false, "滛")},
	{0xfa99, 0, 0, 0, f(No, false, "滋")},
	{0xfa9a, 0, 0, 0, f(No, false, "漢")},
	{0xfa9b, 0, 0, 0, f(No, false, "瀞")},
	{0xfa9c, 0, 0, 0, f(No, false, "煮")},
	{0xfa9d, 0, 0, 0, f(No, false, "瞧")},
	{0xfa9e, 0, 0, 0, f(No, false, "爵")},
	{0xfa9f, 0, 0, 0, f(No, false, "犯")},
	{0xfaa0, 0, 0, 0, f(No, false, "猪")},
	{0xfaa1, 0, 0, 0, f(No, false, "瑱")},
	{0xfaa2, 0, 0, 0, f(No, false, "甆")},
	{0xfaa3, 0, 0, 0, f(No, false, "画")},
	{0xfaa4, 0, 0, 0, f(No, false, "瘝")},
	{0xfaa5, 0, 0, 0, f(No, false, "瘟")},
	{0xfaa6, 0, 0, 0, f(No, false, "益")},
	{0xfaa7, 0, 0, 0, f(No, false, "盛")},
	{0xfaa8, 0, 0, 0, f(No, false, "直")},
	{0xfaa9, 0, 0, 0, f(No, false, "睊")},
	{0xfaaa, 0, 0, 0, f(No, false, "着")},
	{0xfaab, 0, 0, 0, f(No, false, "磌")},
	{0xfaac, 0, 0, 0, f(No, false, "窱")},
	{0xfaad, 0, 0, 0, f(No, false, "節")},
	{0xfaae, 0, 0, 0, f(No, false, "类")},
	{0xfaaf, 0, 0, 0, f(No, false, "絛")},
	{0xfab0, 0, 0, 0, f(No, false, "練")},
	{0xfab1, 0, 0, 0, f(No, false, "缾")},
	{0xfab2, 0, 0, 0, f(No, false, "者")},
	{0xfab3, 0, 0, 0, f(No, false, "荒")},
	{0xfab4, 0, 0, 0, f(No, false, "華")},
	{0xfab5, 0, 0, 0, f(No, false, "蝹")},
	{0xfab6, 0, 0, 0, f(No, false, "襁")},
	{0xfab7, 0, 0, 0, f(No, false, "覆")},
	{0xfab8, 0, 0, 0, f(No, false, "視")},
	{0xfab9, 0, 0, 0, f(No, false, "調")},
	{0xfaba, 0, 0, 0, f(No, false, "諸")},
	{0xfabb, 0, 0, 0, f(No, false, "請")},
	{0xfabc, 0, 0, 0, f(No, false, "謁")},
	{0xfabd, 0, 0, 0, f(No, false, "諾")},
	{0xfabe, 0, 0, 0, f(No, false, "諭")},
	{0xfabf, 0, 0, 0, f(No, false, "謹")},
	{0xfac0, 0, 0, 0, f(No, false, "變")},
	{0xfac1, 0, 0, 0, f(No, false, "贈")},
	{0xfac2, 0, 0, 0, f(No, false, "輸")},
	{0xfac3, 0, 0, 0, f(No, false, "遲")},
	{0xfac4, 0, 0, 0, f(No, false, "醙")},
	{0xfac5, 0, 0, 0, f(No, false, "鉶")},
	{0xfac6, 0, 0, 0, f(No, false, "陼")},
	{0xfac7, 0, 0, 0, f(No, false, "難")},
	{0xfac8, 0, 0, 0, f(No, false, "靖")},
	{0xfac9, 0, 0, 0, f(No, false, "韛")},
	{0xfaca, 0, 0, 0, f(No, false, "響")},
	{0xfacb, 0, 0, 0, f(No, false, "頋")},
	{0xfacc, 0, 0, 0, f(No, false, "頻")},
	{0xfacd, 0, 0, 0, f(No, false, "鬒")},
	{0xface, 0, 0, 0, f(No, false, "龜")},
	{0xfacf, 0, 0, 0, f(No, false, "𢡊")},
	{0xfad0, 0, 0, 0, f(No, false, "𢡄")},
	{0xfad1, 0, 0, 0, f(No, false, "𣏕")},
	{0xfad2, 0, 0, 0, f(No, false, "㮝")},
	{0xfad3, 0, 0, 0, f(No, false, "䀘")},
	{0xfad4, 0, 0, 0, f(No, false, "䀹")},
	{0xfad5, 0, 0, 0, f(No, false, "𥉉")},
	{0xfad6, 0, 0, 0, f(No, false, "𥳐")},
	{0xfad7, 0, 0, 0, f(No, false, "𧻓")},
	{0xfad8, 0, 0, 0, f(No, false, "齃")},
	{0xfad9, 0, 0, 0, f(No, false, "龎")},
	{0xfada, 0, 0, 0, f(Yes, false, "")},
	{0xfb00, 0, 0, 0, g(Yes, No, false, false, "", "ff")},
	{0xfb01, 0, 0, 0, g(Yes, No, false, false, "", "fi")},
	{0xfb02, 0, 0, 0, g(Yes, No, false, false, "", "fl")},
	{0xfb03, 0, 0, 0, g(Yes, No, false, false, "", "ffi")},
	{0xfb04, 0, 0, 0, g(Yes, No, false, false, "", "ffl")},
	{0xfb05, 0, 0, 0, g(Yes, No, false, false, "", "st")},
	{0xfb07, 0, 0, 0, f(Yes, false, "")},
	{0xfb13, 0, 0, 0, g(Yes, No, false, false, "", "մն")},
	{0xfb14, 0, 0, 0, g(Yes, No, false, false, "", "մե")},
	{0xfb15, 0, 0, 0, g(Yes, No, false, false, "", "մի")},
	{0xfb16, 0, 0, 0, g(Yes, No, false, false, "", "վն")},
	{0xfb17, 0, 0, 0, g(Yes, No, false, false, "", "մխ")},
	{0xfb18, 0, 0, 0, f(Yes, false, "")},
	{0xfb1d, 0, 0, 1, f(No, false, "יִ")},
	{0xfb1e, 26, 1, 1, f(Yes, false, "")},
	{0xfb1f, 0, 0, 1, f(No, false, "ײַ")},
	{0xfb20, 0, 0, 0, g(Yes, No, false, false, "", "ע")},
	{0xfb21, 0, 0, 0, g(Yes, No, false, false, "", "א")},
	{0xfb22, 0, 0, 0, g(Yes, No, false, false, "", "ד")},
	{0xfb23, 0, 0, 0, g(Yes, No, false, false, "", "ה")},
	{0xfb24, 0, 0, 0, g(Yes, No, false, false, "", "כ")},
	{0xfb25, 0, 0, 0, g(Yes, No, false, false, "", "ל")},
	{0xfb26, 0, 0, 0, g(Yes, No, false, false, "", "ם")},
	{0xfb27, 0, 0, 0, g(Yes, No, false, false, "", "ר")},
	{0xfb28, 0, 0, 0, g(Yes, No, false, false, "", "ת")},
	{0xfb29, 0, 0, 0, g(Yes, No, false, false, "", "+")},
	{0xfb2a, 0, 0, 1, f(No, false, "שׁ")},
	{0xfb2b, 0, 0, 1, f(No, false, "שׂ")},
	{0xfb2c, 0, 0, 2, f(No, false, "שּׁ")},
	{0xfb2d, 0, 0, 2, f(No, false, "שּׂ")},
	{0xfb2e, 0, 0, 1, f(No, false, "אַ")},
	{0xfb2f, 0, 0, 1, f(No, false, "אָ")},
	{0xfb30, 0, 0, 1, f(No, false, "אּ")},
	{0xfb31, 0, 0, 1, f(No, false, "בּ")},
	{0xfb32, 0, 0, 1, f(No, false, "גּ")},
	{0xfb33, 0, 0, 1, f(No, false, "דּ")},
	{0xfb34, 0, 0, 1, f(No, false, "הּ")},
	{0xfb35, 0, 0, 1, f(No, false, "וּ")},
	{0xfb36, 0, 0, 1, f(No, false, "זּ")},
	{0xfb37, 0, 0, 0, f(Yes, false, "")},
	{0xfb38, 0, 0, 1, f(No, false, "טּ")},
	{0xfb39, 0, 0, 1, f(No, false, "יּ")},
	{0xfb3a, 0, 0, 1, f(No, false, "ךּ")},
	{0xfb3b, 0, 0, 1, f(No, false, "כּ")},
	{0xfb3c, 0, 0, 1, f(No, false, "לּ")},
	{0xfb3d, 0, 0, 0, f(Yes, false, "")},
	{0xfb3e, 0, 0, 1, f(No, false, "מּ")},
	{0xfb3f, 0, 0, 0, f(Yes, false, "")},
	{0xfb40, 0, 0, 1, f(No, false, "נּ")},
	{0xfb41, 0, 0, 1, f(No, false, "סּ")},
	{0xfb42, 0, 0, 0, f(Yes, false, "")},
	{0xfb43, 0, 0, 1, f(No, false, "ףּ")},
	{0xfb44, 0, 0, 1, f(No, false, "פּ")},
	{0xfb45, 0, 0, 0, f(Yes, false, "")},
	{0xfb46, 0, 0, 1, f(No, false, "צּ")},
	{0xfb47, 0, 0, 1, f(No, false, "קּ")},
	{0xfb48, 0, 0, 1, f(No, false, "רּ")},
	{0xfb49, 0, 0, 1, f(No, false, "שּ")},
	{0xfb4a, 0, 0, 1, f(No, false, "תּ")},
	{0xfb4b, 0, 0, 1, f(No, false, "וֹ")},
	{0xfb4c, 0, 0, 1, f(No, false, "בֿ")},
	{0xfb4d, 0, 0, 1, f(No, false, "כֿ")},
	{0xfb4e, 0, 0, 1, f(No, false, "פֿ")},
	{0xfb4f, 0, 0, 0, g(Yes, No, false, false, "", "אל")},
	{0xfb50, 0, 0, 0, g(Yes, No, false, false, "", "ٱ")},
	{0xfb52, 0, 0, 0, g(Yes, No, false, false, "", "ٻ")},
	{0xfb56, 0, 0, 0, g(Yes, No, false, false, "", "پ")},
	{0xfb5a, 0, 0, 0, g(Yes, No, false, false, "", "ڀ")},
	{0xfb5e, 0, 0, 0, g(Yes, No, false, false, "", "ٺ")},
	{0xfb62, 0, 0, 0, g(Yes, No, false, false, "", "ٿ")},
	{0xfb66, 0, 0, 0, g(Yes, No, false, false, "", "ٹ")},
	{0xfb6a, 0, 0, 0, g(Yes, No, false, false, "", "ڤ")},
	{0xfb6e, 0, 0, 0, g(Yes, No, false, false, "", "ڦ")},
	{0xfb72, 0, 0, 0, g(Yes, No, false, false, "", "ڄ")},
	{0xfb76, 0, 0, 0, g(Yes, No, false, false, "", "ڃ")},
	{0xfb7a, 0, 0, 0, g(Yes, No, false, false, "", "چ")},
	{0xfb7e, 0, 0, 0, g(Yes, No, false, false, "", "ڇ")},
	{0xfb82, 0, 0, 0, g(Yes, No, false, false, "", "ڍ")},
	{0xfb84, 0, 0, 0, g(Yes, No, false, false, "", "ڌ")},
	{0xfb86, 0, 0, 0, g(Yes, No, false, false, "", "ڎ")},
	{0xfb88, 0, 0, 0, g(Yes, No, false, false, "", "ڈ")},
	{0xfb8a, 0, 0, 0, g(Yes, No, false, false, "", "ژ")},
	{0xfb8c, 0, 0, 0, g(Yes, No, false, false, "", "ڑ")},
	{0xfb8e, 0, 0, 0, g(Yes, No, false, false, "", "ک")},
	{0xfb92, 0, 0, 0, g(Yes, No, false, false, "", "گ")},
	{0xfb96, 0, 0, 0, g(Yes, No, false, false, "", "ڳ")},
	{0xfb9a, 0, 0, 0, g(Yes, No, false, false, "", "ڱ")},
	{0xfb9e, 0, 0, 0, g(Yes, No, false, false, "", "ں")},
	{0xfba0, 0, 0, 0, g(Yes, No, false, false, "", "ڻ")},
	{0xfba4, 0, 0, 1, g(Yes, No, false, false, "", "ۀ")},
	{0xfba6, 0, 0, 0, g(Yes, No, false, false, "", "ہ")},
	{0xfbaa, 0, 0, 0, g(Yes, No, false, false, "", "ھ")},
	{0xfbae, 0, 0, 0, g(Yes, No, false, false, "", "ے")},
	{0xfbb0, 0, 0, 1, g(Yes, No, false, false, "", "ۓ")},
	{0xfbb2, 0, 0, 0, f(Yes, false, "")},
	{0xfbd3, 0, 0, 0, g(Yes, No, false, false, "", "ڭ")},
	{0xfbd7, 0, 0, 0, g(Yes, No, false, false, "", "ۇ")},
	{0xfbd9, 0, 0, 0, g(Yes, No, false, false, "", "ۆ")},
	{0xfbdb, 0, 0, 0, g(Yes, No, false, false, "", "ۈ")},
	{0xfbdd, 0, 0, 0, g(Yes, No, false, false, "", "ۇٴ")},
	{0xfbde, 0, 0, 0, g(Yes, No, false, false, "", "ۋ")},
	{0xfbe0, 0, 0, 0, g(Yes, No, false, false, "", "ۅ")},
	{0xfbe2, 0, 0, 0, g(Yes, No, false, false, "", "ۉ")},
	{0xfbe4, 0, 0, 0, g(Yes, No, false, false, "", "ې")},
	{0xfbe8, 0, 0, 0, g(Yes, No, false, false, "", "ى")},
	{0xfbea, 0, 0, 0, g(Yes, No, false, false, "", "ئا")},
	{0xfbec, 0, 0, 0, g(Yes, No, false, false, "", "ئە")},
	{0xfbee, 0, 0, 0, g(Yes, No, false, false, "", "ئو")},
	{0xfbf0, 0, 0, 0, g(Yes, No, false, false, "", "ئۇ")},
	{0xfbf2, 0, 0, 0, g(Yes, No, false, false, "", "ئۆ")},
	{0xfbf4, 0, 0, 0, g(Yes, No, false, false, "", "ئۈ")},
	{0xfbf6, 0, 0, 0, g(Yes, No, false, false, "", "ئې")},
	{0xfbf9, 0, 0, 0, g(Yes, No, false, false, "", "ئى")},
	{0xfbfc, 0, 0, 0, g(Yes, No, false, false, "", "ی")},
	{0xfc00, 0, 0, 0, g(Yes, No, false, false, "", "ئج")},
	{0xfc01, 0, 0, 0, g(Yes, No, false, false, "", "ئح")},
	{0xfc02, 0, 0, 0, g(Yes, No, false, false, "", "ئم")},
	{0xfc03, 0, 0, 0, g(Yes, No, false, false, "", "ئى")},
	{0xfc04, 0, 0, 0, g(Yes, No, false, false, "", "ئي")},
	{0xfc05, 0, 0, 0, g(Yes, No, false, false, "", "بج")},
	{0xfc06, 0, 0, 0, g(Yes, No, false, false, "", "بح")},
	{0xfc07, 0, 0, 0, g(Yes, No, false, false, "", "بخ")},
	{0xfc08, 0, 0, 0, g(Yes, No, false, false, "", "بم")},
	{0xfc09, 0, 0, 0, g(Yes, No, false, false, "", "بى")},
	{0xfc0a, 0, 0, 0, g(Yes, No, false, false, "", "بي")},
	{0xfc0b, 0, 0, 0, g(Yes, No, false, false, "", "تج")},
	{0xfc0c, 0, 0, 0, g(Yes, No, false, false, "", "تح")},
	{0xfc0d, 0, 0, 0, g(Yes, No, false, false, "", "تخ")},
	{0xfc0e, 0, 0, 0, g(Yes, No, false, false, "", "تم")},
	{0xfc0f, 0, 0, 0, g(Yes, No, false, false, "", "تى")},
	{0xfc10, 0, 0, 0, g(Yes, No, false, false, "", "تي")},
	{0xfc11, 0, 0, 0, g(Yes, No, false, false, "", "ثج")},
	{0xfc12, 0, 0, 0, g(Yes, No, false, false, "", "ثم")},
	{0xfc13, 0, 0, 0, g(Yes, No, false, false, "", "ثى")},
	{0xfc14, 0, 0, 0, g(Yes, No, false, false, "", "ثي")},
	{0xfc15, 0, 0, 0, g(Yes, No, false, false, "", "جح")},
	{0xfc16, 0, 0, 0, g(Yes, No, false, false, "", "جم")},
	{0xfc17, 0, 0, 0, g(Yes, No, false, false, "", "حج")},
	{0xfc18, 0, 0, 0, g(Yes, No, false, false, "", "حم")},
	{0xfc19, 0, 0, 0, g(Yes, No, false, false, "", "خج")},
	{0xfc1a, 0, 0, 0, g(Yes, No, false, false, "", "خح")},
	{0xfc1b, 0, 0, 0, g(Yes, No, false, false, "", "خم")},
	{0xfc1c, 0, 0, 0, g(Yes, No, false, false, "", "سج")},
	{0xfc1d, 0, 0, 0, g(Yes, No, false, false, "", "سح")},
	{0xfc1e, 0, 0, 0, g(Yes, No, false, false, "", "سخ")},
	{0xfc1f, 0, 0, 0, g(Yes, No, false, false, "", "سم")},
	{0xfc20, 0, 0, 0, g(Yes, No, false, false, "", "صح")},
	{0xfc21, 0, 0, 0, g(Yes, No, false, false, "", "صم")},
	{0xfc22, 0, 0, 0, g(Yes, No, false, false, "", "ضج")},
	{0xfc23, 0, 0, 0, g(Yes, No, false, false, "", "ضح")},
	{0xfc24, 0, 0, 0, g(Yes, No, false, false, "", "ضخ")},
	{0xfc25, 0, 0, 0, g(Yes, No, false, false, "", "ضم")},
	{0xfc26, 0, 0, 0, g(Yes, No, false, false, "", "طح")},
	{0xfc27, 0, 0, 0, g(Yes, No, false, false, "", "طم")},
	{0xfc28, 0, 0, 0, g(Yes, No, false, false, "", "ظم")},
	{0xfc29, 0, 0, 0, g(Yes, No, false, false, "", "عج")},
	{0xfc2a, 0, 0, 0, g(Yes, No, false, false, "", "عم")},
	{0xfc2b, 0, 0, 0, g(Yes, No, false, false, "", "غج")},
	{0xfc2c, 0, 0, 0, g(Yes, No, false, false, "", "غم")},
	{0xfc2d, 0, 0, 0, g(Yes, No, false, false, "", "فج")},
	{0xfc2e, 0, 0, 0, g(Yes, No, false, false, "", "فح")},
	{0xfc2f, 0, 0, 0, g(Yes, No, false, false, "", "فخ")},
	{0xfc30, 0, 0, 0, g(Yes, No, false, false, "", "فم")},
	{0xfc31, 0, 0, 0, g(Yes, No, false, false, "", "فى")},
	{0xfc32, 0, 0, 0, g(Yes, No, false, false, "", "في")},
	{0xfc33, 0, 0, 0, g(Yes, No, false, false, "", "قح")},
	{0xfc34, 0, 0, 0, g(Yes, No, false, false, "", "قم")},
	{0xfc35, 0, 0, 0, g(Yes, No, false, false, "", "قى")},
	{0xfc36, 0, 0, 0, g(Yes, No, false, false, "", "قي")},
	{0xfc37, 0, 0, 0, g(Yes, No, false, false, "", "كا")},
	{0xfc38, 0, 0, 0, g(Yes, No, false, false, "", "كج")},
	{0xfc39, 0, 0, 0, g(Yes, No, false, false, "", "كح")},
	{0xfc3a, 0, 0, 0, g(Yes, No, false, false, "", "كخ")},
	{0xfc3b, 0, 0, 0, g(Yes, No, false, false, "", "كل")},
	{0xfc3c, 0, 0, 0, g(Yes, No, false, false, "", "كم")},
	{0xfc3d, 0, 0, 0, g(Yes, No, false, false, "", "كى")},
	{0xfc3e, 0, 0, 0, g(Yes, No, false, false, "", "كي")},
	{0xfc3f, 0, 0, 0, g(Yes, No, false, false, "", "لج")},
	{0xfc40, 0, 0, 0, g(Yes, No, false, false, "", "لح")},
	{0xfc41, 0, 0, 0, g(Yes, No, false, false, "", "لخ")},
	{0xfc42, 0, 0, 0, g(Yes, No, false, false, "", "لم")},
	{0xfc43, 0, 0, 0, g(Yes, No, false, false, "", "لى")},
	{0xfc44, 0, 0, 0, g(Yes, No, false, false, "", "لي")},
	{0xfc45, 0, 0, 0, g(Yes, No, false, false, "", "مج")},
	{0xfc46, 0, 0, 0, g(Yes, No, false, false, "", "مح")},
	{0xfc47, 0, 0, 0, g(Yes, No, false, false, "", "مخ")},
	{0xfc48, 0, 0, 0, g(Yes, No, false, false, "", "مم")},
	{0xfc49, 0, 0, 0, g(Yes, No, false, false, "", "مى")},
	{0xfc4a, 0, 0, 0, g(Yes, No, false, false, "", "مي")},
	{0xfc4b, 0, 0, 0, g(Yes, No, false, false, "", "نج")},
	{0xfc4c, 0, 0, 0, g(Yes, No, false, false, "", "نح")},
	{0xfc4d, 0, 0, 0, g(Yes, No, false, false, "", "نخ")},
	{0xfc4e, 0, 0, 0, g(Yes, No, false, false, "", "نم")},
	{0xfc4f, 0, 0, 0, g(Yes, No, false, false, "", "نى")},
	{0xfc50, 0, 0, 0, g(Yes, No, false, false, "", "ني")},
	{0xfc51, 0, 0, 0, g(Yes, No, false, false, "", "هج")},
	{0xfc52, 0, 0, 0, g(Yes, No, false, false, "", "هم")},
	{0xfc53, 0, 0, 0, g(Yes, No, false, false, "", "هى")},
	{0xfc54, 0, 0, 0, g(Yes, No, false, false, "", "هي")},
	{0xfc55, 0, 0, 0, g(Yes, No, false, false, "", "يج")},
	{0xfc56, 0, 0, 0, g(Yes, No, false, false, "", "يح")},
	{0xfc57, 0, 0, 0, g(Yes, No, false, false, "", "يخ")},
	{0xfc58, 0, 0, 0, g(Yes, No, false, false, "", "يم")},
	{0xfc59, 0, 0, 0, g(Yes, No, false, false, "", "يى")},
	{0xfc5a, 0, 0, 0, g(Yes, No, false, false, "", "يي")},
	{0xfc5b, 0, 0, 1, g(Yes, No, false, false, "", "ذٰ")},
	{0xfc5c, 0, 0, 1, g(Yes, No, false, false, "", "رٰ")},
	{0xfc5d, 0, 0, 1, g(Yes, No, false, false, "", "ىٰ")},
	{0xfc5e, 0, 0, 2, g(Yes, No, false, false, "", " ٌّ")},
	{0xfc5f, 0, 0, 2, g(Yes, No, false, false, "", " ٍّ")},
	{0xfc60, 0, 0, 2, g(Yes, No, false, false, "", " َّ")},
	{0xfc61, 0, 0, 2, g(Yes, No, false, false, "", " ُّ")},
	{0xfc62, 0, 0, 2, g(Yes, No, false, false, "", " ِّ")},
	{0xfc63, 0, 0, 2, g(Yes, No, false, false, "", " ّٰ")},
	{0xfc64, 0, 0, 0, g(Yes, No, false, false, "", "ئر")},
	{0xfc65, 0, 0, 0, g(Yes, No, false, false, "", "ئز")},
	{0xfc66, 0, 0, 0, g(Yes, No, false, false, "", "ئم")},
	{0xfc67, 0, 0, 0, g(Yes, No, false, false, "", "ئن")},
	{0xfc68, 0, 0, 0, g(Yes, No, false, false, "", "ئى")},
	{0xfc69, 0, 0, 0, g(Yes, No, false, false, "", "ئي")},
	{0xfc6a, 0, 0, 0, g(Yes, No, false, false, "", "بر")},
	{0xfc6b, 0, 0, 0, g(Yes, No, false, false, "", "بز")},
	{0xfc6c, 0, 0, 0, g(Yes, No, false, false, "", "بم")},
	{0xfc6d, 0, 0, 0, g(Yes, No, false, false, "", "بن")},
	{0xfc6e, 0, 0, 0, g(Yes, No, false, false, "", "بى")},
	{0xfc6f, 0, 0, 0, g(Yes, No, false, false, "", "بي")},
	{0xfc70, 0, 0, 0, g(Yes, No, false, false, "", "تر")},
	{0xfc71, 0, 0, 0, g(Yes, No, false, false, "", "تز")},
	{0xfc72, 0, 0, 0, g(Yes, No, false, false, "", "تم")},
	{0xfc73, 0, 0, 0, g(Yes, No, false, false, "", "تن")},
	{0xfc74, 0, 0, 0, g(Yes, No, false, false, "", "تى")},
	{0xfc75, 0, 0, 0, g(Yes, No, false, false, "", "تي")},
	{0xfc76, 0, 0, 0, g(Yes, No, false, false, "", "ثر")},
	{0xfc77, 0, 0, 0, g(Yes, No, false, false, "", "ثز")},
	{0xfc78, 0, 0, 0, g(Yes, No, false, false, "", "ثم")},
	{0xfc79, 0, 0, 0, g(Yes, No, false, false, "", "ثن")},
	{0xfc7a, 0, 0, 0, g(Yes, No, false, false, "", "ثى")},
	{0xfc7b, 0, 0, 0, g(Yes, No, false, false, "", "ثي")},
	{0xfc7c, 0, 0, 0, g(Yes, No, false, false, "", "فى")},
	{0xfc7d, 0, 0, 0, g(Yes, No, false, false, "", "في")},
	{0xfc7e, 0, 0, 0, g(Yes, No, false, false, "", "قى")},
	{0xfc7f, 0, 0, 0, g(Yes, No, false, false, "", "قي")},
	{0xfc80, 0, 0, 0, g(Yes, No, false, false, "", "كا")},
	{0xfc81, 0, 0, 0, g(Yes, No, false, false, "", "كل")},
	{0xfc82, 0, 0, 0, g(Yes, No, false, false, "", "كم")},
	{0xfc83, 0, 0, 0, g(Yes, No, false, false, "", "كى")},
	{0xfc84, 0, 0, 0, g(Yes, No, false, false, "", "كي")},
	{0xfc85, 0, 0, 0, g(Yes, No, false, false, "", "لم")},
	{0xfc86, 0, 0, 0, g(Yes, No, false, false, "", "لى")},
	{0xfc87, 0, 0, 0, g(Yes, No, false, false, "", "لي")},
	{0xfc88, 0, 0, 0, g(Yes, No, false, false, "", "ما")},
	{0xfc89, 0, 0, 0, g(Yes, No, false, false, "", "مم")},
	{0xfc8a, 0, 0, 0, g(Yes, No, false, false, "", "نر")},
	{0xfc8b, 0, 0, 0, g(Yes, No, false, false, "", "نز")},
	{0xfc8c, 0, 0, 0, g(Yes, No, false, false, "", "نم")},
	{0xfc8d, 0, 0, 0, g(Yes, No, false, false, "", "نن")},
	{0xfc8e, 0, 0, 0, g(Yes, No, false, false, "", "نى")},
	{0xfc8f, 0, 0, 0, g(Yes, No, false, false, "", "ني")},
	{0xfc90, 0, 0, 1, g(Yes, No, false, false, "", "ىٰ")},
	{0xfc91, 0, 0, 0, g(Yes, No, false, false, "", "ير")},
	{0xfc92, 0, 0, 0, g(Yes, No, false, false, "", "يز")},
	{0xfc93, 0, 0, 0, g(Yes, No, false, false, "", "يم")},
	{0xfc94, 0, 0, 0, g(Yes, No, false, false, "", "ين")},
	{0xfc95, 0, 0, 0, g(Yes, No, false, false, "", "يى")},
	{0xfc96, 0, 0, 0, g(Yes, No, false, false, "", "يي")},
	{0xfc97, 0, 0, 0, g(Yes, No, false, false, "", "ئج")},
	{0xfc98, 0, 0, 0, g(Yes, No, false, false, "", "ئح")},
	{0xfc99, 0, 0, 0, g(Yes, No, false, false, "", "ئخ")},
	{0xfc9a, 0, 0, 0, g(Yes, No, false, false, "", "ئم")},
	{0xfc9b, 0, 0, 0, g(Yes, No, false, false, "", "ئه")},
	{0xfc9c, 0, 0, 0, g(Yes, No, false, false, "", "بج")},
	{0xfc9d, 0, 0, 0, g(Yes, No, false, false, "", "بح")},
	{0xfc9e, 0, 0, 0, g(Yes, No, false, false, "", "بخ")},
	{0xfc9f, 0, 0, 0, g(Yes, No, false, false, "", "بم")},
	{0xfca0, 0, 0, 0, g(Yes, No, false, false, "", "به")},
	{0xfca1, 0, 0, 0, g(Yes, No, false, false, "", "تج")},
	{0xfca2, 0, 0, 0, g(Yes, No, false, false, "", "تح")},
	{0xfca3, 0, 0, 0, g(Yes, No, false, false, "", "تخ")},
	{0xfca4, 0, 0, 0, g(Yes, No, false, false, "", "تم")},
	{0xfca5, 0, 0, 0, g(Yes, No, false, false, "", "ته")},
	{0xfca6, 0, 0, 0, g(Yes, No, false, false, "", "ثم")},
	{0xfca7, 0, 0, 0, g(Yes, No, false, false, "", "جح")},
	{0xfca8, 0, 0, 0, g(Yes, No, false, false, "", "جم")},
	{0xfca9, 0, 0, 0, g(Yes, No, false, false, "", "حج")},
	{0xfcaa, 0, 0, 0, g(Yes, No, false, false, "", "حم")},
	{0xfcab, 0, 0, 0, g(Yes, No, false, false, "", "خج")},
	{0xfcac, 0, 0, 0, g(Yes, No, false, false, "", "خم")},
	{0xfcad, 0, 0, 0, g(Yes, No, false, false, "", "سج")},
	{0xfcae, 0, 0, 0, g(Yes, No, false, false, "", "سح")},
	{0xfcaf, 0, 0, 0, g(Yes, No, false, false, "", "سخ")},
	{0xfcb0, 0, 0, 0, g(Yes, No, false, false, "", "سم")},
	{0xfcb1, 0, 0, 0, g(Yes, No, false, false, "", "صح")},
	{0xfcb2, 0, 0, 0, g(Yes, No, false, false, "", "صخ")},
	{0xfcb3, 0, 0, 0, g(Yes, No, false, false, "", "صم")},
	{0xfcb4, 0, 0, 0, g(Yes, No, false, false, "", "ضج")},
	{0xfcb5, 0, 0, 0, g(Yes, No, false, false, "", "ضح")},
	{0xfcb6, 0, 0, 0, g(Yes, No, false, false, "", "ضخ")},
	{0xfcb7, 0, 0, 0, g(Yes, No, false, false, "", "ضم")},
	{0xfcb8, 0, 0, 0, g(Yes, No, false, false, "", "طح")},
	{0xfcb9, 0, 0, 0, g(Yes, No, false, false, "", "ظم")},
	{0xfcba, 0, 0, 0, g(Yes, No, false, false, "", "عج")},
	{0xfcbb, 0, 0, 0, g(Yes, No, false, false, "", "عم")},
	{0xfcbc, 0, 0, 0, g(Yes, No, false, false, "", "غج")},
	{0xfcbd, 0, 0, 0, g(Yes, No, false, false, "", "غم")},
	{0xfcbe, 0, 0, 0, g(Yes, No, false, false, "", "فج")},
	{0xfcbf, 0, 0, 0, g(Yes, No, false, false, "", "فح")},
	{0xfcc0, 0, 0, 0, g(Yes, No, false, false, "", "فخ")},
	{0xfcc1, 0, 0, 0, g(Yes, No, false, false, "", "فم")},
	{0xfcc2, 0, 0, 0, g(Yes, No, false, false, "", "قح")},
	{0xfcc3, 0, 0, 0, g(Yes, No, false, false, "", "قم")},
	{0xfcc4, 0, 0, 0, g(Yes, No, false, false, "", "كج")},
	{0xfcc5, 0, 0, 0, g(Yes, No, false, false, "", "كح")},
	{0xfcc6, 0, 0, 0, g(Yes, No, false, false, "", "كخ")},
	{0xfcc7, 0, 0, 0, g(Yes, No, false, false, "", "كل")},
	{0xfcc8, 0, 0, 0, g(Yes, No, false, false, "", "كم")},
	{0xfcc9, 0, 0, 0, g(Yes, No, false, false, "", "لج")},
	{0xfcca, 0, 0, 0, g(Yes, No, false, false, "", "لح")},
	{0xfccb, 0, 0, 0, g(Yes, No, false, false, "", "لخ")},
	{0xfccc, 0, 0, 0, g(Yes, No, false, false, "", "لم")},
	{0xfccd, 0, 0, 0, g(Yes, No, false, false, "", "له")},
	{0xfcce, 0, 0, 0, g(Yes, No, false, false, "", "مج")},
	{0xfccf, 0, 0, 0, g(Yes, No, false, false, "", "مح")},
	{0xfcd0, 0, 0, 0, g(Yes, No, false, false, "", "مخ")},
	{0xfcd1, 0, 0, 0, g(Yes, No, false, false, "", "مم")},
	{0xfcd2, 0, 0, 0, g(Yes, No, false, false, "", "نج")},
	{0xfcd3, 0, 0, 0, g(Yes, No, false, false, "", "نح")},
	{0xfcd4, 0, 0, 0, g(Yes, No, false, false, "", "نخ")},
	{0xfcd5, 0, 0, 0, g(Yes, No, false, false, "", "نم")},
	{0xfcd6, 0, 0, 0, g(Yes, No, false, false, "", "نه")},
	{0xfcd7, 0, 0, 0, g(Yes, No, false, false, "", "هج")},
	{0xfcd8, 0, 0, 0, g(Yes, No, false, false, "", "هم")},
	{0xfcd9, 0, 0, 1, g(Yes, No, false, false, "", "هٰ")},
	{0xfcda, 0, 0, 0, g(Yes, No, false, false, "", "يج")},
	{0xfcdb, 0, 0, 0, g(Yes, No, false, false, "", "يح")},
	{0xfcdc, 0, 0, 0, g(Yes, No, false, false, "", "يخ")},
	{0xfcdd, 0, 0, 0, g(Yes, No, false, false, "", "يم")},
	{0xfcde, 0, 0, 0, g(Yes, No, false, false, "", "يه")},
	{0xfcdf, 0, 0, 0, g(Yes, No, false, false, "", "ئم")},
	{0xfce0, 0, 0, 0, g(Yes, No, false, false, "", "ئه")},
	{0xfce1, 0, 0, 0, g(Yes, No, false, false, "", "بم")},
	{0xfce2, 0, 0, 0, g(Yes, No, false, false, "", "به")},
	{0xfce3, 0, 0, 0, g(Yes, No, false, false, "", "تم")},
	{0xfce4, 0, 0, 0, g(Yes, No, false, false, "", "ته")},
	{0xfce5, 0, 0, 0, g(Yes, No, false, false, "", "ثم")},
	{0xfce6, 0, 0, 0, g(Yes, No, false, false, "", "ثه")},
	{0xfce7, 0, 0, 0, g(Yes, No, false, false, "", "سم")},
	{0xfce8, 0, 0, 0, g(Yes, No, false, false, "", "سه")},
	{0xfce9, 0, 0, 0, g(Yes, No, false, false, "", "شم")},
	{0xfcea, 0, 0, 0, g(Yes, No, false, false, "", "شه")},
	{0xfceb, 0, 0, 0, g(Yes, No, false, false, "", "كل")},
	{0xfcec, 0, 0, 0, g(Yes, No, false, false, "", "كم")},
	{0xfced, 0, 0, 0, g(Yes, No, false, false, "", "لم")},
	{0xfcee, 0, 0, 0, g(Yes, No, false, false, "", "نم")},
	{0xfcef, 0, 0, 0, g(Yes, No, false, false, "", "نه")},
	{0xfcf0, 0, 0, 0, g(Yes, No, false, false, "", "يم")},
	{0xfcf1, 0, 0, 0, g(Yes, No, false, false, "", "يه")},
	{0xfcf2, 0, 0, 2, g(Yes, No, false, false, "", "ـَّ")},
	{0xfcf3, 0, 0, 2, g(Yes, No, false, false, "", "ـُّ")},
	{0xfcf4, 0, 0, 2, g(Yes, No, false, false, "", "ـِّ")},
	{0xfcf5, 0, 0, 0, g(Yes, No, false, false, "", "طى")},
	{0xfcf6, 0, 0, 0, g(Yes, No, false, false, "", "طي")},
	{0xfcf7, 0, 0, 0, g(Yes, No, false, false, "", "عى")},
	{0xfcf8, 0, 0, 0, g(Yes, No, false, false, "", "عي")},
	{0xfcf9, 0, 0, 0, g(Yes, No, false, false, "", "غى")},
	{0xfcfa, 0, 0, 0, g(Yes, No, false, false, "", "غي")},
	{0xfcfb, 0, 0, 0, g(Yes, No, false, false, "", "سى")},
	{0xfcfc, 0, 0, 0, g(Yes, No, false, false, "", "سي")},
	{0xfcfd, 0, 0, 0, g(Yes, No, false, false, "", "شى")},
	{0xfcfe, 0, 0, 0, g(Yes, No, false, false, "", "شي")},
	{0xfcff, 0, 0, 0, g(Yes, No, false, false, "", "حى")},
	{0xfd00, 0, 0, 0, g(Yes, No, false, false, "", "حي")},
	{0xfd01, 0, 0, 0, g(Yes, No, false, false, "", "جى")},
	{0xfd02, 0, 0, 0, g(Yes, No, false, false, "", "جي")},
	{0xfd03, 0, 0, 0, g(Yes, No, false, false, "", "خى")},
	{0xfd04, 0, 0, 0, g(Yes, No, false, false, "", "خي")},
	{0xfd05, 0, 0, 0, g(Yes, No, false, false, "", "صى")},
	{0xfd06, 0, 0, 0, g(Yes, No, false, false, "", "صي")},
	{0xfd07, 0, 0, 0, g(Yes, No, false, false, "", "ضى")},
	{0xfd08, 0, 0, 0, g(Yes, No, false, false, "", "ضي")},
	{0xfd09, 0, 0, 0, g(Yes, No, false, false, "", "شج")},
	{0xfd0a, 0, 0, 0, g(Yes, No, false, false, "", "شح")},
	{0xfd0b, 0, 0, 0, g(Yes, No, false, false, "", "شخ")},
	{0xfd0c, 0, 0, 0, g(Yes, No, false, false, "", "شم")},
	{0xfd0d, 0, 0, 0, g(Yes, No, false, false, "", "شر")},
	{0xfd0e, 0, 0, 0, g(Yes, No, false, false, "", "سر")},
	{0xfd0f, 0, 0, 0, g(Yes, No, false, false, "", "صر")},
	{0xfd10, 0, 0, 0, g(Yes, No, false, false, "", "ضر")},
	{0xfd11, 0, 0, 0, g(Yes, No, false, false, "", "طى")},
	{0xfd12, 0, 0, 0, g(Yes, No, false, false, "", "طي")},
	{0xfd13, 0, 0, 0, g(Yes, No, false, false, "", "عى")},
	{0xfd14, 0, 0, 0, g(Yes, No, false, false, "", "عي")},
	{0xfd15, 0, 0, 0, g(Yes, No, false, false, "", "غى")},
	{0xfd16, 0, 0, 0, g(Yes, No, false, false, "", "غي")},
	{0xfd17, 0, 0, 0, g(Yes, No, false, false, "", "سى")},
	{0xfd18, 0, 0, 0, g(Yes, No, false, false, "", "سي")},
	{0xfd19, 0, 0, 0, g(Yes, No, false, false, "", "شى")},
	{0xfd1a, 0, 0, 0, g(Yes, No, false, false, "", "شي")},
	{0xfd1b, 0, 0, 0, g(Yes, No, false, false, "", "حى")},
	{0xfd1c, 0, 0, 0, g(Yes, No, false, false, "", "حي")},
	{0xfd1d, 0, 0, 0, g(Yes, No, false, false, "", "جى")},
	{0xfd1e, 0, 0, 0, g(Yes, No, false, false, "", "جي")},
	{0xfd1f, 0, 0, 0, g(Yes, No, false, false, "", "خى")},
	{0xfd20, 0, 0, 0, g(Yes, No, false, false, "", "خي")},
	{0xfd21, 0, 0, 0, g(Yes, No, false, false, "", "صى")},
	{0xfd22, 0, 0, 0, g(Yes, No, false, false, "", "صي")},
	{0xfd23, 0, 0, 0, g(Yes, No, false, false, "", "ضى")},
	{0xfd24, 0, 0, 0, g(Yes, No, false, false, "", "ضي")},
	{0xfd25, 0, 0, 0, g(Yes, No, false, false, "", "شج")},
	{0xfd26, 0, 0, 0, g(Yes, No, false, false, "", "شح")},
	{0xfd27, 0, 0, 0, g(Yes, No, false, false, "", "شخ")},
	{0xfd28, 0, 0, 0, g(Yes, No, false, false, "", "شم")},
	{0xfd29, 0, 0, 0, g(Yes, No, false, false, "", "شر")},
	{0xfd2a, 0, 0, 0, g(Yes, No, false, false, "", "سر")},
	{0xfd2b, 0, 0, 0, g(Yes, No, false, false, "", "صر")},
	{0xfd2c, 0, 0, 0, g(Yes, No, false, false, "", "ضر")},
	{0xfd2d, 0, 0, 0, g(Yes, No, false, false, "", "شج")},
	{0xfd2e, 0, 0, 0, g(Yes, No, false, false, "", "شح")},
	{0xfd2f, 0, 0, 0, g(Yes, No, false, false, "", "شخ")},
	{0xfd30, 0, 0, 0, g(Yes, No, false, false, "", "شم")},
	{0xfd31, 0, 0, 0, g(Yes, No, false, false, "", "سه")},
	{0xfd32, 0, 0, 0, g(Yes, No, false, false, "", "شه")},
	{0xfd33, 0, 0, 0, g(Yes, No, false, false, "", "طم")},
	{0xfd34, 0, 0, 0, g(Yes, No, false, false, "", "سج")},
	{0xfd35, 0, 0, 0, g(Yes, No, false, false, "", "سح")},
	{0xfd36, 0, 0, 0, g(Yes, No, false, false, "", "سخ")},
	{0xfd37, 0, 0, 0, g(Yes, No, false, false, "", "شج")},
	{0xfd38, 0, 0, 0, g(Yes, No, false, false, "", "شح")},
	{0xfd39, 0, 0, 0, g(Yes, No, false, false, "", "شخ")},
	{0xfd3a, 0, 0, 0, g(Yes, No, false, false, "", "طم")},
	{0xfd3b, 0, 0, 0, g(Yes, No, false, false, "", "ظم")},
	{0xfd3c, 0, 0, 1, g(Yes, No, false, false, "", "اً")},
	{0xfd3e, 0, 0, 0, f(Yes, false, "")},
	{0xfd50, 0, 0, 0, g(Yes, No, false, false, "", "تجم")},
	{0xfd51, 0, 0, 0, g(Yes, No, false, false, "", "تحج")},
	{0xfd53, 0, 0, 0, g(Yes, No, false, false, "", "تحم")},
	{0xfd54, 0, 0, 0, g(Yes, No, false, false, "", "تخم")},
	{0xfd55, 0, 0, 0, g(Yes, No, false, false, "", "تمج")},
	{0xfd56, 0, 0, 0, g(Yes, No, false, false, "", "تمح")},
	{0xfd57, 0, 0, 0, g(Yes, No, false, false, "", "تمخ")},
	{0xfd58, 0, 0, 0, g(Yes, No, false, false, "", "جمح")},
	{0xfd5a, 0, 0, 0, g(Yes, No, false, false, "", "حمي")},
	{0xfd5b, 0, 0, 0, g(Yes, No, false, false, "", "حمى")},
	{0xfd5c, 0, 0, 0, g(Yes, No, false, false, "", "سحج")},
	{0xfd5d, 0, 0, 0, g(Yes, No, false, false, "", "سجح")},
	{0xfd5e, 0, 0, 0, g(Yes, No, false, false, "", "سجى")},
	{0xfd5f, 0, 0, 0, g(Yes, No, false, false, "", "سمح")},
	{0xfd61, 0, 0, 0, g(Yes, No, false, false, "", "سمج")},
	{0xfd62, 0, 0, 0, g(Yes, No, false, false, "", "سمم")},
	{0xfd64, 0, 0, 0, g(Yes, No, false, false, "", "صحح")},
	{0xfd66, 0, 0, 0, g(Yes, No, false, false, "", "صمم")},
	{0xfd67, 0, 0, 0, g(Yes, No, false, false, "", "شحم")},
	{0xfd69, 0, 0, 0, g(Yes, No, false, false, "", "شجي")},
	{0xfd6a, 0, 0, 0, g(Yes, No, false, false, "", "شمخ")},
	{0xfd6c, 0, 0, 0, g(Yes, No, false, false, "", "شمم")},
	{0xfd6e, 0, 0, 0, g(Yes, No, false, false, "", "ضحى")},
	{0xfd6f, 0, 0, 0, g(Yes, No, false, false, "", "ضخم")},
	{0xfd71, 0, 0, 0, g(Yes, No, false, false, "", "طمح")},
	{0xfd73, 0, 0, 0, g(Yes, No, false, false, "", "طمم")},
	{0xfd74, 0, 0, 0, g(Yes, No, false, false, "", "طمي")},
	{0xfd75, 0, 0, 0, g(Yes, No, false, false, "", "عجم")},
	{0xfd76, 0, 0, 0, g(Yes, No, false, false, "", "عمم")},
	{0xfd78, 0, 0, 0, g(Yes, No, false, false, "", "عمى")},
	{0xfd79, 0, 0, 0, g(Yes, No, false, false, "", "غمم")},
	{0xfd7a, 0, 0, 0, g(Yes, No, false, false, "", "غمي")},
	{0xfd7b, 0, 0, 0, g(Yes, No, false, false, "", "غمى")},
	{0xfd7c, 0, 0, 0, g(Yes, No, false, false, "", "فخم")},
	{0xfd7e, 0, 0, 0, g(Yes, No, false, false, "", "قمح")},
	{0xfd7f, 0, 0, 0, g(Yes, No, false, false, "", "قمم")},
	{0xfd80, 0, 0, 0, g(Yes, No, false, false, "", "لحم")},
	{0xfd81, 0, 0, 0, g(Yes, No, false, false, "", "لحي")},
	{0xfd82, 0, 0, 0, g(Yes, No, false, false, "", "لحى")},
	{0xfd83, 0, 0, 0, g(Yes, No, false, false, "", "لجج")},
	{0xfd85, 0, 0, 0, g(Yes, No, false, false, "", "لخم")},
	{0xfd87, 0, 0, 0, g(Yes, No, false, false, "", "لمح")},
	{0xfd89, 0, 0, 0, g(Yes, No, false, false, "", "محج")},
	{0xfd8a, 0, 0, 0, g(Yes, No, false, false, "", "محم")},
	{0xfd8b, 0, 0, 0, g(Yes, No, false, false, "", "محي")},
	{0xfd8c, 0, 0, 0, g(Yes, No, false, false, "", "مجح")},
	{0xfd8d, 0, 0, 0, g(Yes, No, false, false, "", "مجم")},
	{0xfd8e, 0, 0, 0, g(Yes, No, false, false, "", "مخج")},
	{0xfd8f, 0, 0, 0, g(Yes, No, false, false, "", "مخم")},
	{0xfd90, 0, 0, 0, f(Yes, false, "")},
	{0xfd92, 0, 0, 0, g(Yes, No, false, false, "", "مجخ")},
	{0xfd93, 0, 0, 0, g(Yes, No, false, false, "", "همج")},
	{0xfd94, 0, 0, 0, g(Yes, No, false, false, "", "همم")},
	{0xfd95, 0, 0, 0, g(Yes, No, false, false, "", "نحم")},
	{0xfd96, 0, 0, 0, g(Yes, No, false, false, "", "نحى")},
	{0xfd97, 0, 0, 0, g(Yes, No, false, false, "", "نجم")},
	{0xfd99, 0, 0, 0, g(Yes, No, false, false, "", "نجى")},
	{0xfd9a, 0, 0, 0, g(Yes, No, false, false, "", "نمي")},
	{0xfd9b, 0, 0, 0, g(Yes, No, false, false, "", "نمى")},
	{0xfd9c, 0, 0, 0, g(Yes, No, false, false, "", "يمم")},
	{0xfd9e, 0, 0, 0, g(Yes, No, false, false, "", "بخي")},
	{0xfd9f, 0, 0, 0, g(Yes, No, false, false, "", "تجي")},
	{0xfda0, 0, 0, 0, g(Yes, No, false, false, "", "تجى")},
	{0xfda1, 0, 0, 0, g(Yes, No, false, false, "", "تخي")},
	{0xfda2, 0, 0, 0, g(Yes, No, false, false, "", "تخى")},
	{0xfda3, 0, 0, 0, g(Yes, No, false, false, "", "تمي")},
	{0xfda4, 0, 0, 0, g(Yes, No, false, false, "", "تمى")},
	{0xfda5, 0, 0, 0, g(Yes, No, false, false, "", "جمي")},
	{0xfda6, 0, 0, 0, g(Yes, No, false, false, "", "جحى")},
	{0xfda7, 0, 0, 0, g(Yes, No, false, false, "", "جمى")},
	{0xfda8, 0, 0, 0, g(Yes, No, false, false, "", "سخى")},
	{0xfda9, 0, 0, 0, g(Yes, No, false, false, "", "صحي")},
	{0xfdaa, 0, 0, 0, g(Yes, No, false, false, "", "شحي")},
	{0xfdab, 0, 0, 0, g(Yes, No, false, false, "", "ضحي")},
	{0xfdac, 0, 0, 0, g(Yes, No, false, false, "", "لجي")},
	{0xfdad, 0, 0, 0, g(Yes, No, false, false, "", "لمي")},
	{0xfdae, 0, 0, 0, g(Yes, No, false, false, "", "يحي")},
	{0xfdaf, 0, 0, 0, g(Yes, No, false, false, "", "يجي")},
	{0xfdb0, 0, 0, 0, g(Yes, No, false, false, "", "يمي")},
	{0xfdb1, 0, 0, 0, g(Yes, No, false, false, "", "ممي")},
	{0xfdb2, 0, 0, 0, g(Yes, No, false, false, "", "قمي")},
	{0xfdb3, 0, 0, 0, g(Yes, No, false, false, "", "نحي")},
	{0xfdb4, 0, 0, 0, g(Yes, No, false, false, "", "قمح")},
	{0xfdb5, 0, 0, 0, g(Yes, No, false, false, "", "لحم")},
	{0xfdb6, 0, 0, 0, g(Yes, No, false, false, "", "عمي")},
	{0xfdb7, 0, 0, 0, g(Yes, No, false, false, "", "كمي")},
	{0xfdb8, 0, 0, 0, g(Yes, No, false, false, "", "نجح")},
	{0xfdb9, 0, 0, 0, g(Yes, No, false, false, "", "مخي")},
	{0xfdba, 0, 0, 0, g(Yes, No, false, false, "", "لجم")},
	{0xfdbb, 0, 0, 0, g(Yes, No, false, false, "", "كمم")},
	{0xfdbc, 0, 0, 0, g(Yes, No, false, false, "", "لجم")},
	{0xfdbd, 0, 0, 0, g(Yes, No, false, false, "", "نجح")},
	{0xfdbe, 0, 0, 0, g(Yes, No, false, false, "", "جحي")},
	{0xfdbf, 0, 0, 0, g(Yes, No, false, false, "", "حجي")},
	{0xfdc0, 0, 0, 0, g(Yes, No, false, false, "", "مجي")},
	{0xfdc1, 0, 0, 0, g(Yes, No, false, false, "", "فمي")},
	{0xfdc2, 0, 0, 0, g(Yes, No, false, false, "", "بحي")},
	{0xfdc3, 0, 0, 0, g(Yes, No, false, false, "", "كمم")},
	{0xfdc4, 0, 0, 0, g(Yes, No, false, false, "", "عجم")},
	{0xfdc5, 0, 0, 0, g(Yes, No, false, false, "", "صمم")},
	{0xfdc6, 0, 0, 0, g(Yes, No, false, false, "", "سخي")},
	{0xfdc7, 0, 0, 0, g(Yes, No, false, false, "", "نجي")},
	{0xfdc8, 0, 0, 0, f(Yes, false, "")},
	{0xfdf0, 0, 0, 0, g(Yes, No, false, false, "", "صلے")},
	{0xfdf1, 0, 0, 0, g(Yes, No, false, false, "", "قلے")},
	{0xfdf2, 0, 0, 0, g(Yes, No, false, false, "", "الله")},
	{0xfdf3, 0, 0, 0, g(Yes, No, false, false, "", "اكبر")},
	{0xfdf4, 0, 0, 0, g(Yes, No, false, false, "", "محمد")},
	{0xfdf5, 0, 0, 0, g(Yes, No, false, false, "", "صلعم")},
	{0xfdf6, 0, 0, 0, g(Yes, No, false, false, "", "رسول")},
	{0xfdf7, 0, 0, 0, g(Yes, No, false, false, "", "عليه")},
	{0xfdf8, 0, 0, 0, g(Yes, No, false, false, "", "وسلم")},
	{0xfdf9, 0, 0, 0, g(Yes, No, false, false, "", "صلى")},
	{0xfdfa, 0, 0, 0, g(Yes, No, false, false, "", "صلى الله عليه وسلم")},
	{0xfdfb, 0, 0, 0, g(Yes, No, false, false, "", "جل جلاله")},
	{0xfdfc, 0, 0, 0, g(Yes, No, false, false, "", "ریال")},
	{0xfdfd, 0, 0, 0, f(Yes, false, "")},
	{0xfe10, 0, 0, 0, g(Yes, No, false, false, "", ",")},
	{0xfe11, 0, 0, 0, g(Yes, No, false, false, "", "、")},
	{0xfe12, 0, 0, 0, g(Yes, No, false, false, "", "。")},
	{0xfe13, 0, 0, 0, g(Yes, No, false, false, "", ":")},
	{0xfe14, 0, 0, 0, g(Yes, No, false, false, "", ";")},
	{0xfe15, 0, 0, 0, g(Yes, No, false, false, "", "!")},
	{0xfe16, 0, 0, 0, g(Yes, No, false, false, "", "?")},
	{0xfe17, 0, 0, 0, g(Yes, No, false, false, "", "〖")},
	{0xfe18, 0, 0, 0, g(Yes, No, false, false, "", "〗")},
	{0xfe19, 0, 0, 0, g(Yes, No, false, false, "", "...")},
	{0xfe1a, 0, 0, 0, f(Yes, false, "")},
	{0xfe20, 230, 1, 1, f(Yes, false, "")},
	{0xfe27, 220, 1, 1, f(Yes, false, "")},
	{0xfe2e, 230, 1, 1, f(Yes, false, "")},
	{0xfe30, 0, 0, 0, g(Yes, No, false, false, "", "..")},
	{0xfe31, 0, 0, 0, g(Yes, No, false, false, "", "—")},
	{0xfe32, 0, 0, 0, g(Yes, No, false, false, "", "–")},
	{0xfe33, 0, 0, 0, g(Yes, No, false, false, "", "_")},
	{0xfe35, 0, 0, 0, g(Yes, No, false, false, "", "(")},
	{0xfe36, 0, 0, 0, g(Yes, No, false, false, "", ")")},
	{0xfe37, 0, 0, 0, g(Yes, No, false, false, "", "{")},
	{0xfe38, 0, 0, 0, g(Yes, No, false, false, "", "}")},
	{0xfe39, 0, 0, 0, g(Yes, No, false, false, "", "〔")},
	{0xfe3a, 0, 0, 0, g(Yes, No, false, false, "", "〕")},
	{0xfe3b, 0, 0, 0, g(Yes, No, false, false, "", "【")},
	{0xfe3c, 0, 0, 0, g(Yes, No, false, false, "", "】")},
	{0xfe3d, 0, 0, 0, g(Yes, No, false, false, "", "《")},
	{0xfe3e, 0, 0, 0, g(Yes, No, false, false, "", "》")},
	{0xfe3f, 0, 0, 0, g(Yes, No, false, false, "", "〈")},
	{0xfe40, 0, 0, 0, g(Yes, No, false, false, "", "〉")},
	{0xfe41, 0, 0, 0, g(Yes, No, false, false, "", "「")},
	{0xfe42, 0, 0, 0, g(Yes, No, false, false, "", "」")},
	{0xfe43, 0, 0, 0, g(Yes, No, false, false, "", "『")},
	{0xfe44, 0, 0, 0, g(Yes, No, false, false, "", "』")},
	{0xfe45, 0, 0, 0, f(Yes, false, "")},
	{0xfe47, 0, 0, 0, g(Yes, No, false, false, "", "[")},
	{0xfe48, 0, 0, 0, g(Yes, No, false, false, "", "]")},
	{0xfe49, 0, 0, 1, g(Yes, No, false, false, "", " ̅")},
	{0xfe4d, 0, 0, 0, g(Yes, No, false, false, "", "_")},
	{0xfe50, 0, 0, 0, g(Yes, No, false, false, "", ",")},
	{0xfe51, 0, 0, 0, g(Yes, No, false, false, "", "、")},
	{0xfe52, 0, 0, 0, g(Yes, No, false, false, "", ".")},
	{0xfe53, 0, 0, 0, f(Yes, false, "")},
	{0xfe54, 0, 0, 0, g(Yes, No, false, false, "", ";")},
	{0xfe55, 0, 0, 0, g(Yes, No, false, false, "", ":")},
	{0xfe56, 0, 0, 0, g(Yes, No, false, false, "", "?")},
	{0xfe57, 0, 0, 0, g(Yes, No, false, false, "", "!")},
	{0xfe58, 0, 0, 0, g(Yes, No, false, false, "", "—")},
	{0xfe59, 0, 0, 0, g(Yes, No, false, false, "", "(")},
	{0xfe5a, 0, 0, 0, g(Yes, No, false, false, "", ")")},
	{0xfe5b, 0, 0, 0, g(Yes, No, false, false, "", "{")},
	{0xfe5c, 0, 0, 0, g(Yes, No, false, false, "", "}")},
	{0xfe5d, 0, 0, 0, g(Yes, No, false, false, "", "〔")},
	{0xfe5e, 0, 0, 0, g(Yes, No, false, false, "", "〕")},
	{0xfe5f, 0, 0, 0, g(Yes, No, false, false, "", "#")},
	{0xfe60, 0, 0, 0, g(Yes, No, false, false, "", "&")},
	{0xfe61, 0, 0, 0, g(Yes, No, false, false, "", "*")},
	{0xfe62, 0, 0, 0, g(Yes, No, false, false, "", "+")},
	{0xfe63, 0, 0, 0, g(Yes, No, false, false, "", "-")},
	{0xfe64, 0, 0, 0, g(Yes, No, false, false, "", "<")},
	{0xfe65, 0, 0, 0, g(Yes, No, false, false, "", ">")},
	{0xfe66, 0, 0, 0, g(Yes, No, false, false, "", "=")},
	{0xfe67, 0, 0, 0, f(Yes, false, "")},
	{0xfe68, 0, 0, 0, g(Yes, No, false, false, "", "\\")},
	{0xfe69, 0, 0, 0, g(Yes, No, false, false, "", "$")},
	{0xfe6a, 0, 0, 0, g(Yes, No, false, false, "", "%")},
	{0xfe6b, 0, 0, 0, g(Yes, No, false, false, "", "@")},
	{0xfe6c, 0, 0, 0, f(Yes, false, "")},
	{0xfe70, 0, 0, 1, g(Yes, No, false, false, "", " ً")},
	{0xfe71, 0, 0, 1, g(Yes, No, false, false, "", "ـً")},
	{0xfe72, 0, 0, 1, g(Yes, No, false, false, "", " ٌ")},
	{0xfe73, 0, 0, 0, f(Yes, false, "")},
	{0xfe74, 0, 0, 1, g(Yes, No, false, false, "", " ٍ")},
	{0xfe75, 0, 0, 0, f(Yes, false, "")},
	{0xfe76, 0, 0, 1, g(Yes, No, false, false, "", " َ")},
	{0xfe77, 0, 0, 1, g(Yes, No, false, false, "", "ـَ")},
	{0xfe78, 0, 0, 1, g(Yes, No, false, false, "", " ُ")},
	{0xfe79, 0, 0, 1, g(Yes, No, false, false, "", "ـُ")},
	{0xfe7a, 0, 0, 1, g(Yes, No, false, false, "", " ِ")},
	{0xfe7b, 0, 0, 1, g(Yes, No, false, false, "", "ـِ")},
	{0xfe7c, 0, 0, 1, g(Yes, No, false, false, "", " ّ")},
	{0xfe7d, 0, 0, 1, g(Yes, No, false, false, "", "ـّ")},
	{0xfe7e, 0, 0, 1, g(Yes, No, false, false, "", " ْ")},
	{0xfe7f, 0, 0, 1, g(Yes, No, false, false, "", "ـْ")},
	{0xfe80, 0, 0, 0, g(Yes, No, false, false, "", "ء")},
	{0xfe81, 0, 0, 1, g(Yes, No, false, false, "", "آ")},
	{0xfe83, 0, 0, 1, g(Yes, No, false, false, "", "أ")},
	{0xfe85, 0, 0, 1, g(Yes, No, false, false, "", "ؤ")},
	{0xfe87, 0, 0, 1, g(Yes, No, false, false, "", "إ")},
	{0xfe89, 0, 0, 1, g(Yes, No, false, false, "", "ئ")},
	{0xfe8d, 0, 0, 0, g(Yes, No, false, false, "", "ا")},
	{0xfe8f, 0, 0, 0, g(Yes, No, false, false, "", "ب")},
	{0xfe93, 0, 0, 0, g(Yes, No, false, false, "", "ة")},
	{0xfe95, 0, 0, 0, g(Yes, No, false, false, "", "ت")},
	{0xfe99, 0, 0, 0, g(Yes, No, false, false, "", "ث")},
	{0xfe9d, 0, 0, 0, g(Yes, No, false, false, "", "ج")},
	{0xfea1, 0, 0, 0, g(Yes, No, false, false, "", "ح")},
	{0xfea5, 0, 0, 0, g(Yes, No, false, false, "", "خ")},
	{0xfea9, 0, 0, 0, g(Yes, No, false, false, "", "د")},
	{0xfeab, 0, 0, 0, g(Yes, No, false, false, "", "ذ")},
	{0xfead, 0, 0, 0, g(Yes, No, false, false, "", "ر")},
	{0xfeaf, 0, 0, 0, g(Yes, No, false, false, "", "ز")},
	{0xfeb1, 0, 0, 0, g(Yes, No, false, false, "", "س")},
	{0xfeb5, 0, 0, 0, g(Yes, No, false, false, "", "ش")},
	{0xfeb9, 0, 0, 0, g(Yes, No, false, false, "", "ص")},
	{0xfebd, 0, 0, 0, g(Yes, No, false, false, "", "ض")},
	{0xfec1, 0, 0, 0, g(Yes, No, false, false, "", "ط")},
	{0xfec5, 0, 0, 0, g(Yes, No, false, false, "", "ظ")},
	{0xfec9, 0, 0, 0, g(Yes, No, false, false, "", "ع")},
	{0xfecd, 0, 0, 0, g(Yes, No, false, false, "", "غ")},
	{0xfed1, 0, 0, 0, g(Yes, No, false, false, "", "ف")},
	{0xfed5, 0, 0, 0, g(Yes, No, false, false, "", "ق")},
	{0xfed9, 0, 0, 0, g(Yes, No, false, false, "", "ك")},
	{0xfedd, 0, 0, 0, g(Yes, No, false, false, "", "ل")},
	{0xfee1, 0, 0, 0, g(Yes, No, false, false, "", "م")},
	{0xfee5, 0, 0, 0, g(Yes, No, false, false, "", "ن")},
	{0xfee9, 0, 0, 0, g(Yes, No, false, false, "", "ه")},
	{0xfeed, 0, 0, 0, g(Yes, No, false, false, "", "و")},
	{0xfeef, 0, 0, 0, g(Yes, No, false, false, "", "ى")},
	{0xfef1, 0, 0, 0, g(Yes, No, false, false, "", "ي")},
	{0xfef5, 0, 0, 1, g(Yes, No, false, false, "", "لآ")},
	{0xfef7, 0, 0, 1, g(Yes, No, false, false, "", "لأ")},
	{0xfef9, 0, 0, 1, g(Yes, No, false, false, "", "لإ")},
	{0xfefb, 0, 0, 0, g(Yes, No, false, false, "", "لا")},
	{0xfefd, 0, 0, 0, f(Yes, false, "")},
	{0xff01, 0, 0, 0, g(Yes, No, false, false, "", "!")},
	{0xff02, 0, 0, 0, g(Yes, No, false, false, "", "\"")},
	{0xff03, 0, 0, 0, g(Yes, No, false, false, "", "#")},
	{0xff04, 0, 0, 0, g(Yes, No, false, false, "", "$")},
	{0xff05, 0, 0, 0, g(Yes, No, false, false, "", "%")},
	{0xff06, 0, 0, 0, g(Yes, No, false, false, "", "&")},
	{0xff07, 0, 0, 0, g(Yes, No, false, false, "", "'")},
	{0xff08, 0, 0, 0, g(Yes, No, false, false, "", "(")},
	{0xff09, 0, 0, 0, g(Yes, No, false, false, "", ")")},
	{0xff0a, 0, 0, 0, g(Yes, No, false, false, "", "*")},
	{0xff0b, 0, 0, 0, g(Yes, No, false, false, "", "+")},
	{0xff0c, 0, 0, 0, g(Yes, No, false, false, "", ",")},
	{0xff0d, 0, 0, 0, g(Yes, No, false, false, "", "-")},
	{0xff0e, 0, 0, 0, g(Yes, No, false, false, "", ".")},
	{0xff0f, 0, 0, 0, g(Yes, No, false, false, "", "/")},
	{0xff10, 0, 0, 0, g(Yes, No, false, false, "", "0")},
	{0xff11, 0, 0, 0, g(Yes, No, false, false, "", "1")},
	{0xff12, 0, 0, 0, g(Yes, No, false, false, "", "2")},
	{0xff13, 0, 0, 0, g(Yes, No, false, false, "", "3")},
	{0xff14, 0, 0, 0, g(Yes, No, false, false, "", "4")},
	{0xff15, 0, 0, 0, g(Yes, No, false, false, "", "5")},
	{0xff16, 0, 0, 0, g(Yes, No, false, false, "", "6")},
	{0xff17, 0, 0, 0, g(Yes, No, false, false, "", "7")},
	{0xff18, 0, 0, 0, g(Yes, No, false, false, "", "8")},
	{0xff19, 0, 0, 0, g(Yes, No, false, false, "", "9")},
	{0xff1a, 0, 0, 0, g(Yes, No, false, false, "", ":")},
	{0xff1b, 0, 0, 0, g(Yes, No, false, false, "", ";")},
	{0xff1c, 0, 0, 0, g(Yes, No, false, false, "", "<")},
	{0xff1d, 0, 0, 0, g(Yes, No, false, false, "", "=")},
	{0xff1e, 0, 0, 0, g(Yes, No, false, false, "", ">")},
	{0xff1f, 0, 0, 0, g(Yes, No, false, false, "", "?")},
	{0xff20, 0, 0, 0, g(Yes, No, false, false, "", "@")},
	{0xff21, 0, 0, 0, g(Yes, No, false, false, "", "A")},
	{0xff22, 0, 0, 0, g(Yes, No, false, false, "", "B")},
	{0xff23, 0, 0, 0, g(Yes, No, false, false, "", "C")},
	{0xff24, 0, 0, 0, g(Yes, No, false, false, "", "D")},
	{0xff25, 0, 0, 0, g(Yes, No, false, false, "", "E")},
	{0xff26, 0, 0, 0, g(Yes, No, false, false, "", "F")},
	{0xff27, 0, 0, 0, g(Yes, No, false, false, "", "G")},
	{0xff28, 0, 0, 0, g(Yes, No, false, false, "", "H")},
	{0xff29, 0, 0, 0, g(Yes, No, false, false, "", "I")},
	{0xff2a, 0, 0, 0, g(Yes, No, false, false, "", "J")},
	{0xff2b, 0, 0, 0, g(Yes, No, false, false, "", "K")},
	{0xff2c, 0, 0, 0, g(Yes, No, false, false, "", "L")},
	{0xff2d, 0, 0, 0, g(Yes, No, false, false, "", "M")},
	{0xff2e, 0, 0, 0, g(Yes, No, false, false, "", "N")},
	{0xff2f, 0, 0, 0, g(Yes, No, false, false, "", "O")},
	{0xff30, 0, 0, 0, g(Yes, No, false, false, "", "P")},
	{0xff31, 0, 0, 0, g(Yes, No, false, false, "", "Q")},
	{0xff32, 0, 0, 0, g(Yes, No, false, false, "", "R")},
	{0xff33, 0, 0, 0, g(Yes, No, false, false, "", "S")},
	{0xff34, 0, 0, 0, g(Yes, No, false, false, "", "T")},
	{0xff35, 0, 0, 0, g(Yes, No, false, false, "", "U")},
	{0xff36, 0, 0, 0, g(Yes, No, false, false, "", "V")},
	{0xff37, 0, 0, 0, g(Yes, No, false, false, "", "W")},
	{0xff38, 0, 0, 0, g(Yes, No, false, false, "", "X")},
	{0xff39, 0, 0, 0, g(Yes, No, false, false, "", "Y")},
	{0xff3a, 0, 0, 0, g(Yes, No, false, false, "", "Z")},
	{0xff3b, 0, 0, 0, g(Yes, No, false, false, "", "[")},
	{0xff3c, 0, 0, 0, g(Yes, No, false, false, "", "\\")},
	{0xff3d, 0, 0, 0, g(Yes, No, false, false, "", "]")},
	{0xff3e, 0, 0, 0, g(Yes, No, false, false, "", "^")},
	{0xff3f, 0, 0, 0, g(Yes, No, false, false, "", "_")},
	{0xff40, 0, 0, 0, g(Yes, No, false, false, "", "`")},
	{0xff41, 0, 0, 0, g(Yes, No, false, false, "", "a")},
	{0xff42, 0, 0, 0, g(Yes, No, false, false, "", "b")},
	{0xff43, 0, 0, 0, g(Yes, No, false, false, "", "c")},
	{0xff44, 0, 0, 0, g(Yes, No, false, false, "", "d")},
	{0xff45, 0, 0, 0, g(Yes, No, false, false, "", "e")},
	{0xff46, 0, 0, 0, g(Yes, No, false, false, "", "f")},
	{0xff47, 0, 0, 0, g(Yes, No, false, false, "", "g")},
	{0xff48, 0, 0, 0, g(Yes, No, false, false, "", "h")},
	{0xff49, 0, 0, 0, g(Yes, No, false, false, "", "i")},
	{0xff4a, 0, 0, 0, g(Yes, No, false, false, "", "j")},
	{0xff4b, 0, 0, 0, g(Yes, No, false, false, "", "k")},
	{0xff4c, 0, 0, 0, g(Yes, No, false, false, "", "l")},
	{0xff4d, 0, 0, 0, g(Yes, No, false, false, "", "m")},
	{0xff4e, 0, 0, 0, g(Yes, No, false, false, "", "n")},
	{0xff4f, 0, 0, 0, g(Yes, No, false, false, "", "o")},
	{0xff50, 0, 0, 0, g(Yes, No, false, false, "", "p")},
	{0xff51, 0, 0, 0, g(Yes, No, false, false, "", "q")},
	{0xff52, 0, 0, 0, g(Yes, No, false, false, "", "r")},
	{0xff53, 0, 0, 0, g(Yes, No, false, false, "", "s")},
	{0xff54, 0, 0, 0, g(Yes, No, false, false, "", "t")},
	{0xff55, 0, 0, 0, g(Yes, No, false, false, "", "u")},
	{0xff56, 0, 0, 0, g(Yes, No, false, false, "", "v")},
	{0xff57, 0, 0, 0, g(Yes, No, false, false, "", "w")},
	{0xff58, 0, 0, 0, g(Yes, No, false, false, "", "x")},
	{0xff59, 0, 0, 0, g(Yes, No, false, false, "", "y")},
	{0xff5a, 0, 0, 0, g(Yes, No, false, false, "", "z")},
	{0xff5b, 0, 0, 0, g(Yes, No, false, false, "", "{")},
	{0xff5c, 0, 0, 0, g(Yes, No, false, false, "", "|")},
	{0xff5d, 0, 0, 0, g(Yes, No, false, false, "", "}")},
	{0xff5e, 0, 0, 0, g(Yes, No, false, false, "", "~")},
	{0xff5f, 0, 0, 0, g(Yes, No, false, false, "", "⦅")},
	{0xff60, 0, 0, 0, g(Yes, No, false, false, "", "⦆")},
	{0xff61, 0, 0, 0, g(Yes, No, false, false, "", "。")},
	{0xff62, 0, 0, 0, g(Yes, No, false, false, "", "「")},
	{0xff63, 0, 0, 0, g(Yes, No, false, false, "", "」")},
	{0xff64, 0, 0, 0, g(Yes, No, false, false, "", "、")},
	{0xff65, 0, 0, 0, g(Yes, No, false, false, "", "・")},
	{0xff66, 0, 0, 0, g(Yes, No, false, false, "", "ヲ")},
	{0xff67, 0, 0, 0, g(Yes, No, false, false, "", "ァ")},
	{0xff68, 0, 0, 0, g(Yes, No, false, false, "", "ィ")},
	{0xff69, 0, 0, 0, g(Yes, No, false, false, "", "ゥ")},
	{0xff6a, 0, 0, 0, g(Yes, No, false, false, "", "ェ")},
	{0xff6b, 0, 0, 0, g(Yes, No, false, false, "", "ォ")},
	{0xff6c, 0, 0, 0, g(Yes, No, false, false, "", "ャ")},
	{0xff6d, 0, 0, 0, g(Yes, No, false, false, "", "ュ")},
	{0xff6e, 0, 0, 0, g(Yes, No, false, false, "", "ョ")},
	{0xff6f, 0, 0, 0, g(Yes, No, false, false, "", "ッ")},
	{0xff70, 0, 0, 0, g(Yes, No, false, false, "", "ー")},
	{0xff71, 0, 0, 0, g(Yes, No, false, false, "", "ア")},
	{0xff72, 0, 0, 0, g(Yes, No, false, false, "", "イ")},
	{0xff73, 0, 0, 0, g(Yes, No, false, false, "", "ウ")},
	{0xff74, 0, 0, 0, g(Yes, No, false, false, "", "エ")},
	{0xff75, 0, 0, 0, g(Yes, No, false, false, "", "オ")},
	{0xff76, 0, 0, 0, g(Yes, No, false, false, "", "カ")},
	{0xff77, 0, 0, 0, g(Yes, No, false, false, "", "キ")},
	{0xff78, 0, 0, 0, g(Yes, No, false, false, "", "ク")},
	{0xff79, 0, 0, 0, g(Yes, No, false, false, "", "ケ")},
	{0xff7a, 0, 0, 0, g(Yes, No, false, false, "", "コ")},
	{0xff7b, 0, 0, 0, g(Yes, No, false, false, "", "サ")},
	{0xff7c, 0, 0, 0, g(Yes, No, false, false, "", "シ")},
	{0xff7d, 0, 0, 0, g(Yes, No, false, false, "", "ス")},
	{0xff7e, 0, 0, 0, g(Yes, No, false, false, "", "セ")},
	{0xff7f, 0, 0, 0, g(Yes, No, false, false, "", "ソ")},
	{0xff80, 0, 0, 0, g(Yes, No, false, false, "", "タ")},
	{0xff81, 0, 0, 0, g(Yes, No, false, false, "", "チ")},
	{0xff82, 0, 0, 0, g(Yes, No, false, false, "", "ツ")},
	{0xff83, 0, 0, 0, g(Yes, No, false, false, "", "テ")},
	{0xff84, 0, 0, 0, g(Yes, No, false, false, "", "ト")},
	{0xff85, 0, 0, 0, g(Yes, No, false, false, "", "ナ")},
	{0xff86, 0, 0, 0, g(Yes, No, false, false, "", "ニ")},
	{0xff87, 0, 0, 0, g(Yes, No, false, false, "", "ヌ")},
	{0xff88, 0, 0, 0, g(Yes, No, false, false, "", "ネ")},
	{0xff89, 0, 0, 0, g(Yes, No, false, false, "", "ノ")},
	{0xff8a, 0, 0, 0, g(Yes, No, false, false, "", "ハ")},
	{0xff8b, 0, 0, 0, g(Yes, No, false, false, "", "ヒ")},
	{0xff8c, 0, 0, 0, g(Yes, No, false, false, "", "フ")},
	{0xff8d, 0, 0, 0, g(Yes, No, false, false, "", "ヘ")},
	{0xff8e, 0, 0, 0, g(Yes, No, false, false, "", "ホ")},
	{0xff8f, 0, 0, 0, g(Yes, No, false, false, "", "マ")},
	{0xff90, 0, 0, 0, g(Yes, No, false, false, "", "ミ")},
	{0xff91, 0, 0, 0, g(Yes, No, false, false, "", "ム")},
	{0xff92, 0, 0, 0, g(Yes, No, false, false, "", "メ")},
	{0xff93, 0, 0, 0, g(Yes, No, false, false, "", "モ")},
	{0xff94, 0, 0, 0, g(Yes, No, false, false, "", "ヤ")},
	{0xff95, 0, 0, 0, g(Yes, No, false, false, "", "ユ")},
	{0xff96, 0, 0, 0, g(Yes, No, false, false, "", "ヨ")},
	{0xff97, 0, 0, 0, g(Yes, No, false, false, "", "ラ")},
	{0xff98, 0, 0, 0, g(Yes, No, false, false, "", "リ")},
	{0xff99, 0, 0, 0, g(Yes, No, false, false, "", "ル")},
	{0xff9a, 0, 0, 0, g(Yes, No, false, false, "", "レ")},
	{0xff9b, 0, 0, 0, g(Yes, No, false, false, "", "ロ")},
	{0xff9c, 0, 0, 0, g(Yes, No, false, false, "", "ワ")},
	{0xff9d, 0, 0, 0, g(Yes, No, false, false, "", "ン")},
	{0xff9e, 0, 1, 1, g(Yes, No, false, false, "", "゙")},
	{0xff9f, 0, 1, 1, g(Yes, No, false, false, "", "゚")},
	{0xffa0, 0, 0, 0, g(Yes, No, false, false, "", "ᅠ")},
	{0xffa1, 0, 0, 0, g(Yes, No, false, false, "", "ᄀ")},
	{0xffa2, 0, 0, 0, g(Yes, No, false, false, "", "ᄁ")},
	{0xffa3, 0, 1, 1, g(Yes, No, false, false, "", "ᆪ")},
	{0xffa4, 0, 0, 0, g(Yes, No, false, false, "", "ᄂ")},
	{0xffa5, 0, 1, 1, g(Yes, No, false, false, "", "ᆬ")},
	{0xffa6, 0, 1, 1, g(Yes, No, false, false, "", "ᆭ")},
	{0xffa7, 0, 0, 0, g(Yes, No, false, false, "", "ᄃ")},
	{0xffa8, 0, 0, 0, g(Yes, No, false, false, "", "ᄄ")},
	{0xffa9, 0, 0, 0, g(Yes, No, false, false, "", "ᄅ")},
	{0xffaa, 0, 1, 1, g(Yes, No, false, false, "", "ᆰ")},
	{0xffab, 0, 1, 1, g(Yes, No, false, false, "", "ᆱ")},
	{0xffac, 0, 1, 1, g(Yes, No, false, false, "", "ᆲ")},
	{0xffad, 0, 1, 1, g(Yes, No, false, false, "", "ᆳ")},
	{0xffae, 0, 1, 1, g(Yes, No, false, false, "", "ᆴ")},
	{0xffaf, 0, 1, 1, g(Yes, No, false, false, "", "ᆵ")},
	{0xffb0, 0, 0, 0, g(Yes, No, false, false, "", "ᄚ")},
	{0xffb1, 0, 0, 0, g(Yes, No, false, false, "", "ᄆ")},
	{0xffb2, 0, 0, 0, g(Yes, No, false, false, "", "ᄇ")},
	{0xffb3, 0, 0, 0, g(Yes, No, false, false, "", "ᄈ")},
	{0xffb4, 0, 0, 0, g(Yes, No, false, false, "", "ᄡ")},
	{0xffb5, 0, 0, 0, g(Yes, No, false, false, "", "ᄉ")},
	{0xffb6, 0, 0, 0, g(Yes, No, false, false, "", "ᄊ")},
	{0xffb7, 0, 0, 0, g(Yes, No, false, false, "", "ᄋ")},
	{0xffb8, 0, 0, 0, g(Yes, No, false, false, "", "ᄌ")},
	{0xffb9, 0, 0, 0, g(Yes, No, false, false, "", "ᄍ")},
	{0xffba, 0, 0, 0, g(Yes, No, false, false, "", "ᄎ")},
	{0xffbb, 0, 0, 0, g(Yes, No, false, false, "", "ᄏ")},
	{0xffbc, 0, 0, 0, g(Yes, No, false, false, "", "ᄐ")},
	{0xffbd, 0, 0, 0, g(Yes, No, false, false, "", "ᄑ")},
	{0xffbe, 0, 0, 0, g(Yes, No, false, false, "", "ᄒ")},
	{0xffbf, 0, 0, 0, f(Yes, false, "")},
	{0xffc2, 0, 1, 1, g(Yes, No, false, false, "", "ᅡ")},
	{0xffc3, 0, 1, 1, g(Yes, No, false, false, "", "ᅢ")},
	{0xffc4, 0, 1, 1, g(Yes, No, false, false, "", "ᅣ")},
	{0xffc5, 0, 1, 1, g(Yes, No, false, false, "", "ᅤ")},
	{0xffc6, 0, 1, 1, g(Yes, No, false, false, "", "ᅥ")},
	{0xffc7, 0, 1, 1, g(Yes, No, false, false, "", "ᅦ")},
	{0xffc8, 0, 0, 0, f(Yes, false, "")},
	{0xffca, 0, 1, 1, g(Yes, No, false, false, "", "ᅧ")},
	{0xffcb, 0, 1, 1, g(Yes, No, false, false, "", "ᅨ")},
	{0xffcc, 0, 1, 1, g(Yes, No, false, false, "", "ᅩ")},
	{0xffcd, 0, 1, 1, g(Yes, No, false, false, "", "ᅪ")},
	{0xffce, 0, 1, 1, g(Yes, No, false, false, "", "ᅫ")},
	{0xffcf, 0, 1, 1, g(Yes, No, false, false, "", "ᅬ")},
	{0xffd0, 0, 0, 0, f(Yes, false, "")},
	{0xffd2, 0, 1, 1, g(Yes, No, false, false, "", "ᅭ")},
	{0xffd3, 0, 1, 1, g(Yes, No, false, false, "", "ᅮ")},
	{0xffd4, 0, 1, 1, g(Yes, No, false, false, "", "ᅯ")},
	{0xffd5, 0, 1, 1, g(Yes, No, false, false, "", "ᅰ")},
	{0xffd6, 0, 1, 1, g(Yes, No, false, false, "", "ᅱ")},
	{0xffd7, 0, 1, 1, g(Yes, No, false, false, "", "ᅲ")},
	{0xffd8, 0, 0, 0, f(Yes, false, "")},
	{0xffda, 0, 1, 1, g(Yes, No, false, false, "", "ᅳ")},
	{0xffdb, 0, 1, 1, g(Yes, No, false, false, "", "ᅴ")},
	{0xffdc, 0, 1, 1, g(Yes, No, false, false, "", "ᅵ")},
	{0xffdd, 0, 0, 0, f(Yes, false, "")},
	{0xffe0, 0, 0, 0, g(Yes, No, false, false, "", "¢")},
	{0xffe1, 0, 0, 0, g(Yes, No, false, false, "", "£")},
	{0xffe2, 0, 0, 0, g(Yes, No, false, false, "", "¬")},
	{0xffe3, 0, 0, 1, g(Yes, No, false, false, "", " ̄")},
	{0xffe4, 0, 0, 0, g(Yes, No, false, false, "", "¦")},
	{0xffe5, 0, 0, 0, g(Yes, No, false, false, "", "¥")},
	{0xffe6, 0, 0, 0, g(Yes, No, false, false, "", "₩")},
	{0xffe7, 0, 0, 0, f(Yes, false, "")},
	{0xffe8, 0, 0, 0, g(Yes, No, false, false, "", "│")},
	{0xffe9, 0, 0, 0, g(Yes, No, false, false, "", "←")},
	{0xffea, 0, 0, 0, g(Yes, No, false, false, "", "↑")},
	{0xffeb, 0, 0, 0, g(Yes, No, false, false, "", "→")},
	{0xffec, 0, 0, 0, g(Yes, No, false, false, "", "↓")},
	{0xffed, 0, 0, 0, g(Yes, No, false, false, "", "■")},
	{0xffee, 0, 0, 0, g(Yes, No, false, false, "", "○")},
	{0xffef, 0, 0, 0, f(Yes, false, "")},
	{0x101fd, 220, 1, 1, f(Yes, false, "")},
	{0x101fe, 0, 0, 0, f(Yes, false, "")},
	{0x102e0, 220, 1, 1, f(Yes, false, "")},
	{0x102e1, 0, 0, 0, f(Yes, false, "")},
	{0x10376, 230, 1, 1, f(Yes, false, "")},
	{0x1037b, 0, 0, 0, f(Yes, false, "")},
	{0x10a0d, 220, 1, 1, f(Yes, false, "")},
	{0x10a0e, 0, 0, 0, f(Yes, false, "")},
	{0x10a0f, 230, 1, 1, f(Yes, false, "")},
	{0x10a10, 0, 0, 0, f(Yes, false, "")},
	{0x10a38, 230, 1, 1, f(Yes, false, "")},
	{0x10a39, 1, 1, 1, f(Yes, false, "")},
	{0x10a3a, 220, 1, 1, f(Yes, false, "")},
	{0x10a3b, 0, 0, 0, f(Yes, false, "")},
	{0x10a3f, 9, 1, 1, f(Yes, false, "")},
	{0x10a40, 0, 0, 0, f(Yes, false, "")},
	{0x10ae5, 230, 1, 1, f(Yes, false, "")},
	{0x10ae6, 220, 1, 1, f(Yes, false, "")},
	{0x10ae7, 0, 0, 0, f(Yes, false, "")},
	{0x11046, 9, 1, 1, f(Yes, false, "")},
	{0x11047, 0, 0, 0, f(Yes, false, "")},
	{0x1107f, 9, 1, 1, f(Yes, false, "")},
	{0x11080, 0, 0, 0, f(Yes, false, "")},
	{0x11099, 0, 0, 0, f(Yes, true, "")},
	{0x1109a, 0, 0, 1, f(Yes, false, "𑂚")},
	{0x1109b, 0, 0, 0, f(Yes, true, "")},
	{0x1109c, 0, 0, 1, f(Yes, false, "𑂜")},
	{0x1109d, 0, 0, 0, f(Yes, false, "")},
	{0x110a5, 0, 0, 0, f(Yes, true, "")},
	{0x110a6, 0, 0, 0, f(Yes, false, "")},
	{0x110ab, 0, 0, 1, f(Yes, false, "𑂫")},
	{0x110ac, 0, 0, 0, f(Yes, false, "")},
	{0x110b9, 9, 1, 1, f(Yes, false, "")},
	{0x110ba, 7, 1, 1, f(Maybe, false, "")},
	{0x110bb, 0, 0, 0, f(Yes, false, "")},
	{0x11100, 230, 1, 1, f(Yes, false, "")},
	{0x11103, 0, 0, 0, f(Yes, false, "")},
	{0x11127, 0, 1, 1, f(Maybe, false, "")},
	{0x11128, 0, 0, 0, f(Yes, false, "")},
	{0x1112e, 0, 0, 1, f(Yes, false, "𑄮")},
	{0x1112f, 0, 0, 1, f(Yes, false, "𑄯")},
	{0x11130, 0, 0, 0, f(Yes, false, "")},
	{0x11131, 0, 0, 0, f(Yes, true, "")},
	{0x11133, 9, 1, 1, f(Yes, false, "")},
	{0x11135, 0, 0, 0, f(Yes, false, "")},
	{0x11173, 7, 1, 1, f(Yes, false, "")},
	{0x11174, 0, 0, 0, f(Yes, false, "")},
	{0x111c0, 9, 1, 1, f(Yes, false, "")},
	{0x111c1, 0, 0, 0, f(Yes, false, "")},
	{0x111ca, 7, 1, 1, f(Yes, false, "")},
	{0x111cb, 0, 0, 0, f(Yes, false, "")},
	{0x11235, 9, 1, 1, f(Yes, false, "")},
	{0x11236, 7, 1, 1, f(Yes, false, "")},
	{0x11237, 0, 0, 0, f(Yes, false, "")},
	{0x112e9, 7, 1, 1, f(Yes, false, "")},
	{0x112ea, 9, 1, 1, f(Yes, false, "")},
	{0x112eb, 0, 0, 0, f(Yes, false, "")},
	{0x1133c, 7, 1, 1, f(Yes, false, "")},
	{0x1133d, 0, 0, 0, f(Yes, false, "")},
	{0x1133e, 0, 1, 1, f(Maybe, false, "")},
	{0x1133f, 0, 0, 0, f(Yes, false, "")},
	{0x11347, 0, 0, 0, f(Yes, true, "")},
	{0x11348, 0, 0, 0, f(Yes, false, "")},
	{0x1134b, 0, 0, 1, f(Yes, false, "𑍋")},
	{0x1134c, 0, 0, 1, f(Yes, false, "𑍌")},
	{0x1134d, 9, 1, 1, f(Yes, false, "")},
	{0x1134e, 0, 0, 0, f(Yes, false, "")},
	{0x11357, 0, 1, 1, f(Maybe, false, "")},
	{0x11358, 0, 0, 0, f(Yes, false, "")},
	{0x11366, 230, 1, 1, f(Yes, false, "")},
	{0x1136d, 0, 0, 0, f(Yes, false, "")},
	{0x11370, 230, 1, 1, f(Yes, false, "")},
	{0x11375, 0, 0, 0, f(Yes, false, "")},
	{0x11442, 9, 1, 1, f(Yes, false, "")},
	{0x11443, 0, 0, 0, f(Yes, false, "")},
	{0x11446, 7, 1, 1, f(Yes, false, "")},
	{0x11447, 0, 0, 0, f(Yes, false, "")},
	{0x114b0, 0, 1, 1, f(Maybe, false, "")},
	{0x114b1, 0, 0, 0, f(Yes, false, "")},
	{0x114b9, 0, 0, 0, f(Yes, true, "")},
	{0x114ba, 0, 1, 1, f(Maybe, false, "")},
	{0x114bb, 0, 0, 1, f(Yes, false, "𑒻")},
	{0x114bc, 0, 0, 1, f(Yes, false, "𑒼")},
	{0x114bd, 0, 1, 1, f(Maybe, false, "")},
	{0x114be, 0, 0, 1, f(Yes, false, "𑒾")},
	{0x114bf, 0, 0, 0, f(Yes, false, "")},
	{0x114c2, 9, 1, 1, f(Yes, false, "")},
	{0x114c3, 7, 1, 1, f(Yes, false, "")},
	{0x114c4, 0, 0, 0, f(Yes, false, "")},
	{0x115af, 0, 1, 1, f(Maybe, false, "")},
	{0x115b0, 0, 0, 0, f(Yes, false, "")},
	{0x115b8, 0, 0, 0, f(Yes, true, "")},
	{0x115ba, 0, 0, 1, f(Yes, false, "𑖺")},
	{0x115bb, 0, 0, 1, f(Yes, false, "𑖻")},
	{0x115bc, 0, 0, 0, f(Yes, false, "")},
	{0x115bf, 9, 1, 1, f(Yes, false, "")},
	{0x115c0, 7, 1, 1, f(Yes, false, "")},
	{0x115c1, 0, 0, 0, f(Yes, false, "")},
	{0x1163f, 9, 1, 1, f(Yes, false, "")},
	{0x11640, 0, 0, 0, f(Yes, false, "")},
	{0x116b6, 9, 1, 1, f(Yes, false, "")},
	{0x116b7, 7, 1, 1, f(Yes, false, "")},
	{0x116b8, 0, 0, 0, f(Yes, false, "")},
	{0x1172b, 9, 1, 1, f(Yes, false, "")},
	{0x1172c, 0, 0, 0, f(Yes, false, "")},
	{0x11a34, 9, 1, 1, f(Yes, false, "")},
	{0x11a35, 0, 0, 0, f(Yes, false, "")},
	{0x11a47, 9, 1, 1, f(Yes, false, "")},
	{0x11a48, 0, 0, 0, f(Yes, false, "")},
	{0x11a99, 9, 1, 1, f(Yes, false, "")},
	{0x11a9a, 0, 0, 0, f(Yes, false, "")},
	{0x11c3f, 9, 1, 1, f(Yes, false, "")},
	{0x11c40, 0, 0, 0, f(Yes, false, "")},
	{0x11d42, 7, 1, 1, f(Yes, false, "")},
	{0x11d43, 0, 0, 0, f(Yes, false, "")},
	{0x11d44, 9, 1, 1, f(Yes, false, "")},
	{0x11d46, 0, 0, 0, f(Yes, false, "")},
	{0x16af0, 1, 1, 1, f(Yes, false, "")},
	{0x16af5, 0, 0, 0, f(Yes, false, "")},
	{0x16b30, 230, 1, 1, f(Yes, false, "")},
	{0x16b37, 0, 0, 0, f(Yes, false, "")},
	{0x1bc9e, 1, 1, 1, f(Yes, false, "")},
	{0x1bc9f, 0, 0, 0, f(Yes, false, "")},
	{0x1d15e, 0, 0, 1, f(No, false, "𝅗𝅥")},
	{0x1d15f, 0, 0, 1, f(No, false, "𝅘𝅥")},
	{0x1d160, 0, 0, 2, f(No, false, "𝅘𝅥𝅮")},
	{0x1d161, 0, 0, 2, f(No, false, "𝅘𝅥𝅯")},
	{0x1d162, 0, 0, 2, f(No, false, "𝅘𝅥𝅰")},
	{0x1d163, 0, 0, 2, f(No, false, "𝅘𝅥𝅱")},
	{0x1d164, 0, 0, 2, f(No, false, "𝅘𝅥𝅲")},
	{0x1d165, 216, 1, 1, f(Yes, false, "")},
	{0x1d167, 1, 1, 1, f(Yes, false, "")},
	{0x1d16a, 0, 0, 0, f(Yes, false, "")},
	{0x1d16d, 226, 1, 1, f(Yes, false, "")},
	{0x1d16e, 216, 1, 1, f(Yes, false, "")},
	{0x1d173, 0, 0, 0, f(Yes, false, "")},
	{0x1d17b, 220, 1, 1, f(Yes, false, "")},
	{0x1d183, 0, 0, 0, f(Yes, false, "")},
	{0x1d185, 230, 1, 1, f(Yes, false, "")},
	{0x1d18a, 220, 1, 1, f(Yes, false, "")},
	{0x1d18c, 0, 0, 0, f(Yes, false, "")},
	{0x1d1aa, 230, 1, 1, f(Yes, false, "")},
	{0x1d1ae, 0, 0, 0, f(Yes, false, "")},
	{0x1d1bb, 0, 0, 1, f(No, false, "𝆹𝅥")},
	{0x1d1bc, 0, 0, 1, f(No, false, "𝆺𝅥")},
	{0x1d1bd, 0, 0, 2, f(No, false, "𝆹𝅥𝅮")},
	{0x1d1be, 0, 0, 2, f(No, false, "𝆺𝅥𝅮")},
	{0x1d1bf, 0, 0, 2, f(No, false, "𝆹𝅥𝅯")},
	{0x1d1c0, 0, 0, 2, f(No, false, "𝆺𝅥𝅯")},
	{0x1d1c1, 0, 0, 0, f(Yes, false, "")},
	{0x1d242, 230, 1, 1, f(Yes, false, "")},
	{0x1d245, 0, 0, 0, f(Yes, false, "")},
	{0x1d400, 0, 0, 0, g(Yes, No, false, false, "", "A")},
	{0x1d401, 0, 0, 0, g(Yes, No, false, false, "", "B")},
	{0x1d402, 0, 0, 0, g(Yes, No, false, false, "", "C")},
	{0x1d403, 0, 0, 0, g(Yes, No, false, false, "", "D")},
	{0x1d404, 0, 0, 0, g(Yes, No, false, false, "", "E")},
	{0x1d405, 0, 0, 0, g(Yes, No, false, false, "", "F")},
	{0x1d406, 0, 0, 0, g(Yes, No, false, false, "", "G")},
	{0x1d407, 0, 0, 0, g(Yes, No, false, false, "", "H")},
	{0x1d408, 0, 0, 0, g(Yes, No, false, false, "", "I")},
	{0x1d409, 0, 0, 0, g(Yes, No, false, false, "", "J")},
	{0x1d40a, 0, 0, 0, g(Yes, No, false, false, "", "K")},
	{0x1d40b, 0, 0, 0, g(Yes, No, false, false, "", "L")},
	{0x1d40c, 0, 0, 0, g(Yes, No, false, false, "", "M")},
	{0x1d40d, 0, 0, 0, g(Yes, No, false, false, "", "N")},
	{0x1d40e, 0, 0, 0, g(Yes, No, false, false, "", "O")},
	{0x1d40f, 0, 0, 0, g(Yes, No, false, false, "", "P")},
	{0x1d410, 0, 0, 0, g(Yes, No, false, false, "", "Q")},
	{0x1d411, 0, 0, 0, g(Yes, No, false, false, "", "R")},
	{0x1d412, 0, 0, 0, g(Yes, No, false, false, "", "S")},
	{0x1d413, 0, 0, 0, g(Yes, No, false, false, "", "T")},
	{0x1d414, 0, 0, 0, g(Yes, No, false, false, "", "U")},
	{0x1d415, 0, 0, 0, g(Yes, No, false, false, "", "V")},
	{0x1d416, 0, 0, 0, g(Yes, No, false, false, "", "W")},
	{0x1d417, 0, 0, 0, g(Yes, No, false, false, "", "X")},
	{0x1d418, 0, 0, 0, g(Yes, No, false, false, "", "Y")},
	{0x1d419, 0, 0, 0, g(Yes, No, false, false, "", "Z")},
	{0x1d41a, 0, 0, 0, g(Yes, No, false, false, "", "a")},
	{0x1d41b, 0, 0, 0, g(Yes, No, false, false, "", "b")},
	{0x1d41c, 0, 0, 0, g(Yes, No, false, false, "", "c")},
	{0x1d41d, 0, 0, 0, g(Yes, No, false, false, "", "d")},
	{0x1d41e, 0, 0, 0, g(Yes, No, false, false, "", "e")},
	{0x1d41f, 0, 0, 0, g(Yes, No, false, false, "", "f")},
	{0x1d420, 0, 0, 0, g(Yes, No, false, false, "", "g")},
	{0x1d421, 0, 0, 0, g(Yes, No, false, false, "", "h")},
	{0x1d422, 0, 0, 0, g(Yes, No, false, false, "", "i")},
	{0x1d423, 0, 0, 0, g(Yes, No, false, false, "", "j")},
	{0x1d424, 0, 0, 0, g(Yes, No, false, false, "", "k")},
	{0x1d425, 0, 0, 0, g(Yes, No, false, false, "", "l")},
	{0x1d426, 0, 0, 0, g(Yes, No, false, false, "", "m")},
	{0x1d427, 0, 0, 0, g(Yes, No, false, false, "", "n")},
	{0x1d428, 0, 0, 0, g(Yes, No, false, false, "", "o")},
	{0x1d429, 0, 0, 0, g(Yes, No, false, false, "", "p")},
	{0x1d42a, 0, 0, 0, g(Yes, No, false, false, "", "q")},
	{0x1d42b, 0, 0, 0, g(Yes, No, false, false, "", "r")},
	{0x1d42c, 0, 0, 0, g(Yes, No, false, false, "", "s")},
	{0x1d42d, 0, 0, 0, g(Yes, No, false, false, "", "t")},
	{0x1d42e, 0, 0, 0, g(Yes, No, false, false, "", "u")},
	{0x1d42f, 0, 0, 0, g(Yes, No, false, false, "", "v")},
	{0x1d430, 0, 0, 0, g(Yes, No, false, false, "", "w")},
	{0x1d431, 0, 0, 0, g(Yes, No, false, false, "", "x")},
	{0x1d432, 0, 0, 0, g(Yes, No, false, false, "", "y")},
	{0x1d433, 0, 0, 0, g(Yes, No, false, false, "", "z")},
	{0x1d434, 0, 0, 0, g(Yes, No, false, false, "", "A")},
	{0x1d435, 0, 0, 0, g(Yes, No, false, false, "", "B")},
	{0x1d436, 0, 0, 0, g(Yes, No, false, false, "", "C")},
	{0x1d437, 0, 0, 0, g(Yes, No, false, false, "", "D")},
	{0x1d438, 0, 0, 0, g(Yes, No, false, false, "", "E")},
	{0x1d439, 0, 0, 0, g(Yes, No, false, false, "", "F")},
	{0x1d43a, 0, 0, 0, g(Yes, No, false, false, "", "G")},
	{0x1d43b, 0, 0, 0, g(Yes, No, false, false, "", "H")},
	{0x1d43c, 0, 0, 0, g(Yes, No, false, false, "", "I")},
	{0x1d43d, 0, 0, 0, g(Yes, No, false, false, "", "J")},
	{0x1d43e, 0, 0, 0, g(Yes, No, false, false, "", "K")},
	{0x1d43f, 0, 0, 0, g(Yes, No, false, false, "", "L")},
	{0x1d440, 0, 0, 0, g(Yes, No, false, false, "", "M")},
	{0x1d441, 0, 0, 0, g(Yes, No, false, false, "", "N")},
	{0x1d442, 0, 0, 0, g(Yes, No, false, false, "", "O")},
	{0x1d443, 0, 0, 0, g(Yes, No, false, false, "", "P")},
	{0x1d444, 0, 0, 0, g(Yes, No, false, false, "", "Q")},
	{0x1d445, 0, 0, 0, g(Yes, No, false, false, "", "R")},
	{0x1d446, 0, 0, 0, g(Yes, No, false, false, "", "S")},
	{0x1d447, 0, 0, 0, g(Yes, No, false, false, "", "T")},
	{0x1d448, 0, 0, 0, g(Yes, No, false, false, "", "U")},
	{0x1d449, 0, 0, 0, g(Yes, No, false, false, "", "V")},
	{0x1d44a, 0, 0, 0, g(Yes, No, false, false, "", "W")},
	{0x1d44b, 0, 0, 0, g(Yes, No, false, false, "", "X")},
	{0x1d44c, 0, 0, 0, g(Yes, No, false, false, "", "Y")},
	{0x1d44d, 0, 0, 0, g(Yes, No, false, false, "", "Z")},
	{0x1d44e, 0, 0, 0, g(Yes, No, false, false, "", "a")},
	{0x1d44f, 0, 0, 0, g(Yes, No, false, false, "", "b")},
	{0x1d450, 0, 0, 0, g(Yes, No, false, false, "", "c")},
	{0x1d451, 0, 0, 0, g(Yes, No, false, false, "", "d")},
	{0x1d452, 0, 0, 0, g(Yes, No, false, false, "", "e")},
	{0x1d453, 0, 0, 0, g(Yes, No, false, false, "", "f")},
	{0x1d454, 0, 0, 0, g(Yes, No, false, false, "", "g")},
	{0x1d455, 0, 0, 0, f(Yes, false, "")},
	{0x1d456, 0, 0, 0, g(Yes, No, false, false, "", "i")},
	{0x1d457, 0, 0, 0, g(Yes, No, false, false, "", "j")},
	{0x1d458, 0, 0, 0, g(Yes, No, false, false, "", "k")},
	{0x1d459, 0, 0, 0, g(Yes, No, false, false, "", "l")},
	{0x1d45a, 0, 0, 0, g(Yes, No, false, false, "", "m")},
	{0x1d45b, 0, 0, 0, g(Yes, No, false, false, "", "n")},
	{0x1d45c, 0, 0, 0, g(Yes, No, false, false, "", "o")},
	{0x1d45d, 0, 0, 0, g(Yes, No, false, false, "", "p")},
	{0x1d45e, 0, 0, 0, g(Yes, No, false, false, "", "q")},
	{0x1d45f, 0, 0, 0, g(Yes, No, false, false, "", "r")},
	{0x1d460, 0, 0, 0, g(Yes, No, false, false, "", "s")},
	{0x1d461, 0, 0, 0, g(Yes, No, false, false, "", "t")},
	{0x1d462, 0, 0, 0, g(Yes, No, false, false, "", "u")},
	{0x1d463, 0, 0, 0, g(Yes, No, false, false, "", "v")},
	{0x1d464, 0, 0, 0, g(Yes, No, false, false, "", "w")},
	{0x1d465, 0, 0, 0, g(Yes, No, false, false, "", "x")},
	{0x1d466, 0, 0, 0, g(Yes, No, false, false, "", "y")},
	{0x1d467, 0, 0, 0, g(Yes, No, false, false, "", "z")},
	{0x1d468, 0, 0, 0, g(Yes, No, false, false, "", "A")},
	{0x1d469, 0, 0, 0, g(Yes, No, false, false, "", "B")},
	{0x1d46a, 0, 0, 0, g(Yes, No, false, false, "", "C")},
	{0x1d46b, 0, 0, 0, g(Yes, No, false, false, "", "D")},
	{0x1d46c, 0, 0, 0, g(Yes, No, false, false, "", "E")},
	{0x1d46d, 0, 0, 0, g(Yes, No, false, false, "", "F")},
	{0x1d46e, 0, 0, 0, g(Yes, No, false, false, "", "G")},
	{0x1d46f, 0, 0, 0, g(Yes, No, false, false, "", "H")},
	{0x1d470, 0, 0, 0, g(Yes, No, false, false, "", "I")},
	{0x1d471, 0, 0, 0, g(Yes, No, false, false, "", "J")},
	{0x1d472, 0, 0, 0, g(Yes, No, false, false, "", "K")},
	{0x1d473, 0, 0, 0, g(Yes, No, false, false, "", "L")},
	{0x1d474, 0, 0, 0, g(Yes, No, false, false, "", "M")},
	{0x1d475, 0, 0, 0, g(Yes, No, false, false, "", "N")},
	{0x1d476, 0, 0, 0, g(Yes, No, false, false, "", "O")},
	{0x1d477, 0, 0, 0, g(Yes, No, false, false, "", "P")},
	{0x1d478, 0, 0, 0, g(Yes, No, false, false, "", "Q")},
	{0x1d479, 0, 0, 0, g(Yes, No, false, false, "", "R")},
	{0x1d47a, 0, 0, 0, g(Yes, No, false, false, "", "S")},
	{0x1d47b, 0, 0, 0, g(Yes, No, false, false, "", "T")},
	{0x1d47c, 0, 0, 0, g(Yes, No, false, false, "", "U")},
	{0x1d47d, 0, 0, 0, g(Yes, No, false, false, "", "V")},
	{0x1d47e, 0, 0, 0, g(Yes, No, false, false, "", "W")},
	{0x1d47f, 0, 0, 0, g(Yes, No, false, false, "", "X")},
	{0x1d480, 0, 0, 0, g(Yes, No, false, false, "", "Y")},
	{0x1d481, 0, 0, 0, g(Yes, No, false, false, "", "Z")},
	{0x1d482, 0, 0, 0, g(Yes, No, false, false, "", "a")},
	{0x1d483, 0, 0, 0, g(Yes, No, false, false, "", "b")},
	{0x1d484, 0, 0, 0, g(Yes, No, false, false, "", "c")},
	{0x1d485, 0, 0, 0, g(Yes, No, false, false, "", "d")},
	{0x1d486, 0, 0, 0, g(Yes, No, false, false, "", "e")},
	{0x1d487, 0, 0, 0, g(Yes, No, false, false, "", "f")},
	{0x1d488, 0, 0, 0, g(Yes, No, false, false, "", "g")},
	{0x1d489, 0, 0, 0, g(Yes, No, false, false, "", "h")},
	{0x1d48a, 0, 0, 0, g(Yes, No, false, false, "", "i")},
	{0x1d48b, 0, 0, 0, g(Yes, No, false, false, "", "j")},
	{0x1d48c, 0, 0, 0, g(Yes, No, false, false, "", "k")},
	{0x1d48d, 0, 0, 0, g(Yes, No, false, false, "", "l")},
	{0x1d48e, 0, 0, 0, g(Yes, No, false, false, "", "m")},
	{0x1d48f, 0, 0, 0, g(Yes, No, false, false, "", "n")},
	{0x1d490, 0, 0, 0, g(Yes, No, false, false, "", "o")},
	{0x1d491, 0, 0, 0, g(Yes, No, false, false, "", "p")},
	{0x1d492, 0, 0, 0, g(Yes, No, false, false, "", "q")},
	{0x1d493, 0, 0, 0, g(Yes, No, false, false, "", "r")},
	{0x1d494, 0, 0, 0, g(Yes, No, false, false, "", "s")},
	{0x1d495, 0, 0, 0, g(Yes, No, false, false, "", "t")},
	{0x1d496, 0, 0, 0, g(Yes, No, false, false, "", "u")},
	{0x1d497, 0, 0, 0, g(Yes, No, false, false, "", "v")},
	{0x1d498, 0, 0, 0, g(Yes, No, false, false, "", "w")},
	{0x1d499, 0, 0, 0, g(Yes, No, false, false, "", "x")},
	{0x1d49a, 0, 0, 0, g(Yes, No, false, false, "", "y")},
	{0x1d49b, 0, 0, 0, g(Yes, No, false, false, "", "z")},
	{0x1d49c, 0, 0, 0, g(Yes, No, false, false, "", "A")},
	{0x1d49d, 0, 0, 0, f(Yes, false, "")},
	{0x1d49e, 0, 0, 0, g(Yes, No, false, false, "", "C")},
	{0x1d49f, 0, 0, 0, g(Yes, No, false, false, "", "D")},
	{0x1d4a0, 0, 0, 0, f(Yes, false, "")},
	{0x1d4a2, 0, 0, 0, g(Yes, No, false, false, "", "G")},
	{0x1d4a3, 0, 0, 0, f(Yes, false, "")},
	{0x1d4a5, 0, 0, 0, g(Yes, No, false, false, "", "J")},
	{0x1d4a6, 0, 0, 0, g(Yes, No, false, false, "", "K")},
	{0x1d4a7, 0, 0, 0, f(Yes, false, "")},
	{0x1d4a9, 0, 0, 0, g(Yes, No, false, false, "", "N")},
	{0x1d4aa, 0, 0, 0, g(Yes, No, false, false, "", "O")},
	{0x1d4ab, 0, 0, 0, g(Yes, No, false, false, "", "P")},
	{0x1d4ac, 0, 0, 0, g(Yes, No, false, false, "", "Q")},
	{0x1d4ad, 0, 0, 0, f(Yes, false, "")},
	{0x1d4ae, 0, 0, 0, g(Yes, No, false, false, "", "S")},
	{0x1d4af, 0, 0, 0, g(Yes, No, false, false, "", "T")},
	{0x1d4b0, 0, 0, 0, g(Yes, No, false, false, "", "U")},
	{0x1d4b1, 0, 0, 0, g(Yes, No, false, false, "", "V")},
	{0x1d4b2, 0, 0, 0, g(Yes, No, false, false, "", "W")},
	{0x1d4b3, 0, 0, 0, g(Yes, No, false, false, "", "X")},
	{0x1d4b4, 0, 0, 0, g(Yes, No, false, false, "", "Y")},
	{0x1d4b5, 0, 0, 0, g(Yes, No, false, false, "", "Z")},
	{0x1d4b6, 0, 0, 0, g(Yes, No, false, false, "", "a")},
	{0x1d4b7, 0, 0, 0, g(Yes, No, false, false, "", "b")},
	{0x1d4b8, 0, 0, 0, g(Yes, No, false, false, "", "c")},
	{0x1d4b9, 0, 0, 0, g(Yes, No, false, false, "", "d")},
	{0x1d4ba, 0, 0, 0, f(Yes, false, "")},
	{0x1d4bb, 0, 0, 0, g(Yes, No, false, false, "", "f")},
	{0x1d4bc, 0, 0, 0, f(Yes, false, "")},
	{0x1d4bd, 0, 0, 0, g(Yes, No, false, false, "", "h")},
	{0x1d4be, 0, 0, 0, g(Yes, No, false, false, "", "i")},
	{0x1d4bf, 0, 0, 0, g(Yes, No, false, false, "", "j")},
	{0x1d4c0, 0, 0, 0, g(Yes, No, false, false, "", "k")},
	{0x1d4c1, 0, 0, 0, g(Yes, No, false, false, "", "l")},
	{0x1d4c2, 0, 0, 0, g(Yes, No, false, false, "", "m")},
	{0x1d4c3, 0, 0, 0, g(Yes, No, false, false, "", "n")},
	{0x1d4c4, 0, 0, 0, f(Yes, false, "")},
	{0x1d4c5, 0, 0, 0, g(Yes, No, false, false, "", "p")},
	{0x1d4c6, 0, 0, 0, g(Yes, No, false, false, "", "q")},
	{0x1d4c7, 0, 0, 0, g(Yes, No, false, false, "", "r")},
	{0x1d4c8, 0, 0, 0, g(Yes, No, false, false, "", "s")},
	{0x1d4c9, 0, 0, 0, g(Yes, No, false, false, "", "t")},
	{0x1d4ca, 0, 0, 0, g(Yes, No, false, false, "", "u")},
	{0x1d4cb, 0, 0, 0, g(Yes, No, false, false, "", "v")},
	{0x1d4cc, 0, 0, 0, g(Yes, No, false, false, "", "w")},
	{0x1d4cd, 0, 0, 0, g(Yes, No, false, false, "", "x")},
	{0x1d4ce, 0, 0, 0, g(Yes, No, false, false, "", "y")},
	{0x1d4cf, 0, 0, 0, g(Yes, No, false, false, "", "z")},
	{0x1d4d0, 0, 0, 0, g(Yes, No, false, false, "", "A")},
	{0x1d4d1, 0, 0, 0, g(Yes, No, false, false, "", "B")},
	{0x1d4d2, 0, 0, 0, g(Yes, No, false, false, "", "C")},
	{0x1d4d3, 0, 0, 0, g(Yes, No, false, false, "", "D")},
	{0x1d4d4, 0, 0, 0, g(Yes, No, false, false, "", "E")},
	{0x1d4d5, 0, 0, 0, g(Yes, No, false, false, "", "F")},
	{0x1d4d6, 0, 0, 0, g(Yes, No, false, false, "", "G")},
	{0x1d4d7, 0, 0, 0, g(Yes, No, false, false, "", "H")},
	{0x1d4d8, 0, 0, 0, g(Yes, No, false, false, "", "I")},
	{0x1d4d9, 0, 0, 0, g(Yes, No, false, false, "", "J")},
	{0x1d4da, 0, 0, 0, g(Yes, No, false, false, "", "K")},
	{0x1d4db, 0, 0, 0, g(Yes, No, false, false, "", "L")},
	{0x1d4dc, 0, 0, 0, g(Yes, No, false, false, "", "M")},
	{0x1d4dd, 0, 0, 0, g(Yes, No, false, false, "", "N")},
	{0x1d4de, 0, 0, 0, g(Yes, No, false, false, "", "O")},
	{0x1d4df, 0, 0, 0, g(Yes, No, false, false, "", "P")},
	{0x1d4e0, 0, 0, 0, g(Yes, No, false, false, "", "Q")},
	{0x1d4e1, 0, 0, 0, g(Yes, No, false, false, "", "R")},
	{0x1d4e2, 0, 0, 0, g(Yes, No, false, false, "", "S")},
	{0x1d4e3, 0, 0, 0, g(Yes, No, false, false, "", "T")},
	{0x1d4e4, 0, 0, 0, g(Yes, No, false, false, "", "U")},
	{0x1d4e5, 0, 0, 0, g(Yes, No, false, false, "", "V")},
	{0x1d4e6, 0, 0, 0, g(Yes, No, false, false, "", "W")},
	{0x1d4e7, 0, 0, 0, g(Yes, No, false, false, "", "X")},
	{0x1d4e8, 0, 0, 0, g(Yes, No, false, false, "", "Y")},
	{0x1d4e9, 0, 0, 0, g(Yes, No, false, false, "", "Z")},
	{0x1d4ea, 0, 0, 0, g(Yes, No, false, false, "", "a")},
	{0x1d4eb, 0, 0, 0, g(Yes, No, false, false, "", "b")},
	{0x1d4ec, 0, 0, 0, g(Yes, No, false, false, "", "c")},
	{0x1d4ed, 0, 0, 0, g(Yes, No, false, false, "", "d")},
	{0x1d4ee, 0, 0, 0, g(Yes, No, false, false, "", "e")},
	{0x1d4ef, 0, 0, 0, g(Yes, No, false, false, "", "f")},
	{0x1d4f0, 0, 0, 0, g(Yes, No, false, false, "", "g")},
	{0x1d4f1, 0, 0, 0, g(Yes, No, false, false, "", "h")},
	{0x1d4f2, 0, 0, 0, g(Yes, No, false, false, "", "i")},
	{0x1d4f3, 0, 0, 0, g(Yes, No, false, false, "", "j")},
	{0x1d4f4, 0, 0, 0, g(Yes, No, false, false, "", "k")},
	{0x1d4f5, 0, 0, 0, g(Yes, No, false, false, "", "l")},
	{0x1d4f6, 0, 0, 0, g(Yes, No, false, false, "", "m")},
	{0x1d4f7, 0, 0, 0, g(Yes, No, false, false, "", "n")},
	{0x1d4f8, 0, 0, 0, g(Yes, No, false, false, "", "o")},
	{0x1d4f9, 0, 0, 0, g(Yes, No, false, false, "", "p")},
	{0x1d4fa, 0, 0, 0, g(Yes, No, false, false, "", "q")},
	{0x1d4fb, 0, 0, 0, g(Yes, No, false, false, "", "r")},
	{0x1d4fc, 0, 0, 0, g(Yes, No, false, false, "", "s")},
	{0x1d4fd, 0, 0, 0, g(Yes, No, false, false, "", "t")},
	{0x1d4fe, 0, 0, 0, g(Yes, No, false, false, "", "u")},
	{0x1d4ff, 0, 0, 0, g(Yes, No, false, false, "", "v")},
	{0x1d500, 0, 0, 0, g(Yes, No, false, false, "", "w")},
	{0x1d501, 0, 0, 0, g(Yes, No, false, false, "", "x")},
	{0x1d502, 0, 0, 0, g(Yes, No, false, false, "", "y")},
	{0x1d503, 0, 0, 0, g(Yes, No, false, false, "", "z")},
	{0x1d504, 0, 0, 0, g(Yes, No, false, false, "", "A")},
	{0x1d505, 0, 0, 0, g(Yes, No, false, false, "", "B")},
	{0x1d506, 0, 0, 0, f(Yes, false, "")},
	{0x1d507, 0, 0, 0, g(Yes, No, false, false, "", "D")},
	{0x1d508, 0, 0, 0, g(Yes, No, false, false, "", "E")},
	{0x1d509, 0, 0, 0, g(Yes, No, false, false, "", "F")},
	{0x1d50a, 0, 0, 0, g(Yes, No, false, false, "", "G")},
	{0x1d50b, 0, 0, 0, f(Yes, false, "")},
	{0x1d50d, 0, 0, 0, g(Yes, No, false, false, "", "J")},
	{0x1d50e, 0, 0, 0, g(Yes, No, false, false, "", "K")},
	{0x1d50f, 0, 0, 0, g(Yes, No, false, false, "", "L")},
	{0x1d510, 0, 0, 0, g(Yes, No, false, false, "", "M")},
	{0x1d511, 0, 0, 0, g(Yes, No, false, false, "", "N")},
	{0x1d512, 0, 0, 0, g(Yes, No, false, false, "", "O")},
	{0x1d513, 0, 0, 0, g(Yes, No, false, false, "", "P")},
	{0x1d514, 0, 0, 0, g(Yes, No, false, false, "", "Q")},
	{0x1d515, 0, 0, 0, f(Yes, false, "")},
	{0x1d516, 0, 0, 0, g(Yes, No, false, false, "", "S")},
	{0x1d517, 0, 0, 0, g(Yes, No, false, false, "", "T")},
	{0x1d518, 0, 0, 0, g(Yes, No, false, false, "", "U")},
	{0x1d519, 0, 0, 0, g(Yes, No, false, false, "", "V")},
	{0x1d51a, 0, 0, 0, g(Yes, No, false, false, "", "W")},
	{0x1d51b, 0, 0, 0, g(Yes, No, false, false, "", "X")},
	{0x1d51c, 0, 0, 0, g(Yes, No, false, false, "", "Y")},
	{0x1d51d, 0, 0, 0, f(Yes, false, "")},
	{0x1d51e, 0, 0, 0, g(Yes, No, false, false, "", "a")},
	{0x1d51f, 0, 0, 0, g(Yes, No, false, false, "", "b")},
	{0x1d520, 0, 0, 0, g(Yes, No, false, false, "", "c")},
	{0x1d521, 0, 0, 0, g(Yes, No, false, false, "", "d")},
	{0x1d522, 0, 0, 0, g(Yes, No, false, false, "", "e")},
	{0x1d523, 0, 0, 0, g(Yes, No, false, false, "", "f")},
	{0x1d524, 0, 0, 0, g(Yes, No, false, false, "", "g")},
	{0x1d525, 0, 0, 0, g(Yes, No, false, false, "", "h")},
	{0x1d526, 0, 0, 0, g(Yes, No, false, false, "", "i")},
	{0x1d527, 0, 0, 0, g(Yes, No, false, false, "", "j")},
	{0x1d528, 0, 0, 0, g(Yes, No, false, false, "", "k")},
	{0x1d529, 0, 0, 0, g(Yes, No, false, false, "", "l")},
	{0x1d52a, 0, 0, 0, g(Yes, No, false, false, "", "m")},
	{0x1d52b, 0, 0, 0, g(Yes, No, false, false, "", "n")},
	{0x1d52c, 0, 0, 0, g(Yes, No, false, false, "", "o")},
	{0x1d52d, 0, 0, 0, g(Yes, No, false, false, "", "p")},
	{0x1d52e, 0, 0, 0, g(Yes, No, false, false, "", "q")},
	{0x1d52f, 0, 0, 0, g(Yes, No, false, false, "", "r")},
	{0x1d530, 0, 0, 0, g(Yes, No, false, false, "", "s")},
	{0x1d531, 0, 0, 0, g(Yes, No, false, false, "", "t")},
	{0x1d532, 0, 0, 0, g(Yes, No, false, false, "", "u")},
	{0x1d533, 0, 0, 0, g(Yes, No, false, false, "", "v")},
	{0x1d534, 0, 0, 0, g(Yes, No, false, false, "", "w")},
	{0x1d535, 0, 0, 0, g(Yes, No, false, false, "", "x")},
	{0x1d536, 0, 0, 0, g(Yes, No, false, false, "", "y")},
	{0x1d537, 0, 0, 0, g(Yes, No, false, false, "", "z")},
	{0x1d538, 0, 0, 0, g(Yes, No, false, false, "", "A")},
	{0x1d539, 0, 0, 0, g(Yes, No, false, false, "", "B")},
	{0x1d53a, 0, 0, 0, f(Yes, false, "")},
	{0x1d53b, 0, 0, 0, g(Yes, No, false, false, "", "D")},
	{0x1d53c, 0, 0, 0, g(Yes, No, false, false, "", "E")},
	{0x1d53d, 0, 0, 0, g(Yes, No, false, false, "", "F")},
	{0x1d53e, 0, 0, 0, g(Yes, No, false, false, "", "G")},
	{0x1d53f, 0, 0, 0, f(Yes, false, "")},
	{0x1d540, 0, 0, 0, g(Yes, No, false, false, "", "I")},
	{0x1d541, 0, 0, 0, g(Yes, No, false, false, "", "J")},
	{0x1d542, 0, 0, 0, g(Yes, No, false, false, "", "K")},
	{0x1d543, 0, 0, 0, g(Yes, No, false, false, "", "L")},
	{0x1d544, 0, 0, 0, g(Yes, No, false, false, "", "M")},
	{0x1d545, 0, 0, 0, f(Yes, false, "")},
	{0x1d546, 0, 0, 0, g(Yes, No, false, false, "", "O")},
	{0x1d547, 0, 0, 0, f(Yes, false, "")},
	{0x1d54a, 0, 0, 0, g(Yes, No, false, false, "", "S")},
	{0x1d54b, 0, 0, 0, g(Yes, No, false, false, "", "T")},
	{0x1d54c, 0, 0, 0, g(Yes, No, false, false, "", "U")},
	{0x1d54d, 0, 0, 0, g(Yes, No, false, false, "", "V")},
	{0x1d54e, 0, 0, 0, g(Yes, No, false, false, "", "W")},
	{0x1d54f, 0, 0, 0, g(Yes, No, false, false, "", "X")},
	{0x1d550, 0, 0, 0, g(Yes, No, false, false, "", "Y")},
	{0x1d551, 0, 0, 0, f(Yes, false, "")},
	{0x1d552, 0, 0, 0, g(Yes, No, false, false, "", "a")},
	{0x1d553, 0, 0, 0, g(Yes, No, false, false, "", "b")},
	{0x1d554, 0, 0, 0, g(Yes, No, false, false, "", "c")},
	{0x1d555, 0, 0, 0, g(Yes, No, false, false, "", "d")},
	{0x1d556, 0, 0, 0, g(Yes, No, false, false, "", "e")},
	{0x1d557, 0, 0, 0, g(Yes, No, false, false, "", "f")},
	{0x1d558, 0, 0, 0, g(Yes, No, false, false, "", "g")},
	{0x1d559, 0, 0, 0, g(Yes, No, false, false, "", "h")},
	{0x1d55a, 0, 0, 0, g(Yes, No, false, false, "", "i")},
	{0x1d55b, 0, 0, 0, g(Yes, No, false, false, "", "j")},
	{0x1d55c, 0, 0, 0, g(Yes, No, false, false, "", "k")},
	{0x1d55d, 0, 0, 0, g(Yes, No, false, false, "", "l")},
	{0x1d55e, 0, 0, 0, g(Yes, No, false, false, "", "m")},
	{0x1d55f, 0, 0, 0, g(Yes, No, false, false, "", "n")},
	{0x1d560, 0, 0, 0, g(Yes, No, false, false, "", "o")},
	{0x1d561, 0, 0, 0, g(Yes, No, false, false, "", "p")},
	{0x1d562, 0, 0, 0, g(Yes, No, false, false, "", "q")},
	{0x1d563, 0, 0, 0, g(Yes, No, false, false, "", "r")},
	{0x1d564, 0, 0, 0, g(Yes, No, false, false, "", "s")},
	{0x1d565, 0, 0, 0, g(Yes, No, false, false, "", "t")},
	{0x1d566, 0, 0, 0, g(Yes, No, false, false, "", "u")},
	{0x1d567, 0, 0, 0, g(Yes, No, false, false, "", "v")},
	{0x1d568, 0, 0, 0, g(Yes, No, false, false, "", "w")},
	{0x1d569, 0, 0, 0, g(Yes, No, false, false, "", "x")},
	{0x1d56a, 0, 0, 0, g(Yes, No, false, false, "", "y")},
	{0x1d56b, 0, 0, 0, g(Yes, No, false, false, "", "z")},
	{0x1d56c, 0, 0, 0, g(Yes, No, false, false, "", "A")},
	{0x1d56d, 0, 0, 0, g(Yes, No, false, false, "", "B")},
	{0x1d56e, 0, 0, 0, g(Yes, No, false, false, "", "C")},
	{0x1d56f, 0, 0, 0, g(Yes, No, false, false, "", "D")},
	{0x1d570, 0, 0, 0, g(Yes, No, false, false, "", "E")},
	{0x1d571, 0, 0, 0, g(Yes, No, false, false, "", "F")},
	{0x1d572, 0, 0, 0, g(Yes, No, false, false, "", "G")},
	{0x1d573, 0, 0, 0, g(Yes, No, false, false, "", "H")},
	{0x1d574, 0, 0, 0, g(Yes, No, false, false, "", "I")},
	{0x1d575, 0, 0, 0, g(Yes, No, false, false, "", "J")},
	{0x1d576, 0, 0, 0, g(Yes, No, false, false, "", "K")},
	{0x1d577, 0, 0, 0, g(Yes, No, false, false, "", "L")},
	{0x1d578, 0, 0, 0, g(Yes, No, false, false, "", "M")},
	{0x1d579, 0, 0, 0, g(Yes, No, false, false, "", "N")},
	{0x1d57a, 0, 0, 0, g(Yes, No, false, false, "", "O")},
	{0x1d57b, 0, 0, 0, g(Yes, No, false, false, "", "P")},
	{0x1d57c, 0, 0, 0, g(Yes, No, false, false, "", "Q")},
	{0x1d57d, 0, 0, 0, g(Yes, No, false, false, "", "R")},
	{0x1d57e, 0, 0, 0, g(Yes, No, false, false, "", "S")},
	{0x1d57f, 0, 0, 0, g(Yes, No, false, false, "", "T")},
	{0x1d580, 0, 0, 0, g(Yes, No, false, false, "", "U")},
	{0x1d581, 0, 0, 0, g(Yes, No, false, false, "", "V")},
	{0x1d582, 0, 0, 0, g(Yes, No, false, false, "", "W")},
	{0x1d583, 0, 0, 0, g(Yes, No, false, false, "", "X")},
	{0x1d584, 0, 0, 0, g(Yes, No, false, false, "", "Y")},
	{0x1d585, 0, 0, 0, g(Yes, No, false, false, "", "Z")},
	{0x1d586, 0, 0, 0, g(Yes, No, false, false, "", "a")},
	{0x1d587, 0, 0, 0, g(Yes, No, false, false, "", "b")},
	{0x1d588, 0, 0, 0, g(Yes, No, false, false, "", "c")},
	{0x1d589, 0, 0, 0, g(Yes, No, false, false, "", "d")},
	{0x1d58a, 0, 0, 0, g(Yes, No, false, false, "", "e")},
	{0x1d58b, 0, 0, 0, g(Yes, No, false, false, "", "f")},
	{0x1d58c, 0, 0, 0, g(Yes, No, false, false, "", "g")},
	{0x1d58d, 0, 0, 0, g(Yes, No, false, false, "", "h")},
	{0x1d58e, 0, 0, 0, g(Yes, No, false, false, "", "i")},
	{0x1d58f, 0, 0, 0, g(Yes, No, false, false, "", "j")},
	{0x1d590, 0, 0, 0, g(Yes, No, false, false, "", "k")},
	{0x1d591, 0, 0, 0, g(Yes, No, false, false, "", "l")},
	{0x1d592, 0, 0, 0, g(Yes, No, false, false, "", "m")},
	{0x1d593, 0, 0, 0, g(Yes, No, false, false, "", "n")},
	{0x1d594, 0, 0, 0, g(Yes, No, false, false, "", "o")},
	{0x1d595, 0, 0, 0, g(Yes, No, false, false, "", "p")},
	{0x1d596, 0, 0, 0, g(Yes, No, false, false, "", "q")},
	{0x1d597, 0, 0, 0, g(Yes, No, false, false, "", "r")},
	{0x1d598, 0, 0, 0, g(Yes, No, false, false, "", "s")},
	{0x1d599, 0, 0, 0, g(Yes, No, false, false, "", "t")},
	{0x1d59a, 0, 0, 0, g(Yes, No, false, false, "", "u")},
	{0x1d59b, 0, 0, 0, g(Yes, No, false, false, "", "v")},
	{0x1d59c, 0, 0, 0, g(Yes, No, false, false, "", "w")},
	{0x1d59d, 0, 0, 0, g(Yes, No, false, false, "", "x")},
	{0x1d59e, 0, 0, 0, g(Yes, No, false, false, "", "y")},
	{0x1d59f, 0, 0, 0, g(Yes, No, false, false, "", "z")},
	{0x1d5a0, 0, 0, 0, g(Yes, No, false, false, "", "A")},
	{0x1d5a1, 0, 0, 0, g(Yes, No, false, false, "", "B")},
	{0x1d5a2, 0, 0, 0, g(Yes, No, false, false, "", "C")},
	{0x1d5a3, 0, 0, 0, g(Yes, No, false, false, "", "D")},
	{0x1d5a4, 0, 0, 0, g(Yes, No, false, false, "", "E")},
	{0x1d5a5, 0, 0, 0, g(Yes, No, false, false, "", "F")},
	{0x1d5a6, 0, 0, 0, g(Yes, No, false, false, "", "G")},
	{0x1d5a7, 0, 0, 0, g(Yes, No, false, false, "", "H")},
	{0x1d5a8, 0, 0, 0, g(Yes, No, false, false, "", "I")},
	{0x1d5a9, 0, 0, 0, g(Yes, No, false, false, "", "J")},
	{0x1d5aa, 0, 0, 0, g(Yes, No, false, false, "", "K")},
	{0x1d5ab, 0, 0, 0, g(Yes, No, false, false, "", "L")},
	{0x1d5ac, 0, 0, 0, g(Yes, No, false, false, "", "M")},
	{0x1d5ad, 0, 0, 0, g(Yes, No, false, false, "", "N")},
	{0x1d5ae, 0, 0, 0, g(Yes, No, false, false, "", "O")},
	{0x1d5af, 0, 0, 0, g(Yes, No, false, false, "", "P")},
	{0x1d5b0, 0, 0, 0, g(Yes, No, false, false, "", "Q")},
	{0x1d5b1, 0, 0, 0, g(Yes, No, false, false, "", "R")},
	{0x1d5b2, 0, 0, 0, g(Yes, No, false, false, "", "S")},
	{0x1d5b3, 0, 0, 0, g(Yes, No, false, false, "", "T")},
	{0x1d5b4, 0, 0, 0, g(Yes, No, false, false, "", "U")},
	{0x1d5b5, 0, 0, 0, g(Yes, No, false, false, "", "V")},
	{0x1d5b6, 0, 0, 0, g(Yes, No, false, false, "", "W")},
	{0x1d5b7, 0, 0, 0, g(Yes, No, false, false, "", "X")},
	{0x1d5b8, 0, 0, 0, g(Yes, No, false, false, "", "Y")},
	{0x1d5b9, 0, 0, 0, g(Yes, No, false, false, "", "Z")},
	{0x1d5ba, 0, 0, 0, g(Yes, No, false, false, "", "a")},
	{0x1d5bb, 0, 0, 0, g(Yes, No, false, false, "", "b")},
	{0x1d5bc, 0, 0, 0, g(Yes, No, false, false, "", "c")},
	{0x1d5bd, 0, 0, 0, g(Yes, No, false, false, "", "d")},
	{0x1d5be, 0, 0, 0, g(Yes, No, false, false, "", "e")},
	{0x1d5bf, 0, 0, 0, g(Yes, No, false, false, "", "f")},
	{0x1d5c0, 0, 0, 0, g(Yes, No, false, false, "", "g")},
	{0x1d5c1, 0, 0, 0, g(Yes, No, false, false, "", "h")},
	{0x1d5c2, 0, 0, 0, g(Yes, No, false, false, "", "i")},
	{0x1d5c3, 0, 0, 0, g(Yes, No, false, false, "", "j")},
	{0x1d5c4, 0, 0, 0, g(Yes, No, false, false, "", "k")},
	{0x1d5c5, 0, 0, 0, g(Yes, No, false, false, "", "l")},
	{0x1d5c6, 0, 0, 0, g(Yes, No, false, false, "", "m")},
	{0x1d5c7, 0, 0, 0, g(Yes, No, false, false, "", "n")},
	{0x1d5c8, 0, 0, 0, g(Yes, No, false, false, "", "o")},
	{0x1d5c9, 0, 0, 0, g(Yes, No, false, false, "", "p")},
	{0x1d5ca, 0, 0, 0, g(Yes, No, false, false, "", "q")},
	{0x1d5cb, 0, 0, 0, g(Yes, No, false, false, "", "r")},
	{0x1d5cc, 0, 0, 0, g(Yes, No, false, false, "", "s")},
	{0x1d5cd, 0, 0, 0, g(Yes, No, false, false, "", "t")},
	{0x1d5ce, 0, 0, 0, g(Yes, No, false, false, "", "u")},
	{0x1d5cf, 0, 0, 0, g(Yes, No, false, false, "", "v")},
	{0x1d5d0, 0, 0, 0, g(Yes, No, false, false, "", "w")},
	{0x1d5d1, 0, 0, 0, g(Yes, No, false, false, "", "x")},
	{0x1d5d2, 0, 0, 0, g(Yes, No, false, false, "", "y")},
	{0x1d5d3, 0, 0, 0, g(Yes, No, false, false, "", "z")},
	{0x1d5d4, 0, 0, 0, g(Yes, No, false, false, "", "A")},
	{0x1d5d5, 0, 0, 0, g(Yes, No, false, false, "", "B")},
	{0x1d5d6, 0, 0, 0, g(Yes, No, false, false, "", "C")},
	{0x1d5d7, 0, 0, 0, g(Yes, No, false, false, "", "D")},
	{0x1d5d8, 0, 0, 0, g(Yes, No, false, false, "", "E")},
	{0x1d5d9, 0, 0, 0, g(Yes, No, false, false, "", "F")},
	{0x1d5da, 0, 0, 0, g(Yes, No, false, false, "", "G")},
	{0x1d5db, 0, 0, 0, g(Yes, No, false, false, "", "H")},
	{0x1d5dc, 0, 0, 0, g(Yes, No, false, false, "", "I")},
	{0x1d5dd, 0, 0, 0, g(Yes, No, false, false, "", "J")},
	{0x1d5de, 0, 0, 0, g(Yes, No, false, false, "", "K")},
	{0x1d5df, 0, 0, 0, g(Yes, No, false, false, "", "L")},
	{0x1d5e0, 0, 0, 0, g(Yes, No, false, false, "", "M")},
	{0x1d5e1, 0, 0, 0, g(Yes, No, false, false, "", "N")},
	{0x1d5e2, 0, 0, 0, g(Yes, No, false, false, "", "O")},
	{0x1d5e3, 0, 0, 0, g(Yes, No, false, false, "", "P")},
	{0x1d5e4, 0, 0, 0, g(Yes, No, false, false, "", "Q")},
	{0x1d5e5, 0, 0, 0, g(Yes, No, false, false, "", "R")},
	{0x1d5e6, 0, 0, 0, g(Yes, No, false, false, "", "S")},
	{0x1d5e7, 0, 0, 0, g(Yes, No, false, false, "", "T")},
	{0x1d5e8, 0, 0, 0, g(Yes, No, false, false, "", "U")},
	{0x1d5e9, 0, 0, 0, g(Yes, No, false, false, "", "V")},
	{0x1d5ea, 0, 0, 0, g(Yes, No, false, false, "", "W")},
	{0x1d5eb, 0, 0, 0, g(Yes, No, false, false, "", "X")},
	{0x1d5ec, 0, 0, 0, g(Yes, No, false, false, "", "Y")},
	{0x1d5ed, 0, 0, 0, g(Yes, No, false, false, "", "Z")},
	{0x1d5ee, 0, 0, 0, g(Yes, No, false, false, "", "a")},
	{0x1d5ef, 0, 0, 0, g(Yes, No, false, false, "", "b")},
	{0x1d5f0, 0, 0, 0, g(Yes, No, false, false, "", "c")},
	{0x1d5f1, 0, 0, 0, g(Yes, No, false, false, "", "d")},
	{0x1d5f2, 0, 0, 0, g(Yes, No, false, false, "", "e")},
	{0x1d5f3, 0, 0, 0, g(Yes, No, false, false, "", "f")},
	{0x1d5f4, 0, 0, 0, g(Yes, No, false, false, "", "g")},
	{0x1d5f5, 0, 0, 0, g(Yes, No, false, false, "", "h")},
	{0x1d5f6, 0, 0, 0, g(Yes, No, false, false, "", "i")},
	{0x1d5f7, 0, 0, 0, g(Yes, No, false, false, "", "j")},
	{0x1d5f8, 0, 0, 0, g(Yes, No, false, false, "", "k")},
	{0x1d5f9, 0, 0, 0, g(Yes, No, false, false, "", "l")},
	{0x1d5fa, 0, 0, 0, g(Yes, No, false, false, "", "m")},
	{0x1d5fb, 0, 0, 0, g(Yes, No, false, false, "", "n")},
	{0x1d5fc, 0, 0, 0, g(Yes, No, false, false, "", "o")},
	{0x1d5fd, 0, 0, 0, g(Yes, No, false, false, "", "p")},
	{0x1d5fe, 0, 0, 0, g(Yes, No, false, false, "", "q")},
	{0x1d5ff, 0, 0, 0, g(Yes, No, false, false, "", "r")},
	{0x1d600, 0, 0, 0, g(Yes, No, false, false, "", "s")},
	{0x1d601, 0, 0, 0, g(Yes, No, false, false, "", "t")},
	{0x1d602, 0, 0, 0, g(Yes, No, false, false, "", "u")},
	{0x1d603, 0, 0, 0, g(Yes, No, false, false, "", "v")},
	{0x1d604, 0, 0, 0, g(Yes, No, false, false, "", "w")},
	{0x1d605, 0, 0, 0, g(Yes, No, false, false, "", "x")},
	{0x1d606, 0, 0, 0, g(Yes, No, false, false, "", "y")},
	{0x1d607, 0, 0, 0, g(Yes, No, false, false, "", "z")},
	{0x1d608, 0, 0, 0, g(Yes, No, false, false, "", "A")},
	{0x1d609, 0, 0, 0, g(Yes, No, false, false, "", "B")},
	{0x1d60a, 0, 0, 0, g(Yes, No, false, false, "", "C")},
	{0x1d60b, 0, 0, 0, g(Yes, No, false, false, "", "D")},
	{0x1d60c, 0, 0, 0, g(Yes, No, false, false, "", "E")},
	{0x1d60d, 0, 0, 0, g(Yes, No, false, false, "", "F")},
	{0x1d60e, 0, 0, 0, g(Yes, No, false, false, "", "G")},
	{0x1d60f, 0, 0, 0, g(Yes, No, false, false, "", "H")},
	{0x1d610, 0, 0, 0, g(Yes, No, false, false, "", "I")},
	{0x1d611, 0, 0, 0, g(Yes, No, false, false, "", "J")},
	{0x1d612, 0, 0, 0, g(Yes, No, false, false, "", "K")},
	{0x1d613, 0, 0, 0, g(Yes, No, false, false, "", "L")},
	{0x1d614, 0, 0, 0, g(Yes, No, false, false, "", "M")},
	{0x1d615, 0, 0, 0, g(Yes, No, false, false, "", "N")},
	{0x1d616, 0, 0, 0, g(Yes, No, false, false, "", "O")},
	{0x1d617, 0, 0, 0, g(Yes, No, false, false, "", "P")},
	{0x1d618, 0, 0, 0, g(Yes, No, false, false, "", "Q")},
	{0x1d619, 0, 0, 0, g(Yes, No, false, false, "", "R")},
	{0x1d61a, 0, 0, 0, g(Yes, No, false, false, "", "S")},
	{0x1d61b, 0, 0, 0, g(Yes, No, false, false, "", "T")},
	{0x1d61c, 0, 0, 0, g(Yes, No, false, false, "", "U")},
	{0x1d61d, 0, 0, 0, g(Yes, No, false, false, "", "V")},
	{0x1d61e, 0, 0, 0, g(Yes, No, false, false, "", "W")},
	{0x1d61f, 0, 0, 0, g(Yes, No, false, false, "", "X")},
	{0x1d620, 0, 0, 0, g(Yes, No, false, false, "", "Y")},
	{0x1d621, 0, 0, 0, g(Yes, No, false, false, "", "Z")},
	{0x1d622, 0, 0, 0, g(Yes, No, false, false, "", "a")},
	{0x1d623, 0, 0, 0, g(Yes, No, false, false, "", "b")},
	{0x1d624, 0, 0, 0, g(Yes, No, false, false, "", "c")},
	{0x1d625, 0, 0, 0, g(Yes, No, false, false, "", "d")},
	{0x1d626, 0, 0, 0, g(Yes, No, false, false, "", "e")},
	{0x1d627, 0, 0, 0, g(Yes, No, false, false, "", "f")},
	{0x1d628, 0, 0, 0, g(Yes, No, false, false, "", "g")},
	{0x1d629, 0, 0, 0, g(Yes, No, false, false, "", "h")},
	{0x1d62a, 0, 0, 0, g(Yes, No, false, false, "", "i")},
	{0x1d62b, 0, 0, 0, g(Yes, No, false, false, "", "j")},
	{0x1d62c, 0, 0, 0, g(Yes, No, false, false, "", "k")},
	{0x1d62d, 0, 0, 0, g(Yes, No, false, false, "", "l")},
	{0x1d62e, 0, 0, 0, g(Yes, No, false, false, "", "m")},
	{0x1d62f, 0, 0, 0, g(Yes, No, false, false, "", "n")},
	{0x1d630, 0, 0, 0, g(Yes, No, false, false, "", "o")},
	{0x1d631, 0, 0, 0, g(Yes, No, false, false, "", "p")},
	{0x1d632, 0, 0, 0, g(Yes, No, false, false, "", "q")},
	{0x1d633, 0, 0, 0, g(Yes, No, false, false, "", "r")},
	{0x1d634, 0, 0, 0, g(Yes, No, false, false, "", "s")},
	{0x1d635, 0, 0, 0, g(Yes, No, false, false, "", "t")},
	{0x1d636, 0, 0, 0, g(Yes, No, false, false, "", "u")},
	{0x1d637, 0, 0, 0, g(Yes, No, false, false, "", "v")},
	{0x1d638, 0, 0, 0, g(Yes, No, false, false, "", "w")},
	{0x1d639, 0, 0, 0, g(Yes, No, false, false, "", "x")},
	{0x1d63a, 0, 0, 0, g(Yes, No, false, false, "", "y")},
	{0x1d63b, 0, 0, 0, g(Yes, No, false, false, "", "z")},
	{0x1d63c, 0, 0, 0, g(Yes, No, false, false, "", "A")},
	{0x1d63d, 0, 0, 0, g(Yes, No, false, false, "", "B")},
	{0x1d63e, 0, 0, 0, g(Yes, No, false, false, "", "C")},
	{0x1d63f, 0, 0, 0, g(Yes, No, false, false, "", "D")},
	{0x1d640, 0, 0, 0, g(Yes, No, false, false, "", "E")},
	{0x1d641, 0, 0, 0, g(Yes, No, false, false, "", "F")},
	{0x1d642, 0, 0, 0, g(Yes, No, false, false, "", "G")},
	{0x1d643, 0, 0, 0, g(Yes, No, false, false, "", "H")},
	{0x1d644, 0, 0, 0, g(Yes, No, false, false, "", "I")},
	{0x1d645, 0, 0, 0, g(Yes, No, false, false, "", "J")},
	{0x1d646, 0, 0, 0, g(Yes, No, false, false, "", "K")},
	{0x1d647, 0, 0, 0, g(Yes, No, false, false, "", "L")},
	{0x1d648, 0, 0, 0, g(Yes, No, false, false, "", "M")},
	{0x1d649, 0, 0, 0, g(Yes, No, false, false, "", "N")},
	{0x1d64a, 0, 0, 0, g(Yes, No, false, false, "", "O")},
	{0x1d64b, 0, 0, 0, g(Yes, No, false, false, "", "P")},
	{0x1d64c, 0, 0, 0, g(Yes, No, false, false, "", "Q")},
	{0x1d64d, 0, 0, 0, g(Yes, No, false, false, "", "R")},
	{0x1d64e, 0, 0, 0, g(Yes, No, false, false, "", "S")},
	{0x1d64f, 0, 0, 0, g(Yes, No, false, false, "", "T")},
	{0x1d650, 0, 0, 0, g(Yes, No, false, false, "", "U")},
	{0x1d651, 0, 0, 0, g(Yes, No, false, false, "", "V")},
	{0x1d652, 0, 0, 0, g(Yes, No, false, false, "", "W")},
	{0x1d653, 0, 0, 0, g(Yes, No, false, false, "", "X")},
	{0x1d654, 0, 0, 0, g(Yes, No, false, false, "", "Y")},
	{0x1d655, 0, 0, 0, g(Yes, No, false, false, "", "Z")},
	{0x1d656, 0, 0, 0, g(Yes, No, false, false, "", "a")},
	{0x1d657, 0, 0, 0, g(Yes, No, false, false, "", "b")},
	{0x1d658, 0, 0, 0, g(Yes, No, false, false, "", "c")},
	{0x1d659, 0, 0, 0, g(Yes, No, false, false, "", "d")},
	{0x1d65a, 0, 0, 0, g(Yes, No, false, false, "", "e")},
	{0x1d65b, 0, 0, 0, g(Yes, No, false, false, "", "f")},
	{0x1d65c, 0, 0, 0, g(Yes, No, false, false, "", "g")},
	{0x1d65d, 0, 0, 0, g(Yes, No, false, false, "", "h")},
	{0x1d65e, 0, 0, 0, g(Yes, No, false, false, "", "i")},
	{0x1d65f, 0, 0, 0, g(Yes, No, false, false, "", "j")},
	{0x1d660, 0, 0, 0, g(Yes, No, false, false, "", "k")},
	{0x1d661, 0, 0, 0, g(Yes, No, false, false, "", "l")},
	{0x1d662, 0, 0, 0, g(Yes, No, false, false, "", "m")},
	{0x1d663, 0, 0, 0, g(Yes, No, false, false, "", "n")},
	{0x1d664, 0, 0, 0, g(Yes, No, false, false, "", "o")},
	{0x1d665, 0, 0, 0, g(Yes, No, false, false, "", "p")},
	{0x1d666, 0, 0, 0, g(Yes, No, false, false, "", "q")},
	{0x1d667, 0, 0, 0, g(Yes, No, false, false, "", "r")},
	{0x1d668, 0, 0, 0, g(Yes, No, false, false, "", "s")},
	{0x1d669, 0, 0, 0, g(Yes, No, false, false, "", "t")},
	{0x1d66a, 0, 0, 0, g(Yes, No, false, false, "", "u")},
	{0x1d66b, 0, 0, 0, g(Yes, No, false, false, "", "v")},
	{0x1d66c, 0, 0, 0, g(Yes, No, false, false, "", "w")},
	{0x1d66d, 0, 0, 0, g(Yes, No, false, false, "", "x")},
	{0x1d66e, 0, 0, 0, g(Yes, No, false, false, "", "y")},
	{0x1d66f, 0, 0, 0, g(Yes, No, false, false, "", "z")},
	{0x1d670, 0, 0, 0, g(Yes, No, false, false, "", "A")},
	{0x1d671, 0, 0, 0, g(Yes, No, false, false, "", "B")},
	{0x1d672, 0, 0, 0, g(Yes, No, false, false, "", "C")},
	{0x1d673, 0, 0, 0, g(Yes, No, false, false, "", "D")},
	{0x1d674, 0, 0, 0, g(Yes, No, false, false, "", "E")},
	{0x1d675, 0, 0, 0, g(Yes, No, false, false, "", "F")},
	{0x1d676, 0, 0, 0, g(Yes, No, false, false, "", "G")},
	{0x1d677, 0, 0, 0, g(Yes, No, false, false, "", "H")},
	{0x1d678, 0, 0, 0, g(Yes, No, false, false, "", "I")},
	{0x1d679, 0, 0, 0, g(Yes, No, false, false, "", "J")},
	{0x1d67a, 0, 0, 0, g(Yes, No, false, false, "", "K")},
	{0x1d67b, 0, 0, 0, g(Yes, No, false, false, "", "L")},
	{0x1d67c, 0, 0, 0, g(Yes, No, false, false, "", "M")},
	{0x1d67d, 0, 0, 0, g(Yes, No, false, false, "", "N")},
	{0x1d67e, 0, 0, 0, g(Yes, No, false, false, "", "O")},
	{0x1d67f, 0, 0, 0, g(Yes, No, false, false, "", "P")},
	{0x1d680, 0, 0, 0, g(Yes, No, false, false, "", "Q")},
	{0x1d681, 0, 0, 0, g(Yes, No, false, false, "", "R")},
	{0x1d682, 0, 0, 0, g(Yes, No, false, false, "", "S")},
	{0x1d683, 0, 0, 0, g(Yes, No, false, false, "", "T")},
	{0x1d684, 0, 0, 0, g(Yes, No, false, false, "", "U")},
	{0x1d685, 0, 0, 0, g(Yes, No, false, false, "", "V")},
	{0x1d686, 0, 0, 0, g(Yes, No, false, false, "", "W")},
	{0x1d687, 0, 0, 0, g(Yes, No, false, false, "", "X")},
	{0x1d688, 0, 0, 0, g(Yes, No, false, false, "", "Y")},
	{0x1d689, 0, 0, 0, g(Yes, No, false, false, "", "Z")},
	{0x1d68a, 0, 0, 0, g(Yes, No, false, false, "", "a")},
	{0x1d68b, 0, 0, 0, g(Yes, No, false, false, "", "b")},
	{0x1d68c, 0, 0, 0, g(Yes, No, false, false, "", "c")},
	{0x1d68d, 0, 0, 0, g(Yes, No, false, false, "", "d")},
	{0x1d68e, 0, 0, 0, g(Yes, No, false, false, "", "e")},
	{0x1d68f, 0, 0, 0, g(Yes, No, false, false, "", "f")},
	{0x1d690, 0, 0, 0, g(Yes, No, false, false, "", "g")},
	{0x1d691, 0, 0, 0, g(Yes, No, false, false, "", "h")},
	{0x1d692, 0, 0, 0, g(Yes, No, false, false, "", "i")},
	{0x1d693, 0, 0, 0, g(Yes, No, false, false, "", "j")},
	{0x1d694, 0, 0, 0, g(Yes, No, false, false, "", "k")},
	{0x1d695, 0, 0, 0, g(Yes, No, false, false, "", "l")},
	{0x1d696, 0, 0, 0, g(Yes, No, false, false, "", "m")},
	{0x1d697, 0, 0, 0, g(Yes, No, false, false, "", "n")},
	{0x1d698, 0, 0, 0, g(Yes, No, false, false, "", "o")},
	{0x1d699, 0, 0, 0, g(Yes, No, false, false, "", "p")},
	{0x1d69a, 0, 0, 0, g(Yes, No, false, false, "", "q")},
	{0x1d69b, 0, 0, 0, g(Yes, No, false, false, "", "r")},
	{0x1d69c, 0, 0, 0, g(Yes, No, false, false, "", "s")},
	{0x1d69d, 0, 0, 0, g(Yes, No, false, false, "", "t")},
	{0x1d69e, 0, 0, 0, g(Yes, No, false, false, "", "u")},
	{0x1d69f, 0, 0, 0, g(Yes, No, false, false, "", "v")},
	{0x1d6a0, 0, 0, 0, g(Yes, No, false, false, "", "w")},
	{0x1d6a1, 0, 0, 0, g(Yes, No, false, false, "", "x")},
	{0x1d6a2, 0, 0, 0, g(Yes, No, false, false, "", "y")},
	{0x1d6a3, 0, 0, 0, g(Yes, No, false, false, "", "z")},
	{0x1d6a4, 0, 0, 0, g(Yes, No, false, false, "", "ı")},
	{0x1d6a5, 0, 0, 0, g(Yes, No, false, false, "", "ȷ")},
	{0x1d6a6, 0, 0, 0, f(Yes, false, "")},
	{0x1d6a8, 0, 0, 0, g(Yes, No, false, false, "", "Α")},
	{0x1d6a9, 0, 0, 0, g(Yes, No, false, false, "", "Β")},
	{0x1d6aa, 0, 0, 0, g(Yes, No, false, false, "", "Γ")},
	{0x1d6ab, 0, 0, 0, g(Yes, No, false, false, "", "Δ")},
	{0x1d6ac, 0, 0, 0, g(Yes, No, false, false, "", "Ε")},
	{0x1d6ad, 0, 0, 0, g(Yes, No, false, false, "", "Ζ")},
	{0x1d6ae, 0, 0, 0, g(Yes, No, false, false, "", "Η")},
	{0x1d6af, 0, 0, 0, g(Yes, No, false, false, "", "Θ")},
	{0x1d6b0, 0, 0, 0, g(Yes, No, false, false, "", "Ι")},
	{0x1d6b1, 0, 0, 0, g(Yes, No, false, false, "", "Κ")},
	{0x1d6b2, 0, 0, 0, g(Yes, No, false, false, "", "Λ")},
	{0x1d6b3, 0, 0, 0, g(Yes, No, false, false, "", "Μ")},
	{0x1d6b4, 0, 0, 0, g(Yes, No, false, false, "", "Ν")},
	{0x1d6b5, 0, 0, 0, g(Yes, No, false, false, "", "Ξ")},
	{0x1d6b6, 0, 0, 0, g(Yes, No, false, false, "", "Ο")},
	{0x1d6b7, 0, 0, 0, g(Yes, No, false, false, "", "Π")},
	{0x1d6b8, 0, 0, 0, g(Yes, No, false, false, "", "Ρ")},
	{0x1d6b9, 0, 0, 0, g(Yes, No, false, false, "", "Θ")},
	{0x1d6ba, 0, 0, 0, g(Yes, No, false, false, "", "Σ")},
	{0x1d6bb, 0, 0, 0, g(Yes, No, false, false, "", "Τ")},
	{0x1d6bc, 0, 0, 0, g(Yes, No, false, false, "", "Υ")},
	{0x1d6bd, 0, 0, 0, g(Yes, No, false, false, "", "Φ")},
	{0x1d6be, 0, 0, 0, g(Yes, No, false, false, "", "Χ")},
	{0x1d6bf, 0, 0, 0, g(Yes, No, false, false, "", "Ψ")},
	{0x1d6c0, 0, 0, 0, g(Yes, No, false, false, "", "Ω")},
	{0x1d6c1, 0, 0, 0, g(Yes, No, false, false, "", "∇")},
	{0x1d6c2, 0, 0, 0, g(Yes, No, false, false, "", "α")},
	{0x1d6c3, 0, 0, 0, g(Yes, No, false, false, "", "β")},
	{0x1d6c4, 0, 0, 0, g(Yes, No, false, false, "", "γ")},
	{0x1d6c5, 0, 0, 0, g(Yes, No, false, false, "", "δ")},
	{0x1d6c6, 0, 0, 0, g(Yes, No, false, false, "", "ε")},
	{0x1d6c7, 0, 0, 0, g(Yes, No, false, false, "", "ζ")},
	{0x1d6c8, 0, 0, 0, g(Yes, No, false, false, "", "η")},
	{0x1d6c9, 0, 0, 0, g(Yes, No, false, false, "", "θ")},
	{0x1d6ca, 0, 0, 0, g(Yes, No, false, false, "", "ι")},
	{0x1d6cb, 0, 0, 0, g(Yes, No, false, false, "", "κ")},
	{0x1d6cc, 0, 0, 0, g(Yes, No, false, false, "", "λ")},
	{0x1d6cd, 0, 0, 0, g(Yes, No, false, false, "", "μ")},
	{0x1d6ce, 0, 0, 0, g(Yes, No, false, false, "", "ν")},
	{0x1d6cf, 0, 0, 0, g(Yes, No, false, false, "", "ξ")},
	{0x1d6d0, 0, 0, 0, g(Yes, No, false, false, "", "ο")},
	{0x1d6d1, 0, 0, 0, g(Yes, No, false, false, "", "π")},
	{0x1d6d2, 0, 0, 0, g(Yes, No, false, false, "", "ρ")},
	{0x1d6d3, 0, 0, 0, g(Yes, No, false, false, "", "ς")},
	{0x1d6d4, 0, 0, 0, g(Yes, No, false, false, "", "σ")},
	{0x1d6d5, 0, 0, 0, g(Yes, No, false, false, "", "τ")},
	{0x1d6d6, 0, 0, 0, g(Yes, No, false, false, "", "υ")},
	{0x1d6d7, 0, 0, 0, g(Yes, No, false, false, "", "φ")},
	{0x1d6d8, 0, 0, 0, g(Yes, No, false, false, "", "χ")},
	{0x1d6d9, 0, 0, 0, g(Yes, No, false, false, "", "ψ")},
	{0x1d6da, 0, 0, 0, g(Yes, No, false, false, "", "ω")},
	{0x1d6db, 0, 0, 0, g(Yes, No, false, false, "", "∂")},
	{0x1d6dc, 0, 0, 0, g(Yes, No, false, false, "", "ε")},
	{0x1d6dd, 0, 0, 0, g(Yes, No, false, false, "", "θ")},
	{0x1d6de, 0, 0, 0, g(Yes, No, false, false, "", "κ")},
	{0x1d6df, 0, 0, 0, g(Yes, No, false, false, "", "φ")},
	{0x1d6e0, 0, 0, 0, g(Yes, No, false, false, "", "ρ")},
	{0x1d6e1, 0, 0, 0, g(Yes, No, false, false, "", "π")},
	{0x1d6e2, 0, 0, 0, g(Yes, No, false, false, "", "Α")},
	{0x1d6e3, 0, 0, 0, g(Yes, No, false, false, "", "Β")},
	{0x1d6e4, 0, 0, 0, g(Yes, No, false, false, "", "Γ")},
	{0x1d6e5, 0, 0, 0, g(Yes, No, false, false, "", "Δ")},
	{0x1d6e6, 0, 0, 0, g(Yes, No, false, false, "", "Ε")},
	{0x1d6e7, 0, 0, 0, g(Yes, No, false, false, "", "Ζ")},
	{0x1d6e8, 0, 0, 0, g(Yes, No, false, false, "", "Η")},
	{0x1d6e9, 0, 0, 0, g(Yes, No, false, false, "", "Θ")},
	{0x1d6ea, 0, 0, 0, g(Yes, No, false, false, "", "Ι")},
	{0x1d6eb, 0, 0, 0, g(Yes, No, false, false, "", "Κ")},
	{0x1d6ec, 0, 0, 0, g(Yes, No, false, false, "", "Λ")},
	{0x1d6ed, 0, 0, 0, g(Yes, No, false, false, "", "Μ")},
	{0x1d6ee, 0, 0, 0, g(Yes, No, false, false, "", "Ν")},
	{0x1d6ef, 0, 0, 0, g(Yes, No, false, false, "", "Ξ")},
	{0x1d6f0, 0, 0, 0, g(Yes, No, false, false, "", "Ο")},
	{0x1d6f1, 0, 0, 0, g(Yes, No, false, false, "", "Π")},
	{0x1d6f2, 0, 0, 0, g(Yes, No, false, false, "", "Ρ")},
	{0x1d6f3, 0, 0, 0, g(Yes, No, false, false, "", "Θ")},
	{0x1d6f4, 0, 0, 0, g(Yes, No, false, false, "", "Σ")},
	{0x1d6f5, 0, 0, 0, g(Yes, No, false, false, "", "Τ")},
	{0x1d6f6, 0, 0, 0, g(Yes, No, false, false, "", "Υ")},
	{0x1d6f7, 0, 0, 0, g(Yes, No, false, false, "", "Φ")},
	{0x1d6f8, 0, 0, 0, g(Yes, No, false, false, "", "Χ")},
	{0x1d6f9, 0, 0, 0, g(Yes, No, false, false, "", "Ψ")},
	{0x1d6fa, 0, 0, 0, g(Yes, No, false, false, "", "Ω")},
	{0x1d6fb, 0, 0, 0, g(Yes, No, false, false, "", "∇")},
	{0x1d6fc, 0, 0, 0, g(Yes, No, false, false, "", "α")},
	{0x1d6fd, 0, 0, 0, g(Yes, No, false, false, "", "β")},
	{0x1d6fe, 0, 0, 0, g(Yes, No, false, false, "", "γ")},
	{0x1d6ff, 0, 0, 0, g(Yes, No, false, false, "", "δ")},
	{0x1d700, 0, 0, 0, g(Yes, No, false, false, "", "ε")},
	{0x1d701, 0, 0, 0, g(Yes, No, false, false, "", "ζ")},
	{0x1d702, 0, 0, 0, g(Yes, No, false, false, "", "η")},
	{0x1d703, 0, 0, 0, g(Yes, No, false, false, "", "θ")},
	{0x1d704, 0, 0, 0, g(Yes, No, false, false, "", "ι")},
	{0x1d705, 0, 0, 0, g(Yes, No, false, false, "", "κ")},
	{0x1d706, 0, 0, 0, g(Yes, No, false, false, "", "λ")},
	{0x1d707, 0, 0, 0, g(Yes, No, false, false, "", "μ")},
	{0x1d708, 0, 0, 0, g(Yes, No, false, false, "", "ν")},
	{0x1d709, 0, 0, 0, g(Yes, No, false, false, "", "ξ")},
	{0x1d70a, 0, 0, 0, g(Yes, No, false, false, "", "ο")},
	{0x1d70b, 0, 0, 0, g(Yes, No, false, false, "", "π")},
	{0x1d70c, 0, 0, 0, g(Yes, No, false, false, "", "ρ")},
	{0x1d70d, 0, 0, 0, g(Yes, No, false, false, "", "ς")},
	{0x1d70e, 0, 0, 0, g(Yes, No, false, false, "", "σ")},
	{0x1d70f, 0, 0, 0, g(Yes, No, false, false, "", "τ")},
	{0x1d710, 0, 0, 0, g(Yes, No, false, false, "", "υ")},
	{0x1d711, 0, 0, 0, g(Yes, No, false, false, "", "φ")},
	{0x1d712, 0, 0, 0, g(Yes, No, false, false, "", "χ")},
	{0x1d713, 0, 0, 0, g(Yes, No, false, false, "", "ψ")},
	{0x1d714, 0, 0, 0, g(Yes, No, false, false, "", "ω")},
	{0x1d715, 0, 0, 0, g(Yes, No, false, false, "", "∂")},
	{0x1d716, 0, 0, 0, g(Yes, No, false, false, "", "ε")},
	{0x1d717, 0, 0, 0, g(Yes, No, false, false, "", "θ")},
	{0x1d718, 0, 0, 0, g(Yes, No, false, false, "", "κ")},
	{0x1d719, 0, 0, 0, g(Yes, No, false, false, "", "φ")},
	{0x1d71a, 0, 0, 0, g(Yes, No, false, false, "", "ρ")},
	{0x1d71b, 0, 0, 0, g(Yes, No, false, false, "", "π")},
	{0x1d71c, 0, 0, 0, g(Yes, No, false, false, "", "Α")},
	{0x1d71d, 0, 0, 0, g(Yes, No, false, false, "", "Β")},
	{0x1d71e, 0, 0, 0, g(Yes, No, false, false, "", "Γ")},
	{0x1d71f, 0, 0, 0, g(Yes, No, false, false, "", "Δ")},
	{0x1d720, 0, 0, 0, g(Yes, No, false, false, "", "Ε")},
	{0x1d721, 0, 0, 0, g(Yes, No, false, false, "", "Ζ")},
	{0x1d722, 0, 0, 0, g(Yes, No, false, false, "", "Η")},
	{0x1d723, 0, 0, 0, g(Yes, No, false, false, "", "Θ")},
	{0x1d724, 0, 0, 0, g(Yes, No, false, false, "", "Ι")},
	{0x1d725, 0, 0, 0, g(Yes, No, false, false, "", "Κ")},
	{0x1d726, 0, 0, 0, g(Yes, No, false, false, "", "Λ")},
	{0x1d727, 0, 0, 0, g(Yes, No, false, false, "", "Μ")},
	{0x1d728, 0, 0, 0, g(Yes, No, false, false, "", "Ν")},
	{0x1d729, 0, 0, 0, g(Yes, No, false, false, "", "Ξ")},
	{0x1d72a, 0, 0, 0, g(Yes, No, false, false, "", "Ο")},
	{0x1d72b, 0, 0, 0, g(Yes, No, false, false, "", "Π")},
	{0x1d72c, 0, 0, 0, g(Yes, No, false, false, "", "Ρ")},
	{0x1d72d, 0, 0, 0, g(Yes, No, false, false, "", "Θ")},
	{0x1d72e, 0, 0, 0, g(Yes, No, false, false, "", "Σ")},
	{0x1d72f, 0, 0, 0, g(Yes, No, false, false, "", "Τ")},
	{0x1d730, 0, 0, 0, g(Yes, No, false, false, "", "Υ")},
	{0x1d731, 0, 0, 0, g(Yes, No, false, false, "", "Φ")},
	{0x1d732, 0, 0, 0, g(Yes, No, false, false, "", "Χ")},
	{0x1d733, 0, 0, 0, g(Yes, No, false, false, "", "Ψ")},
	{0x1d734, 0, 0, 0, g(Yes, No, false, false, "", "Ω")},
	{0x1d735, 0, 0, 0, g(Yes, No, false, false, "", "∇")},
	{0x1d736, 0, 0, 0, g(Yes, No, false, false, "", "α")},
	{0x1d737, 0, 0, 0, g(Yes, No, false, false, "", "β")},
	{0x1d738, 0, 0, 0, g(Yes, No, false, false, "", "γ")},
	{0x1d739, 0, 0, 0, g(Yes, No, false, false, "", "δ")},
	{0x1d73a, 0, 0, 0, g(Yes, No, false, false, "", "ε")},
	{0x1d73b, 0, 0, 0, g(Yes, No, false, false, "", "ζ")},
	{0x1d73c, 0, 0, 0, g(Yes, No, false, false, "", "η")},
	{0x1d73d, 0, 0, 0, g(Yes, No, false, false, "", "θ")},
	{0x1d73e, 0, 0, 0, g(Yes, No, false, false, "", "ι")},
	{0x1d73f, 0, 0, 0, g(Yes, No, false, false, "", "κ")},
	{0x1d740, 0, 0, 0, g(Yes, No, false, false, "", "λ")},
	{0x1d741, 0, 0, 0, g(Yes, No, false, false, "", "μ")},
	{0x1d742, 0, 0, 0, g(Yes, No, false, false, "", "ν")},
	{0x1d743, 0, 0, 0, g(Yes, No, false, false, "", "ξ")},
	{0x1d744, 0, 0, 0, g(Yes, No, false, false, "", "ο")},
	{0x1d745, 0, 0, 0, g(Yes, No, false, false, "", "π")},
	{0x1d746, 0, 0, 0, g(Yes, No, false, false, "", "ρ")},
	{0x1d747, 0, 0, 0, g(Yes, No, false, false, "", "ς")},
	{0x1d748, 0, 0, 0, g(Yes, No, false, false, "", "σ")},
	{0x1d749, 0, 0, 0, g(Yes, No, false, false, "", "τ")},
	{0x1d74a, 0, 0, 0, g(Yes, No, false, false, "", "υ")},
	{0x1d74b, 0, 0, 0, g(Yes, No, false, false, "", "φ")},
	{0x1d74c, 0, 0, 0, g(Yes, No, false, false, "", "χ")},
	{0x1d74d, 0, 0, 0, g(Yes, No, false, false, "", "ψ")},
	{0x1d74e, 0, 0, 0, g(Yes, No, false, false, "", "ω")},
	{0x1d74f, 0, 0, 0, g(Yes, No, false, false, "", "∂")},
	{0x1d750, 0, 0, 0, g(Yes, No, false, false, "", "ε")},
	{0x1d751, 0, 0, 0, g(Yes, No, false, false, "", "θ")},
	{0x1d752, 0, 0, 0, g(Yes, No, false, false, "", "κ")},
	{0x1d753, 0, 0, 0, g(Yes, No, false, false, "", "φ")},
	{0x1d754, 0, 0, 0, g(Yes, No, false, false, "", "ρ")},
	{0x1d755, 0, 0, 0, g(Yes, No, false, false, "", "π")},
	{0x1d756, 0, 0, 0, g(Yes, No, false, false, "", "Α")},
	{0x1d757, 0, 0, 0, g(Yes, No, false, false, "", "Β")},
	{0x1d758, 0, 0, 0, g(Yes, No, false, false, "", "Γ")},
	{0x1d759, 0, 0, 0, g(Yes, No, false, false, "", "Δ")},
	{0x1d75a, 0, 0, 0, g(Yes, No, false, false, "", "Ε")},
	{0x1d75b, 0, 0, 0, g(Yes, No, false, false, "", "Ζ")},
	{0x1d75c, 0, 0, 0, g(Yes, No, false, false, "", "Η")},
	{0x1d75d, 0, 0, 0, g(Yes, No, false, false, "", "Θ")},
	{0x1d75e, 0, 0, 0, g(Yes, No, false, false, "", "Ι")},
	{0x1d75f, 0, 0, 0, g(Yes, No, false, false, "", "Κ")},
	{0x1d760, 0, 0, 0, g(Yes, No, false, false, "", "Λ")},
	{0x1d761, 0, 0, 0, g(Yes, No, false, false, "", "Μ")},
	{0x1d762, 0, 0, 0, g(Yes, No, false, false, "", "Ν")},
	{0x1d763, 0, 0, 0, g(Yes, No, false, false, "", "Ξ")},
	{0x1d764, 0, 0, 0, g(Yes, No, false, false, "", "Ο")},
	{0x1d765, 0, 0, 0, g(Yes, No, false, false, "", "Π")},
	{0x1d766, 0, 0, 0, g(Yes, No, false, false, "", "Ρ")},
	{0x1d767, 0, 0, 0, g(Yes, No, false, false, "", "Θ")},
	{0x1d768, 0, 0, 0, g(Yes, No, false, false, "", "Σ")},
	{0x1d769, 0, 0, 0, g(Yes, No, false, false, "", "Τ")},
	{0x1d76a, 0, 0, 0, g(Yes, No, false, false, "", "Υ")},
	{0x1d76b, 0, 0, 0, g(Yes, No, false, false, "", "Φ")},
	{0x1d76c, 0, 0, 0, g(Yes, No, false, false, "", "Χ")},
	{0x1d76d, 0, 0, 0, g(Yes, No, false, false, "", "Ψ")},
	{0x1d76e, 0, 0, 0, g(Yes, No, false, false, "", "Ω")},
	{0x1d76f, 0, 0, 0, g(Yes, No, false, false, "", "∇")},
	{0x1d770, 0, 0, 0, g(Yes, No, false, false, "", "α")},
	{0x1d771, 0, 0, 0, g(Yes, No, false, false, "", "β")},
	{0x1d772, 0, 0, 0, g(Yes, No, false, false, "", "γ")},
	{0x1d773, 0, 0, 0, g(Yes, No, false, false, "", "δ")},
	{0x1d774, 0, 0, 0, g(Yes, No, false, false, "", "ε")},
	{0x1d775, 0, 0, 0, g(Yes, No, false, false, "", "ζ")},
	{0x1d776, 0, 0, 0, g(Yes, No, false, false, "", "η")},
	{0x1d777, 0, 0, 0, g(Yes, No, false, false, "", "θ")},
	{0x1d778, 0, 0, 0, g(Yes, No, false, false, "", "ι")},
	{0x1d779, 0, 0, 0, g(Yes, No, false, false, "", "κ")},
	{0x1d77a, 0, 0, 0, g(Yes, No, false, false, "", "λ")},
	{0x1d77b, 0, 0, 0, g(Yes, No, false, false, "", "μ")},
	{0x1d77c, 0, 0, 0, g(Yes, No, false, false, "", "ν")},
	{0x1d77d, 0, 0, 0, g(Yes, No, false, false, "", "ξ")},
	{0x1d77e, 0, 0, 0, g(Yes, No, false, false, "", "ο")},
	{0x1d77f, 0, 0, 0, g(Yes, No, false, false, "", "π")},
	{0x1d780, 0, 0, 0, g(Yes, No, false, false, "", "ρ")},
	{0x1d781, 0, 0, 0, g(Yes, No, false, false, "", "ς")},
	{0x1d782, 0, 0, 0, g(Yes, No, false, false, "", "σ")},
	{0x1d783, 0, 0, 0, g(Yes, No, false, false, "", "τ")},
	{0x1d784, 0, 0, 0, g(Yes, No, false, false, "", "υ")},
	{0x1d785, 0, 0, 0, g(Yes, No, false, false, "", "φ")},
	{0x1d786, 0, 0, 0, g(Yes, No, false, false, "", "χ")},
	{0x1d787, 0, 0, 0, g(Yes, No, false, false, "", "ψ")},
	{0x1d788, 0, 0, 0, g(Yes, No, false, false, "", "ω")},
	{0x1d789, 0, 0, 0, g(Yes, No, false, false, "", "∂")},
	{0x1d78a, 0, 0, 0, g(Yes, No, false, false, "", "ε")},
	{0x1d78b, 0, 0, 0, g(Yes, No, false, false, "", "θ")},
	{0x1d78c, 0, 0, 0, g(Yes, No, false, false, "", "κ")},
	{0x1d78d, 0, 0, 0, g(Yes, No, false, false, "", "φ")},
	{0x1d78e, 0, 0, 0, g(Yes, No, false, false, "", "ρ")},
	{0x1d78f, 0, 0, 0, g(Yes, No, false, false, "", "π")},
	{0x1d790, 0, 0, 0, g(Yes, No, false, false, "", "Α")},
	{0x1d791, 0, 0, 0, g(Yes, No, false, false, "", "Β")},
	{0x1d792, 0, 0, 0, g(Yes, No, false, false, "", "Γ")},
	{0x1d793, 0, 0, 0, g(Yes, No, false, false, "", "Δ")},
	{0x1d794, 0, 0, 0, g(Yes, No, false, false, "", "Ε")},
	{0x1d795, 0, 0, 0, g(Yes, No, false, false, "", "Ζ")},
	{0x1d796, 0, 0, 0, g(Yes, No, false, false, "", "Η")},
	{0x1d797, 0, 0, 0, g(Yes, No, false, false, "", "Θ")},
	{0x1d798, 0, 0, 0, g(Yes, No, false, false, "", "Ι")},
	{0x1d799, 0, 0, 0, g(Yes, No, false, false, "", "Κ")},
	{0x1d79a, 0, 0, 0, g(Yes, No, false, false, "", "Λ")},
	{0x1d79b, 0, 0, 0, g(Yes, No, false, false, "", "Μ")},
	{0x1d79c, 0, 0, 0, g(Yes, No, false, false, "", "Ν")},
	{0x1d79d, 0, 0, 0, g(Yes, No, false, false, "", "Ξ")},
	{0x1d79e, 0, 0, 0, g(Yes, No, false, false, "", "Ο")},
	{0x1d79f, 0, 0, 0, g(Yes, No, false, false, "", "Π")},
	{0x1d7a0, 0, 0, 0, g(Yes, No, false, false, "", "Ρ")},
	{0x1d7a1, 0, 0, 0, g(Yes, No, false, false, "", "Θ")},
	{0x1d7a2, 0, 0, 0, g(Yes, No, false, false, "", "Σ")},
	{0x1d7a3, 0, 0, 0, g(Yes, No, false, false, "", "Τ")},
	{0x1d7a4, 0, 0, 0, g(Yes, No, false, false, "", "Υ")},
	{0x1d7a5, 0, 0, 0, g(Yes, No, false, false, "", "Φ")},
	{0x1d7a6, 0, 0, 0, g(Yes, No, false, false, "", "Χ")},
	{0x1d7a7, 0, 0, 0, g(Yes, No, false, false, "", "Ψ")},
	{0x1d7a8, 0, 0, 0, g(Yes, No, false, false, "", "Ω")},
	{0x1d7a9, 0, 0, 0, g(Yes, No, false, false, "", "∇")},
	{0x1d7aa, 0, 0, 0, g(Yes, No, false, false, "", "α")},
	{0x1d7ab, 0, 0, 0, g(Yes, No, false, false, "", "β")},
	{0x1d7ac, 0, 0, 0, g(Yes, No, false, false, "", "γ")},
	{0x1d7ad, 0, 0, 0, g(Yes, No, false, false, "", "δ")},
	{0x1d7ae, 0, 0, 0, g(Yes, No, false, false, "", "ε")},
	{0x1d7af, 0, 0, 0, g(Yes, No, false, false, "", "ζ")},
	{0x1d7b0, 0, 0, 0, g(Yes, No, false, false, "", "η")},
	{0x1d7b1, 0, 0, 0, g(Yes, No, false, false, "", "θ")},
	{0x1d7b2, 0, 0, 0, g(Yes, No, false, false, "", "ι")},
	{0x1d7b3, 0, 0, 0, g(Yes, No, false, false, "", "κ")},
	{0x1d7b4, 0, 0, 0, g(Yes, No, false, false, "", "λ")},
	{0x1d7b5, 0, 0, 0, g(Yes, No, false, false, "", "μ")},
	{0x1d7b6, 0, 0, 0, g(Yes, No, false, false, "", "ν")},
	{0x1d7b7, 0, 0, 0, g(Yes, No, false, false, "", "ξ")},
	{0x1d7b8, 0, 0, 0, g(Yes, No, false, false, "", "ο")},
	{0x1d7b9, 0, 0, 0, g(Yes, No, false, false, "", "π")},
	{0x1d7ba, 0, 0, 0, g(Yes, No, false, false, "", "ρ")},
	{0x1d7bb, 0, 0, 0, g(Yes, No, false, false, "", "ς")},
	{0x1d7bc, 0, 0, 0, g(Yes, No, false, false, "", "σ")},
	{0x1d7bd, 0, 0, 0, g(Yes, No, false, false, "", "τ")},
	{0x1d7be, 0, 0, 0, g(Yes, No, false, false, "", "υ")},
	{0x1d7bf, 0, 0, 0, g(Yes, No, false, false, "", "φ")},
	{0x1d7c0, 0, 0, 0, g(Yes, No, false, false, "", "χ")},
	{0x1d7c1, 0, 0, 0, g(Yes, No, false, false, "", "ψ")},
	{0x1d7c2, 0, 0, 0, g(Yes, No, false, false, "", "ω")},
	{0x1d7c3, 0, 0, 0, g(Yes, No, false, false, "", "∂")},
	{0x1d7c4, 0, 0, 0, g(Yes, No, false, false, "", "ε")},
	{0x1d7c5, 0, 0, 0, g(Yes, No, false, false, "", "θ")},
	{0x1d7c6, 0, 0, 0, g(Yes, No, false, false, "", "κ")},
	{0x1d7c7, 0, 0, 0, g(Yes, No, false, false, "", "φ")},
	{0x1d7c8, 0, 0, 0, g(Yes, No, false, false, "", "ρ")},
	{0x1d7c9, 0, 0, 0, g(Yes, No, false, false, "", "π")},
	{0x1d7ca, 0, 0, 0, g(Yes, No, false, false, "", "Ϝ")},
	{0x1d7cb, 0, 0, 0, g(Yes, No, false, false, "", "ϝ")},
	{0x1d7cc, 0, 0, 0, f(Yes, false, "")},
	{0x1d7ce, 0, 0, 0, g(Yes, No, false, false, "", "0")},
	{0x1d7cf, 0, 0, 0, g(Yes, No, false, false, "", "1")},
	{0x1d7d0, 0, 0, 0, g(Yes, No, false, false, "", "2")},
	{0x1d7d1, 0, 0, 0, g(Yes, No, false, false, "", "3")},
	{0x1d7d2, 0, 0, 0, g(Yes, No, false, false, "", "4")},
	{0x1d7d3, 0, 0, 0, g(Yes, No, false, false, "", "5")},
	{0x1d7d4, 0, 0, 0, g(Yes, No, false, false, "", "6")},
	{0x1d7d5, 0, 0, 0, g(Yes, No, false, false, "", "7")},
	{0x1d7d6, 0, 0, 0, g(Yes, No, false, false, "", "8")},
	{0x1d7d7, 0, 0, 0, g(Yes, No, false, false, "", "9")},
	{0x1d7d8, 0, 0, 0, g(Yes, No, false, false, "", "0")},
	{0x1d7d9, 0, 0, 0, g(Yes, No, false, false, "", "1")},
	{0x1d7da, 0, 0, 0, g(Yes, No, false, false, "", "2")},
	{0x1d7db, 0, 0, 0, g(Yes, No, false, false, "", "3")},
	{0x1d7dc, 0, 0, 0, g(Yes, No, false, false, "", "4")},
	{0x1d7dd, 0, 0, 0, g(Yes, No, false, false, "", "5")},
	{0x1d7de, 0, 0, 0, g(Yes, No, false, false, "", "6")},
	{0x1d7df, 0, 0, 0, g(Yes, No, false, false, "", "7")},
	{0x1d7e0, 0, 0, 0, g(Yes, No, false, false, "", "8")},
	{0x1d7e1, 0, 0, 0, g(Yes, No, false, false, "", "9")},
	{0x1d7e2, 0, 0, 0, g(Yes, No, false, false, "", "0")},
	{0x1d7e3, 0, 0, 0, g(Yes, No, false, false, "", "1")},
	{0x1d7e4, 0, 0, 0, g(Yes, No, false, false, "", "2")},
	{0x1d7e5, 0, 0, 0, g(Yes, No, false, false, "", "3")},
	{0x1d7e6, 0, 0, 0, g(Yes, No, false, false, "", "4")},
	{0x1d7e7, 0, 0, 0, g(Yes, No, false, false, "", "5")},
	{0x1d7e8, 0, 0, 0, g(Yes, No, false, false, "", "6")},
	{0x1d7e9, 0, 0, 0, g(Yes, No, false, false, "", "7")},
	{0x1d7ea, 0, 0, 0, g(Yes, No, false, false, "", "8")},
	{0x1d7eb, 0, 0, 0, g(Yes, No, false, false, "", "9")},
	{0x1d7ec, 0, 0, 0, g(Yes, No, false, false, "", "0")},
	{0x1d7ed, 0, 0, 0, g(Yes, No, false, false, "", "1")},
	{0x1d7ee, 0, 0, 0, g(Yes, No, false, false, "", "2")},
	{0x1d7ef, 0, 0, 0, g(Yes, No, false, false, "", "3")},
	{0x1d7f0, 0, 0, 0, g(Yes, No, false, false, "", "4")},
	{0x1d7f1, 0, 0, 0, g(Yes, No, false, false, "", "5")},
	{0x1d7f2, 0, 0, 0, g(Yes, No, false, false, "", "6")},
	{0x1d7f3, 0, 0, 0, g(Yes, No, false, false, "", "7")},
	{0x1d7f4, 0, 0, 0, g(Yes, No, false, false, "", "8")},
	{0x1d7f5, 0, 0, 0, g(Yes, No, false, false, "", "9")},
	{0x1d7f6, 0, 0, 0, g(Yes, No, false, false, "", "0")},
	{0x1d7f7, 0, 0, 0, g(Yes, No, false, false, "", "1")},
	{0x1d7f8, 0, 0, 0, g(Yes, No, false, false, "", "2")},
	{0x1d7f9, 0, 0, 0, g(Yes, No, false, false, "", "3")},
	{0x1d7fa, 0, 0, 0, g(Yes, No, false, false, "", "4")},
	{0x1d7fb, 0, 0, 0, g(Yes, No, false, false, "", "5")},
	{0x1d7fc, 0, 0, 0, g(Yes, No, false, false, "", "6")},
	{0x1d7fd, 0, 0, 0, g(Yes, No, false, false, "", "7")},
	{0x1d7fe, 0, 0, 0, g(Yes, No, false, false, "", "8")},
	{0x1d7ff, 0, 0, 0, g(Yes, No, false, false, "", "9")},
	{0x1d800, 0, 0, 0, f(Yes, false, "")},
	{0x1e000, 230, 1, 1, f(Yes, false, "")},
	{0x1e007, 0, 0, 0, f(Yes, false, "")},
	{0x1e008, 230, 1, 1, f(Yes, false, "")},
	{0x1e019, 0, 0, 0, f(Yes, false, "")},
	{0x1e01b, 230, 1, 1, f(Yes, false, "")},
	{0x1e022, 0, 0, 0, f(Yes, false, "")},
	{0x1e023, 230, 1, 1, f(Yes, false, "")},
	{0x1e025, 0, 0, 0, f(Yes, false, "")},
	{0x1e026, 230, 1, 1, f(Yes, false, "")},
	{0x1e02b, 0, 0, 0, f(Yes, false, "")},
	{0x1e8d0, 220, 1, 1, f(Yes, false, "")},
	{0x1e8d7, 0, 0, 0, f(Yes, false, "")},
	{0x1e944, 230, 1, 1, f(Yes, false, "")},
	{0x1e94a, 7, 1, 1, f(Yes, false, "")},
	{0x1e94b, 0, 0, 0, f(Yes, false, "")},
	{0x1ee00, 0, 0, 0, g(Yes, No, false, false, "", "ا")},
	{0x1ee01, 0, 0, 0, g(Yes, No, false, false, "", "ب")},
	{0x1ee02, 0, 0, 0, g(Yes, No, false, false, "", "ج")},
	{0x1ee03, 0, 0, 0, g(Yes, No, false, false, "", "د")},
	{0x1ee04, 0, 0, 0, f(Yes, false, "")},
	{0x1ee05, 0, 0, 0, g(Yes, No, false, false, "", "و")},
	{0x1ee06, 0, 0, 0, g(Yes, No, false, false, "", "ز")},
	{0x1ee07, 0, 0, 0, g(Yes, No, false, false, "", "ح")},
	{0x1ee08, 0, 0, 0, g(Yes, No, false, false, "", "ط")},
	{0x1ee09, 0, 0, 0, g(Yes, No, false, false, "", "ي")},
	{0x1ee0a, 0, 0, 0, g(Yes, No, false, false, "", "ك")},
	{0x1ee0b, 0, 0, 0, g(Yes, No, false, false, "", "ل")},
	{0x1ee0c, 0, 0, 0, g(Yes, No, false, false, "", "م")},
	{0x1ee0d, 0, 0, 0, g(Yes, No, false, false, "", "ن")},
	{0x1ee0e, 0, 0, 0, g(Yes, No, false, false, "", "س")},
	{0x1ee0f, 0, 0, 0, g(Yes, No, false, false, "", "ع")},
	{0x1ee10, 0, 0, 0, g(Yes, No, false, false, "", "ف")},
	{0x1ee11, 0, 0, 0, g(Yes, No, false, false, "", "ص")},
	{0x1ee12, 0, 0, 0, g(Yes, No, false, false, "", "ق")},
	{0x1ee13, 0, 0, 0, g(Yes, No, false, false, "", "ر")},
	{0x1ee14, 0, 0, 0, g(Yes, No, false, false, "", "ش")},
	{0x1ee15, 0, 0, 0, g(Yes, No, false, false, "", "ت")},
	{0x1ee16, 0, 0, 0, g(Yes, No, false, false, "", "ث")},
	{0x1ee17, 0, 0, 0, g(Yes, No, false, false, "", "خ")},
	{0x1ee18, 0, 0, 0, g(Yes, No, false, false, "", "ذ")},
	{0x1ee19, 0, 0, 0, g(Yes, No, false, false, "", "ض")},
	{0x1ee1a, 0, 0, 0, g(Yes, No, false, false, "", "ظ")},
	{0x1ee1b, 0, 0, 0, g(Yes, No, false, false, "", "غ")},
	{0x1ee1c, 0, 0, 0, g(Yes, No, false, false, "", "ٮ")},
	{0x1ee1d, 0, 0, 0, g(Yes, No, false, false, "", "ں")},
	{0x1ee1e, 0, 0, 0, g(Yes, No, false, false, "", "ڡ")},
	{0x1ee1f, 0, 0, 0, g(Yes, No, false, false, "", "ٯ")},
	{0x1ee20, 0, 0, 0, f(Yes, false, "")},
	{0x1ee21, 0, 0, 0, g(Yes, No, false, false, "", "ب")},
	{0x1ee22, 0, 0, 0, g(Yes, No, false, false, "", "ج")},
	{0x1ee23, 0, 0, 0, f(Yes, false, "")},
	{0x1ee24, 0, 0, 0, g(Yes, No, false, false, "", "ه")},
	{0x1ee25, 0, 0, 0, f(Yes, false, "")},
	{0x1ee27, 0, 0, 0, g(Yes, No, false, false, "", "ح")},
	{0x1ee28, 0, 0, 0, f(Yes, false, "")},
	{0x1ee29, 0, 0, 0, g(Yes, No, false, false, "", "ي")},
	{0x1ee2a, 0, 0, 0, g(Yes, No, false, false, "", "ك")},
	{0x1ee2b, 0, 0, 0, g(Yes, No, false, false, "", "ل")},
	{0x1ee2c, 0, 0, 0, g(Yes, No, false, false, "", "م")},
	{0x1ee2d, 0, 0, 0, g(Yes, No, false, false, "", "ن")},
	{0x1ee2e, 0, 0, 0, g(Yes, No, false, false, "", "س")},
	{0x1ee2f, 0, 0, 0, g(Yes, No, false, false, "", "ع")},
	{0x1ee30, 0, 0, 0, g(Yes, No, false, false, "", "ف")},
	{0x1ee31, 0, 0, 0, g(Yes, No, false, false, "", "ص")},
	{0x1ee32, 0, 0, 0, g(Yes, No, false, false, "", "ق")},
	{0x1ee33, 0, 0, 0, f(Yes, false, "")},
	{0x1ee34, 0, 0, 0, g(Yes, No, false, false, "", "ش")},
	{0x1ee35, 0, 0, 0, g(Yes, No, false, false, "", "ت")},
	{0x1ee36, 0, 0, 0, g(Yes, No, false, false, "", "ث")},
	{0x1ee37, 0, 0, 0, g(Yes, No, false, false, "", "خ")},
	{0x1ee38, 0, 0, 0, f(Yes, false, "")},
	{0x1ee39, 0, 0, 0, g(Yes, No, false, false, "", "ض")},
	{0x1ee3a, 0, 0, 0, f(Yes, false, "")},
	{0x1ee3b, 0, 0, 0, g(Yes, No, false, false, "", "غ")},
	{0x1ee3c, 0, 0, 0, f(Yes, false, "")},
	{0x1ee42, 0, 0, 0, g(Yes, No, false, false, "", "ج")},
	{0x1ee43, 0, 0, 0, f(Yes, false, "")},
	{0x1ee47, 0, 0, 0, g(Yes, No, false, false, "", "ح")},
	{0x1ee48, 0, 0, 0, f(Yes, false, "")},
	{0x1ee49, 0, 0, 0, g(Yes, No, false, false, "", "ي")},
	{0x1ee4a, 0, 0, 0, f(Yes, false, "")},
	{0x1ee4b, 0, 0, 0, g(Yes, No, false, false, "", "ل")},
	{0x1ee4c, 0, 0, 0, f(Yes, false, "")},
	{0x1ee4d, 0, 0, 0, g(Yes, No, false, false, "", "ن")},
	{0x1ee4e, 0, 0, 0, g(Yes, No, false, false, "", "س")},
	{0x1ee4f, 0, 0, 0, g(Yes, No, false, false, "", "ع")},
	{0x1ee50, 0, 0, 0, f(Yes, false, "")},
	{0x1ee51, 0, 0, 0, g(Yes, No, false, false, "", "ص")},
	{0x1ee52, 0, 0, 0, g(Yes, No, false, false, "", "ق")},
	{0x1ee53, 0, 0, 0, f(Yes, false, "")},
	{0x1ee54, 0, 0, 0, g(Yes, No, false, false, "", "ش")},
	{0x1ee55, 0, 0, 0, f(Yes, false, "")},
	{0x1ee57, 0, 0, 0, g(Yes, No, false, false, "", "خ")},
	{0x1ee58, 0, 0, 0, f(Yes, false, "")},
	{0x1ee59, 0, 0, 0, g(Yes, No, false, false, "", "ض")},
	{0x1ee5a, 0, 0, 0, f(Yes, false, "")},
	{0x1ee5b, 0, 0, 0, g(Yes, No, false, false, "", "غ")},
	{0x1ee5c, 0, 0, 0, f(Yes, false, "")},
	{0x1ee5d, 0, 0, 0, g(Yes, No, false, false, "", "ں")},
	{0x1ee5e, 0, 0, 0, f(Yes, false, "")},
	{0x1ee5f, 0, 0, 0, g(Yes, No, false, false, "", "ٯ")},
	{0x1ee60, 0, 0, 0, f(Yes, false, "")},
	{0x1ee61, 0, 0, 0, g(Yes, No, false, false, "", "ب")},
	{0x1ee62, 0, 0, 0, g(Yes, No, false, false, "", "ج")},
	{0x1ee63, 0, 0, 0, f(Yes, false, "")},
	{0x1ee64, 0, 0, 0, g(Yes, No, false, false, "", "ه")},
	{0x1ee65, 0, 0, 0, f(Yes, false, "")},
	{0x1ee67, 0, 0, 0, g(Yes, No, false, false, "", "ح")},
	{0x1ee68, 0, 0, 0, g(Yes, No, false, false, "", "ط")},
	{0x1ee69, 0, 0, 0, g(Yes, No, false, false, "", "ي")},
	{0x1ee6a, 0, 0, 0, g(Yes, No, false, false, "", "ك")},
	{0x1ee6b, 0, 0, 0, f(Yes, false, "")},
	{0x1ee6c, 0, 0, 0, g(Yes, No, false, false, "", "م")},
	{0x1ee6d, 0, 0, 0, g(Yes, No, false, false, "", "ن")},
	{0x1ee6e, 0, 0, 0, g(Yes, No, false, false, "", "س")},
	{0x1ee6f, 0, 0, 0, g(Yes, No, false, false, "", "ع")},
	{0x1ee70, 0, 0, 0, g(Yes, No, false, false, "", "ف")},
	{0x1ee71, 0, 0, 0, g(Yes, No, false, false, "", "ص")},
	{0x1ee72, 0, 0, 0, g(Yes, No, false, false, "", "ق")},
	{0x1ee73, 0, 0, 0, f(Yes, false, "")},
	{0x1ee74, 0, 0, 0, g(Yes, No, false, false, "", "ش")},
	{0x1ee75, 0, 0, 0, g(Yes, No, false, false, "", "ت")},
	{0x1ee76, 0, 0, 0, g(Yes, No, false, false, "", "ث")},
	{0x1ee77, 0, 0, 0, g(Yes, No, false, false, "", "خ")},
	{0x1ee78, 0, 0, 0, f(Yes, false, "")},
	{0x1ee79, 0, 0, 0, g(Yes, No, false, false, "", "ض")},
	{0x1ee7a, 0, 0, 0, g(Yes, No, false, false, "", "ظ")},
	{0x1ee7b, 0, 0, 0, g(Yes, No, false, false, "", "غ")},
	{0x1ee7c, 0, 0, 0, g(Yes, No, false, false, "", "ٮ")},
	{0x1ee7d, 0, 0, 0, f(Yes, false, "")},
	{0x1ee7e, 0, 0, 0, g(Yes, No, false, false, "", "ڡ")},
	{0x1ee7f, 0, 0, 0, f(Yes, false, "")},
	{0x1ee80, 0, 0, 0, g(Yes, No, false, false, "", "ا")},
	{0x1ee81, 0, 0, 0, g(Yes, No, false, false, "", "ب")},
	{0x1ee82, 0, 0, 0, g(Yes, No, false, false, "", "ج")},
	{0x1ee83, 0, 0, 0, g(Yes, No, false, false, "", "د")},
	{0x1ee84, 0, 0, 0, g(Yes, No, false, false, "", "ه")},
	{0x1ee85, 0, 0, 0, g(Yes, No, false, false, "", "و")},
	{0x1ee86, 0, 0, 0, g(Yes, No, false, false, "", "ز")},
	{0x1ee87, 0, 0, 0, g(Yes, No, false, false, "", "ح")},
	{0x1ee88, 0, 0, 0, g(Yes, No, false, false, "", "ط")},
	{0x1ee89, 0, 0, 0, g(Yes, No, false, false, "", "ي")},
	{0x1ee8a, 0, 0, 0, f(Yes, false, "")},
	{0x1ee8b, 0, 0, 0, g(Yes, No, false, false, "", "ل")},
	{0x1ee8c, 0, 0, 0, g(Yes, No, false, false, "", "م")},
	{0x1ee8d, 0, 0, 0, g(Yes, No, false, false, "", "ن")},
	{0x1ee8e, 0, 0, 0, g(Yes, No, false, false, "", "س")},
	{0x1ee8f, 0, 0, 0, g(Yes, No, false, false, "", "ع")},
	{0x1ee90, 0, 0, 0, g(Yes, No, false, false, "", "ف")},
	{0x1ee91, 0, 0, 0, g(Yes, No, false, false, "", "ص")},
	{0x1ee92, 0, 0, 0, g(Yes, No, false, false, "", "ق")},
	{0x1ee93, 0, 0, 0, g(Yes, No, false, false, "", "ر")},
	{0x1ee94, 0, 0, 0, g(Yes, No, false, false, "", "ش")},
	{0x1ee95, 0, 0, 0, g(Yes, No, false, false, "", "ت")},
	{0x1ee96, 0, 0, 0, g(Yes, No, false, false, "", "ث")},
	{0x1ee97, 0, 0, 0, g(Yes, No, false, false, "", "خ")},
	{0x1ee98, 0, 0, 0, g(Yes, No, false, false, "", "ذ")},
	{0x1ee99, 0, 0, 0, g(Yes, No, false, false, "", "ض")},
	{0x1ee9a, 0, 0, 0, g(Yes, No, false, false, "", "ظ")},
	{0x1ee9b, 0, 0, 0, g(Yes, No, false, false, "", "غ")},
	{0x1ee9c, 0, 0, 0, f(Yes, false, "")},
	{0x1eea1, 0, 0, 0, g(Yes, No, false, false, "", "ب")},
	{0x1eea2, 0, 0, 0, g(Yes, No, false, false, "", "ج")},
	{0x1eea3, 0, 0, 0, g(Yes, No, false, false, "", "د")},
	{0x1eea4, 0, 0, 0, f(Yes, false, "")},
	{0x1eea5, 0, 0, 0, g(Yes, No, false, false, "", "و")},
	{0x1eea6, 0, 0, 0, g(Yes, No, false, false, "", "ز")},
	{0x1eea7, 0, 0, 0, g(Yes, No, false, false, "", "ح")},
	{0x1eea8, 0, 0, 0, g(Yes, No, false, false, "", "ط")},
	{0x1eea9, 0, 0, 0, g(Yes, No, false, false, "", "ي")},
	{0x1eeaa, 0, 0, 0, f(Yes, false, "")},
	{0x1eeab, 0, 0, 0, g(Yes, No, false, false, "", "ل")},
	{0x1eeac, 0, 0, 0, g(Yes, No, false, false, "", "م")},
	{0x1eead, 0, 0, 0, g(Yes, No, false, false, "", "ن")},
	{0x1eeae, 0, 0, 0, g(Yes, No, false, false, "", "س")},
	{0x1eeaf, 0, 0, 0, g(Yes, No, false, false, "", "ع")},
	{0x1eeb0, 0, 0, 0, g(Yes, No, false, false, "", "ف")},
	{0x1eeb1, 0, 0, 0, g(Yes, No, false, false, "", "ص")},
	{0x1eeb2, 0, 0, 0, g(Yes, No, false, false, "", "ق")},
	{0x1eeb3, 0, 0, 0, g(Yes, No, false, false, "", "ر")},
	{0x1eeb4, 0, 0, 0, g(Yes, No, false, false, "", "ش")},
	{0x1eeb5, 0, 0, 0, g(Yes, No, false, false, "", "ت")},
	{0x1eeb6, 0, 0, 0, g(Yes, No, false, false, "", "ث")},
	{0x1eeb7, 0, 0, 0, g(Yes, No, false, false, "", "خ")},
	{0x1eeb8, 0, 0, 0, g(Yes, No, false, false, "", "ذ")},
	{0x1eeb9, 0, 0, 0, g(Yes, No, false, false, "", "ض")},
	{0x1eeba, 0, 0, 0, g(Yes, No, false, false, "", "ظ")},
	{0x1eebb, 0, 0, 0, g(Yes, No, false, false, "", "غ")},
	{0x1eebc, 0, 0, 0, f(Yes, false, "")},
	{0x1f100, 0, 0, 0, g(Yes, No, false, false, "", "0.")},
	{0x1f101, 0, 0, 0, g(Yes, No, false, false, "", "0,")},
	{0x1f102, 0, 0, 0, g(Yes, No, false, false, "", "1,")},
	{0x1f103, 0, 0, 0, g(Yes, No, false, false, "", "2,")},
	{0x1f104, 0, 0, 0, g(Yes, No, false, false, "", "3,")},
	{0x1f105, 0, 0, 0, g(Yes, No, false, false, "", "4,")},
	{0x1f106, 0, 0, 0, g(Yes, No, false, false, "", "5,")},
	{0x1f107, 0, 0, 0, g(Yes, No, false, false, "", "6,")},
	{0x1f108, 0, 0, 0, g(Yes, No, false, false, "", "7,")},
	{0x1f109, 0, 0, 0, g(Yes, No, false, false, "", "8,")},
	{0x1f10a, 0, 0, 0, g(Yes, No, false, false, "", "9,")},
	{0x1f10b, 0, 0, 0, f(Yes, false, "")},
	{0x1f110, 0, 0, 0, g(Yes, No, false, false, "", "(A)")},
	{0x1f111, 0, 0, 0, g(Yes, No, false, false, "", "(B)")},
	{0x1f112, 0, 0, 0, g(Yes, No, false, false, "", "(C)")},
	{0x1f113, 0, 0, 0, g(Yes, No, false, false, "", "(D)")},
	{0x1f114, 0, 0, 0, g(Yes, No, false, false, "", "(E)")},
	{0x1f115, 0, 0, 0, g(Yes, No, false, false, "", "(F)")},
	{0x1f116, 0, 0, 0, g(Yes, No, false, false, "", "(G)")},
	{0x1f117, 0, 0, 0, g(Yes, No, false, false, "", "(H)")},
	{0x1f118, 0, 0, 0, g(Yes, No, false, false, "", "(I)")},
	{0x1f119, 0, 0, 0, g(Yes, No, false, false, "", "(J)")},
	{0x1f11a, 0, 0, 0, g(Yes, No, false, false, "", "(K)")},
	{0x1f11b, 0, 0, 0, g(Yes, No, false, false, "", "(L)")},
	{0x1f11c, 0, 0, 0, g(Yes, No, false, false, "", "(M)")},
	{0x1f11d, 0, 0, 0, g(Yes, No, false, false, "", "(N)")},
	{0x1f11e, 0, 0, 0, g(Yes, No, false, false, "", "(O)")},
	{0x1f11f, 0, 0, 0, g(Yes, No, false, false, "", "(P)")},
	{0x1f120, 0, 0, 0, g(Yes, No, false, false, "", "(Q)")},
	{0x1f121, 0, 0, 0, g(Yes, No, false, false, "", "(R)")},
	{0x1f122, 0, 0, 0, g(Yes, No, false, false, "", "(S)")},
	{0x1f123, 0, 0, 0, g(Yes, No, false, false, "", "(T)")},
	{0x1f124, 0, 0, 0, g(Yes, No, false, false, "", "(U)")},
	{0x1f125, 0, 0, 0, g(Yes, No, false, false, "", "(V)")},
	{0x1f126, 0, 0, 0, g(Yes, No, false, false, "", "(W)")},
	{0x1f127, 0, 0, 0, g(Yes, No, false, false, "", "(X)")},
	{0x1f128, 0, 0, 0, g(Yes, No, false, false, "", "(Y)")},
	{0x1f129, 0, 0, 0, g(Yes, No, false, false, "", "(Z)")},
	{0x1f12a, 0, 0, 0, g(Yes, No, false, false, "", "〔S〕")},
	{0x1f12b, 0, 0, 0, g(Yes, No, false, false, "", "C")},
	{0x1f12c, 0, 0, 0, g(Yes, No, false, false, "", "R")},
	{0x1f12d, 0, 0, 0, g(Yes, No, false, false, "", "CD")},
	{0x1f12e, 0, 0, 0, g(Yes, No, false, false, "", "WZ")},
	{0x1f12f, 0, 0, 0, f(Yes, false, "")},
	{0x1f130, 0, 0, 0, g(Yes, No, false, false, "", "A")},
	{0x1f131, 0, 0, 0, g(Yes, No, false, false, "", "B")},
	{0x1f132, 0, 0, 0, g(Yes, No, false, false, "", "C")},
	{0x1f133, 0, 0, 0, g(Yes, No, false, false, "", "D")},
	{0x1f134, 0, 0, 0, g(Yes, No, false, false, "", "E")},
	{0x1f135, 0, 0, 0, g(Yes, No, false, false, "", "F")},
	{0x1f136, 0, 0, 0, g(Yes, No, false, false, "", "G")},
	{0x1f137, 0, 0, 0, g(Yes, No, false, false, "", "H")},
	{0x1f138, 0, 0, 0, g(Yes, No, false, false, "", "I")},
	{0x1f139, 0, 0, 0, g(Yes, No, false, false, "", "J")},
	{0x1f13a, 0, 0, 0, g(Yes, No, false, false, "", "K")},
	{0x1f13b, 0, 0, 0, g(Yes, No, false, false, "", "L")},
	{0x1f13c, 0, 0, 0, g(Yes, No, false, false, "", "M")},
	{0x1f13d, 0, 0, 0, g(Yes, No, false, false, "", "N")},
	{0x1f13e, 0, 0, 0, g(Yes, No, false, false, "", "O")},
	{0x1f13f, 0, 0, 0, g(Yes, No, false, false, "", "P")},
	{0x1f140, 0, 0, 0, g(Yes, No, false, false, "", "Q")},
	{0x1f141, 0, 0, 0, g(Yes, No, false, false, "", "R")},
	{0x1f142, 0, 0, 0, g(Yes, No, false, false, "", "S")},
	{0x1f143, 0, 0, 0, g(Yes, No, false, false, "", "T")},
	{0x1f144, 0, 0, 0, g(Yes, No, false, false, "", "U")},
	{0x1f145, 0, 0, 0, g(Yes, No, false, false, "", "V")},
	{0x1f146, 0, 0, 0, g(Yes, No, false, false, "", "W")},
	{0x1f147, 0, 0, 0, g(Yes, No, false, false, "", "X")},
	{0x1f148, 0, 0, 0, g(Yes, No, false, false, "", "Y")},
	{0x1f149, 0, 0, 0, g(Yes, No, false, false, "", "Z")},
	{0x1f14a, 0, 0, 0, g(Yes, No, false, false, "", "HV")},
	{0x1f14b, 0, 0, 0, g(Yes, No, false, false, "", "MV")},
	{0x1f14c, 0, 0, 0, g(Yes, No, false, false, "", "SD")},
	{0x1f14d, 0, 0, 0, g(Yes, No, false, false, "", "SS")},
	{0x1f14e, 0, 0, 0, g(Yes, No, false, false, "", "PPV")},
	{0x1f14f, 0, 0, 0, g(Yes, No, false, false, "", "WC")},
	{0x1f150, 0, 0, 0, f(Yes, false, "")},
	{0x1f16a, 0, 0, 0, g(Yes, No, false, false, "", "MC")},
	{0x1f16b, 0, 0, 0, g(Yes, No, false, false, "", "MD")},
	{0x1f16c, 0, 0, 0, f(Yes, false, "")},
	{0x1f190, 0, 0, 0, g(Yes, No, false, false, "", "DJ")},
	{0x1f191, 0, 0, 0, f(Yes, false, "")},
	{0x1f200, 0, 0, 0, g(Yes, No, false, false, "", "ほか")},
	{0x1f201, 0, 0, 0, g(Yes, No, false, false, "", "ココ")},
	{0x1f202, 0, 0, 0, g(Yes, No, false, false, "", "サ")},
	{0x1f203, 0, 0, 0, f(Yes, false, "")},
	{0x1f210, 0, 0, 0, g(Yes, No, false, false, "", "手")},
	{0x1f211, 0, 0, 0, g(Yes, No, false, false, "", "字")},
	{0x1f212, 0, 0, 0, g(Yes, No, false, false, "", "双")},
	{0x1f213, 0, 0, 1, g(Yes, No, false, false, "", "デ")},
	{0x1f214, 0, 0, 0, g(Yes, No, false, false, "", "二")},
	{0x1f215, 0, 0, 0, g(Yes, No, false, false, "", "多")},
	{0x1f216, 0, 0, 0, g(Yes, No, false, false, "", "解")},
	{0x1f217, 0, 0, 0, g(Yes, No, false, false, "", "天")},
	{0x1f218, 0, 0, 0, g(Yes, No, false, false, "", "交")},
	{0x1f219, 0, 0, 0, g(Yes, No, false, false, "", "映")},
	{0x1f21a, 0, 0, 0, g(Yes, No, false, false, "", "無")},
	{0x1f21b, 0, 0, 0, g(Yes, No, false, false, "", "料")},
	{0x1f21c, 0, 0, 0, g(Yes, No, false, false, "", "前")},
	{0x1f21d, 0, 0, 0, g(Yes, No, false, false, "", "後")},
	{0x1f21e, 0, 0, 0, g(Yes, No, false, false, "", "再")},
	{0x1f21f, 0, 0, 0, g(Yes, No, false, false, "", "新")},
	{0x1f220, 0, 0, 0, g(Yes, No, false, false, "", "初")},
	{0x1f221, 0, 0, 0, g(Yes, No, false, false, "", "終")},
	{0x1f222, 0, 0, 0, g(Yes, No, false, false, "", "生")},
	{0x1f223, 0, 0, 0, g(Yes, No, false, false, "", "販")},
	{0x1f224, 0, 0, 0, g(Yes, No, false, false, "", "声")},
	{0x1f225, 0, 0, 0, g(Yes, No, false, false, "", "吹")},
	{0x1f226, 0, 0, 0, g(Yes, No, false, false, "", "演")},
	{0x1f227, 0, 0, 0, g(Yes, No, false, false, "", "投")},
	{0x1f228, 0, 0, 0, g(Yes, No, false, false, "", "捕")},
	{0x1f229, 0, 0, 0, g(Yes, No, false, false, "", "一")},
	{0x1f22a, 0, 0, 0, g(Yes, No, false, false, "", "三")},
	{0x1f22b, 0, 0, 0, g(Yes, No, false, false, "", "遊")},
	{0x1f22c, 0, 0, 0, g(Yes, No, false, false, "", "左")},
	{0x1f22d, 0, 0, 0, g(Yes, No, false, false, "", "中")},
	{0x1f22e, 0, 0, 0, g(Yes, No, false, false, "", "右")},
	{0x1f22f, 0, 0, 0, g(Yes, No, false, false, "", "指")},
	{0x1f230, 0, 0, 0, g(Yes, No, false, false, "", "走")},
	{0x1f231, 0, 0, 0, g(Yes, No, false, false, "", "打")},
	{0x1f232, 0, 0, 0, g(Yes, No, false, false, "", "禁")},
	{0x1f233, 0, 0, 0, g(Yes, No, false, false, "", "空")},
	{0x1f234, 0, 0, 0, g(Yes, No, false, false, "", "合")},
	{0x1f235, 0, 0, 0, g(Yes, No, false, false, "", "満")},
	{0x1f236, 0, 0, 0, g(Yes, No, false, false, "", "有")},
	{0x1f237, 0, 0, 0, g(Yes, No, false, false, "", "月")},
	{0x1f238, 0, 0, 0, g(Yes, No, false, false, "", "申")},
	{0x1f239, 0, 0, 0, g(Yes, No, false, false, "", "割")},
	{0x1f23a, 0, 0, 0, g(Yes, No, false, false, "", "営")},
	{0x1f23b, 0, 0, 0, g(Yes, No, false, false, "", "配")},
	{0x1f23c, 0, 0, 0, f(Yes, false, "")},
	{0x1f240, 0, 0, 0, g(Yes, No, false, false, "", "〔本〕")},
	{0x1f241, 0, 0, 0, g(Yes, No, false, false, "", "〔三〕")},
	{0x1f242, 0, 0, 0, g(Yes, No, false, false, "", "〔二〕")},
	{0x1f243, 0, 0, 0, g(Yes, No, false, false, "", "〔安〕")},
	{0x1f244, 0, 0, 0, g(Yes, No, false, false, "", "〔点〕")},
	{0x1f245, 0, 0, 0, g(Yes, No, false, false, "", "〔打〕")},
	{0x1f246, 0, 0, 0, g(Yes, No, false, false, "", "〔盗〕")},
	{0x1f247, 0, 0, 0, g(Yes, No, false, false, "", "〔勝〕")},
	{0x1f248, 0, 0, 0, g(Yes, No, false, false, "", "〔敗〕")},
	{0x1f249, 0, 0, 0, f(Yes, false, "")},
	{0x1f250, 0, 0, 0, g(Yes, No, false, false, "", "得")},
	{0x1f251, 0, 0, 0, g(Yes, No, false, false, "", "可")},
	{0x1f252, 0, 0, 0, f(Yes, false, "")},
	{0x2f800, 0, 0, 0, f(No, false, "丽")},
	{0x2f801, 0, 0, 0, f(No, false, "丸")},
	{0x2f802, 0, 0, 0, f(No, false, "乁")},
	{0x2f803, 0, 0, 0, f(No, false, "𠄢")},
	{0x2f804, 0, 0, 0, f(No, false, "你")},
	{0x2f805, 0, 0, 0, f(No, false, "侮")},
	{0x2f806, 0, 0, 0, f(No, false, "侻")},
	{0x2f807, 0, 0, 0, f(No, false, "倂")},
	{0x2f808, 0, 0, 0, f(No, false, "偺")},
	{0x2f809, 0, 0, 0, f(No, false, "備")},
	{0x2f80a, 0, 0, 0, f(No, false, "僧")},
	{0x2f80b, 0, 0, 0, f(No, false, "像")},
	{0x2f80c, 0, 0, 0, f(No, false, "㒞")},
	{0x2f80d, 0, 0, 0, f(No, false, "𠘺")},
	{0x2f80e, 0, 0, 0, f(No, false, "免")},
	{0x2f80f, 0, 0, 0, f(No, false, "兔")},
	{0x2f810, 0, 0, 0, f(No, false, "兤")},
	{0x2f811, 0, 0, 0, f(No, false, "具")},
	{0x2f812, 0, 0, 0, f(No, false, "𠔜")},
	{0x2f813, 0, 0, 0, f(No, false, "㒹")},
	{0x2f814, 0, 0, 0, f(No, false, "內")},
	{0x2f815, 0, 0, 0, f(No, false, "再")},
	{0x2f816, 0, 0, 0, f(No, false, "𠕋")},
	{0x2f817, 0, 0, 0, f(No, false, "冗")},
	{0x2f818, 0, 0, 0, f(No, false, "冤")},
	{0x2f819, 0, 0, 0, f(No, false, "仌")},
	{0x2f81a, 0, 0, 0, f(No, false, "冬")},
	{0x2f81b, 0, 0, 0, f(No, false, "况")},
	{0x2f81c, 0, 0, 0, f(No, false, "𩇟")},
	{0x2f81d, 0, 0, 0, f(No, false, "凵")},
	{0x2f81e, 0, 0, 0, f(No, false, "刃")},
	{0x2f81f, 0, 0, 0, f(No, false, "㓟")},
	{0x2f820, 0, 0, 0, f(No, false, "刻")},
	{0x2f821, 0, 0, 0, f(No, false, "剆")},
	{0x2f822, 0, 0, 0, f(No, false, "割")},
	{0x2f823, 0, 0, 0, f(No, false, "剷")},
	{0x2f824, 0, 0, 0, f(No, false, "㔕")},
	{0x2f825, 0, 0, 0, f(No, false, "勇")},
	{0x2f826, 0, 0, 0, f(No, false, "勉")},
	{0x2f827, 0, 0, 0, f(No, false, "勤")},
	{0x2f828, 0, 0, 0, f(No, false, "勺")},
	{0x2f829, 0, 0, 0, f(No, false, "包")},
	{0x2f82a, 0, 0, 0, f(No, false, "匆")},
	{0x2f82b, 0, 0, 0, f(No, false, "北")},
	{0x2f82c, 0, 0, 0, f(No, false, "卉")},
	{0x2f82d, 0, 0, 0, f(No, false, "卑")},
	{0x2f82e, 0, 0, 0, f(No, false, "博")},
	{0x2f82f, 0, 0, 0, f(No, false, "即")},
	{0x2f830, 0, 0, 0, f(No, false, "卽")},
	{0x2f831, 0, 0, 0, f(No, false, "卿")},
	{0x2f834, 0, 0, 0, f(No, false, "𠨬")},
	{0x2f835, 0, 0, 0, f(No, false, "灰")},
	{0x2f836, 0, 0, 0, f(No, false, "及")},
	{0x2f837, 0, 0, 0, f(No, false, "叟")},
	{0x2f838, 0, 0, 0, f(No, false, "𠭣")},
	{0x2f839, 0, 0, 0, f(No, false, "叫")},
	{0x2f83a, 0, 0, 0, f(No, false, "叱")},
	{0x2f83b, 0, 0, 0, f(No, false, "吆")},
	{0x2f83c, 0, 0, 0, f(No, false, "咞")},
	{0x2f83d, 0, 0, 0, f(No, false, "吸")},
	{0x2f83e, 0, 0, 0, f(No, false, "呈")},
	{0x2f83f, 0, 0, 0, f(No, false, "周")},
	{0x2f840, 0, 0, 0, f(No, false, "咢")},
	{0x2f841, 0, 0, 0, f(No, false, "哶")},
	{0x2f842, 0, 0, 0, f(No, false, "唐")},
	{0x2f843, 0, 0, 0, f(No, false, "啓")},
	{0x2f844, 0, 0, 0, f(No, false, "啣")},
	{0x2f845, 0, 0, 0, f(No, false, "善")},
	{0x2f847, 0, 0, 0, f(No, false, "喙")},
	{0x2f848, 0, 0, 0, f(No, false, "喫")},
	{0x2f849, 0, 0, 0, f(No, false, "喳")},
	{0x2f84a, 0, 0, 0, f(No, false, "嗂")},
	{0x2f84b, 0, 0, 0, f(No, false, "圖")},
	{0x2f84c, 0, 0, 0, f(No, false, "嘆")},
	{0x2f84d, 0, 0, 0, f(No, false, "圗")},
	{0x2f84e, 0, 0, 0, f(No, false, "噑")},
	{0x2f84f, 0, 0, 0, f(No, false, "噴")},
	{0x2f850, 0, 0, 0, f(No, false, "切")},
	{0x2f851, 0, 0, 0, f(No, false, "壮")},
	{0x2f852, 0, 0, 0, f(No, false, "城")},
	{0x2f853, 0, 0, 0, f(No, false, "埴")},
	{0x2f854, 0, 0, 0, f(No, false, "堍")},
	{0x2f855, 0, 0, 0, f(No, false, "型")},
	{0x2f856, 0, 0, 0, f(No, false, "堲")},
	{0x2f857, 0, 0, 0, f(No, false, "報")},
	{0x2f858, 0, 0, 0, f(No, false, "墬")},
	{0x2f859, 0, 0, 0, f(No, false, "𡓤")},
	{0x2f85a, 0, 0, 0, f(No, false, "売")},
	{0x2f85b, 0, 0, 0, f(No, false, "壷")},
	{0x2f85c, 0, 0, 0, f(No, false, "夆")},
	{0x2f85d, 0, 0, 0, f(No, false, "多")},
	{0x2f85e, 0, 0, 0, f(No, false, "夢")},
	{0x2f85f, 0, 0, 0, f(No, false, "奢")},
	{0x2f860, 0, 0, 0, f(No, false, "𡚨")},
	{0x2f861, 0, 0, 0, f(No, false, "𡛪")},
	{0x2f862, 0, 0, 0, f(No, false, "姬")},
	{0x2f863, 0, 0, 0, f(No, false, "娛")},
	{0x2f864, 0, 0, 0, f(No, false, "娧")},
	{0x2f865, 0, 0, 0, f(No, false, "姘")},
	{0x2f866, 0, 0, 0, f(No, false, "婦")},
	{0x2f867, 0, 0, 0, f(No, false, "㛮")},
	{0x2f868, 0, 0, 0, f(No, false, "㛼")},
	{0x2f869, 0, 0, 0, f(No, false, "嬈")},
	{0x2f86a, 0, 0, 0, f(No, false, "嬾")},
	{0x2f86c, 0, 0, 0, f(No, false, "𡧈")},
	{0x2f86d, 0, 0, 0, f(No, false, "寃")},
	{0x2f86e, 0, 0, 0, f(No, false, "寘")},
	{0x2f86f, 0, 0, 0, f(No, false, "寧")},
	{0x2f870, 0, 0, 0, f(No, false, "寳")},
	{0x2f871, 0, 0, 0, f(No, false, "𡬘")},
	{0x2f872, 0, 0, 0, f(No, false, "寿")},
	{0x2f873, 0, 0, 0, f(No, false, "将")},
	{0x2f874, 0, 0, 0, f(No, false, "当")},
	{0x2f875, 0, 0, 0, f(No, false, "尢")},
	{0x2f876, 0, 0, 0, f(No, false, "㞁")},
	{0x2f877, 0, 0, 0, f(No, false, "屠")},
	{0x2f878, 0, 0, 0, f(No, false, "屮")},
	{0x2f879, 0, 0, 0, f(No, false, "峀")},
	{0x2f87a, 0, 0, 0, f(No, false, "岍")},
	{0x2f87b, 0, 0, 0, f(No, false, "𡷤")},
	{0x2f87c, 0, 0, 0, f(No, false, "嵃")},
	{0x2f87d, 0, 0, 0, f(No, false, "𡷦")},
	{0x2f87e, 0, 0, 0, f(No, false, "嵮")},
	{0x2f87f, 0, 0, 0, f(No, false, "嵫")},
	{0x2f880, 0, 0, 0, f(No, false, "嵼")},
	{0x2f881, 0, 0, 0, f(No, false, "巡")},
	{0x2f882, 0, 0, 0, f(No, false, "巢")},
	{0x2f883, 0, 0, 0, f(No, false, "㠯")},
	{0x2f884, 0, 0, 0, f(No, false, "巽")},
	{0x2f885, 0, 0, 0, f(No, false, "帨")},
	{0x2f886, 0, 0, 0, f(No, false, "帽")},
	{0x2f887, 0, 0, 0, f(No, false, "幩")},
	{0x2f888, 0, 0, 0, f(No, false, "㡢")},
	{0x2f889, 0, 0, 0, f(No, false, "𢆃")},
	{0x2f88a, 0, 0, 0, f(No, false, "㡼")},
	{0x2f88b, 0, 0, 0, f(No, false, "庰")},
	{0x2f88c, 0, 0, 0, f(No, false, "庳")},
	{0x2f88d, 0, 0, 0, f(No, false, "庶")},
	{0x2f88e, 0, 0, 0, f(No, false, "廊")},
	{0x2f88f, 0, 0, 0, f(No, false, "𪎒")},
	{0x2f890, 0, 0, 0, f(No, false, "廾")},
	{0x2f891, 0, 0, 0, f(No, false, "𢌱")},
	{0x2f893, 0, 0, 0, f(No, false, "舁")},
	{0x2f894, 0, 0, 0, f(No, false, "弢")},
	{0x2f896, 0, 0, 0, f(No, false, "㣇")},
	{0x2f897, 0, 0, 0, f(No, false, "𣊸")},
	{0x2f898, 0, 0, 0, f(No, false, "𦇚")},
	{0x2f899, 0, 0, 0, f(No, false, "形")},
	{0x2f89a, 0, 0, 0, f(No, false, "彫")},
	{0x2f89b, 0, 0, 0, f(No, false, "㣣")},
	{0x2f89c, 0, 0, 0, f(No, false, "徚")},
	{0x2f89d, 0, 0, 0, f(No, false, "忍")},
	{0x2f89e, 0, 0, 0, f(No, false, "志")},
	{0x2f89f, 0, 0, 0, f(No, false, "忹")},
	{0x2f8a0, 0, 0, 0, f(No, false, "悁")},
	{0x2f8a1, 0, 0, 0, f(No, false, "㤺")},
	{0x2f8a2, 0, 0, 0, f(No, false, "㤜")},
	{0x2f8a3, 0, 0, 0, f(No, false, "悔")},
	{0x2f8a4, 0, 0, 0, f(No, false, "𢛔")},
	{0x2f8a5, 0, 0, 0, f(No, false, "惇")},
	{0x2f8a6, 0, 0, 0, f(No, false, "慈")},
	{0x2f8a7, 0, 0, 0, f(No, false, "慌")},
	{0x2f8a8, 0, 0, 0, f(No, false, "慎")},
	{0x2f8a9, 0, 0, 0, f(No, false, "慌")},
	{0x2f8aa, 0, 0, 0, f(No, false, "慺")},
	{0x2f8ab, 0, 0, 0, f(No, false, "憎")},
	{0x2f8ac, 0, 0, 0, f(No, false, "憲")},
	{0x2f8ad, 0, 0, 0, f(No, false, "憤")},
	{0x2f8ae, 0, 0, 0, f(No, false, "憯")},
	{0x2f8af, 0, 0, 0, f(No, false, "懞")},
	{0x2f8b0, 0, 0, 0, f(No, false, "懲")},
	{0x2f8b1, 0, 0, 0, f(No, false, "懶")},
	{0x2f8b2, 0, 0, 0, f(No, false, "成")},
	{0x2f8b3, 0, 0, 0, f(No, false, "戛")},
	{0x2f8b4, 0, 0, 0, f(No, false, "扝")},
	{0x2f8b5, 0, 0, 0, f(No, false, "抱")},
	{0x2f8b6, 0, 0, 0, f(No, false, "拔")},
	{0x2f8b7, 0, 0, 0, f(No, false, "捐")},
	{0x2f8b8, 0, 0, 0, f(No, false, "𢬌")},
	{0x2f8b9, 0, 0, 0, f(No, false, "挽")},
	{0x2f8ba, 0, 0, 0, f(No, false, "拼")},
	{0x2f8bb, 0, 0, 0, f(No, false, "捨")},
	{0x2f8bc, 0, 0, 0, f(No, false, "掃")},
	{0x2f8bd, 0, 0, 0, f(No, false, "揤")},
	{0x2f8be, 0, 0, 0, f(No, false, "𢯱")},
	{0x2f8bf, 0, 0, 0, f(No, false, "搢")},
	{0x2f8c0, 0, 0, 0, f(No, false, "揅")},
	{0x2f8c1, 0, 0, 0, f(No, false, "掩")},
	{0x2f8c2, 0, 0, 0, f(No, false, "㨮")},
	{0x2f8c3, 0, 0, 0, f(No, false, "摩")},
	{0x2f8c4, 0, 0, 0, f(No, false, "摾")},
	{0x2f8c5, 0, 0, 0, f(No, false, "撝")},
	{0x2f8c6, 0, 0, 0, f(No, false, "摷")},
	{0x2f8c7, 0, 0, 0, f(No, false, "㩬")},
	{0x2f8c8, 0, 0, 0, f(No, false, "敏")},
	{0x2f8c9, 0, 0, 0, f(No, false, "敬")},
	{0x2f8ca, 0, 0, 0, f(No, false, "𣀊")},
	{0x2f8cb, 0, 0, 0, f(No, false, "旣")},
	{0x2f8cc, 0, 0, 0, f(No, false, "書")},
	{0x2f8cd, 0, 0, 0, f(No, false, "晉")},
	{0x2f8ce, 0, 0, 0, f(No, false, "㬙")},
	{0x2f8cf, 0, 0, 0, f(No, false, "暑")},
	{0x2f8d0, 0, 0, 0, f(No, false, "㬈")},
	{0x2f8d1, 0, 0, 0, f(No, false, "㫤")},
	{0x2f8d2, 0, 0, 0, f(No, false, "冒")},
	{0x2f8d3, 0, 0, 0, f(No, false, "冕")},
	{0x2f8d4, 0, 0, 0, f(No, false, "最")},
	{0x2f8d5, 0, 0, 0, f(No, false, "暜")},
	{0x2f8d6, 0, 0, 0, f(No, false, "肭")},
	{0x2f8d7, 0, 0, 0, f(No, false, "䏙")},
	{0x2f8d8, 0, 0, 0, f(No, false, "朗")},
	{0x2f8d9, 0, 0, 0, f(No, false, "望")},
	{0x2f8da, 0, 0, 0, f(No, false, "朡")},
	{0x2f8db, 0, 0, 0, f(No, false, "杞")},
	{0x2f8dc, 0, 0, 0, f(No, false, "杓")},
	{0x2f8dd, 0, 0, 0, f(No, false, "𣏃")},
	{0x2f8de, 0, 0, 0, f(No, false, "㭉")},
	{0x2f8df, 0, 0, 0, f(No, false, "柺")},
	{0x2f8e0, 0, 0, 0, f(No, false, "枅")},
	{0x2f8e1, 0, 0, 0, f(No, false, "桒")},
	{0x2f8e2, 0, 0, 0, f(No, false, "梅")},
	{0x2f8e3, 0, 0, 0, f(No, false, "𣑭")},
	{0x2f8e4, 0, 0, 0, f(No, false, "梎")},
	{0x2f8e5, 0, 0, 0, f(No, false, "栟")},
	{0x2f8e6, 0, 0, 0, f(No, false, "椔")},
	{0x2f8e7, 0, 0, 0, f(No, false, "㮝")},
	{0x2f8e8, 0, 0, 0, f(No, false, "楂")},
	{0x2f8e9, 0, 0, 0, f(No, false, "榣")},
	{0x2f8ea, 0, 0, 0, f(No, false, "槪")},
	{0x2f8eb, 0, 0, 0, f(No, false, "檨")},
	{0x2f8ec, 0, 0, 0, f(No, false, "𣚣")},
	{0x2f8ed, 0, 0, 0, f(No, false, "櫛")},
	{0x2f8ee, 0, 0, 0, f(No, false, "㰘")},
	{0x2f8ef, 0, 0, 0, f(No, false, "次")},
	{0x2f8f0, 0, 0, 0, f(No, false, "𣢧")},
	{0x2f8f1, 0, 0, 0, f(No, false, "歔")},
	{0x2f8f2, 0, 0, 0, f(No, false, "㱎")},
	{0x2f8f3, 0, 0, 0, f(No, false, "歲")},
	{0x2f8f4, 0, 0, 0, f(No, false, "殟")},
	{0x2f8f5, 0, 0, 0, f(No, false, "殺")},
	{0x2f8f6, 0, 0, 0, f(No, false, "殻")},
	{0x2f8f7, 0, 0, 0, f(No, false, "𣪍")},
	{0x2f8f8, 0, 0, 0, f(No, false, "𡴋")},
	{0x2f8f9, 0, 0, 0, f(No, false, "𣫺")},
	{0x2f8fa, 0, 0, 0, f(No, false, "汎")},
	{0x2f8fb, 0, 0, 0, f(No, false, "𣲼")},
	{0x2f8fc, 0, 0, 0, f(No, false, "沿")},
	{0x2f8fd, 0, 0, 0, f(No, false, "泍")},
	{0x2f8fe, 0, 0, 0, f(No, false, "汧")},
	{0x2f8ff, 0, 0, 0, f(No, false, "洖")},
	{0x2f900, 0, 0, 0, f(No, false, "派")},
	{0x2f901, 0, 0, 0, f(No, false, "海")},
	{0x2f902, 0, 0, 0, f(No, false, "流")},
	{0x2f903, 0, 0, 0, f(No, false, "浩")},
	{0x2f904, 0, 0, 0, f(No, false, "浸")},
	{0x2f905, 0, 0, 0, f(No, false, "涅")},
	{0x2f906, 0, 0, 0, f(No, false, "𣴞")},
	{0x2f907, 0, 0, 0, f(No, false, "洴")},
	{0x2f908, 0, 0, 0, f(No, false, "港")},
	{0x2f909, 0, 0, 0, f(No, false, "湮")},
	{0x2f90a, 0, 0, 0, f(No, false, "㴳")},
	{0x2f90b, 0, 0, 0, f(No, false, "滋")},
	{0x2f90c, 0, 0, 0, f(No, false, "滇")},
	{0x2f90d, 0, 0, 0, f(No, false, "𣻑")},
	{0x2f90e, 0, 0, 0, f(No, false, "淹")},
	{0x2f90f, 0, 0, 0, f(No, false, "潮")},
	{0x2f910, 0, 0, 0, f(No, false, "𣽞")},
	{0x2f911, 0, 0, 0, f(No, false, "𣾎")},
	{0x2f912, 0, 0, 0, f(No, false, "濆")},
	{0x2f913, 0, 0, 0, f(No, false, "瀹")},
	{0x2f914, 0, 0, 0, f(No, false, "瀞")},
	{0x2f915, 0, 0, 0, f(No, false, "瀛")},
	{0x2f916, 0, 0, 0, f(No, false, "㶖")},
	{0x2f917, 0, 0, 0, f(No, false, "灊")},
	{0x2f918, 0, 0, 0, f(No, false, "災")},
	{0x2f919, 0, 0, 0, f(No, false, "灷")},
	{0x2f91a, 0, 0, 0, f(No, false, "炭")},
	{0x2f91b, 0, 0, 0, f(No, false, "𠔥")},
	{0x2f91c, 0, 0, 0, f(No, false, "煅")},
	{0x2f91d, 0, 0, 0, f(No, false, "𤉣")},
	{0x2f91e, 0, 0, 0, f(No, false, "熜")},
	{0x2f91f, 0, 0, 0, f(No, false, "𤎫")},
	{0x2f920, 0, 0, 0, f(No, false, "爨")},
	{0x2f921, 0, 0, 0, f(No, false, "爵")},
	{0x2f922, 0, 0, 0, f(No, false, "牐")},
	{0x2f923, 0, 0, 0, f(No, false, "𤘈")},
	{0x2f924, 0, 0, 0, f(No, false, "犀")},
	{0x2f925, 0, 0, 0, f(No, false, "犕")},
	{0x2f926, 0, 0, 0, f(No, false, "𤜵")},
	{0x2f927, 0, 0, 0, f(No, false, "𤠔")},
	{0x2f928, 0, 0, 0, f(No, false, "獺")},
	{0x2f929, 0, 0, 0, f(No, false, "王")},
	{0x2f92a, 0, 0, 0, f(No, false, "㺬")},
	{0x2f92b, 0, 0, 0, f(No, false, "玥")},
	{0x2f92c, 0, 0, 0, f(No, false, "㺸")},
	{0x2f92e, 0, 0, 0, f(No, false, "瑇")},
	{0x2f92f, 0, 0, 0, f(No, false, "瑜")},
	{0x2f930, 0, 0, 0, f(No, false, "瑱")},
	{0x2f931, 0, 0, 0, f(No, false, "璅")},
	{0x2f932, 0, 0, 0, f(No, false, "瓊")},
	{0x2f933, 0, 0, 0, f(No, false, "㼛")},
	{0x2f934, 0, 0, 0, f(No, false, "甤")},
	{0x2f935, 0, 0, 0, f(No, false, "𤰶")},
	{0x2f936, 0, 0, 0, f(No, false, "甾")},
	{0x2f937, 0, 0, 0, f(No, false, "𤲒")},
	{0x2f938, 0, 0, 0, f(No, false, "異")},
	{0x2f939, 0, 0, 0, f(No, false, "𢆟")},
	{0x2f93a, 0, 0, 0, f(No, false, "瘐")},
	{0x2f93b, 0, 0, 0, f(No, false, "𤾡")},
	{0x2f93c, 0, 0, 0, f(No, false, "𤾸")},
	{0x2f93d, 0, 0, 0, f(No, false, "𥁄")},
	{0x2f93e, 0, 0, 0, f(No, false, "㿼")},
	{0x2f93f, 0, 0, 0, f(No, false, "䀈")},
	{0x2f940, 0, 0, 0, f(No, false, "直")},
	{0x2f941, 0, 0, 0, f(No, false, "𥃳")},
	{0x2f942, 0, 0, 0, f(No, false, "𥃲")},
	{0x2f943, 0, 0, 0, f(No, false, "𥄙")},
	{0x2f944, 0, 0, 0, f(No, false, "𥄳")},
	{0x2f945, 0, 0, 0, f(No, false, "眞")},
	{0x2f946, 0, 0, 0, f(No, false, "真")},
	{0x2f948, 0, 0, 0, f(No, false, "睊")},
	{0x2f949, 0, 0, 0, f(No, false, "䀹")},
	{0x2f94a, 0, 0, 0, f(No, false, "瞋")},
	{0x2f94b, 0, 0, 0, f(No, false, "䁆")},
	{0x2f94c, 0, 0, 0, f(No, false, "䂖")},
	{0x2f94d, 0, 0, 0, f(No, false, "𥐝")},
	{0x2f94e, 0, 0, 0, f(No, false, "硎")},
	{0x2f94f, 0, 0, 0, f(No, false, "碌")},
	{0x2f950, 0, 0, 0, f(No, false, "磌")},
	{0x2f951, 0, 0, 0, f(No, false, "䃣")},
	{0x2f952, 0, 0, 0, f(No, false, "𥘦")},
	{0x2f953, 0, 0, 0, f(No, false, "祖")},
	{0x2f954, 0, 0, 0, f(No, false, "𥚚")},
	{0x2f955, 0, 0, 0, f(No, false, "𥛅")},
	{0x2f956, 0, 0, 0, f(No, false, "福")},
	{0x2f957, 0, 0, 0, f(No, false, "秫")},
	{0x2f958, 0, 0, 0, f(No, false, "䄯")},
	{0x2f959, 0, 0, 0, f(No, false, "穀")},
	{0x2f95a, 0, 0, 0, f(No, false, "穊")},
	{0x2f95b, 0, 0, 0, f(No, false, "穏")},
	{0x2f95c, 0, 0, 0, f(No, false, "𥥼")},
	{0x2f95d, 0, 0, 0, f(No, false, "𥪧")},
	{0x2f95f, 0, 0, 0, f(No, false, "竮")},
	{0x2f960, 0, 0, 0, f(No, false, "䈂")},
	{0x2f961, 0, 0, 0, f(No, false, "𥮫")},
	{0x2f962, 0, 0, 0, f(No, false, "篆")},
	{0x2f963, 0, 0, 0, f(No, false, "築")},
	{0x2f964, 0, 0, 0, f(No, false, "䈧")},
	{0x2f965, 0, 0, 0, f(No, false, "𥲀")},
	{0x2f966, 0, 0, 0, f(No, false, "糒")},
	{0x2f967, 0, 0, 0, f(No, false, "䊠")},
	{0x2f968, 0, 0, 0, f(No, false, "糨")},
	{0x2f969, 0, 0, 0, f(No, false, "糣")},
	{0x2f96a, 0, 0, 0, f(No, false, "紀")},
	{0x2f96b, 0, 0, 0, f(No, false, "𥾆")},
	{0x2f96c, 0, 0, 0, f(No, false, "絣")},
	{0x2f96d, 0, 0, 0, f(No, false, "䌁")},
	{0x2f96e, 0, 0, 0, f(No, false, "緇")},
	{0x2f96f, 0, 0, 0, f(No, false, "縂")},
	{0x2f970, 0, 0, 0, f(No, false, "繅")},
	{0x2f971, 0, 0, 0, f(No, false, "䌴")},
	{0x2f972, 0, 0, 0, f(No, false, "𦈨")},
	{0x2f973, 0, 0, 0, f(No, false, "𦉇")},
	{0x2f974, 0, 0, 0, f(No, false, "䍙")},
	{0x2f975, 0, 0, 0, f(No, false, "𦋙")},
	{0x2f976, 0, 0, 0, f(No, false, "罺")},
	{0x2f977, 0, 0, 0, f(No, false, "𦌾")},
	{0x2f978, 0, 0, 0, f(No, false, "羕")},
	{0x2f979, 0, 0, 0, f(No, false, "翺")},
	{0x2f97a, 0, 0, 0, f(No, false, "者")},
	{0x2f97b, 0, 0, 0, f(No, false, "𦓚")},
	{0x2f97c, 0, 0, 0, f(No, false, "𦔣")},
	{0x2f97d, 0, 0, 0, f(No, false, "聠")},
	{0x2f97e, 0, 0, 0, f(No, false, "𦖨")},
	{0x2f97f, 0, 0, 0, f(No, false, "聰")},
	{0x2f980, 0, 0, 0, f(No, false, "𣍟")},
	{0x2f981, 0, 0, 0, f(No, false, "䏕")},
	{0x2f982, 0, 0, 0, f(No, false, "育")},
	{0x2f983, 0, 0, 0, f(No, false, "脃")},
	{0x2f984, 0, 0, 0, f(No, false, "䐋")},
	{0x2f985, 0, 0, 0, f(No, false, "脾")},
	{0x2f986, 0, 0, 0, f(No, false, "媵")},
	{0x2f987, 0, 0, 0, f(No, false, "𦞧")},
	{0x2f988, 0, 0, 0, f(No, false, "𦞵")},
	{0x2f989, 0, 0, 0, f(No, false, "𣎓")},
	{0x2f98a, 0, 0, 0, f(No, false, "𣎜")},
	{0x2f98b, 0, 0, 0, f(No, false, "舁")},
	{0x2f98c, 0, 0, 0, f(No, false, "舄")},
	{0x2f98d, 0, 0, 0, f(No, false, "辞")},
	{0x2f98e, 0, 0, 0, f(No, false, "䑫")},
	{0x2f98f, 0, 0, 0, f(No, false, "芑")},
	{0x2f990, 0, 0, 0, f(No, false, "芋")},
	{0x2f991, 0, 0, 0, f(No, false, "芝")},
	{0x2f992, 0, 0, 0, f(No, false, "劳")},
	{0x2f993, 0, 0, 0, f(No, false, "花")},
	{0x2f994, 0, 0, 0, f(No, false, "芳")},
	{0x2f995, 0, 0, 0, f(No, false, "芽")},
	{0x2f996, 0, 0, 0, f(No, false, "苦")},
	{0x2f997, 0, 0, 0, f(No, false, "𦬼")},
	{0x2f998, 0, 0, 0, f(No, false, "若")},
	{0x2f999, 0, 0, 0, f(No, false, "茝")},
	{0x2f99a, 0, 0, 0, f(No, false, "荣")},
	{0x2f99b, 0, 0, 0, f(No, false, "莭")},
	{0x2f99c, 0, 0, 0, f(No, false, "茣")},
	{0x2f99d, 0, 0, 0, f(No, false, "莽")},
	{0x2f99e, 0, 0, 0, f(No, false, "菧")},
	{0x2f99f, 0, 0, 0, f(No, false, "著")},
	{0x2f9a0, 0, 0, 0, f(No, false, "荓")},
	{0x2f9a1, 0, 0, 0, f(No, false, "菊")},
	{0x2f9a2, 0, 0, 0, f(No, false, "菌")},
	{0x2f9a3, 0, 0, 0, f(No, false, "菜")},
	{0x2f9a4, 0, 0, 0, f(No, false, "𦰶")},
	{0x2f9a5, 0, 0, 0, f(No, false, "𦵫")},
	{0x2f9a6, 0, 0, 0, f(No, false, "𦳕")},
	{0x2f9a7, 0, 0, 0, f(No, false, "䔫")},
	{0x2f9a8, 0, 0, 0, f(No, false, "蓱")},
	{0x2f9a9, 0, 0, 0, f(No, false, "蓳")},
	{0x2f9aa, 0, 0, 0, f(No, false, "蔖")},
	{0x2f9ab, 0, 0, 0, f(No, false, "𧏊")},
	{0x2f9ac, 0, 0, 0, f(No, false, "蕤")},
	{0x2f9ad, 0, 0, 0, f(No, false, "𦼬")},
	{0x2f9ae, 0, 0, 0, f(No, false, "䕝")},
	{0x2f9af, 0, 0, 0, f(No, false, "䕡")},
	{0x2f9b0, 0, 0, 0, f(No, false, "𦾱")},
	{0x2f9b1, 0, 0, 0, f(No, false, "𧃒")},
	{0x2f9b2, 0, 0, 0, f(No, false, "䕫")},
	{0x2f9b3, 0, 0, 0, f(No, false, "虐")},
	{0x2f9b4, 0, 0, 0, f(No, false, "虜")},
	{0x2f9b5, 0, 0, 0, f(No, false, "虧")},
	{0x2f9b6, 0, 0, 0, f(No, false, "虩")},
	{0x2f9b7, 0, 0, 0, f(No, false, "蚩")},
	{0x2f9b8, 0, 0, 0, f(No, false, "蚈")},
	{0x2f9b9, 0, 0, 0, f(No, false, "蜎")},
	{0x2f9ba, 0, 0, 0, f(No, false, "蛢")},
	{0x2f9bb, 0, 0, 0, f(No, false, "蝹")},
	{0x2f9bc, 0, 0, 0, f(No, false, "蜨")},
	{0x2f9bd, 0, 0, 0, f(No, false, "蝫")},
	{0x2f9be, 0, 0, 0, f(No, false, "螆")},
	{0x2f9bf, 0, 0, 0, f(No, false, "䗗")},
	{0x2f9c0, 0, 0, 0, f(No, false, "蟡")},
	{0x2f9c1, 0, 0, 0, f(No, false, "蠁")},
	{0x2f9c2, 0, 0, 0, f(No, false, "䗹")},
	{0x2f9c3, 0, 0, 0, f(No, false, "衠")},
	{0x2f9c4, 0, 0, 0, f(No, false, "衣")},
	{0x2f9c5, 0, 0, 0, f(No, false, "𧙧")},
	{0x2f9c6, 0, 0, 0, f(No, false, "裗")},
	{0x2f9c7, 0, 0, 0, f(No, false, "裞")},
	{0x2f9c8, 0, 0, 0, f(No, false, "䘵")},
	{0x2f9c9, 0, 0, 0, f(No, false, "裺")},
	{0x2f9ca, 0, 0, 0, f(No, false, "㒻")},
	{0x2f9cb, 0, 0, 0, f(No, false, "𧢮")},
	{0x2f9cc, 0, 0, 0, f(No, false, "𧥦")},
	{0x2f9cd, 0, 0, 0, f(No, false, "䚾")},
	{0x2f9ce, 0, 0, 0, f(No, false, "䛇")},
	{0x2f9cf, 0, 0, 0, f(No, false, "誠")},
	{0x2f9d0, 0, 0, 0, f(No, false, "諭")},
	{0x2f9d1, 0, 0, 0, f(No, false, "變")},
	{0x2f9d2, 0, 0, 0, f(No, false, "豕")},
	{0x2f9d3, 0, 0, 0, f(No, false, "𧲨")},
	{0x2f9d4, 0, 0, 0, f(No, false, "貫")},
	{0x2f9d5, 0, 0, 0, f(No, false, "賁")},
	{0x2f9d6, 0, 0, 0, f(No, false, "贛")},
	{0x2f9d7, 0, 0, 0, f(No, false, "起")},
	{0x2f9d8, 0, 0, 0, f(No, false, "𧼯")},
	{0x2f9d9, 0, 0, 0, f(No, false, "𠠄")},
	{0x2f9da, 0, 0, 0, f(No, false, "跋")},
	{0x2f9db, 0, 0, 0, f(No, false, "趼")},
	{0x2f9dc, 0, 0, 0, f(No, false, "跰")},
	{0x2f9dd, 0, 0, 0, f(No, false, "𠣞")},
	{0x2f9de, 0, 0, 0, f(No, false, "軔")},
	{0x2f9df, 0, 0, 0, f(No, false, "輸")},
	{0x2f9e0, 0, 0, 0, f(No, false, "𨗒")},
	{0x2f9e1, 0, 0, 0, f(No, false, "𨗭")},
	{0x2f9e2, 0, 0, 0, f(No, false, "邔")},
	{0x2f9e3, 0, 0, 0, f(No, false, "郱")},
	{0x2f9e4, 0, 0, 0, f(No, false, "鄑")},
	{0x2f9e5, 0, 0, 0, f(No, false, "𨜮")},
	{0x2f9e6, 0, 0, 0, f(No, false, "鄛")},
	{0x2f9e7, 0, 0, 0, f(No, false, "鈸")},
	{0x2f9e8, 0, 0, 0, f(No, false, "鋗")},
	{0x2f9e9, 0, 0, 0, f(No, false, "鋘")},
	{0x2f9ea, 0, 0, 0, f(No, false, "鉼")},
	{0x2f9eb, 0, 0, 0, f(No, false, "鏹")},
	{0x2f9ec, 0, 0, 0, f(No, false, "鐕")},
	{0x2f9ed, 0, 0, 0, f(No, false, "𨯺")},
	{0x2f9ee, 0, 0, 0, f(No, false, "開")},
	{0x2f9ef, 0, 0, 0, f(No, false, "䦕")},
	{0x2f9f0, 0, 0, 0, f(No, false, "閷")},
	{0x2f9f1, 0, 0, 0, f(No, false, "𨵷")},
	{0x2f9f2, 0, 0, 0, f(No, false, "䧦")},
	{0x2f9f3, 0, 0, 0, f(No, false, "雃")},
	{0x2f9f4, 0, 0, 0, f(No, false, "嶲")},
	{0x2f9f5, 0, 0, 0, f(No, false, "霣")},
	{0x2f9f6, 0, 0, 0, f(No, false, "𩅅")},
	{0x2f9f7, 0, 0, 0, f(No, false, "𩈚")},
	{0x2f9f8, 0, 0, 0, f(No, false, "䩮")},
	{0x2f9f9, 0, 0, 0, f(No, false, "䩶")},
	{0x2f9fa, 0, 0, 0, f(No, false, "韠")},
	{0x2f9fb, 0, 0, 0, f(No, false, "𩐊")},
	{0x2f9fc, 0, 0, 0, f(No, false, "䪲")},
	{0x2f9fd, 0, 0, 0, f(No, false, "𩒖")},
	{0x2f9fe, 0, 0, 0, f(No, false, "頋")},
	{0x2fa00, 0, 0, 0, f(No, false, "頩")},
	{0x2fa01, 0, 0, 0, f(No, false, "𩖶")},
	{0x2fa02, 0, 0, 0, f(No, false, "飢")},
	{0x2fa03, 0, 0, 0, f(No, false, "䬳")},
	{0x2fa04, 0, 0, 0, f(No, false, "餩")},
	{0x2fa05, 0, 0, 0, f(No, false, "馧")},
	{0x2fa06, 0, 0, 0, f(No, false, "駂")},
	{0x2fa07, 0, 0, 0, f(No, false, "駾")},
	{0x2fa08, 0, 0, 0, f(No, false, "䯎")},
	{0x2fa09, 0, 0, 0, f(No, false, "𩬰")},
	{0x2fa0a, 0, 0, 0, f(No, false, "鬒")},
	{0x2fa0b, 0, 0, 0, f(No, false, "鱀")},
	{0x2fa0c, 0, 0, 0, f(No, false, "鳽")},
	{0x2fa0d, 0, 0, 0, f(No, false, "䳎")},
	{0x2fa0e, 0, 0, 0, f(No, false, "䳭")},
	{0x2fa0f, 0, 0, 0, f(No, false, "鵧")},
	{0x2fa10, 0, 0, 0, f(No, false, "𪃎")},
	{0x2fa11, 0, 0, 0, f(No, false, "䳸")},
	{0x2fa12, 0, 0, 0, f(No, false, "𪄅")},
	{0x2fa13, 0, 0, 0, f(No, false, "𪈎")},
	{0x2fa14, 0, 0, 0, f(No, false, "𪊑")},
	{0x2fa15, 0, 0, 0, f(No, false, "麻")},
	{0x2fa16, 0, 0, 0, f(No, false, "䵖")},
	{0x2fa17, 0, 0, 0, f(No, false, "黹")},
	{0x2fa18, 0, 0, 0, f(No, false, "黾")},
	{0x2fa19, 0, 0, 0, f(No, false, "鼅")},
	{0x2fa1a, 0, 0, 0, f(No, false, "鼏")},
	{0x2fa1b, 0, 0, 0, f(No, false, "鼖")},
	{0x2fa1c, 0, 0, 0, f(No, false, "鼻")},
	{0x2fa1d, 0, 0, 0, f(No, false, "𪘀")},
	{0x2fa1e, 0, 0, 0, f(Yes, false, "")},
}
