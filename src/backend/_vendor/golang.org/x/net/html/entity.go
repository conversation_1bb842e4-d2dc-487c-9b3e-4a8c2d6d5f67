// Copyright 2010 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

package html

// All entities that do not end with ';' are 6 or fewer bytes long.
const longestEntityWithoutSemicolon = 6

// entity is a map from HTML entity names to their values. The semicolon matters:
// https://html.spec.whatwg.org/multipage/syntax.html#named-character-references
// lists both "amp" and "amp;" as two separate entries.
//
// Note that the HTML5 list is larger than the HTML4 list at
// http://www.w3.org/TR/html4/sgml/entities.html
var entity = map[string]rune{
	"AElig;":                           '\U000000C6',
	"AMP;":                             '\*********',
	"Aacute;":                          '\U000000C1',
	"Abreve;":                          '\*********',
	"Acirc;":                           '\U000000C2',
	"Acy;":                             '\*********',
	"Afr;":                             '\U0001D504',
	"Agrave;":                          '\U000000C0',
	"Alpha;":                           '\*********',
	"Amacr;":                           '\U00000100',
	"And;":                             '\U00002A53',
	"Aogon;":                           '\U00000104',
	"Aopf;":                            '\U0001D538',
	"ApplyFunction;":                   '\U00002061',
	"Aring;":                           '\U000000C5',
	"Ascr;":                            '\U0001D49C',
	"Assign;":                          '\U00002254',
	"Atilde;":                          '\U000000C3',
	"Auml;":                            '\U000000C4',
	"Backslash;":                       '\U00002216',
	"Barv;":                            '\U00002AE7',
	"Barwed;":                          '\*********',
	"Bcy;":                             '\U00000411',
	"Because;":                         '\U00002235',
	"Bernoullis;":                      '\U0000212C',
	"Beta;":                            '\U00000392',
	"Bfr;":                             '\U0001D505',
	"Bopf;":                            '\U0001D539',
	"Breve;":                           '\U000002D8',
	"Bscr;":                            '\U0000212C',
	"Bumpeq;":                          '\U0000224E',
	"CHcy;":                            '\U00000427',
	"COPY;":                            '\U000000A9',
	"Cacute;":                          '\U00000106',
	"Cap;":                             '\U000022D2',
	"CapitalDifferentialD;":            '\U00002145',
	"Cayleys;":                         '\U0000212D',
	"Ccaron;":                          '\U0000010C',
	"Ccedil;":                          '\U000000C7',
	"Ccirc;":                           '\U00000108',
	"Cconint;":                         '\U00002230',
	"Cdot;":                            '\U0000010A',
	"Cedilla;":                         '\U000000B8',
	"CenterDot;":                       '\U000000B7',
	"Cfr;":                             '\U0000212D',
	"Chi;":                             '\U000003A7',
	"CircleDot;":                       '\U00002299',
	"CircleMinus;":                     '\U00002296',
	"CirclePlus;":                      '\U00002295',
	"CircleTimes;":                     '\U00002297',
	"ClockwiseContourIntegral;":        '\U00002232',
	"CloseCurlyDoubleQuote;":           '\U0000201D',
	"CloseCurlyQuote;":                 '\U00002019',
	"Colon;":                           '\U00002237',
	"Colone;":                          '\U00002A74',
	"Congruent;":                       '\U00002261',
	"Conint;":                          '\U0000222F',
	"ContourIntegral;":                 '\U0000222E',
	"Copf;":                            '\U00002102',
	"Coproduct;":                       '\U00002210',
	"CounterClockwiseContourIntegral;": '\U00002233',
	"Cross;":                           '\U00002A2F',
	"Cscr;":                            '\U0001D49E',
	"Cup;":                             '\U000022D3',
	"CupCap;":                          '\U0000224D',
	"DD;":                              '\U00002145',
	"DDotrahd;":                        '\U00002911',
	"DJcy;":                            '\U00000402',
	"DScy;":                            '\U00000405',
	"DZcy;":                            '\U0000040F',
	"Dagger;":                          '\U00002021',
	"Darr;":                            '\U000021A1',
	"Dashv;":                           '\U00002AE4',
	"Dcaron;":                          '\U0000010E',
	"Dcy;":                             '\U00000414',
	"Del;":                             '\U00002207',
	"Delta;":                           '\U00000394',
	"Dfr;":                             '\U0001D507',
	"DiacriticalAcute;":                '\U000000B4',
	"DiacriticalDot;":                  '\U000002D9',
	"DiacriticalDoubleAcute;":          '\U000002DD',
	"DiacriticalGrave;":                '\U00000060',
	"DiacriticalTilde;":                '\U000002DC',
	"Diamond;":                         '\U000022C4',
	"DifferentialD;":                   '\U00002146',
	"Dopf;":                            '\U0001D53B',
	"Dot;":                             '\U000000A8',
	"DotDot;":                          '\U000020DC',
	"DotEqual;":                        '\*********',
	"DoubleContourIntegral;":           '\U0000222F',
	"DoubleDot;":                       '\U000000A8',
	"DoubleDownArrow;":                 '\U000021D3',
	"DoubleLeftArrow;":                 '\U000021D0',
	"DoubleLeftRightArrow;":            '\U000021D4',
	"DoubleLeftTee;":                   '\U00002AE4',
	"DoubleLongLeftArrow;":             '\U000027F8',
	"DoubleLongLeftRightArrow;":        '\U000027FA',
	"DoubleLongRightArrow;":            '\U000027F9',
	"DoubleRightArrow;":                '\U000021D2',
	"DoubleRightTee;":                  '\U000022A8',
	"DoubleUpArrow;":                   '\U000021D1',
	"DoubleUpDownArrow;":               '\U000021D5',
	"DoubleVerticalBar;":               '\U00002225',
	"DownArrow;":                       '\*********',
	"DownArrowBar;":                    '\U00002913',
	"DownArrowUpArrow;":                '\U000021F5',
	"DownBreve;":                       '\U00000311',
	"DownLeftRightVector;":             '\U00002950',
	"DownLeftTeeVector;":               '\U0000295E',
	"DownLeftVector;":                  '\U000021BD',
	"DownLeftVectorBar;":               '\U00002956',
	"DownRightTeeVector;":              '\U0000295F',
	"DownRightVector;":                 '\U000021C1',
	"DownRightVectorBar;":              '\U00002957',
	"DownTee;":                         '\U000022A4',
	"DownTeeArrow;":                    '\U000021A7',
	"Downarrow;":                       '\U000021D3',
	"Dscr;":                            '\U0001D49F',
	"Dstrok;":                          '\U00000110',
	"ENG;":                             '\U0000014A',
	"ETH;":                             '\U000000D0',
	"Eacute;":                          '\U000000C9',
	"Ecaron;":                          '\U0000011A',
	"Ecirc;":                           '\U000000CA',
	"Ecy;":                             '\U0000042D',
	"Edot;":                            '\U00000116',
	"Efr;":                             '\U0001D508',
	"Egrave;":                          '\U000000C8',
	"Element;":                         '\U00002208',
	"Emacr;":                           '\U00000112',
	"EmptySmallSquare;":                '\U000025FB',
	"EmptyVerySmallSquare;":            '\U000025AB',
	"Eogon;":                           '\U00000118',
	"Eopf;":                            '\U0001D53C',
	"Epsilon;":                         '\U00000395',
	"Equal;":                           '\U00002A75',
	"EqualTilde;":                      '\U00002242',
	"Equilibrium;":                     '\U000021CC',
	"Escr;":                            '\U00002130',
	"Esim;":                            '\U00002A73',
	"Eta;":                             '\U00000397',
	"Euml;":                            '\U000000CB',
	"Exists;":                          '\U00002203',
	"ExponentialE;":                    '\U00002147',
	"Fcy;":                             '\U00000424',
	"Ffr;":                             '\U0001D509',
	"FilledSmallSquare;":               '\U000025FC',
	"FilledVerySmallSquare;":           '\U000025AA',
	"Fopf;":                            '\U0001D53D',
	"ForAll;":                          '\U00002200',
	"Fouriertrf;":                      '\U00002131',
	"Fscr;":                            '\U00002131',
	"GJcy;":                            '\U00000403',
	"GT;":                              '\U0000003E',
	"Gamma;":                           '\U00000393',
	"Gammad;":                          '\U000003DC',
	"Gbreve;":                          '\U0000011E',
	"Gcedil;":                          '\U00000122',
	"Gcirc;":                           '\U0000011C',
	"Gcy;":                             '\U00000413',
	"Gdot;":                            '\U00000120',
	"Gfr;":                             '\U0001D50A',
	"Gg;":                              '\U000022D9',
	"Gopf;":                            '\U0001D53E',
	"GreaterEqual;":                    '\U00002265',
	"GreaterEqualLess;":                '\U000022DB',
	"GreaterFullEqual;":                '\U00002267',
	"GreaterGreater;":                  '\U00002AA2',
	"GreaterLess;":                     '\U00002277',
	"GreaterSlantEqual;":               '\U00002A7E',
	"GreaterTilde;":                    '\U00002273',
	"Gscr;":                            '\U0001D4A2',
	"Gt;":                              '\U0000226B',
	"HARDcy;":                          '\U0000042A',
	"Hacek;":                           '\U000002C7',
	"Hat;":                             '\U0000005E',
	"Hcirc;":                           '\U00000124',
	"Hfr;":                             '\U0000210C',
	"HilbertSpace;":                    '\U0000210B',
	"Hopf;":                            '\U0000210D',
	"HorizontalLine;":                  '\*********',
	"Hscr;":                            '\U0000210B',
	"Hstrok;":                          '\U00000126',
	"HumpDownHump;":                    '\U0000224E',
	"HumpEqual;":                       '\U0000224F',
	"IEcy;":                            '\U00000415',
	"IJlig;":                           '\U00000132',
	"IOcy;":                            '\U00000401',
	"Iacute;":                          '\U000000CD',
	"Icirc;":                           '\U000000CE',
	"Icy;":                             '\U00000418',
	"Idot;":                            '\U00000130',
	"Ifr;":                             '\U00002111',
	"Igrave;":                          '\U000000CC',
	"Im;":                              '\U00002111',
	"Imacr;":                           '\U0000012A',
	"ImaginaryI;":                      '\U00002148',
	"Implies;":                         '\U000021D2',
	"Int;":                             '\U0000222C',
	"Integral;":                        '\U0000222B',
	"Intersection;":                    '\U000022C2',
	"InvisibleComma;":                  '\U00002063',
	"InvisibleTimes;":                  '\U00002062',
	"Iogon;":                           '\U0000012E',
	"Iopf;":                            '\U0001D540',
	"Iota;":                            '\U00000399',
	"Iscr;":                            '\U00002110',
	"Itilde;":                          '\U00000128',
	"Iukcy;":                           '\U00000406',
	"Iuml;":                            '\U000000CF',
	"Jcirc;":                           '\U00000134',
	"Jcy;":                             '\U00000419',
	"Jfr;":                             '\U0001D50D',
	"Jopf;":                            '\U0001D541',
	"Jscr;":                            '\U0001D4A5',
	"Jsercy;":                          '\U00000408',
	"Jukcy;":                           '\U00000404',
	"KHcy;":                            '\U00000425',
	"KJcy;":                            '\U0000040C',
	"Kappa;":                           '\U0000039A',
	"Kcedil;":                          '\U00000136',
	"Kcy;":                             '\U0000041A',
	"Kfr;":                             '\U0001D50E',
	"Kopf;":                            '\U0001D542',
	"Kscr;":                            '\U0001D4A6',
	"LJcy;":                            '\U00000409',
	"LT;":                              '\U0000003C',
	"Lacute;":                          '\U00000139',
	"Lambda;":                          '\U0000039B',
	"Lang;":                            '\U000027EA',
	"Laplacetrf;":                      '\U00002112',
	"Larr;":                            '\U0000219E',
	"Lcaron;":                          '\U0000013D',
	"Lcedil;":                          '\U0000013B',
	"Lcy;":                             '\U0000041B',
	"LeftAngleBracket;":                '\U000027E8',
	"LeftArrow;":                       '\U00002190',
	"LeftArrowBar;":                    '\U000021E4',
	"LeftArrowRightArrow;":             '\U000021C6',
	"LeftCeiling;":                     '\U00002308',
	"LeftDoubleBracket;":               '\U000027E6',
	"LeftDownTeeVector;":               '\U00002961',
	"LeftDownVector;":                  '\U000021C3',
	"LeftDownVectorBar;":               '\U00002959',
	"LeftFloor;":                       '\U0000230A',
	"LeftRightArrow;":                  '\U00002194',
	"LeftRightVector;":                 '\U0000294E',
	"LeftTee;":                         '\U000022A3',
	"LeftTeeArrow;":                    '\U000021A4',
	"LeftTeeVector;":                   '\U0000295A',
	"LeftTriangle;":                    '\U000022B2',
	"LeftTriangleBar;":                 '\U000029CF',
	"LeftTriangleEqual;":               '\U000022B4',
	"LeftUpDownVector;":                '\U00002951',
	"LeftUpTeeVector;":                 '\U00002960',
	"LeftUpVector;":                    '\U000021BF',
	"LeftUpVectorBar;":                 '\U00002958',
	"LeftVector;":                      '\U000021BC',
	"LeftVectorBar;":                   '\U00002952',
	"Leftarrow;":                       '\U000021D0',
	"Leftrightarrow;":                  '\U000021D4',
	"LessEqualGreater;":                '\U000022DA',
	"LessFullEqual;":                   '\U00002266',
	"LessGreater;":                     '\U00002276',
	"LessLess;":                        '\U00002AA1',
	"LessSlantEqual;":                  '\U00002A7D',
	"LessTilde;":                       '\U00002272',
	"Lfr;":                             '\U0001D50F',
	"Ll;":                              '\U000022D8',
	"Lleftarrow;":                      '\U000021DA',
	"Lmidot;":                          '\U0000013F',
	"LongLeftArrow;":                   '\U000027F5',
	"LongLeftRightArrow;":              '\U000027F7',
	"LongRightArrow;":                  '\U000027F6',
	"Longleftarrow;":                   '\U000027F8',
	"Longleftrightarrow;":              '\U000027FA',
	"Longrightarrow;":                  '\U000027F9',
	"Lopf;":                            '\U0001D543',
	"LowerLeftArrow;":                  '\U00002199',
	"LowerRightArrow;":                 '\U00002198',
	"Lscr;":                            '\U00002112',
	"Lsh;":                             '\U000021B0',
	"Lstrok;":                          '\U00000141',
	"Lt;":                              '\U0000226A',
	"Map;":                             '\U00002905',
	"Mcy;":                             '\U0000041C',
	"MediumSpace;":                     '\U0000205F',
	"Mellintrf;":                       '\U00002133',
	"Mfr;":                             '\U0001D510',
	"MinusPlus;":                       '\U00002213',
	"Mopf;":                            '\U0001D544',
	"Mscr;":                            '\U00002133',
	"Mu;":                              '\U0000039C',
	"NJcy;":                            '\U0000040A',
	"Nacute;":                          '\*********',
	"Ncaron;":                          '\*********',
	"Ncedil;":                          '\*********',
	"Ncy;":                             '\U0000041D',
	"NegativeMediumSpace;":             '\U0000200B',
	"NegativeThickSpace;":              '\U0000200B',
	"NegativeThinSpace;":               '\U0000200B',
	"NegativeVeryThinSpace;":           '\U0000200B',
	"NestedGreaterGreater;":            '\U0000226B',
	"NestedLessLess;":                  '\U0000226A',
	"NewLine;":                         '\U0000000A',
	"Nfr;":                             '\U0001D511',
	"NoBreak;":                         '\*********',
	"NonBreakingSpace;":                '\U000000A0',
	"Nopf;":                            '\*********',
	"Not;":                             '\U00002AEC',
	"NotCongruent;":                    '\*********',
	"NotCupCap;":                       '\U0000226D',
	"NotDoubleVerticalBar;":            '\*********',
	"NotElement;":                      '\*********',
	"NotEqual;":                        '\*********',
	"NotExists;":                       '\*********',
	"NotGreater;":                      '\U0000226F',
	"NotGreaterEqual;":                 '\*********',
	"NotGreaterLess;":                  '\*********',
	"NotGreaterTilde;":                 '\*********',
	"NotLeftTriangle;":                 '\U000022EA',
	"NotLeftTriangleEqual;":            '\U000022EC',
	"NotLess;":                         '\U0000226E',
	"NotLessEqual;":                    '\U00002270',
	"NotLessGreater;":                  '\U00002278',
	"NotLessTilde;":                    '\U00002274',
	"NotPrecedes;":                     '\U00002280',
	"NotPrecedesSlantEqual;":           '\U000022E0',
	"NotReverseElement;":               '\U0000220C',
	"NotRightTriangle;":                '\U000022EB',
	"NotRightTriangleEqual;":           '\U000022ED',
	"NotSquareSubsetEqual;":            '\U000022E2',
	"NotSquareSupersetEqual;":          '\U000022E3',
	"NotSubsetEqual;":                  '\U00002288',
	"NotSucceeds;":                     '\U00002281',
	"NotSucceedsSlantEqual;":           '\U000022E1',
	"NotSupersetEqual;":                '\U00002289',
	"NotTilde;":                        '\U00002241',
	"NotTildeEqual;":                   '\U00002244',
	"NotTildeFullEqual;":               '\U00002247',
	"NotTildeTilde;":                   '\U00002249',
	"NotVerticalBar;":                  '\U00002224',
	"Nscr;":                            '\U0001D4A9',
	"Ntilde;":                          '\U000000D1',
	"Nu;":                              '\U0000039D',
	"OElig;":                           '\U00000152',
	"Oacute;":                          '\U000000D3',
	"Ocirc;":                           '\U000000D4',
	"Ocy;":                             '\U0000041E',
	"Odblac;":                          '\U00000150',
	"Ofr;":                             '\U0001D512',
	"Ograve;":                          '\U000000D2',
	"Omacr;":                           '\U0000014C',
	"Omega;":                           '\U000003A9',
	"Omicron;":                         '\U0000039F',
	"Oopf;":                            '\U0001D546',
	"OpenCurlyDoubleQuote;":            '\U0000201C',
	"OpenCurlyQuote;":                  '\U00002018',
	"Or;":                              '\U00002A54',
	"Oscr;":                            '\U0001D4AA',
	"Oslash;":                          '\U000000D8',
	"Otilde;":                          '\U000000D5',
	"Otimes;":                          '\U00002A37',
	"Ouml;":                            '\U000000D6',
	"OverBar;":                         '\U0000203E',
	"OverBrace;":                       '\U000023DE',
	"OverBracket;":                     '\U000023B4',
	"OverParenthesis;":                 '\U000023DC',
	"PartialD;":                        '\U00002202',
	"Pcy;":                             '\U0000041F',
	"Pfr;":                             '\U0001D513',
	"Phi;":                             '\U000003A6',
	"Pi;":                              '\U000003A0',
	"PlusMinus;":                       '\U000000B1',
	"Poincareplane;":                   '\U0000210C',
	"Popf;":                            '\U00002119',
	"Pr;":                              '\U00002ABB',
	"Precedes;":                        '\U0000227A',
	"PrecedesEqual;":                   '\U00002AAF',
	"PrecedesSlantEqual;":              '\U0000227C',
	"PrecedesTilde;":                   '\U0000227E',
	"Prime;":                           '\U00002033',
	"Product;":                         '\U0000220F',
	"Proportion;":                      '\U00002237',
	"Proportional;":                    '\U0000221D',
	"Pscr;":                            '\U0001D4AB',
	"Psi;":                             '\U000003A8',
	"QUOT;":                            '\U00000022',
	"Qfr;":                             '\U0001D514',
	"Qopf;":                            '\U0000211A',
	"Qscr;":                            '\U0001D4AC',
	"RBarr;":                           '\*********',
	"REG;":                             '\U000000AE',
	"Racute;":                          '\U00000154',
	"Rang;":                            '\U000027EB',
	"Rarr;":                            '\U000021A0',
	"Rarrtl;":                          '\U00002916',
	"Rcaron;":                          '\U00000158',
	"Rcedil;":                          '\U00000156',
	"Rcy;":                             '\U00000420',
	"Re;":                              '\U0000211C',
	"ReverseElement;":                  '\U0000220B',
	"ReverseEquilibrium;":              '\U000021CB',
	"ReverseUpEquilibrium;":            '\U0000296F',
	"Rfr;":                             '\U0000211C',
	"Rho;":                             '\U000003A1',
	"RightAngleBracket;":               '\U000027E9',
	"RightArrow;":                      '\*********',
	"RightArrowBar;":                   '\U000021E5',
	"RightArrowLeftArrow;":             '\U000021C4',
	"RightCeiling;":                    '\*********',
	"RightDoubleBracket;":              '\U000027E7',
	"RightDownTeeVector;":              '\U0000295D',
	"RightDownVector;":                 '\U000021C2',
	"RightDownVectorBar;":              '\U00002955',
	"RightFloor;":                      '\U0000230B',
	"RightTee;":                        '\U000022A2',
	"RightTeeArrow;":                   '\U000021A6',
	"RightTeeVector;":                  '\U0000295B',
	"RightTriangle;":                   '\U000022B3',
	"RightTriangleBar;":                '\U000029D0',
	"RightTriangleEqual;":              '\U000022B5',
	"RightUpDownVector;":               '\U0000294F',
	"RightUpTeeVector;":                '\U0000295C',
	"RightUpVector;":                   '\U000021BE',
	"RightUpVectorBar;":                '\U00002954',
	"RightVector;":                     '\U000021C0',
	"RightVectorBar;":                  '\U00002953',
	"Rightarrow;":                      '\U000021D2',
	"Ropf;":                            '\U0000211D',
	"RoundImplies;":                    '\U00002970',
	"Rrightarrow;":                     '\U000021DB',
	"Rscr;":                            '\U0000211B',
	"Rsh;":                             '\U000021B1',
	"RuleDelayed;":                     '\U000029F4',
	"SHCHcy;":                          '\U00000429',
	"SHcy;":                            '\U00000428',
	"SOFTcy;":                          '\U0000042C',
	"Sacute;":                          '\U0000015A',
	"Sc;":                              '\U00002ABC',
	"Scaron;":                          '\U00000160',
	"Scedil;":                          '\U0000015E',
	"Scirc;":                           '\U0000015C',
	"Scy;":                             '\U00000421',
	"Sfr;":                             '\U0001D516',
	"ShortDownArrow;":                  '\*********',
	"ShortLeftArrow;":                  '\U00002190',
	"ShortRightArrow;":                 '\*********',
	"ShortUpArrow;":                    '\*********',
	"Sigma;":                           '\U000003A3',
	"SmallCircle;":                     '\U00002218',
	"Sopf;":                            '\U0001D54A',
	"Sqrt;":                            '\U0000221A',
	"Square;":                          '\U000025A1',
	"SquareIntersection;":              '\U00002293',
	"SquareSubset;":                    '\U0000228F',
	"SquareSubsetEqual;":               '\U00002291',
	"SquareSuperset;":                  '\U00002290',
	"SquareSupersetEqual;":             '\U00002292',
	"SquareUnion;":                     '\U00002294',
	"Sscr;":                            '\U0001D4AE',
	"Star;":                            '\U000022C6',
	"Sub;":                             '\U000022D0',
	"Subset;":                          '\U000022D0',
	"SubsetEqual;":                     '\U00002286',
	"Succeeds;":                        '\U0000227B',
	"SucceedsEqual;":                   '\U00002AB0',
	"SucceedsSlantEqual;":              '\U0000227D',
	"SucceedsTilde;":                   '\U0000227F',
	"SuchThat;":                        '\U0000220B',
	"Sum;":                             '\U00002211',
	"Sup;":                             '\U000022D1',
	"Superset;":                        '\U00002283',
	"SupersetEqual;":                   '\U00002287',
	"Supset;":                          '\U000022D1',
	"THORN;":                           '\U000000DE',
	"TRADE;":                           '\U00002122',
	"TSHcy;":                           '\U0000040B',
	"TScy;":                            '\U00000426',
	"Tab;":                             '\U00000009',
	"Tau;":                             '\U000003A4',
	"Tcaron;":                          '\U00000164',
	"Tcedil;":                          '\U00000162',
	"Tcy;":                             '\U00000422',
	"Tfr;":                             '\U0001D517',
	"Therefore;":                       '\U00002234',
	"Theta;":                           '\U00000398',
	"ThinSpace;":                       '\U00002009',
	"Tilde;":                           '\U0000223C',
	"TildeEqual;":                      '\U00002243',
	"TildeFullEqual;":                  '\U00002245',
	"TildeTilde;":                      '\U00002248',
	"Topf;":                            '\U0001D54B',
	"TripleDot;":                       '\U000020DB',
	"Tscr;":                            '\U0001D4AF',
	"Tstrok;":                          '\U00000166',
	"Uacute;":                          '\U000000DA',
	"Uarr;":                            '\U0000219F',
	"Uarrocir;":                        '\U00002949',
	"Ubrcy;":                           '\U0000040E',
	"Ubreve;":                          '\U0000016C',
	"Ucirc;":                           '\U000000DB',
	"Ucy;":                             '\U00000423',
	"Udblac;":                          '\U00000170',
	"Ufr;":                             '\U0001D518',
	"Ugrave;":                          '\U000000D9',
	"Umacr;":                           '\U0000016A',
	"UnderBar;":                        '\U0000005F',
	"UnderBrace;":                      '\U000023DF',
	"UnderBracket;":                    '\U000023B5',
	"UnderParenthesis;":                '\U000023DD',
	"Union;":                           '\U000022C3',
	"UnionPlus;":                       '\U0000228E',
	"Uogon;":                           '\U00000172',
	"Uopf;":                            '\U0001D54C',
	"UpArrow;":                         '\*********',
	"UpArrowBar;":                      '\U00002912',
	"UpArrowDownArrow;":                '\U000021C5',
	"UpDownArrow;":                     '\U00002195',
	"UpEquilibrium;":                   '\U0000296E',
	"UpTee;":                           '\U000022A5',
	"UpTeeArrow;":                      '\U000021A5',
	"Uparrow;":                         '\U000021D1',
	"Updownarrow;":                     '\U000021D5',
	"UpperLeftArrow;":                  '\U00002196',
	"UpperRightArrow;":                 '\U00002197',
	"Upsi;":                            '\U000003D2',
	"Upsilon;":                         '\U000003A5',
	"Uring;":                           '\U0000016E',
	"Uscr;":                            '\U0001D4B0',
	"Utilde;":                          '\U00000168',
	"Uuml;":                            '\U000000DC',
	"VDash;":                           '\U000022AB',
	"Vbar;":                            '\U00002AEB',
	"Vcy;":                             '\U00000412',
	"Vdash;":                           '\U000022A9',
	"Vdashl;":                          '\U00002AE6',
	"Vee;":                             '\U000022C1',
	"Verbar;":                          '\U00002016',
	"Vert;":                            '\U00002016',
	"VerticalBar;":                     '\U00002223',
	"VerticalLine;":                    '\U0000007C',
	"VerticalSeparator;":               '\U00002758',
	"VerticalTilde;":                   '\U00002240',
	"VeryThinSpace;":                   '\U0000200A',
	"Vfr;":                             '\U0001D519',
	"Vopf;":                            '\U0001D54D',
	"Vscr;":                            '\U0001D4B1',
	"Vvdash;":                          '\U000022AA',
	"Wcirc;":                           '\U00000174',
	"Wedge;":                           '\U000022C0',
	"Wfr;":                             '\U0001D51A',
	"Wopf;":                            '\U0001D54E',
	"Wscr;":                            '\U0001D4B2',
	"Xfr;":                             '\U0001D51B',
	"Xi;":                              '\U0000039E',
	"Xopf;":                            '\U0001D54F',
	"Xscr;":                            '\U0001D4B3',
	"YAcy;":                            '\U0000042F',
	"YIcy;":                            '\U00000407',
	"YUcy;":                            '\U0000042E',
	"Yacute;":                          '\U000000DD',
	"Ycirc;":                           '\U00000176',
	"Ycy;":                             '\U0000042B',
	"Yfr;":                             '\U0001D51C',
	"Yopf;":                            '\U0001D550',
	"Yscr;":                            '\U0001D4B4',
	"Yuml;":                            '\U00000178',
	"ZHcy;":                            '\U00000416',
	"Zacute;":                          '\U00000179',
	"Zcaron;":                          '\U0000017D',
	"Zcy;":                             '\U00000417',
	"Zdot;":                            '\U0000017B',
	"ZeroWidthSpace;":                  '\U0000200B',
	"Zeta;":                            '\U00000396',
	"Zfr;":                             '\U00002128',
	"Zopf;":                            '\U00002124',
	"Zscr;":                            '\U0001D4B5',
	"aacute;":                          '\U000000E1',
	"abreve;":                          '\U00000103',
	"ac;":                              '\U0000223E',
	"acd;":                             '\U0000223F',
	"acirc;":                           '\U000000E2',
	"acute;":                           '\U000000B4',
	"acy;":                             '\U00000430',
	"aelig;":                           '\U000000E6',
	"af;":                              '\U00002061',
	"afr;":                             '\U0001D51E',
	"agrave;":                          '\U000000E0',
	"alefsym;":                         '\U00002135',
	"aleph;":                           '\U00002135',
	"alpha;":                           '\U000003B1',
	"amacr;":                           '\U00000101',
	"amalg;":                           '\U00002A3F',
	"amp;":                             '\*********',
	"and;":                             '\U00002227',
	"andand;":                          '\U00002A55',
	"andd;":                            '\U00002A5C',
	"andslope;":                        '\U00002A58',
	"andv;":                            '\U00002A5A',
	"ang;":                             '\U00002220',
	"ange;":                            '\U000029A4',
	"angle;":                           '\U00002220',
	"angmsd;":                          '\U00002221',
	"angmsdaa;":                        '\U000029A8',
	"angmsdab;":                        '\U000029A9',
	"angmsdac;":                        '\U000029AA',
	"angmsdad;":                        '\U000029AB',
	"angmsdae;":                        '\U000029AC',
	"angmsdaf;":                        '\U000029AD',
	"angmsdag;":                        '\U000029AE',
	"angmsdah;":                        '\U000029AF',
	"angrt;":                           '\U0000221F',
	"angrtvb;":                         '\U000022BE',
	"angrtvbd;":                        '\U0000299D',
	"angsph;":                          '\U00002222',
	"angst;":                           '\U000000C5',
	"angzarr;":                         '\U0000237C',
	"aogon;":                           '\U00000105',
	"aopf;":                            '\U0001D552',
	"ap;":                              '\U00002248',
	"apE;":                             '\U00002A70',
	"apacir;":                          '\U00002A6F',
	"ape;":                             '\U0000224A',
	"apid;":                            '\U0000224B',
	"apos;":                            '\U00000027',
	"approx;":                          '\U00002248',
	"approxeq;":                        '\U0000224A',
	"aring;":                           '\U000000E5',
	"ascr;":                            '\U0001D4B6',
	"ast;":                             '\U0000002A',
	"asymp;":                           '\U00002248',
	"asympeq;":                         '\U0000224D',
	"atilde;":                          '\U000000E3',
	"auml;":                            '\U000000E4',
	"awconint;":                        '\U00002233',
	"awint;":                           '\U00002A11',
	"bNot;":                            '\U00002AED',
	"backcong;":                        '\U0000224C',
	"backepsilon;":                     '\U000003F6',
	"backprime;":                       '\U00002035',
	"backsim;":                         '\U0000223D',
	"backsimeq;":                       '\U000022CD',
	"barvee;":                          '\U000022BD',
	"barwed;":                          '\U00002305',
	"barwedge;":                        '\U00002305',
	"bbrk;":                            '\U000023B5',
	"bbrktbrk;":                        '\U000023B6',
	"bcong;":                           '\U0000224C',
	"bcy;":                             '\U00000431',
	"bdquo;":                           '\U0000201E',
	"becaus;":                          '\U00002235',
	"because;":                         '\U00002235',
	"bemptyv;":                         '\U000029B0',
	"bepsi;":                           '\U000003F6',
	"bernou;":                          '\U0000212C',
	"beta;":                            '\U000003B2',
	"beth;":                            '\U00002136',
	"between;":                         '\U0000226C',
	"bfr;":                             '\U0001D51F',
	"bigcap;":                          '\U000022C2',
	"bigcirc;":                         '\U000025EF',
	"bigcup;":                          '\U000022C3',
	"bigodot;":                         '\U00002A00',
	"bigoplus;":                        '\U00002A01',
	"bigotimes;":                       '\U00002A02',
	"bigsqcup;":                        '\U00002A06',
	"bigstar;":                         '\U00002605',
	"bigtriangledown;":                 '\U000025BD',
	"bigtriangleup;":                   '\U000025B3',
	"biguplus;":                        '\U00002A04',
	"bigvee;":                          '\U000022C1',
	"bigwedge;":                        '\U000022C0',
	"bkarow;":                          '\U0000290D',
	"blacklozenge;":                    '\U000029EB',
	"blacksquare;":                     '\U000025AA',
	"blacktriangle;":                   '\U000025B4',
	"blacktriangledown;":               '\U000025BE',
	"blacktriangleleft;":               '\U000025C2',
	"blacktriangleright;":              '\U000025B8',
	"blank;":                           '\*********',
	"blk12;":                           '\*********',
	"blk14;":                           '\*********',
	"blk34;":                           '\*********',
	"block;":                           '\*********',
	"bnot;":                            '\*********',
	"bopf;":                            '\U0001D553',
	"bot;":                             '\U000022A5',
	"bottom;":                          '\U000022A5',
	"bowtie;":                          '\U000022C8',
	"boxDL;":                           '\*********',
	"boxDR;":                           '\*********',
	"boxDl;":                           '\*********',
	"boxDr;":                           '\*********',
	"boxH;":                            '\*********',
	"boxHD;":                           '\*********',
	"boxHU;":                           '\*********',
	"boxHd;":                           '\*********',
	"boxHu;":                           '\*********',
	"boxUL;":                           '\U0000255D',
	"boxUR;":                           '\U0000255A',
	"boxUl;":                           '\U0000255C',
	"boxUr;":                           '\*********',
	"boxV;":                            '\*********',
	"boxVH;":                           '\U0000256C',
	"boxVL;":                           '\*********',
	"boxVR;":                           '\*********',
	"boxVh;":                           '\U0000256B',
	"boxVl;":                           '\*********',
	"boxVr;":                           '\U0000255F',
	"boxbox;":                          '\U000029C9',
	"boxdL;":                           '\*********',
	"boxdR;":                           '\*********',
	"boxdl;":                           '\*********',
	"boxdr;":                           '\U0000250C',
	"boxh;":                            '\*********',
	"boxhD;":                           '\*********',
	"boxhU;":                           '\*********',
	"boxhd;":                           '\U0000252C',
	"boxhu;":                           '\*********',
	"boxminus;":                        '\U0000229F',
	"boxplus;":                         '\U0000229E',
	"boxtimes;":                        '\U000022A0',
	"boxuL;":                           '\U0000255B',
	"boxuR;":                           '\*********',
	"boxul;":                           '\*********',
	"boxur;":                           '\*********',
	"boxv;":                            '\*********',
	"boxvH;":                           '\U0000256A',
	"boxvL;":                           '\*********',
	"boxvR;":                           '\U0000255E',
	"boxvh;":                           '\U0000253C',
	"boxvl;":                           '\U00002524',
	"boxvr;":                           '\U0000251C',
	"bprime;":                          '\U00002035',
	"breve;":                           '\U000002D8',
	"brvbar;":                          '\U000000A6',
	"bscr;":                            '\U0001D4B7',
	"bsemi;":                           '\U0000204F',
	"bsim;":                            '\U0000223D',
	"bsime;":                           '\U000022CD',
	"bsol;":                            '\U0000005C',
	"bsolb;":                           '\U000029C5',
	"bsolhsub;":                        '\U000027C8',
	"bull;":                            '\U00002022',
	"bullet;":                          '\U00002022',
	"bump;":                            '\U0000224E',
	"bumpE;":                           '\U00002AAE',
	"bumpe;":                           '\U0000224F',
	"bumpeq;":                          '\U0000224F',
	"cacute;":                          '\U00000107',
	"cap;":                             '\U00002229',
	"capand;":                          '\U00002A44',
	"capbrcup;":                        '\U00002A49',
	"capcap;":                          '\U00002A4B',
	"capcup;":                          '\U00002A47',
	"capdot;":                          '\U00002A40',
	"caret;":                           '\U00002041',
	"caron;":                           '\U000002C7',
	"ccaps;":                           '\U00002A4D',
	"ccaron;":                          '\U0000010D',
	"ccedil;":                          '\U000000E7',
	"ccirc;":                           '\U00000109',
	"ccups;":                           '\U00002A4C',
	"ccupssm;":                         '\U00002A50',
	"cdot;":                            '\U0000010B',
	"cedil;":                           '\U000000B8',
	"cemptyv;":                         '\U000029B2',
	"cent;":                            '\U000000A2',
	"centerdot;":                       '\U000000B7',
	"cfr;":                             '\U0001D520',
	"chcy;":                            '\U00000447',
	"check;":                           '\U00002713',
	"checkmark;":                       '\U00002713',
	"chi;":                             '\U000003C7',
	"cir;":                             '\U000025CB',
	"cirE;":                            '\U000029C3',
	"circ;":                            '\U000002C6',
	"circeq;":                          '\U00002257',
	"circlearrowleft;":                 '\U000021BA',
	"circlearrowright;":                '\U000021BB',
	"circledR;":                        '\U000000AE',
	"circledS;":                        '\U000024C8',
	"circledast;":                      '\U0000229B',
	"circledcirc;":                     '\U0000229A',
	"circleddash;":                     '\U0000229D',
	"cire;":                            '\U00002257',
	"cirfnint;":                        '\U00002A10',
	"cirmid;":                          '\U00002AEF',
	"cirscir;":                         '\U000029C2',
	"clubs;":                           '\U00002663',
	"clubsuit;":                        '\U00002663',
	"colon;":                           '\U0000003A',
	"colone;":                          '\U00002254',
	"coloneq;":                         '\U00002254',
	"comma;":                           '\U0000002C',
	"commat;":                          '\U00000040',
	"comp;":                            '\U00002201',
	"compfn;":                          '\U00002218',
	"complement;":                      '\U00002201',
	"complexes;":                       '\U00002102',
	"cong;":                            '\U00002245',
	"congdot;":                         '\U00002A6D',
	"conint;":                          '\U0000222E',
	"copf;":                            '\U0001D554',
	"coprod;":                          '\U00002210',
	"copy;":                            '\U000000A9',
	"copysr;":                          '\U00002117',
	"crarr;":                           '\U000021B5',
	"cross;":                           '\U00002717',
	"cscr;":                            '\U0001D4B8',
	"csub;":                            '\U00002ACF',
	"csube;":                           '\U00002AD1',
	"csup;":                            '\U00002AD0',
	"csupe;":                           '\U00002AD2',
	"ctdot;":                           '\U000022EF',
	"cudarrl;":                         '\U00002938',
	"cudarrr;":                         '\U00002935',
	"cuepr;":                           '\U000022DE',
	"cuesc;":                           '\U000022DF',
	"cularr;":                          '\U000021B6',
	"cularrp;":                         '\U0000293D',
	"cup;":                             '\U0000222A',
	"cupbrcap;":                        '\U00002A48',
	"cupcap;":                          '\U00002A46',
	"cupcup;":                          '\U00002A4A',
	"cupdot;":                          '\U0000228D',
	"cupor;":                           '\U00002A45',
	"curarr;":                          '\U000021B7',
	"curarrm;":                         '\U0000293C',
	"curlyeqprec;":                     '\U000022DE',
	"curlyeqsucc;":                     '\U000022DF',
	"curlyvee;":                        '\U000022CE',
	"curlywedge;":                      '\U000022CF',
	"curren;":                          '\U000000A4',
	"curvearrowleft;":                  '\U000021B6',
	"curvearrowright;":                 '\U000021B7',
	"cuvee;":                           '\U000022CE',
	"cuwed;":                           '\U000022CF',
	"cwconint;":                        '\U00002232',
	"cwint;":                           '\U00002231',
	"cylcty;":                          '\U0000232D',
	"dArr;":                            '\U000021D3',
	"dHar;":                            '\U00002965',
	"dagger;":                          '\U00002020',
	"daleth;":                          '\U00002138',
	"darr;":                            '\*********',
	"dash;":                            '\U00002010',
	"dashv;":                           '\U000022A3',
	"dbkarow;":                         '\U0000290F',
	"dblac;":                           '\U000002DD',
	"dcaron;":                          '\U0000010F',
	"dcy;":                             '\U00000434',
	"dd;":                              '\U00002146',
	"ddagger;":                         '\U00002021',
	"ddarr;":                           '\U000021CA',
	"ddotseq;":                         '\U00002A77',
	"deg;":                             '\U000000B0',
	"delta;":                           '\U000003B4',
	"demptyv;":                         '\U000029B1',
	"dfisht;":                          '\U0000297F',
	"dfr;":                             '\U0001D521',
	"dharl;":                           '\U000021C3',
	"dharr;":                           '\U000021C2',
	"diam;":                            '\U000022C4',
	"diamond;":                         '\U000022C4',
	"diamondsuit;":                     '\*********',
	"diams;":                           '\*********',
	"die;":                             '\U000000A8',
	"digamma;":                         '\U000003DD',
	"disin;":                           '\U000022F2',
	"div;":                             '\U000000F7',
	"divide;":                          '\U000000F7',
	"divideontimes;":                   '\U000022C7',
	"divonx;":                          '\U000022C7',
	"djcy;":                            '\*********',
	"dlcorn;":                          '\U0000231E',
	"dlcrop;":                          '\U0000230D',
	"dollar;":                          '\*********',
	"dopf;":                            '\U0001D555',
	"dot;":                             '\U000002D9',
	"doteq;":                           '\*********',
	"doteqdot;":                        '\*********',
	"dotminus;":                        '\*********',
	"dotplus;":                         '\*********',
	"dotsquare;":                       '\U000022A1',
	"doublebarwedge;":                  '\*********',
	"downarrow;":                       '\*********',
	"downdownarrows;":                  '\U000021CA',
	"downharpoonleft;":                 '\U000021C3',
	"downharpoonright;":                '\U000021C2',
	"drbkarow;":                        '\*********',
	"drcorn;":                          '\U0000231F',
	"drcrop;":                          '\U0000230C',
	"dscr;":                            '\U0001D4B9',
	"dscy;":                            '\*********',
	"dsol;":                            '\U000029F6',
	"dstrok;":                          '\U00000111',
	"dtdot;":                           '\U000022F1',
	"dtri;":                            '\U000025BF',
	"dtrif;":                           '\U000025BE',
	"duarr;":                           '\U000021F5',
	"duhar;":                           '\U0000296F',
	"dwangle;":                         '\U000029A6',
	"dzcy;":                            '\U0000045F',
	"dzigrarr;":                        '\U000027FF',
	"eDDot;":                           '\U00002A77',
	"eDot;":                            '\*********',
	"eacute;":                          '\U000000E9',
	"easter;":                          '\U00002A6E',
	"ecaron;":                          '\U0000011B',
	"ecir;":                            '\U00002256',
	"ecirc;":                           '\U000000EA',
	"ecolon;":                          '\U00002255',
	"ecy;":                             '\U0000044D',
	"edot;":                            '\U00000117',
	"ee;":                              '\U00002147',
	"efDot;":                           '\U00002252',
	"efr;":                             '\U0001D522',
	"eg;":                              '\U00002A9A',
	"egrave;":                          '\U000000E8',
	"egs;":                             '\U00002A96',
	"egsdot;":                          '\U00002A98',
	"el;":                              '\U00002A99',
	"elinters;":                        '\U000023E7',
	"ell;":                             '\U00002113',
	"els;":                             '\U00002A95',
	"elsdot;":                          '\U00002A97',
	"emacr;":                           '\U00000113',
	"empty;":                           '\U00002205',
	"emptyset;":                        '\U00002205',
	"emptyv;":                          '\U00002205',
	"emsp;":                            '\U00002003',
	"emsp13;":                          '\U00002004',
	"emsp14;":                          '\U00002005',
	"eng;":                             '\U0000014B',
	"ensp;":                            '\U00002002',
	"eogon;":                           '\U00000119',
	"eopf;":                            '\U0001D556',
	"epar;":                            '\U000022D5',
	"eparsl;":                          '\U000029E3',
	"eplus;":                           '\U00002A71',
	"epsi;":                            '\U000003B5',
	"epsilon;":                         '\U000003B5',
	"epsiv;":                           '\U000003F5',
	"eqcirc;":                          '\U00002256',
	"eqcolon;":                         '\U00002255',
	"eqsim;":                           '\U00002242',
	"eqslantgtr;":                      '\U00002A96',
	"eqslantless;":                     '\U00002A95',
	"equals;":                          '\U0000003D',
	"equest;":                          '\U0000225F',
	"equiv;":                           '\U00002261',
	"equivDD;":                         '\U00002A78',
	"eqvparsl;":                        '\U000029E5',
	"erDot;":                           '\U00002253',
	"erarr;":                           '\U00002971',
	"escr;":                            '\U0000212F',
	"esdot;":                           '\*********',
	"esim;":                            '\U00002242',
	"eta;":                             '\U000003B7',
	"eth;":                             '\U000000F0',
	"euml;":                            '\U000000EB',
	"euro;":                            '\U000020AC',
	"excl;":                            '\U00000021',
	"exist;":                           '\U00002203',
	"expectation;":                     '\U00002130',
	"exponentiale;":                    '\U00002147',
	"fallingdotseq;":                   '\U00002252',
	"fcy;":                             '\U00000444',
	"female;":                          '\U00002640',
	"ffilig;":                          '\U0000FB03',
	"fflig;":                           '\U0000FB00',
	"ffllig;":                          '\U0000FB04',
	"ffr;":                             '\U0001D523',
	"filig;":                           '\U0000FB01',
	"flat;":                            '\U0000266D',
	"fllig;":                           '\U0000FB02',
	"fltns;":                           '\U000025B1',
	"fnof;":                            '\U00000192',
	"fopf;":                            '\U0001D557',
	"forall;":                          '\U00002200',
	"fork;":                            '\U000022D4',
	"forkv;":                           '\U00002AD9',
	"fpartint;":                        '\U00002A0D',
	"frac12;":                          '\U000000BD',
	"frac13;":                          '\U00002153',
	"frac14;":                          '\U000000BC',
	"frac15;":                          '\U00002155',
	"frac16;":                          '\U00002159',
	"frac18;":                          '\U0000215B',
	"frac23;":                          '\U00002154',
	"frac25;":                          '\U00002156',
	"frac34;":                          '\U000000BE',
	"frac35;":                          '\U00002157',
	"frac38;":                          '\U0000215C',
	"frac45;":                          '\U00002158',
	"frac56;":                          '\U0000215A',
	"frac58;":                          '\U0000215D',
	"frac78;":                          '\U0000215E',
	"frasl;":                           '\U00002044',
	"frown;":                           '\U00002322',
	"fscr;":                            '\U0001D4BB',
	"gE;":                              '\U00002267',
	"gEl;":                             '\U00002A8C',
	"gacute;":                          '\U000001F5',
	"gamma;":                           '\U000003B3',
	"gammad;":                          '\U000003DD',
	"gap;":                             '\U00002A86',
	"gbreve;":                          '\U0000011F',
	"gcirc;":                           '\U0000011D',
	"gcy;":                             '\U00000433',
	"gdot;":                            '\U00000121',
	"ge;":                              '\U00002265',
	"gel;":                             '\U000022DB',
	"geq;":                             '\U00002265',
	"geqq;":                            '\U00002267',
	"geqslant;":                        '\U00002A7E',
	"ges;":                             '\U00002A7E',
	"gescc;":                           '\U00002AA9',
	"gesdot;":                          '\U00002A80',
	"gesdoto;":                         '\U00002A82',
	"gesdotol;":                        '\U00002A84',
	"gesles;":                          '\U00002A94',
	"gfr;":                             '\U0001D524',
	"gg;":                              '\U0000226B',
	"ggg;":                             '\U000022D9',
	"gimel;":                           '\U00002137',
	"gjcy;":                            '\U00000453',
	"gl;":                              '\U00002277',
	"glE;":                             '\U00002A92',
	"gla;":                             '\U00002AA5',
	"glj;":                             '\U00002AA4',
	"gnE;":                             '\U00002269',
	"gnap;":                            '\U00002A8A',
	"gnapprox;":                        '\U00002A8A',
	"gne;":                             '\U00002A88',
	"gneq;":                            '\U00002A88',
	"gneqq;":                           '\U00002269',
	"gnsim;":                           '\U000022E7',
	"gopf;":                            '\U0001D558',
	"grave;":                           '\U00000060',
	"gscr;":                            '\U0000210A',
	"gsim;":                            '\U00002273',
	"gsime;":                           '\U00002A8E',
	"gsiml;":                           '\U00002A90',
	"gt;":                              '\U0000003E',
	"gtcc;":                            '\U00002AA7',
	"gtcir;":                           '\U00002A7A',
	"gtdot;":                           '\U000022D7',
	"gtlPar;":                          '\U00002995',
	"gtquest;":                         '\U00002A7C',
	"gtrapprox;":                       '\U00002A86',
	"gtrarr;":                          '\U00002978',
	"gtrdot;":                          '\U000022D7',
	"gtreqless;":                       '\U000022DB',
	"gtreqqless;":                      '\U00002A8C',
	"gtrless;":                         '\U00002277',
	"gtrsim;":                          '\U00002273',
	"hArr;":                            '\U000021D4',
	"hairsp;":                          '\U0000200A',
	"half;":                            '\U000000BD',
	"hamilt;":                          '\U0000210B',
	"hardcy;":                          '\U0000044A',
	"harr;":                            '\U00002194',
	"harrcir;":                         '\U00002948',
	"harrw;":                           '\U000021AD',
	"hbar;":                            '\U0000210F',
	"hcirc;":                           '\U00000125',
	"hearts;":                          '\U00002665',
	"heartsuit;":                       '\U00002665',
	"hellip;":                          '\U00002026',
	"hercon;":                          '\U000022B9',
	"hfr;":                             '\U0001D525',
	"hksearow;":                        '\U00002925',
	"hkswarow;":                        '\U00002926',
	"hoarr;":                           '\U000021FF',
	"homtht;":                          '\U0000223B',
	"hookleftarrow;":                   '\U000021A9',
	"hookrightarrow;":                  '\U000021AA',
	"hopf;":                            '\U0001D559',
	"horbar;":                          '\U00002015',
	"hscr;":                            '\U0001D4BD',
	"hslash;":                          '\U0000210F',
	"hstrok;":                          '\U00000127',
	"hybull;":                          '\U00002043',
	"hyphen;":                          '\U00002010',
	"iacute;":                          '\U000000ED',
	"ic;":                              '\U00002063',
	"icirc;":                           '\U000000EE',
	"icy;":                             '\U00000438',
	"iecy;":                            '\U00000435',
	"iexcl;":                           '\U000000A1',
	"iff;":                             '\U000021D4',
	"ifr;":                             '\U0001D526',
	"igrave;":                          '\U000000EC',
	"ii;":                              '\U00002148',
	"iiiint;":                          '\U00002A0C',
	"iiint;":                           '\U0000222D',
	"iinfin;":                          '\U000029DC',
	"iiota;":                           '\U00002129',
	"ijlig;":                           '\U00000133',
	"imacr;":                           '\U0000012B',
	"image;":                           '\U00002111',
	"imagline;":                        '\U00002110',
	"imagpart;":                        '\U00002111',
	"imath;":                           '\U00000131',
	"imof;":                            '\U000022B7',
	"imped;":                           '\U000001B5',
	"in;":                              '\U00002208',
	"incare;":                          '\U00002105',
	"infin;":                           '\U0000221E',
	"infintie;":                        '\U000029DD',
	"inodot;":                          '\U00000131',
	"int;":                             '\U0000222B',
	"intcal;":                          '\U000022BA',
	"integers;":                        '\U00002124',
	"intercal;":                        '\U000022BA',
	"intlarhk;":                        '\U00002A17',
	"intprod;":                         '\U00002A3C',
	"iocy;":                            '\U00000451',
	"iogon;":                           '\U0000012F',
	"iopf;":                            '\U0001D55A',
	"iota;":                            '\U000003B9',
	"iprod;":                           '\U00002A3C',
	"iquest;":                          '\U000000BF',
	"iscr;":                            '\U0001D4BE',
	"isin;":                            '\U00002208',
	"isinE;":                           '\U000022F9',
	"isindot;":                         '\U000022F5',
	"isins;":                           '\U000022F4',
	"isinsv;":                          '\U000022F3',
	"isinv;":                           '\U00002208',
	"it;":                              '\U00002062',
	"itilde;":                          '\U00000129',
	"iukcy;":                           '\U00000456',
	"iuml;":                            '\U000000EF',
	"jcirc;":                           '\U00000135',
	"jcy;":                             '\U00000439',
	"jfr;":                             '\U0001D527',
	"jmath;":                           '\U00000237',
	"jopf;":                            '\U0001D55B',
	"jscr;":                            '\U0001D4BF',
	"jsercy;":                          '\U00000458',
	"jukcy;":                           '\U00000454',
	"kappa;":                           '\U000003BA',
	"kappav;":                          '\U000003F0',
	"kcedil;":                          '\U00000137',
	"kcy;":                             '\U0000043A',
	"kfr;":                             '\U0001D528',
	"kgreen;":                          '\U00000138',
	"khcy;":                            '\U00000445',
	"kjcy;":                            '\U0000045C',
	"kopf;":                            '\U0001D55C',
	"kscr;":                            '\U0001D4C0',
	"lAarr;":                           '\U000021DA',
	"lArr;":                            '\U000021D0',
	"lAtail;":                          '\U0000291B',
	"lBarr;":                           '\U0000290E',
	"lE;":                              '\U00002266',
	"lEg;":                             '\U00002A8B',
	"lHar;":                            '\U00002962',
	"lacute;":                          '\U0000013A',
	"laemptyv;":                        '\U000029B4',
	"lagran;":                          '\U00002112',
	"lambda;":                          '\U000003BB',
	"lang;":                            '\U000027E8',
	"langd;":                           '\U00002991',
	"langle;":                          '\U000027E8',
	"lap;":                             '\U00002A85',
	"laquo;":                           '\U000000AB',
	"larr;":                            '\U00002190',
	"larrb;":                           '\U000021E4',
	"larrbfs;":                         '\U0000291F',
	"larrfs;":                          '\U0000291D',
	"larrhk;":                          '\U000021A9',
	"larrlp;":                          '\U000021AB',
	"larrpl;":                          '\U00002939',
	"larrsim;":                         '\U00002973',
	"larrtl;":                          '\U000021A2',
	"lat;":                             '\U00002AAB',
	"latail;":                          '\U00002919',
	"late;":                            '\U00002AAD',
	"lbarr;":                           '\U0000290C',
	"lbbrk;":                           '\U00002772',
	"lbrace;":                          '\U0000007B',
	"lbrack;":                          '\U0000005B',
	"lbrke;":                           '\U0000298B',
	"lbrksld;":                         '\U0000298F',
	"lbrkslu;":                         '\U0000298D',
	"lcaron;":                          '\U0000013E',
	"lcedil;":                          '\U0000013C',
	"lceil;":                           '\U00002308',
	"lcub;":                            '\U0000007B',
	"lcy;":                             '\U0000043B',
	"ldca;":                            '\U00002936',
	"ldquo;":                           '\U0000201C',
	"ldquor;":                          '\U0000201E',
	"ldrdhar;":                         '\U00002967',
	"ldrushar;":                        '\U0000294B',
	"ldsh;":                            '\U000021B2',
	"le;":                              '\U00002264',
	"leftarrow;":                       '\U00002190',
	"leftarrowtail;":                   '\U000021A2',
	"leftharpoondown;":                 '\U000021BD',
	"leftharpoonup;":                   '\U000021BC',
	"leftleftarrows;":                  '\U000021C7',
	"leftrightarrow;":                  '\U00002194',
	"leftrightarrows;":                 '\U000021C6',
	"leftrightharpoons;":               '\U000021CB',
	"leftrightsquigarrow;":             '\U000021AD',
	"leftthreetimes;":                  '\U000022CB',
	"leg;":                             '\U000022DA',
	"leq;":                             '\U00002264',
	"leqq;":                            '\U00002266',
	"leqslant;":                        '\U00002A7D',
	"les;":                             '\U00002A7D',
	"lescc;":                           '\U00002AA8',
	"lesdot;":                          '\U00002A7F',
	"lesdoto;":                         '\U00002A81',
	"lesdotor;":                        '\U00002A83',
	"lesges;":                          '\U00002A93',
	"lessapprox;":                      '\U00002A85',
	"lessdot;":                         '\U000022D6',
	"lesseqgtr;":                       '\U000022DA',
	"lesseqqgtr;":                      '\U00002A8B',
	"lessgtr;":                         '\U00002276',
	"lesssim;":                         '\U00002272',
	"lfisht;":                          '\U0000297C',
	"lfloor;":                          '\U0000230A',
	"lfr;":                             '\U0001D529',
	"lg;":                              '\U00002276',
	"lgE;":                             '\U00002A91',
	"lhard;":                           '\U000021BD',
	"lharu;":                           '\U000021BC',
	"lharul;":                          '\U0000296A',
	"lhblk;":                           '\U00002584',
	"ljcy;":                            '\U00000459',
	"ll;":                              '\U0000226A',
	"llarr;":                           '\U000021C7',
	"llcorner;":                        '\U0000231E',
	"llhard;":                          '\U0000296B',
	"lltri;":                           '\U000025FA',
	"lmidot;":                          '\U00000140',
	"lmoust;":                          '\U000023B0',
	"lmoustache;":                      '\U000023B0',
	"lnE;":                             '\U00002268',
	"lnap;":                            '\U00002A89',
	"lnapprox;":                        '\U00002A89',
	"lne;":                             '\U00002A87',
	"lneq;":                            '\U00002A87',
	"lneqq;":                           '\U00002268',
	"lnsim;":                           '\U000022E6',
	"loang;":                           '\U000027EC',
	"loarr;":                           '\U000021FD',
	"lobrk;":                           '\U000027E6',
	"longleftarrow;":                   '\U000027F5',
	"longleftrightarrow;":              '\U000027F7',
	"longmapsto;":                      '\U000027FC',
	"longrightarrow;":                  '\U000027F6',
	"looparrowleft;":                   '\U000021AB',
	"looparrowright;":                  '\U000021AC',
	"lopar;":                           '\U00002985',
	"lopf;":                            '\U0001D55D',
	"loplus;":                          '\U00002A2D',
	"lotimes;":                         '\U00002A34',
	"lowast;":                          '\U00002217',
	"lowbar;":                          '\U0000005F',
	"loz;":                             '\U000025CA',
	"lozenge;":                         '\U000025CA',
	"lozf;":                            '\U000029EB',
	"lpar;":                            '\U00000028',
	"lparlt;":                          '\U00002993',
	"lrarr;":                           '\U000021C6',
	"lrcorner;":                        '\U0000231F',
	"lrhar;":                           '\U000021CB',
	"lrhard;":                          '\U0000296D',
	"lrm;":                             '\U0000200E',
	"lrtri;":                           '\U000022BF',
	"lsaquo;":                          '\U00002039',
	"lscr;":                            '\U0001D4C1',
	"lsh;":                             '\U000021B0',
	"lsim;":                            '\U00002272',
	"lsime;":                           '\U00002A8D',
	"lsimg;":                           '\U00002A8F',
	"lsqb;":                            '\U0000005B',
	"lsquo;":                           '\U00002018',
	"lsquor;":                          '\U0000201A',
	"lstrok;":                          '\U00000142',
	"lt;":                              '\U0000003C',
	"ltcc;":                            '\U00002AA6',
	"ltcir;":                           '\U00002A79',
	"ltdot;":                           '\U000022D6',
	"lthree;":                          '\U000022CB',
	"ltimes;":                          '\U000022C9',
	"ltlarr;":                          '\U00002976',
	"ltquest;":                         '\U00002A7B',
	"ltrPar;":                          '\U00002996',
	"ltri;":                            '\U000025C3',
	"ltrie;":                           '\U000022B4',
	"ltrif;":                           '\U000025C2',
	"lurdshar;":                        '\U0000294A',
	"luruhar;":                         '\U00002966',
	"mDDot;":                           '\U0000223A',
	"macr;":                            '\U000000AF',
	"male;":                            '\U00002642',
	"malt;":                            '\U00002720',
	"maltese;":                         '\U00002720',
	"map;":                             '\U000021A6',
	"mapsto;":                          '\U000021A6',
	"mapstodown;":                      '\U000021A7',
	"mapstoleft;":                      '\U000021A4',
	"mapstoup;":                        '\U000021A5',
	"marker;":                          '\U000025AE',
	"mcomma;":                          '\U00002A29',
	"mcy;":                             '\U0000043C',
	"mdash;":                           '\U00002014',
	"measuredangle;":                   '\U00002221',
	"mfr;":                             '\U0001D52A',
	"mho;":                             '\U00002127',
	"micro;":                           '\U000000B5',
	"mid;":                             '\U00002223',
	"midast;":                          '\U0000002A',
	"midcir;":                          '\U00002AF0',
	"middot;":                          '\U000000B7',
	"minus;":                           '\U00002212',
	"minusb;":                          '\U0000229F',
	"minusd;":                          '\*********',
	"minusdu;":                         '\U00002A2A',
	"mlcp;":                            '\U00002ADB',
	"mldr;":                            '\U00002026',
	"mnplus;":                          '\U00002213',
	"models;":                          '\U000022A7',
	"mopf;":                            '\U0001D55E',
	"mp;":                              '\U00002213',
	"mscr;":                            '\U0001D4C2',
	"mstpos;":                          '\U0000223E',
	"mu;":                              '\U000003BC',
	"multimap;":                        '\U000022B8',
	"mumap;":                           '\U000022B8',
	"nLeftarrow;":                      '\U000021CD',
	"nLeftrightarrow;":                 '\U000021CE',
	"nRightarrow;":                     '\U000021CF',
	"nVDash;":                          '\U000022AF',
	"nVdash;":                          '\U000022AE',
	"nabla;":                           '\U00002207',
	"nacute;":                          '\U00000144',
	"nap;":                             '\U00002249',
	"napos;":                           '\U00000149',
	"napprox;":                         '\U00002249',
	"natur;":                           '\U0000266E',
	"natural;":                         '\U0000266E',
	"naturals;":                        '\*********',
	"nbsp;":                            '\U000000A0',
	"ncap;":                            '\U00002A43',
	"ncaron;":                          '\U00000148',
	"ncedil;":                          '\U00000146',
	"ncong;":                           '\U00002247',
	"ncup;":                            '\U00002A42',
	"ncy;":                             '\U0000043D',
	"ndash;":                           '\U00002013',
	"ne;":                              '\*********',
	"neArr;":                           '\U000021D7',
	"nearhk;":                          '\U00002924',
	"nearr;":                           '\U00002197',
	"nearrow;":                         '\U00002197',
	"nequiv;":                          '\*********',
	"nesear;":                          '\U00002928',
	"nexist;":                          '\*********',
	"nexists;":                         '\*********',
	"nfr;":                             '\U0001D52B',
	"nge;":                             '\*********',
	"ngeq;":                            '\*********',
	"ngsim;":                           '\*********',
	"ngt;":                             '\U0000226F',
	"ngtr;":                            '\U0000226F',
	"nhArr;":                           '\U000021CE',
	"nharr;":                           '\U000021AE',
	"nhpar;":                           '\U00002AF2',
	"ni;":                              '\U0000220B',
	"nis;":                             '\U000022FC',
	"nisd;":                            '\U000022FA',
	"niv;":                             '\U0000220B',
	"njcy;":                            '\U0000045A',
	"nlArr;":                           '\U000021CD',
	"nlarr;":                           '\U0000219A',
	"nldr;":                            '\U00002025',
	"nle;":                             '\U00002270',
	"nleftarrow;":                      '\U0000219A',
	"nleftrightarrow;":                 '\U000021AE',
	"nleq;":                            '\U00002270',
	"nless;":                           '\U0000226E',
	"nlsim;":                           '\U00002274',
	"nlt;":                             '\U0000226E',
	"nltri;":                           '\U000022EA',
	"nltrie;":                          '\U000022EC',
	"nmid;":                            '\U00002224',
	"nopf;":                            '\U0001D55F',
	"not;":                             '\U000000AC',
	"notin;":                           '\*********',
	"notinva;":                         '\*********',
	"notinvb;":                         '\U000022F7',
	"notinvc;":                         '\U000022F6',
	"notni;":                           '\U0000220C',
	"notniva;":                         '\U0000220C',
	"notnivb;":                         '\U000022FE',
	"notnivc;":                         '\U000022FD',
	"npar;":                            '\*********',
	"nparallel;":                       '\*********',
	"npolint;":                         '\U00002A14',
	"npr;":                             '\U00002280',
	"nprcue;":                          '\U000022E0',
	"nprec;":                           '\U00002280',
	"nrArr;":                           '\U000021CF',
	"nrarr;":                           '\U0000219B',
	"nrightarrow;":                     '\U0000219B',
	"nrtri;":                           '\U000022EB',
	"nrtrie;":                          '\U000022ED',
	"nsc;":                             '\U00002281',
	"nsccue;":                          '\U000022E1',
	"nscr;":                            '\U0001D4C3',
	"nshortmid;":                       '\U00002224',
	"nshortparallel;":                  '\*********',
	"nsim;":                            '\U00002241',
	"nsime;":                           '\U00002244',
	"nsimeq;":                          '\U00002244',
	"nsmid;":                           '\U00002224',
	"nspar;":                           '\*********',
	"nsqsube;":                         '\U000022E2',
	"nsqsupe;":                         '\U000022E3',
	"nsub;":                            '\U00002284',
	"nsube;":                           '\U00002288',
	"nsubseteq;":                       '\U00002288',
	"nsucc;":                           '\U00002281',
	"nsup;":                            '\U00002285',
	"nsupe;":                           '\U00002289',
	"nsupseteq;":                       '\U00002289',
	"ntgl;":                            '\*********',
	"ntilde;":                          '\U000000F1',
	"ntlg;":                            '\U00002278',
	"ntriangleleft;":                   '\U000022EA',
	"ntrianglelefteq;":                 '\U000022EC',
	"ntriangleright;":                  '\U000022EB',
	"ntrianglerighteq;":                '\U000022ED',
	"nu;":                              '\U000003BD',
	"num;":                             '\U00000023',
	"numero;":                          '\U00002116',
	"numsp;":                           '\U00002007',
	"nvDash;":                          '\U000022AD',
	"nvHarr;":                          '\U00002904',
	"nvdash;":                          '\U000022AC',
	"nvinfin;":                         '\U000029DE',
	"nvlArr;":                          '\U00002902',
	"nvrArr;":                          '\U00002903',
	"nwArr;":                           '\U000021D6',
	"nwarhk;":                          '\U00002923',
	"nwarr;":                           '\U00002196',
	"nwarrow;":                         '\U00002196',
	"nwnear;":                          '\U00002927',
	"oS;":                              '\U000024C8',
	"oacute;":                          '\U000000F3',
	"oast;":                            '\U0000229B',
	"ocir;":                            '\U0000229A',
	"ocirc;":                           '\U000000F4',
	"ocy;":                             '\U0000043E',
	"odash;":                           '\U0000229D',
	"odblac;":                          '\U00000151',
	"odiv;":                            '\U00002A38',
	"odot;":                            '\U00002299',
	"odsold;":                          '\U000029BC',
	"oelig;":                           '\U00000153',
	"ofcir;":                           '\U000029BF',
	"ofr;":                             '\U0001D52C',
	"ogon;":                            '\U000002DB',
	"ograve;":                          '\U000000F2',
	"ogt;":                             '\U000029C1',
	"ohbar;":                           '\U000029B5',
	"ohm;":                             '\U000003A9',
	"oint;":                            '\U0000222E',
	"olarr;":                           '\U000021BA',
	"olcir;":                           '\U000029BE',
	"olcross;":                         '\U000029BB',
	"oline;":                           '\U0000203E',
	"olt;":                             '\U000029C0',
	"omacr;":                           '\U0000014D',
	"omega;":                           '\U000003C9',
	"omicron;":                         '\U000003BF',
	"omid;":                            '\U000029B6',
	"ominus;":                          '\U00002296',
	"oopf;":                            '\U0001D560',
	"opar;":                            '\U000029B7',
	"operp;":                           '\U000029B9',
	"oplus;":                           '\U00002295',
	"or;":                              '\U00002228',
	"orarr;":                           '\U000021BB',
	"ord;":                             '\U00002A5D',
	"order;":                           '\U00002134',
	"orderof;":                         '\U00002134',
	"ordf;":                            '\U000000AA',
	"ordm;":                            '\U000000BA',
	"origof;":                          '\U000022B6',
	"oror;":                            '\U00002A56',
	"orslope;":                         '\U00002A57',
	"orv;":                             '\U00002A5B',
	"oscr;":                            '\U00002134',
	"oslash;":                          '\U000000F8',
	"osol;":                            '\U00002298',
	"otilde;":                          '\U000000F5',
	"otimes;":                          '\U00002297',
	"otimesas;":                        '\U00002A36',
	"ouml;":                            '\U000000F6',
	"ovbar;":                           '\U0000233D',
	"par;":                             '\U00002225',
	"para;":                            '\U000000B6',
	"parallel;":                        '\U00002225',
	"parsim;":                          '\U00002AF3',
	"parsl;":                           '\U00002AFD',
	"part;":                            '\U00002202',
	"pcy;":                             '\U0000043F',
	"percnt;":                          '\U00000025',
	"period;":                          '\U0000002E',
	"permil;":                          '\U00002030',
	"perp;":                            '\U000022A5',
	"pertenk;":                         '\U00002031',
	"pfr;":                             '\U0001D52D',
	"phi;":                             '\U000003C6',
	"phiv;":                            '\U000003D5',
	"phmmat;":                          '\U00002133',
	"phone;":                           '\U0000260E',
	"pi;":                              '\U000003C0',
	"pitchfork;":                       '\U000022D4',
	"piv;":                             '\U000003D6',
	"planck;":                          '\U0000210F',
	"planckh;":                         '\U0000210E',
	"plankv;":                          '\U0000210F',
	"plus;":                            '\U0000002B',
	"plusacir;":                        '\U00002A23',
	"plusb;":                           '\U0000229E',
	"pluscir;":                         '\U00002A22',
	"plusdo;":                          '\*********',
	"plusdu;":                          '\U00002A25',
	"pluse;":                           '\U00002A72',
	"plusmn;":                          '\U000000B1',
	"plussim;":                         '\U00002A26',
	"plustwo;":                         '\U00002A27',
	"pm;":                              '\U000000B1',
	"pointint;":                        '\U00002A15',
	"popf;":                            '\U0001D561',
	"pound;":                           '\U000000A3',
	"pr;":                              '\U0000227A',
	"prE;":                             '\U00002AB3',
	"prap;":                            '\U00002AB7',
	"prcue;":                           '\U0000227C',
	"pre;":                             '\U00002AAF',
	"prec;":                            '\U0000227A',
	"precapprox;":                      '\U00002AB7',
	"preccurlyeq;":                     '\U0000227C',
	"preceq;":                          '\U00002AAF',
	"precnapprox;":                     '\U00002AB9',
	"precneqq;":                        '\U00002AB5',
	"precnsim;":                        '\U000022E8',
	"precsim;":                         '\U0000227E',
	"prime;":                           '\U00002032',
	"primes;":                          '\U00002119',
	"prnE;":                            '\U00002AB5',
	"prnap;":                           '\U00002AB9',
	"prnsim;":                          '\U000022E8',
	"prod;":                            '\U0000220F',
	"profalar;":                        '\U0000232E',
	"profline;":                        '\U00002312',
	"profsurf;":                        '\U00002313',
	"prop;":                            '\U0000221D',
	"propto;":                          '\U0000221D',
	"prsim;":                           '\U0000227E',
	"prurel;":                          '\U000022B0',
	"pscr;":                            '\U0001D4C5',
	"psi;":                             '\U000003C8',
	"puncsp;":                          '\U00002008',
	"qfr;":                             '\U0001D52E',
	"qint;":                            '\U00002A0C',
	"qopf;":                            '\U0001D562',
	"qprime;":                          '\U00002057',
	"qscr;":                            '\U0001D4C6',
	"quaternions;":                     '\U0000210D',
	"quatint;":                         '\U00002A16',
	"quest;":                           '\U0000003F',
	"questeq;":                         '\U0000225F',
	"quot;":                            '\U00000022',
	"rAarr;":                           '\U000021DB',
	"rArr;":                            '\U000021D2',
	"rAtail;":                          '\U0000291C',
	"rBarr;":                           '\U0000290F',
	"rHar;":                            '\U00002964',
	"racute;":                          '\U00000155',
	"radic;":                           '\U0000221A',
	"raemptyv;":                        '\U000029B3',
	"rang;":                            '\U000027E9',
	"rangd;":                           '\U00002992',
	"range;":                           '\U000029A5',
	"rangle;":                          '\U000027E9',
	"raquo;":                           '\U000000BB',
	"rarr;":                            '\*********',
	"rarrap;":                          '\U00002975',
	"rarrb;":                           '\U000021E5',
	"rarrbfs;":                         '\U00002920',
	"rarrc;":                           '\U00002933',
	"rarrfs;":                          '\U0000291E',
	"rarrhk;":                          '\U000021AA',
	"rarrlp;":                          '\U000021AC',
	"rarrpl;":                          '\U00002945',
	"rarrsim;":                         '\U00002974',
	"rarrtl;":                          '\U000021A3',
	"rarrw;":                           '\U0000219D',
	"ratail;":                          '\U0000291A',
	"ratio;":                           '\U00002236',
	"rationals;":                       '\U0000211A',
	"rbarr;":                           '\U0000290D',
	"rbbrk;":                           '\U00002773',
	"rbrace;":                          '\U0000007D',
	"rbrack;":                          '\U0000005D',
	"rbrke;":                           '\U0000298C',
	"rbrksld;":                         '\U0000298E',
	"rbrkslu;":                         '\*********',
	"rcaron;":                          '\*********',
	"rcedil;":                          '\*********',
	"rceil;":                           '\*********',
	"rcub;":                            '\U0000007D',
	"rcy;":                             '\*********',
	"rdca;":                            '\*********',
	"rdldhar;":                         '\*********',
	"rdquo;":                           '\U0000201D',
	"rdquor;":                          '\U0000201D',
	"rdsh;":                            '\U000021B3',
	"real;":                            '\U0000211C',
	"realine;":                         '\U0000211B',
	"realpart;":                        '\U0000211C',
	"reals;":                           '\U0000211D',
	"rect;":                            '\U000025AD',
	"reg;":                             '\U000000AE',
	"rfisht;":                          '\U0000297D',
	"rfloor;":                          '\U0000230B',
	"rfr;":                             '\U0001D52F',
	"rhard;":                           '\U000021C1',
	"rharu;":                           '\U000021C0',
	"rharul;":                          '\U0000296C',
	"rho;":                             '\U000003C1',
	"rhov;":                            '\U000003F1',
	"rightarrow;":                      '\*********',
	"rightarrowtail;":                  '\U000021A3',
	"rightharpoondown;":                '\U000021C1',
	"rightharpoonup;":                  '\U000021C0',
	"rightleftarrows;":                 '\U000021C4',
	"rightleftharpoons;":               '\U000021CC',
	"rightrightarrows;":                '\U000021C9',
	"rightsquigarrow;":                 '\U0000219D',
	"rightthreetimes;":                 '\U000022CC',
	"ring;":                            '\U000002DA',
	"risingdotseq;":                    '\U00002253',
	"rlarr;":                           '\U000021C4',
	"rlhar;":                           '\U000021CC',
	"rlm;":                             '\U0000200F',
	"rmoust;":                          '\U000023B1',
	"rmoustache;":                      '\U000023B1',
	"rnmid;":                           '\U00002AEE',
	"roang;":                           '\U000027ED',
	"roarr;":                           '\U000021FE',
	"robrk;":                           '\U000027E7',
	"ropar;":                           '\U00002986',
	"ropf;":                            '\U0001D563',
	"roplus;":                          '\U00002A2E',
	"rotimes;":                         '\U00002A35',
	"rpar;":                            '\U00000029',
	"rpargt;":                          '\U00002994',
	"rppolint;":                        '\U00002A12',
	"rrarr;":                           '\U000021C9',
	"rsaquo;":                          '\U0000203A',
	"rscr;":                            '\U0001D4C7',
	"rsh;":                             '\U000021B1',
	"rsqb;":                            '\U0000005D',
	"rsquo;":                           '\U00002019',
	"rsquor;":                          '\U00002019',
	"rthree;":                          '\U000022CC',
	"rtimes;":                          '\U000022CA',
	"rtri;":                            '\U000025B9',
	"rtrie;":                           '\U000022B5',
	"rtrif;":                           '\U000025B8',
	"rtriltri;":                        '\U000029CE',
	"ruluhar;":                         '\U00002968',
	"rx;":                              '\U0000211E',
	"sacute;":                          '\U0000015B',
	"sbquo;":                           '\U0000201A',
	"sc;":                              '\U0000227B',
	"scE;":                             '\U00002AB4',
	"scap;":                            '\U00002AB8',
	"scaron;":                          '\U00000161',
	"sccue;":                           '\U0000227D',
	"sce;":                             '\U00002AB0',
	"scedil;":                          '\U0000015F',
	"scirc;":                           '\U0000015D',
	"scnE;":                            '\U00002AB6',
	"scnap;":                           '\U00002ABA',
	"scnsim;":                          '\U000022E9',
	"scpolint;":                        '\U00002A13',
	"scsim;":                           '\U0000227F',
	"scy;":                             '\U00000441',
	"sdot;":                            '\U000022C5',
	"sdotb;":                           '\U000022A1',
	"sdote;":                           '\U00002A66',
	"seArr;":                           '\U000021D8',
	"searhk;":                          '\U00002925',
	"searr;":                           '\U00002198',
	"searrow;":                         '\U00002198',
	"sect;":                            '\U000000A7',
	"semi;":                            '\U0000003B',
	"seswar;":                          '\U00002929',
	"setminus;":                        '\U00002216',
	"setmn;":                           '\U00002216',
	"sext;":                            '\U00002736',
	"sfr;":                             '\U0001D530',
	"sfrown;":                          '\U00002322',
	"sharp;":                           '\U0000266F',
	"shchcy;":                          '\U00000449',
	"shcy;":                            '\U00000448',
	"shortmid;":                        '\U00002223',
	"shortparallel;":                   '\U00002225',
	"shy;":                             '\U000000AD',
	"sigma;":                           '\U000003C3',
	"sigmaf;":                          '\U000003C2',
	"sigmav;":                          '\U000003C2',
	"sim;":                             '\U0000223C',
	"simdot;":                          '\U00002A6A',
	"sime;":                            '\U00002243',
	"simeq;":                           '\U00002243',
	"simg;":                            '\U00002A9E',
	"simgE;":                           '\U00002AA0',
	"siml;":                            '\U00002A9D',
	"simlE;":                           '\U00002A9F',
	"simne;":                           '\U00002246',
	"simplus;":                         '\U00002A24',
	"simrarr;":                         '\U00002972',
	"slarr;":                           '\U00002190',
	"smallsetminus;":                   '\U00002216',
	"smashp;":                          '\U00002A33',
	"smeparsl;":                        '\U000029E4',
	"smid;":                            '\U00002223',
	"smile;":                           '\U00002323',
	"smt;":                             '\U00002AAA',
	"smte;":                            '\U00002AAC',
	"softcy;":                          '\U0000044C',
	"sol;":                             '\U0000002F',
	"solb;":                            '\U000029C4',
	"solbar;":                          '\U0000233F',
	"sopf;":                            '\U0001D564',
	"spades;":                          '\U00002660',
	"spadesuit;":                       '\U00002660',
	"spar;":                            '\U00002225',
	"sqcap;":                           '\U00002293',
	"sqcup;":                           '\U00002294',
	"sqsub;":                           '\U0000228F',
	"sqsube;":                          '\U00002291',
	"sqsubset;":                        '\U0000228F',
	"sqsubseteq;":                      '\U00002291',
	"sqsup;":                           '\U00002290',
	"sqsupe;":                          '\U00002292',
	"sqsupset;":                        '\U00002290',
	"sqsupseteq;":                      '\U00002292',
	"squ;":                             '\U000025A1',
	"square;":                          '\U000025A1',
	"squarf;":                          '\U000025AA',
	"squf;":                            '\U000025AA',
	"srarr;":                           '\*********',
	"sscr;":                            '\U0001D4C8',
	"ssetmn;":                          '\U00002216',
	"ssmile;":                          '\U00002323',
	"sstarf;":                          '\U000022C6',
	"star;":                            '\U00002606',
	"starf;":                           '\U00002605',
	"straightepsilon;":                 '\U000003F5',
	"straightphi;":                     '\U000003D5',
	"strns;":                           '\U000000AF',
	"sub;":                             '\U00002282',
	"subE;":                            '\U00002AC5',
	"subdot;":                          '\U00002ABD',
	"sube;":                            '\U00002286',
	"subedot;":                         '\U00002AC3',
	"submult;":                         '\U00002AC1',
	"subnE;":                           '\U00002ACB',
	"subne;":                           '\U0000228A',
	"subplus;":                         '\U00002ABF',
	"subrarr;":                         '\U00002979',
	"subset;":                          '\U00002282',
	"subseteq;":                        '\U00002286',
	"subseteqq;":                       '\U00002AC5',
	"subsetneq;":                       '\U0000228A',
	"subsetneqq;":                      '\U00002ACB',
	"subsim;":                          '\U00002AC7',
	"subsub;":                          '\U00002AD5',
	"subsup;":                          '\U00002AD3',
	"succ;":                            '\U0000227B',
	"succapprox;":                      '\U00002AB8',
	"succcurlyeq;":                     '\U0000227D',
	"succeq;":                          '\U00002AB0',
	"succnapprox;":                     '\U00002ABA',
	"succneqq;":                        '\U00002AB6',
	"succnsim;":                        '\U000022E9',
	"succsim;":                         '\U0000227F',
	"sum;":                             '\U00002211',
	"sung;":                            '\U0000266A',
	"sup;":                             '\U00002283',
	"sup1;":                            '\U000000B9',
	"sup2;":                            '\U000000B2',
	"sup3;":                            '\U000000B3',
	"supE;":                            '\U00002AC6',
	"supdot;":                          '\U00002ABE',
	"supdsub;":                         '\U00002AD8',
	"supe;":                            '\U00002287',
	"supedot;":                         '\U00002AC4',
	"suphsol;":                         '\U000027C9',
	"suphsub;":                         '\U00002AD7',
	"suplarr;":                         '\U0000297B',
	"supmult;":                         '\U00002AC2',
	"supnE;":                           '\U00002ACC',
	"supne;":                           '\U0000228B',
	"supplus;":                         '\U00002AC0',
	"supset;":                          '\U00002283',
	"supseteq;":                        '\U00002287',
	"supseteqq;":                       '\U00002AC6',
	"supsetneq;":                       '\U0000228B',
	"supsetneqq;":                      '\U00002ACC',
	"supsim;":                          '\U00002AC8',
	"supsub;":                          '\U00002AD4',
	"supsup;":                          '\U00002AD6',
	"swArr;":                           '\U000021D9',
	"swarhk;":                          '\U00002926',
	"swarr;":                           '\U00002199',
	"swarrow;":                         '\U00002199',
	"swnwar;":                          '\U0000292A',
	"szlig;":                           '\U000000DF',
	"target;":                          '\U00002316',
	"tau;":                             '\U000003C4',
	"tbrk;":                            '\U000023B4',
	"tcaron;":                          '\U00000165',
	"tcedil;":                          '\U00000163',
	"tcy;":                             '\U00000442',
	"tdot;":                            '\U000020DB',
	"telrec;":                          '\U00002315',
	"tfr;":                             '\U0001D531',
	"there4;":                          '\U00002234',
	"therefore;":                       '\U00002234',
	"theta;":                           '\U000003B8',
	"thetasym;":                        '\U000003D1',
	"thetav;":                          '\U000003D1',
	"thickapprox;":                     '\U00002248',
	"thicksim;":                        '\U0000223C',
	"thinsp;":                          '\U00002009',
	"thkap;":                           '\U00002248',
	"thksim;":                          '\U0000223C',
	"thorn;":                           '\U000000FE',
	"tilde;":                           '\U000002DC',
	"times;":                           '\U000000D7',
	"timesb;":                          '\U000022A0',
	"timesbar;":                        '\U00002A31',
	"timesd;":                          '\U00002A30',
	"tint;":                            '\U0000222D',
	"toea;":                            '\U00002928',
	"top;":                             '\U000022A4',
	"topbot;":                          '\U00002336',
	"topcir;":                          '\U00002AF1',
	"topf;":                            '\U0001D565',
	"topfork;":                         '\U00002ADA',
	"tosa;":                            '\U00002929',
	"tprime;":                          '\U00002034',
	"trade;":                           '\U00002122',
	"triangle;":                        '\U000025B5',
	"triangledown;":                    '\U000025BF',
	"triangleleft;":                    '\U000025C3',
	"trianglelefteq;":                  '\U000022B4',
	"triangleq;":                       '\U0000225C',
	"triangleright;":                   '\U000025B9',
	"trianglerighteq;":                 '\U000022B5',
	"tridot;":                          '\U000025EC',
	"trie;":                            '\U0000225C',
	"triminus;":                        '\U00002A3A',
	"triplus;":                         '\U00002A39',
	"trisb;":                           '\U000029CD',
	"tritime;":                         '\U00002A3B',
	"trpezium;":                        '\U000023E2',
	"tscr;":                            '\U0001D4C9',
	"tscy;":                            '\*********',
	"tshcy;":                           '\U0000045B',
	"tstrok;":                          '\*********',
	"twixt;":                           '\U0000226C',
	"twoheadleftarrow;":                '\U0000219E',
	"twoheadrightarrow;":               '\U000021A0',
	"uArr;":                            '\U000021D1',
	"uHar;":                            '\*********',
	"uacute;":                          '\U000000FA',
	"uarr;":                            '\*********',
	"ubrcy;":                           '\U0000045E',
	"ubreve;":                          '\U0000016D',
	"ucirc;":                           '\U000000FB',
	"ucy;":                             '\*********',
	"udarr;":                           '\U000021C5',
	"udblac;":                          '\*********',
	"udhar;":                           '\U0000296E',
	"ufisht;":                          '\U0000297E',
	"ufr;":                             '\U0001D532',
	"ugrave;":                          '\U000000F9',
	"uharl;":                           '\U000021BF',
	"uharr;":                           '\U000021BE',
	"uhblk;":                           '\*********',
	"ulcorn;":                          '\U0000231C',
	"ulcorner;":                        '\U0000231C',
	"ulcrop;":                          '\U0000230F',
	"ultri;":                           '\U000025F8',
	"umacr;":                           '\U0000016B',
	"uml;":                             '\U000000A8',
	"uogon;":                           '\U00000173',
	"uopf;":                            '\U0001D566',
	"uparrow;":                         '\*********',
	"updownarrow;":                     '\U00002195',
	"upharpoonleft;":                   '\U000021BF',
	"upharpoonright;":                  '\U000021BE',
	"uplus;":                           '\U0000228E',
	"upsi;":                            '\U000003C5',
	"upsih;":                           '\U000003D2',
	"upsilon;":                         '\U000003C5',
	"upuparrows;":                      '\U000021C8',
	"urcorn;":                          '\U0000231D',
	"urcorner;":                        '\U0000231D',
	"urcrop;":                          '\U0000230E',
	"uring;":                           '\U0000016F',
	"urtri;":                           '\U000025F9',
	"uscr;":                            '\U0001D4CA',
	"utdot;":                           '\U000022F0',
	"utilde;":                          '\U00000169',
	"utri;":                            '\U000025B5',
	"utrif;":                           '\U000025B4',
	"uuarr;":                           '\U000021C8',
	"uuml;":                            '\U000000FC',
	"uwangle;":                         '\U000029A7',
	"vArr;":                            '\U000021D5',
	"vBar;":                            '\U00002AE8',
	"vBarv;":                           '\U00002AE9',
	"vDash;":                           '\U000022A8',
	"vangrt;":                          '\U0000299C',
	"varepsilon;":                      '\U000003F5',
	"varkappa;":                        '\U000003F0',
	"varnothing;":                      '\U00002205',
	"varphi;":                          '\U000003D5',
	"varpi;":                           '\U000003D6',
	"varpropto;":                       '\U0000221D',
	"varr;":                            '\U00002195',
	"varrho;":                          '\U000003F1',
	"varsigma;":                        '\U000003C2',
	"vartheta;":                        '\U000003D1',
	"vartriangleleft;":                 '\U000022B2',
	"vartriangleright;":                '\U000022B3',
	"vcy;":                             '\U00000432',
	"vdash;":                           '\U000022A2',
	"vee;":                             '\U00002228',
	"veebar;":                          '\U000022BB',
	"veeeq;":                           '\U0000225A',
	"vellip;":                          '\U000022EE',
	"verbar;":                          '\U0000007C',
	"vert;":                            '\U0000007C',
	"vfr;":                             '\U0001D533',
	"vltri;":                           '\U000022B2',
	"vopf;":                            '\U0001D567',
	"vprop;":                           '\U0000221D',
	"vrtri;":                           '\U000022B3',
	"vscr;":                            '\U0001D4CB',
	"vzigzag;":                         '\U0000299A',
	"wcirc;":                           '\U00000175',
	"wedbar;":                          '\U00002A5F',
	"wedge;":                           '\U00002227',
	"wedgeq;":                          '\U00002259',
	"weierp;":                          '\U00002118',
	"wfr;":                             '\U0001D534',
	"wopf;":                            '\U0001D568',
	"wp;":                              '\U00002118',
	"wr;":                              '\U00002240',
	"wreath;":                          '\U00002240',
	"wscr;":                            '\U0001D4CC',
	"xcap;":                            '\U000022C2',
	"xcirc;":                           '\U000025EF',
	"xcup;":                            '\U000022C3',
	"xdtri;":                           '\U000025BD',
	"xfr;":                             '\U0001D535',
	"xhArr;":                           '\U000027FA',
	"xharr;":                           '\U000027F7',
	"xi;":                              '\U000003BE',
	"xlArr;":                           '\U000027F8',
	"xlarr;":                           '\U000027F5',
	"xmap;":                            '\U000027FC',
	"xnis;":                            '\U000022FB',
	"xodot;":                           '\U00002A00',
	"xopf;":                            '\U0001D569',
	"xoplus;":                          '\U00002A01',
	"xotime;":                          '\U00002A02',
	"xrArr;":                           '\U000027F9',
	"xrarr;":                           '\U000027F6',
	"xscr;":                            '\U0001D4CD',
	"xsqcup;":                          '\U00002A06',
	"xuplus;":                          '\U00002A04',
	"xutri;":                           '\U000025B3',
	"xvee;":                            '\U000022C1',
	"xwedge;":                          '\U000022C0',
	"yacute;":                          '\U000000FD',
	"yacy;":                            '\U0000044F',
	"ycirc;":                           '\U00000177',
	"ycy;":                             '\U0000044B',
	"yen;":                             '\U000000A5',
	"yfr;":                             '\U0001D536',
	"yicy;":                            '\U00000457',
	"yopf;":                            '\U0001D56A',
	"yscr;":                            '\U0001D4CE',
	"yucy;":                            '\U0000044E',
	"yuml;":                            '\U000000FF',
	"zacute;":                          '\U0000017A',
	"zcaron;":                          '\U0000017E',
	"zcy;":                             '\U00000437',
	"zdot;":                            '\U0000017C',
	"zeetrf;":                          '\U00002128',
	"zeta;":                            '\U000003B6',
	"zfr;":                             '\U0001D537',
	"zhcy;":                            '\U00000436',
	"zigrarr;":                         '\U000021DD',
	"zopf;":                            '\U0001D56B',
	"zscr;":                            '\U0001D4CF',
	"zwj;":                             '\U0000200D',
	"zwnj;":                            '\U0000200C',
	"AElig":                            '\U000000C6',
	"AMP":                              '\*********',
	"Aacute":                           '\U000000C1',
	"Acirc":                            '\U000000C2',
	"Agrave":                           '\U000000C0',
	"Aring":                            '\U000000C5',
	"Atilde":                           '\U000000C3',
	"Auml":                             '\U000000C4',
	"COPY":                             '\U000000A9',
	"Ccedil":                           '\U000000C7',
	"ETH":                              '\U000000D0',
	"Eacute":                           '\U000000C9',
	"Ecirc":                            '\U000000CA',
	"Egrave":                           '\U000000C8',
	"Euml":                             '\U000000CB',
	"GT":                               '\U0000003E',
	"Iacute":                           '\U000000CD',
	"Icirc":                            '\U000000CE',
	"Igrave":                           '\U000000CC',
	"Iuml":                             '\U000000CF',
	"LT":                               '\U0000003C',
	"Ntilde":                           '\U000000D1',
	"Oacute":                           '\U000000D3',
	"Ocirc":                            '\U000000D4',
	"Ograve":                           '\U000000D2',
	"Oslash":                           '\U000000D8',
	"Otilde":                           '\U000000D5',
	"Ouml":                             '\U000000D6',
	"QUOT":                             '\U00000022',
	"REG":                              '\U000000AE',
	"THORN":                            '\U000000DE',
	"Uacute":                           '\U000000DA',
	"Ucirc":                            '\U000000DB',
	"Ugrave":                           '\U000000D9',
	"Uuml":                             '\U000000DC',
	"Yacute":                           '\U000000DD',
	"aacute":                           '\U000000E1',
	"acirc":                            '\U000000E2',
	"acute":                            '\U000000B4',
	"aelig":                            '\U000000E6',
	"agrave":                           '\U000000E0',
	"amp":                              '\*********',
	"aring":                            '\U000000E5',
	"atilde":                           '\U000000E3',
	"auml":                             '\U000000E4',
	"brvbar":                           '\U000000A6',
	"ccedil":                           '\U000000E7',
	"cedil":                            '\U000000B8',
	"cent":                             '\U000000A2',
	"copy":                             '\U000000A9',
	"curren":                           '\U000000A4',
	"deg":                              '\U000000B0',
	"divide":                           '\U000000F7',
	"eacute":                           '\U000000E9',
	"ecirc":                            '\U000000EA',
	"egrave":                           '\U000000E8',
	"eth":                              '\U000000F0',
	"euml":                             '\U000000EB',
	"frac12":                           '\U000000BD',
	"frac14":                           '\U000000BC',
	"frac34":                           '\U000000BE',
	"gt":                               '\U0000003E',
	"iacute":                           '\U000000ED',
	"icirc":                            '\U000000EE',
	"iexcl":                            '\U000000A1',
	"igrave":                           '\U000000EC',
	"iquest":                           '\U000000BF',
	"iuml":                             '\U000000EF',
	"laquo":                            '\U000000AB',
	"lt":                               '\U0000003C',
	"macr":                             '\U000000AF',
	"micro":                            '\U000000B5',
	"middot":                           '\U000000B7',
	"nbsp":                             '\U000000A0',
	"not":                              '\U000000AC',
	"ntilde":                           '\U000000F1',
	"oacute":                           '\U000000F3',
	"ocirc":                            '\U000000F4',
	"ograve":                           '\U000000F2',
	"ordf":                             '\U000000AA',
	"ordm":                             '\U000000BA',
	"oslash":                           '\U000000F8',
	"otilde":                           '\U000000F5',
	"ouml":                             '\U000000F6',
	"para":                             '\U000000B6',
	"plusmn":                           '\U000000B1',
	"pound":                            '\U000000A3',
	"quot":                             '\U00000022',
	"raquo":                            '\U000000BB',
	"reg":                              '\U000000AE',
	"sect":                             '\U000000A7',
	"shy":                              '\U000000AD',
	"sup1":                             '\U000000B9',
	"sup2":                             '\U000000B2',
	"sup3":                             '\U000000B3',
	"szlig":                            '\U000000DF',
	"thorn":                            '\U000000FE',
	"times":                            '\U000000D7',
	"uacute":                           '\U000000FA',
	"ucirc":                            '\U000000FB',
	"ugrave":                           '\U000000F9',
	"uml":                              '\U000000A8',
	"uuml":                             '\U000000FC',
	"yacute":                           '\U000000FD',
	"yen":                              '\U000000A5',
	"yuml":                             '\U000000FF',
}

// HTML entities that are two unicode codepoints.
var entity2 = map[string][2]rune{
	// TODO(nigeltao): Handle replacements that are wider than their names.
	// "nLt;":                     {'\u226A', '\u20D2'},
	// "nGt;":                     {'\u226B', '\u20D2'},
	"NotEqualTilde;":           {'\u2242', '\u0338'},
	"NotGreaterFullEqual;":     {'\u2267', '\u0338'},
	"NotGreaterGreater;":       {'\u226B', '\u0338'},
	"NotGreaterSlantEqual;":    {'\u2A7E', '\u0338'},
	"NotHumpDownHump;":         {'\u224E', '\u0338'},
	"NotHumpEqual;":            {'\u224F', '\u0338'},
	"NotLeftTriangleBar;":      {'\u29CF', '\u0338'},
	"NotLessLess;":             {'\u226A', '\u0338'},
	"NotLessSlantEqual;":       {'\u2A7D', '\u0338'},
	"NotNestedGreaterGreater;": {'\u2AA2', '\u0338'},
	"NotNestedLessLess;":       {'\u2AA1', '\u0338'},
	"NotPrecedesEqual;":        {'\u2AAF', '\u0338'},
	"NotRightTriangleBar;":     {'\u29D0', '\u0338'},
	"NotSquareSubset;":         {'\u228F', '\u0338'},
	"NotSquareSuperset;":       {'\u2290', '\u0338'},
	"NotSubset;":               {'\u2282', '\u20D2'},
	"NotSucceedsEqual;":        {'\u2AB0', '\u0338'},
	"NotSucceedsTilde;":        {'\u227F', '\u0338'},
	"NotSuperset;":             {'\u2283', '\u20D2'},
	"ThickSpace;":              {'\u205F', '\u200A'},
	"acE;":                     {'\u223E', '\u0333'},
	"bne;":                     {'\u003D', '\u20E5'},
	"bnequiv;":                 {'\u2261', '\u20E5'},
	"caps;":                    {'\u2229', '\uFE00'},
	"cups;":                    {'\u222A', '\uFE00'},
	"fjlig;":                   {'\u0066', '\u006A'},
	"gesl;":                    {'\u22DB', '\uFE00'},
	"gvertneqq;":               {'\u2269', '\uFE00'},
	"gvnE;":                    {'\u2269', '\uFE00'},
	"lates;":                   {'\u2AAD', '\uFE00'},
	"lesg;":                    {'\u22DA', '\uFE00'},
	"lvertneqq;":               {'\u2268', '\uFE00'},
	"lvnE;":                    {'\u2268', '\uFE00'},
	"nGg;":                     {'\u22D9', '\u0338'},
	"nGtv;":                    {'\u226B', '\u0338'},
	"nLl;":                     {'\u22D8', '\u0338'},
	"nLtv;":                    {'\u226A', '\u0338'},
	"nang;":                    {'\u2220', '\u20D2'},
	"napE;":                    {'\u2A70', '\u0338'},
	"napid;":                   {'\u224B', '\u0338'},
	"nbump;":                   {'\u224E', '\u0338'},
	"nbumpe;":                  {'\u224F', '\u0338'},
	"ncongdot;":                {'\u2A6D', '\u0338'},
	"nedot;":                   {'\u2250', '\u0338'},
	"nesim;":                   {'\u2242', '\u0338'},
	"ngE;":                     {'\u2267', '\u0338'},
	"ngeqq;":                   {'\u2267', '\u0338'},
	"ngeqslant;":               {'\u2A7E', '\u0338'},
	"nges;":                    {'\u2A7E', '\u0338'},
	"nlE;":                     {'\u2266', '\u0338'},
	"nleqq;":                   {'\u2266', '\u0338'},
	"nleqslant;":               {'\u2A7D', '\u0338'},
	"nles;":                    {'\u2A7D', '\u0338'},
	"notinE;":                  {'\u22F9', '\u0338'},
	"notindot;":                {'\u22F5', '\u0338'},
	"nparsl;":                  {'\u2AFD', '\u20E5'},
	"npart;":                   {'\u2202', '\u0338'},
	"npre;":                    {'\u2AAF', '\u0338'},
	"npreceq;":                 {'\u2AAF', '\u0338'},
	"nrarrc;":                  {'\u2933', '\u0338'},
	"nrarrw;":                  {'\u219D', '\u0338'},
	"nsce;":                    {'\u2AB0', '\u0338'},
	"nsubE;":                   {'\u2AC5', '\u0338'},
	"nsubset;":                 {'\u2282', '\u20D2'},
	"nsubseteqq;":              {'\u2AC5', '\u0338'},
	"nsucceq;":                 {'\u2AB0', '\u0338'},
	"nsupE;":                   {'\u2AC6', '\u0338'},
	"nsupset;":                 {'\u2283', '\u20D2'},
	"nsupseteqq;":              {'\u2AC6', '\u0338'},
	"nvap;":                    {'\u224D', '\u20D2'},
	"nvge;":                    {'\u2265', '\u20D2'},
	"nvgt;":                    {'\u003E', '\u20D2'},
	"nvle;":                    {'\u2264', '\u20D2'},
	"nvlt;":                    {'\u003C', '\u20D2'},
	"nvltrie;":                 {'\u22B4', '\u20D2'},
	"nvrtrie;":                 {'\u22B5', '\u20D2'},
	"nvsim;":                   {'\u223C', '\u20D2'},
	"race;":                    {'\u223D', '\u0331'},
	"smtes;":                   {'\u2AAC', '\uFE00'},
	"sqcaps;":                  {'\u2293', '\uFE00'},
	"sqcups;":                  {'\u2294', '\uFE00'},
	"varsubsetneq;":            {'\u228A', '\uFE00'},
	"varsubsetneqq;":           {'\u2ACB', '\uFE00'},
	"varsupsetneq;":            {'\u228B', '\uFE00'},
	"varsupsetneqq;":           {'\u2ACC', '\uFE00'},
	"vnsub;":                   {'\u2282', '\u20D2'},
	"vnsup;":                   {'\u2283', '\u20D2'},
	"vsubnE;":                  {'\u2ACB', '\uFE00'},
	"vsubne;":                  {'\u228A', '\uFE00'},
	"vsupnE;":                  {'\u2ACC', '\uFE00'},
	"vsupne;":                  {'\u228B', '\uFE00'},
}
