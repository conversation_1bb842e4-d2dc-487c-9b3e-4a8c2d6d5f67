package ingress

import (
	"aquaman/src/backend/client"
	"aquaman/src/backend/models"
	"aquaman/src/backend/resources/common"
	"aquaman/src/backend/resources/pod"
	"aquaman/src/backend/util/crypto"
	"aquaman/src/backend/util/logs"
	"fmt"
	jsoniter "github.com/json-iterator/go"
	v1 "k8s.io/api/core/v1"
	k8sv1beta1 "k8s.io/api/extensions/v1beta1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
	"os/exec"
)

const (
	// ProbeDomain 每个ingress专区都会创建一个此域名的ingress.
	// 由运维人员负责保证此ingress后端的svc(see ProbeServiceName)已经创建.
	ProbeDomain = "webzone-test.yy.com"

	ProbeIngressNamePrefix = "generated-probe-"
	// ProbeServiceName 运维人员创建的svc
	ProbeServiceName = "kuorong-check-nginx"
	ProbeServicePort = 80
	ProbeNamespace   = "devops"
	ProbeSecretName  = "webzonesha2crt"
)

const (
	LabelAppIngressReloader   = "ingress-nginx-reloader"
	LabelAppIngressController = "ingress-nginx"
)

// LocalProbeL7Node
// 在本机对给定七层节点进行探测
// 假设此七层已经配置了探测域名. (ProbeDomain)
func LocalProbeL7Node(ip string) error {
	// 80 端口
	domain := ProbeDomain
	var cmd string
	cmd = getHttpProbeCmd(ip, domain)

	httpProbeResp := exec.Command("/bin/bash", "-c", cmd)

	output, err := httpProbeResp.Output()
	if err != nil {
		return err
	}
	if string(output) != "200" {
		return fmt.Errorf("probe ingress server http failed, output: %s", string(output))
	}

	// 443 端口
	cmd = getHttpsProbeCmd(ip, domain)
	httpsProbeResp := exec.Command("/bin/bash", "-c", cmd)
	output, err = httpsProbeResp.Output()
	if err != nil {
		return err
	}
	if string(output) != "200" {
		return fmt.Errorf("probe ingress server http failed, output: %s", string(output))
	}

	return nil
}

func getHttpProbeCmd(ip, probeDomain string) string {
	url := fmt.Sprintf("http://%s/", probeDomain)
	resolveArgs := fmt.Sprintf(" --resolve %s:80:%s", probeDomain, ip)
	return "curl " + resolveArgs + " -I -m 10 -o /dev/null -s -w %{http_code} " + url
}

func getHttpsProbeCmd(ip, domain string) string {
	url := fmt.Sprintf("https://%s/", domain)
	resolveArgs := fmt.Sprintf(" --resolve %s:443:%s", domain, ip)
	return "curl " + resolveArgs + " -I -m 10 -o /dev/null -s -w %{http_code} " + url
}

// RemoteProbeL7Node
// 通过命令行通道在指定的机器(probeIp)上对给定七层节点进行探测
// 假设此七层已经配置了探测域名. (ProbeDomain)
func RemoteProbeL7Node(l7Ip, probeIp string) error {

	// 暂时不做
	return nil
}

// CheckIngressRunning
// 检查节点是否正常运行role角色的ingress和reloader
func CheckIngressRunning(nodeName string, role, clusterName string) error {

	cluster, err := client.Client(clusterName)
	if err != nil {
		return err
	}

	podList, err := pod.GetKPodByNodeName(cluster, nodeName)
	if err != nil {
		return err
	}

	var hasReloader, hasController bool
	for _, po := range podList {
		labels := po.Labels
		if labels["app"] == LabelAppIngressReloader && labels["ingress-role"] == role {
			if e := checkPodReady(&po); e != nil {
				return fmt.Errorf("reloader not ready. err: %v", e)
			}
			hasReloader = true
		}
		if labels["app"] == LabelAppIngressController && labels["ingress-role"] == role {
			if e := checkPodReady(&po); e != nil {
				return fmt.Errorf("contoller not ready. err: %v", e)
			}
			hasController = true
		}
	}

	if !hasReloader {
		return fmt.Errorf("节点并未启动role[%s]的reloader", role)
	}
	if !hasController {
		return fmt.Errorf("节点并未启动role[%s]的ingress controller", role)
	}

	return nil
}

// checkPodReady
// success only if pod Phase == Running and Conditions ready are true
func checkPodReady(po *v1.Pod) error {
	if po.Status.Phase != v1.PodRunning {
		return fmt.Errorf("pod not running")
	}
	var podReady, containerReady bool
	if len(po.Status.Conditions) > 0 {
		for i := range po.Status.Conditions {
			if po.Status.Conditions[i].Type == v1.PodReady {
				if po.Status.Conditions[i].Status == v1.ConditionTrue {
					podReady = true
				} else {
					podReady = false
					break
				}
			}
			if po.Status.Conditions[i].Type == v1.ContainersReady {
				if po.Status.Conditions[i].Status == v1.ConditionTrue {
					containerReady = true
				} else {
					containerReady = false
					break
				}
			}
		}
	}
	if !podReady {
		return fmt.Errorf("pod not ready")
	}
	if !containerReady {
		return fmt.Errorf("container not ready")
	}
	return nil
}

// GetIngressConfigBusiness
// 获取ingress专区对应的业务模块
func GetIngressConfigBusiness() ([]models.DefaultConfItem, error) {

	key := "INGRESS_BUSS"
	confList, err := models.GetSysConfigByKey(key)
	if err != nil {
		return nil, err
	}
	if len(confList) == 0 {
		return nil, fmt.Errorf("config not found")
	}
	conf := confList[0]
	value := conf.Value
	var rawList []models.DefaultConfItem
	err = jsoniter.Unmarshal([]byte(value), &rawList)
	if err != nil {
		return nil, err
	}
	for i := range rawList {
		businessName := rawList[i].Value
		// ingress专区类型只有有限的那几个, 暂时先这样for loop查询
		buss, err := models.GetBusinessByFullName(businessName)
		if err != nil {
			logs.Info("GetBusinessByFullName by %s failed", businessName)
			continue
		}
		// replace value
		rawList[i].Value = buss.BusinessId
	}

	return rawList, nil
}

func SureProbeDomainIngress(clusterName string, zone string) error {
	mnr, err := client.Manager(clusterName)
	if err != nil {
		return err
	}
	ingressName := ProbeIngressNamePrefix + zone
	get, err := mnr.Client.ExtensionsV1beta1().Ingresses(ProbeNamespace).Get(ingressName, metav1.GetOptions{})
	if err == nil && get != nil {
		// 已存在无需创建
		return nil
	}

	domainName := ProbeDomain
	ngressObj := &k8sv1beta1.Ingress{
		TypeMeta: *common.IngressTypeMeta,
		ObjectMeta: metav1.ObjectMeta{
			Name: ingressName,
			Annotations: map[string]string{
				"nginx.ingress.kubernetes.io/ssl-redirect":        "false",
				"kubernetes.io/ingress.class":                     zone,
				"nginx.ingress.kubernetes.io/proxy-next-upstream": "http_502 http_504 error timeout invalid_header",
				"nginx.ingress.kubernetes.io/proxy-body-size":     "500m",
			},
			Namespace: ProbeNamespace,
			Labels: map[string]string{
				models.INGRESS_LABEL_DOMAIN: crypto.Sha1Encryption(domainName),
				models.INGRESS_LABEL_PATH:   crypto.Sha1Encryption("/"),
			},
			ClusterName: mnr.Cluster.ClusterName,
		},
		Spec: k8sv1beta1.IngressSpec{
			Rules: []k8sv1beta1.IngressRule{
				{
					Host: domainName,
					IngressRuleValue: k8sv1beta1.IngressRuleValue{
						HTTP: &k8sv1beta1.HTTPIngressRuleValue{
							Paths: []k8sv1beta1.HTTPIngressPath{
								{
									Path: "/",
									Backend: k8sv1beta1.IngressBackend{
										ServiceName: ProbeServiceName,
										ServicePort: intstr.FromInt(ProbeServicePort),
									},
								},
							},
						},
					},
				},
			},
			TLS: []k8sv1beta1.IngressTLS{{
				Hosts:      []string{domainName},
				SecretName: "ingress-cert/" + ProbeSecretName,
			}},
		},
	}
	_, err = CreateOrUpdateIngress(mnr.Client, ngressObj)
	return err
}
