// Copyright 2021 Chaos Mesh Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

package networkchaos

import (
	"aquaman/src/backend/models"
	"aquaman/src/backend/resources/common"
	"aquaman/src/backend/util/logs"
	metaV1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"strconv"
)

var chaosType = metaV1.TypeMeta{
	APIVersion: "chaos-mesh.org/v1alpha1",
	Kind:       "NetworkChaos",
}

func MakeNetworkChaos(deployment *models.Deployment) (*NetworkChaos, error) {
	var networkChaos = new(NetworkChaos)
	networkChaos.TypeMeta = chaosType
	networkChaos.Annotations = make(map[string]string)
	if deployment.ChaosConfig.ChaosEnable == true {
		networkChaos.Annotations["experiment.chaos-mesh.org/pause"] = "false"
	} else {
		networkChaos.Annotations["experiment.chaos-mesh.org/pause"] = "true"
	}
	if deployment.ChaosConfig.Direction != "" {
		networkChaos.Spec.Direction = Direction(deployment.ChaosConfig.Direction)
	}
	networkChaos.Name = deployment.DeploymentName + "-" + deployment.ChaosConfig.ChaosType
	networkChaos.Namespace = deployment.ProductName
	switch deployment.ChaosConfig.ChaosType {
	case "delay":
		networkChaos.Spec.Action = DelayAction
		if deployment.ChaosConfig.Latency != "" {
			networkChaos.Spec.TcParameter = TcParameter{
				Delay: &DelaySpec{
					Correlation: "0",
					Jitter:      "1ms",
					Latency:     deployment.ChaosConfig.Latency,
				},
			}
		} else {
			networkChaos.Spec.TcParameter = TcParameter{
				Delay: &DelaySpec{
					Correlation: "0",
					Jitter:      "1ms",
					Latency:     "100ms",
				},
			}
		}
	case "loss":
		networkChaos.Spec.Action = LossAction
		if deployment.ChaosConfig.Loss != 0 {
			networkChaos.Spec.TcParameter = TcParameter{
				Loss: &LossSpec{
					Correlation: "0",
					Loss:        strconv.Itoa(deployment.ChaosConfig.Loss),
				},
			}
		} else {
			networkChaos.Spec.TcParameter = TcParameter{
				Loss: &LossSpec{
					Correlation: "0",
					Loss:        "100",
				},
			}
		}
	}

	if deployment.ChaosConfig.ToLabelSelectors == nil {
		labelSelector := map[string]string{
			"deployment-name": deployment.DeploymentName,
		}
		networkChaos.Spec.PodSelector = PodSelector{
			Selector: PodSelectorSpec{
				GenericSelectorSpec: GenericSelectorSpec{
					Namespaces:     []string{deployment.ProductName},
					LabelSelectors: labelSelector,
				},
			},
			Mode: AllMode,
		}
	}
	if deployment.ChaosConfig.Duration != "" {
		networkChaos.Spec.Duration = &deployment.ChaosConfig.Duration
	}

	if deployment.ChaosConfig.ExternalTargets != nil {
		networkChaos.Spec.ExternalTargets = deployment.ChaosConfig.ExternalTargets
	}
	logs.Info("networkChaos: %v", networkChaos)
	return networkChaos, nil
}

func CreateChaos(c *kubernetes.Clientset, namespace string, networkchaos *NetworkChaos) error {
	return c.RESTClient().Post().Resource("NetworkChaos").Namespace(namespace).Body(networkchaos).Do().Into(networkchaos)
}
func DeleteChaos(clusterName string, networkchaos *NetworkChaos) error {
	_, err := common.DeleteObject(clusterName, networkchaos)
	if err != nil {
		logs.Error("删除 chaosMesh 失败: %s", err.Error())
		return err
	}
	return nil
}
func GetChaos(clusterName, namespace, name string) (*NetworkChaos, error) {
	chaos := &NetworkChaos{}
	_, err := common.GetObject(clusterName, &NetworkChaos{
		TypeMeta: metaV1.TypeMeta{
			APIVersion: "chaos-mesh.org/v1alpha1",
			Kind:       "NetworkChaos",
		},
		ObjectMeta: metaV1.ObjectMeta{
			Namespace: namespace,
			Name:      name,
		},
	}, chaos)
	if err != nil {
		return nil, err
	}
	return chaos, nil
}
func CreateorUpdateChaos(clusterName string, networkchaos *NetworkChaos) error {
	_, err := common.CreateOrUpdateObject(clusterName, networkchaos)
	if err != nil {
		logs.Error("创建 chaosMesh 失败: %s", err.Error())
		return err
	}
	return nil
}
