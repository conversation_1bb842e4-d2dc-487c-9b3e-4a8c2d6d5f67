package controllers

import (
	"aquaman/src/backend/controllers/base"
	"aquaman/src/backend/resources/cloud/ali"
	"fmt"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/slb"
)

type AliCommonController struct {
	base.PageController
}

// @router /listRegions [get]
func (c *AliCommonController) ListRegions() {
	accessKeyId, accessKeySecret, err := ali.GetAliAkSk()
	if err != nil {
		c.Error(err)
		return
	}
	client, err := slb.NewClientWithAccessKey("cn-hangzhou", accessKeyId, accessKeySecret)
	if err != nil {
		c.<PERSON>rror(err)
		return
	}

	request := slb.CreateDescribeRegionsRequest()

	response, err := client.DescribeRegions(request)
	if err != nil || !response.IsSuccess() {
		c.Error(fmt.Errorf("listRegions err.%v", err))
		return
	}
	c.<PERSON>(response.Regions.Region)
}
