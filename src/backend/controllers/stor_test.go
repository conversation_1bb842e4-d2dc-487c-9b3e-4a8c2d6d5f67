package controllers

import (
	"aquaman/src/backend/models"
	"fmt"
	"testing"
)

func TestAddConsulConfig(t *testing.T) {
	bussList, _, _ := models.ListStorBussiness(nil, "consul", "", "", "", 0, 9999)
	if len(bussList) > 0 {
		for _, buss := range bussList {
			bussName := buss.Name
			suppEnv := ""
			if buss.DeployType == "test" {
				suppEnv = "test,deve"
			} else {
				suppEnv = "prev,prod"
			}
			insList, _ := models.GetStorInstanceByBussName(bussName)
			insCluster := make(map[string]string)
			for _, ins := range insList {
				insCluster[ins.ClusterName] = ins.PodIp
			}
			for clusterName, podIp := range insCluster {
				if conf, _ := models.GetStorConsulConfByName(bussName, clusterName); conf != nil {
					continue
				}
				if configs, _ := models.GetConfigByPkgNameAndContent("consul-client-sidecar", podIp); len(configs) > 0 {
					conf := configs[0]

					consulConf := models.StorConsulConf{
						BussName:    bussName,
						ClusterName: clusterName,
						ConfName:    conf.ConfigName,
						ConfId:      conf.Id,
						SuppEnv:     suppEnv,
						SuppProduct: "",
						Owner:       buss.Owner,
					}

					pNames := make(map[string]struct{})
					if events, _ := models.ListPublishEvent("", "", "", fmt.Sprintf("%d", conf.Id), 1); len(events) > 0 {
						for _, e := range events {
							pNames[e.ProductName] = struct{}{}
						}
					}
					if len(pNames) > 0 {
						for pName := range pNames {
							if consulConf.SuppProduct != "" {
								consulConf.SuppProduct += ","
							}
							consulConf.SuppProduct += pName
						}
					} else {
						consulConf.SuppProduct = conf.ProductName
					}
					models.AddStorConsulConf(&consulConf)
				}
			}
		}
	}
}

func TestRefreshSuppEnv(t *testing.T) {
	bussList, _, _ := models.ListStorBussiness(nil, "consul", "", "", "", 0, 9999)
	if len(bussList) > 0 {
		for _, buss := range bussList {
			if configs, _ := models.ListStorConsulConfByName(buss.Name); len(configs) > 0 {
				for _, conf := range configs {
					if c, _ := models.GetConfigById(conf.ConfId); c != nil {
						c.SuppEnv = conf.SuppEnv
						models.UpdateConfigById(c)
					}
				}
			}
		}
	}
}

func TestRefreshCompany(t *testing.T) {
	bussList, _, _ := models.ListStorBussiness(nil, "", "", "", "", 0, 9999)
	if len(bussList) > 0 {
		for _, buss := range bussList {
			if buss.Company != "" {
				continue
			}
			insList, _ := models.GetStorInstanceByBussName(buss.Name)
			clusterMap := make(map[string]*models.Cluster)
			companyMap := make(map[string]int)
			for _, ins := range insList {
				clusterName := ins.ClusterName
				var cluster *models.Cluster
				if _, ok := clusterMap[clusterName]; ok {
					cluster = clusterMap[clusterName]
				} else {
					cluster, _ = models.GetClusterByName(clusterName)
				}
				if cluster == nil {
					continue
				}
				count := 1
				if _, ok := companyMap[cluster.Company]; ok {
					count += companyMap[cluster.Company]
				}
				companyMap[cluster.Company] = count
			}
			maxCount := 0
			maxCompany := ""
			for company, count := range companyMap {
				if count > maxCount {
					maxCount = count
					maxCompany = company
				}
			}
			if maxCompany != "" {
				buss.Company = maxCompany
				models.UpdateStorBussinessById(&buss)
			}
		}
	}
}
