package controllers

import (
	"aquaman/src/backend/controllers/base"
	"aquaman/src/backend/models"
	"aquaman/src/backend/util/ip_util"
	"encoding/json"
	"github.com/pkg/errors"
	"strconv"
	"strings"
)

// OuterIpAllocationController operations for OuterIpAllocation
type OuterIpAllocationController struct {
	base.PageController
}

// URLMapping ...
func (c *OuterIpAllocationController) URLMapping() {
	c.Mapping("Post", c.Post)
	c.Mapping("GetOne", c.GetOne)
	c.Mapping("GetAll", c.GetAll)
	c.Mapping("Put", c.Put)
	c.Mapping("Delete", c.Delete)
	c.Mapping("SearchOuterIpAllocation", c.SearchOuterIpAllocation)
	c.Mapping("CidrToIp", c.CidrToIp)
	c.Mapping("BatchAddOuterIp", c.<PERSON>ch<PERSON>ddOuterIp)
}

// Post ...
// @Title Post
// @Description create OuterIpAllocation
// @Param	body		body 	models.OuterIpAllocation	true		"body for OuterIpAllocation content"
// @Success 201 {int} models.OuterIpAllocation
// @Failure 403 body is empty
// @router / [post]
func (c *OuterIpAllocationController) Post() {
	var v models.OuterIpAllocation
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &v); err == nil {
		if _, err := models.AddOuterIpAllocation(&v); err == nil {
			c.Ctx.Output.SetStatus(201)
			c.Data["json"] = v
		} else {
			c.Data["json"] = err.Error()
		}
	} else {
		c.Data["json"] = err.Error()
	}
	c.ServeJSON()
}

// GetOne ...
// @Title Get One
// @Description get OuterIpAllocation by id
// @Param	id		path 	string	true		"The key for staticblock"
// @Success 200 {object} models.OuterIpAllocation
// @Failure 403 :id is empty
// @router /:id [get]
func (c *OuterIpAllocationController) GetOne() {
	idStr := c.Ctx.Input.Param(":id")
	id, _ := strconv.Atoi(idStr)
	v, err := models.GetOuterIpAllocationById(id)
	if err != nil {
		c.Data["json"] = err.Error()
	} else {
		c.Data["json"] = v
	}
	c.ServeJSON()
}

// GetAll ...
// @Title Get All
// @Description get OuterIpAllocation
// @Param	query	query	string	false	"Filter. e.g. col1:v1,col2:v2 ..."
// @Param	fields	query	string	false	"Fields returned. e.g. col1,col2 ..."
// @Param	sortby	query	string	false	"Sorted-by fields. e.g. col1,col2 ..."
// @Param	order	query	string	false	"Order corresponding to each sortby field, if single value, apply to all sortby fields. e.g. desc,asc ..."
// @Param	limit	query	string	false	"Limit the size of result set. Must be an integer"
// @Param	offset	query	string	false	"Start position of result set. Must be an integer"
// @Success 200 {object} models.OuterIpAllocation
// @Failure 403
// @router / [get]
func (c *OuterIpAllocationController) GetAll() {
	logPre := "GetAllOuterIpAllocation"
	param, result := c.BuildPageParam(logPre)

	list, count, err := models.GetAllOuterIpAllocation(param.Query, param.Fields, param.Sort, param.Order, param.Offset, param.PageSize)
	if err != nil {
		c.PageError(logPre, err, result)
	} else {
		c.PageSuccess(logPre, list, result, count)
	}
}

// Put ...
// @Title Put
// @Description update the OuterIpAllocation
// @Param	id		path 	string	true		"The id you want to update"
// @Param	body		body 	models.OuterIpAllocation	true		"body for OuterIpAllocation content"
// @Success 200 {object} models.OuterIpAllocation
// @Failure 403 :id is not int
// @router /:id [put]
func (c *OuterIpAllocationController) Put() {
	idStr := c.Ctx.Input.Param(":id")
	id, _ := strconv.Atoi(idStr)
	v := models.OuterIpAllocation{Id: id}
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &v); err == nil {
		if err := models.UpdateOuterIpAllocationById(&v); err == nil {
			c.Data["json"] = "OK"
		} else {
			c.Data["json"] = err.Error()
		}
	} else {
		c.Data["json"] = err.Error()
	}
	c.ServeJSON()
}

// Delete ...
// @Title Delete
// @Description delete the OuterIpAllocation
// @Param	id		path 	string	true		"The id you want to delete"
// @Success 200 {string} delete success!
// @Failure 403 id is empty
// @router /:id [delete]
func (c *OuterIpAllocationController) Delete() {
	idStr := c.Ctx.Input.Param(":id")
	id, _ := strconv.Atoi(idStr)
	if err := models.DeleteOuterIpAllocation(id); err == nil {
		c.Data["json"] = "OK"
	} else {
		c.Data["json"] = err.Error()
	}
	c.ServeJSON()
}

// search all deployment names ...
// @Title Get Deployment by names
// @Success 200 {object} models.Deployment
// @Failure 403
// @router /searchOuterIpAllocation [get]
func (c *OuterIpAllocationController) SearchOuterIpAllocation() {
	keyStr := c.GetString("key", "")
	clusterName := c.GetString("clusterName", "")
	fuzzy, _ := c.GetInt64("fuzzy", 0)
	offset, _ := c.GetInt32("offset", 0)
	limit, _ := c.GetInt32("limit", 5000)
	results, err := models.SearchOuterIpAllocation(keyStr, clusterName, fuzzy, offset, limit)
	if err != nil {
		c.Error(err)
	} else {
		c.Success(results)
	}

}

// @router /cidrToIp [get]
func (c *OuterIpAllocationController) CidrToIp() {
	cidr := c.GetString("cidr", "")
	lineType := c.GetString("lineType", "")
	hosts, err := ip_util.Hosts(cidr, lineType)
	if err != nil {
		c.Error(err)
	} else {
		c.Success(hosts)
	}

}

// Post ...
// @Title Post
// @Description create OuterIpAllocation
// @Param	body		body 	models.OuterIpAllocation	true		"body for OuterIpAllocation content"
// @Success 201 {int} models.OuterIpAllocation
// @Failure 403 body is empty
// @router /batchAddOuterIp [post]
func (c *OuterIpAllocationController) BatchAddOuterIp() {
	var v models.OuterIpAllocation
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &v); err == nil {

		for _, item := range strings.Split(v.Ips, "\n") {
			outerIp, _ := models.GetOuterIpPoolByIp(item)
			if outerIp != nil {
				c.Error(errors.New("ip:" + item + "已存在!"))
				return
			}
		}

		for _, item := range strings.Split(v.Ips, "\n") {
			outIp := v
			outIp.Ip = item
			models.AddOuterIpAllocation(&outIp)
		}
	} else {
		c.Error(err)
	}
	c.Success("新增成功")
}
