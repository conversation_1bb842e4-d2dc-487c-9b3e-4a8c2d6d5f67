package alarm_query

import (
	"aquaman/src/backend/client"
	"aquaman/src/backend/models"
	"aquaman/src/backend/util/cmdb"
	"aquaman/src/backend/util/logs"
	"aquaman/src/backend/util/slice"
	"aquaman/src/backend/util/yyms"
	"fmt"
	"strings"
	"time"
)

func GetRelativeHulkBusinessNames(lbId int) ([]string, error) {
	loadBalance, err := models.GetLoadBalancerById(lbId)
	if err != nil {
		return nil, err
	}
	resBusinessNameArr := make([]string, 0)
	if loadBalance.Type == models.LB_TYPE_HTTP {
		zoneType := loadBalance.ZoneType
		clusterNames := loadBalance.ClusterName
		clusterArray := strings.Split(clusterNames, ",")
		for _, cluster := range clusterArray {
			hulkBizArr, err := GetRelativeHulkBusinessNameByCluster(cluster, zoneType)
			if err == nil {
				for _, s := range hulkBizArr {
					resBusinessNameArr = append(resBusinessNameArr, s)
				}
			}
		}

	}
	return resBusinessNameArr, nil
}

func GetRelativeHulkBusinessNameByCluster(cluster, zone string) ([]string, error) {
	resBusinessNameArr := make([]string, 0)
	ips, err := GetIngressVipsByCluster(cluster, zone)
	if err == nil {
		hulkBizArr, err := getHulkBusinessNamesByVips(ips)
		if err == nil {
			for _, s := range hulkBizArr {
				resBusinessNameArr = append(resBusinessNameArr, s)
			}
		}
	}
	return resBusinessNameArr, nil
}

func GetIngressVipsByCluster(clusterName, zoneType string) ([]string, error) {
	zoneConfigs, count, err := models.ListWebZoneConfig(clusterName, zoneType, "", 0, 10000)
	if err != nil {
		return nil, err
	}
	serverInfos := make([]*cmdb.ServerInfo, 0)
	if count == 0 {
		// TODO  what if vip is not available
		return nil, nil
	}
	for _, item2 := range zoneConfigs {
		serverInfos = append(serverInfos, item2.ToServerInfo())
	}
	serverIps := make([]string, 0)
	for _, serverInfo := range serverInfos {
		if serverInfo.TelIp != "" {
			serverIps = append(serverIps, serverInfo.TelIp)
		}
		if serverInfo.MobIp != "" {
			serverIps = append(serverIps, serverInfo.MobIp)
		}
		if serverInfo.EduIp != "" {
			serverIps = append(serverIps, serverInfo.EduIp)
		}
		if serverInfo.InIp != "" {
			serverIps = append(serverIps, serverInfo.InIp)
		}
		if serverInfo.UniIp != "" {
			serverIps = append(serverIps, serverInfo.UniIp)
		}
		if serverInfo.HkIp != "" {
			serverIps = append(serverIps, serverInfo.HkIp)
		}
	}
	return serverIps, nil
}

func GetIngressIps(clusterName, zone string, cli *client.ClusterManager) (serverIps []string, err error) {
	if cli == nil {
		cli, err = client.Manager(clusterName)
		if err != nil {
			return nil, err
		}
	}
	serverIps = make([]string, 0)
	if zone == "" {
		return serverIps, nil
	}
	serverIps, _ = cli.Cluster.GetIngressIpMap()[zone]
	return serverIps, nil
}

func GetL4RsIps(businessName string) (serverIps []string, err error) {
	serverIps = make([]string, 0)
	reList, err := models.GetRsListByBusName(businessName)
	if err != nil {
		return
	}
	for _, ns := range reList {
		serverIps = append(serverIps, ns.Ip)
	}
	serverIps = slice.UniqueStrArr(serverIps)
	return
	// QueryMachineAlarmByHulkBusinessName
}

func GetL4GroupIps(groupName string) (serverIps []string, err error) {
	serverIps = make([]string, 0)
	lbServers, err := models.GetLvsServerByGroupName(groupName)
	if err != nil {
		return
	}
	for _, server := range lbServers {
		serverIps = append(serverIps, server.LbIp)
	}
	serverIps = slice.UniqueStrArr(serverIps)
	return
}

func getHulkBusinessNamesByVips(ips []string) ([]string, error) {
	if len(ips) == 0 {
		return nil, nil
	}
	vIps, err := models.ListHulkVipByVIps(ips)
	if err != nil {
		return nil, err
	}
	businessNames := make([]string, 0)
	for _, vipObj := range vIps {
		if StringsContains(businessNames, vipObj.BusinessName) == -1 {
			businessNames = append(businessNames, vipObj.BusinessName)
		}
	}
	return businessNames, nil
}

func StringsContains(array []string, val string) (index int) {
	index = -1
	for i := 0; i < len(array); i++ {
		if array[i] == val {
			index = i
			return
		}
	}
	return
}

//deprecated
func GetL7AlarmSumByLb(lb models.LoadBalancer, startTime, endTime time.Time) (sum models.L7AlarmSumVo, err error) {
	l7Alarms, err := QueryIngressAlarm(lb.Id, startTime, endTime)
	hulks, err := GetRelativeHulkBusinessNames(lb.Id)
	l4AlarmAll := make([]yyms.AlarmData, 0)
	if err == nil {
		for _, hulk := range hulks {
			L4Alarms, err := QueryHulkBusinessAlarm(hulk, startTime, endTime)
			if err == nil {
				for _, alarm := range L4Alarms {
					fmt.Print(alarm)
					fmt.Print("do not used this method, alarm will be drop")
				}
			}
		}
	}
	sum.ClusterName = lb.ClusterName
	sum.ZoneType = lb.ZoneType
	sum.LbName = lb.LbName
	sum.L4AlarmCount = len(l4AlarmAll)
	sum.L7AlarmCount = len(l7Alarms)
	sum.L7AlarmData = AlarmToEventModel(l7Alarms, models.ObjTypeIngress, lb.LbName)
	sum.L4AlarmData = AlarmToEventModel(l7Alarms, models.ObjTypeLvs, lb.LbName)
	return sum, nil
}

func GetL7AlarmSumByClusterAndZone(cluster, zone string, startTime, endTime time.Time) (sum models.L7AlarmSumVo, err error) {
	l7Alarms, err := QueryIngressAlarmByCluster(cluster, zone, startTime, endTime)
	hulks, err := GetRelativeHulkBusinessNameByCluster(cluster, zone)
	l4AlarmRs := make([]models.EventData, 0)
	if err == nil {
		for _, hulk := range hulks {
			// 目前一个七层专区只会对应一个hulk
			sum.L4Business = hulk
			L4Alarms, err := QueryHulkBusinessAlarm(hulk, startTime, endTime)
			if err == nil {
				for _, alarm := range L4Alarms {
					l4AlarmRs = append(l4AlarmRs, alarm)
				}
			}
		}
	}
	machineAlarmRs, err := QueryMachineAlarmByCluster(cluster, startTime, endTime)
	if err == nil {
		sum.ClusterMachineAlarmCount = len(machineAlarmRs)
		sum.ClusterMachineAlarmData = machineAlarmRs
	}
	sum.ClusterName = cluster
	sum.ZoneType = zone
	sum.LbName = ""
	sum.L4AlarmCount = len(l4AlarmRs)
	sum.L7AlarmCount = len(l7Alarms)
	sum.L7AlarmData = l7Alarms
	sum.L4AlarmData = l4AlarmRs
	return sum, nil
}

func GetL7AlarmSumByClustersAndZone(clusters []string, zone string, startTime, endTime time.Time) (res []models.L7AlarmSumVo, err error) {
	for _, cluster := range clusters {
		zoneSum, e := GetL7AlarmSumByClusterAndZone(cluster, zone, startTime, endTime)
		if e == nil {
			res = append(res, zoneSum)
		} else {
			err = e
			logs.Error(e)
		}
	}
	return
}

func GetL7MachineByClustersAndZone(clusters []string, zone string) (res []models.ServerInfoVo, err error) {
	for _, cluster := range clusters {
		list, e := GetL7MachineByClusterAndZone(cluster, zone)
		if e == nil {
			for _, vo := range list {
				res = append(res, vo)
			}
		} else {
			err = e
			logs.Error(e)
		}
	}
	return
}

func GetL7MachineByClusterAndZone(cluster, zone string) (list []models.ServerInfoVo, err error) {
	ips, err := GetIngressIps(cluster, zone, nil)
	list = make([]models.ServerInfoVo, 0)
	for _, ip := range ips {
		vo := models.ServerInfoVo{
			ClusterName:      cluster,
			ZoneType:         zone,
			Ip:               ip,
			MachineAlarmData: nil,
		}
		list = append(list, vo)
	}
	return
}

func GetL4RsMachineByBusinessName(businessName string) (list []models.ServerInfoVo, err error) {
	ips, err := GetL4RsIps(businessName)
	list = make([]models.ServerInfoVo, 0)
	for _, ip := range ips {
		vo := models.ServerInfoVo{
			ClusterName:      businessName,
			ZoneType:         "",
			Ip:               ip,
			MachineAlarmData: nil,
		}
		list = append(list, vo)
	}
	return
}

func GetL4GroupMachineByBusinessName(groupName string) (list []models.ServerInfoVo, err error) {
	ips, err := GetL4GroupIps(groupName)
	list = make([]models.ServerInfoVo, 0)
	for _, ip := range ips {
		vo := models.ServerInfoVo{
			ClusterName:      groupName,
			ZoneType:         "",
			Ip:               ip,
			MachineAlarmData: nil,
		}
		list = append(list, vo)
	}
	return
}

func GetL4MachineInfo(businessName string) (vo models.L4MachineVo, err error) {
	hulk, err := models.GetHulkBusinessByName(businessName)
	if err != nil {
		return
	}
	reMachineList, err := GetL4RsMachineByBusinessName(businessName)
	if err != nil {
		return
	}
	vo = models.L4MachineVo{
		ClusterName:   hulk.LvsGroupName,
		BusinessName:  hulk.BusinessName,
		RsMachineList: reMachineList,
	}
	if hulk.LvsGroupName != "" {
		groupMachines, err := GetL4GroupMachineByBusinessName(hulk.LvsGroupName)
		if err == nil {
			vo.GroupMachineList = groupMachines
		}
	}
	return
}

func GetL4AlarmSumByBusiness(businessName string, startTime, endTime time.Time) (sum models.L4AlarmSumVo, err error) {
	hulkBusiness, err := models.GetHulkBusinessByName(businessName)
	if err != nil {
		return
	}
	L4AlarmRs, err := QueryHulkBusinessAlarm(businessName, startTime, endTime)
	if err != nil {
		logs.Error(err)
	}
	clusterAlarmRs, err := QueryHulkClusterAlarm(hulkBusiness.Cluster, startTime, endTime)
	if err != nil {
		logs.Error(err)
	}
	machineAlarmRs, err := QueryMachineAlarmByHulkBusinessName(hulkBusiness.BusinessName, startTime, endTime)
	if err != nil {
		logs.Error(err)
	}
	sum = models.L4AlarmSumVo{
		BusinessName:      hulkBusiness.BusinessName,
		AliasName:         hulkBusiness.AliasName,
		ClusterName:       hulkBusiness.Cluster,
		BusinessAlarmData: L4AlarmRs,
		MachineAlarmData:  machineAlarmRs,
		ClusterAlarmData:  clusterAlarmRs,
	}
	return
}

func GetL4AlarmSumByByBusinessList(businessNameList []string, startTime, endTime time.Time) (list []models.L4AlarmSumVo, err error) {
	for _, business := range businessNameList {
		alarmSum, e := GetL4AlarmSumByBusiness(business, startTime, endTime)
		if e == nil {
			list = append(list, alarmSum)
		} else {
			err = e
			logs.Error(e)
		}
	}
	return
}
