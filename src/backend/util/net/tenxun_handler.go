package net

import (
	"aquaman/src/backend/client"
	"aquaman/src/backend/models"
	"aquaman/src/backend/util/cmdb"
	"aquaman/src/backend/util/tencent"
	"fmt"
	"strings"
)

func init() {
	tenXunHandler := TenXunHandler{}
	Register(&tenXunHandler)
}

type TenXunHandler struct {
}

func (h *TenXunHandler) IsMatchCluster(cluster string) bool {
	c, _ := client.Cluster(cluster)
	if c != nil {
		return h.IsMatch(c.IdcId)
	}
	return false
}

func (h *TenXunHandler) Name() string {
	return TenXunName
}

func (h *TenXunHandler) IsMatch(idcId int) bool {
	idc, err := cmdb.GetIdc(idcId)
	if err == nil && strings.Contains(idc.IdcName, "腾讯云") {
		return true
	}
	return false
}

func (h *TenXunHandler) NewAddNet(node *models.NodeStatus) error {
	cluster, _ := client.Cluster(node.ClusterName)
	if cluster == nil {
		return fmt.Errorf("k8s cluster %s not found", node.ClusterName)
	}
	ccsClient := tencent.NewSingleCcsClient(node.IdcId)
	if ccsClient == nil {
		return fmt.Errorf("NewSingleCcsClient err")
	}
	return ccsClient.CreateRoute(cluster, node.Ip, node.Segment)
}

func (h *TenXunHandler) NewDelNet(node *models.NodeStatus) error {
	cluster, _ := client.Cluster(node.ClusterName)
	if cluster == nil {
		return fmt.Errorf("k8s cluster %s not found", node.ClusterName)
	}
	ccsClient := tencent.NewSingleCcsClient(node.IdcId)
	if ccsClient == nil {
		return fmt.Errorf("NewSingleCcsClient err")
	}
	return ccsClient.DeleteRoute(cluster, node.Ip, node.Segment)
}

func (h *TenXunHandler) DoAddNet(idcId int, innerIp, segment string) error {
	cluster, _ := models.GetClusterByIdcId(idcId)
	if cluster == nil {
		return fmt.Errorf("not found k8s cluster by idcId %d ", idcId)
	}
	ccsClient := tencent.NewSingleCcsClient(idcId)
	if ccsClient == nil {
		return fmt.Errorf("NewSingleCcsClient err")
	}
	return ccsClient.CreateRoute(cluster, innerIp, segment)
}

func (h *TenXunHandler) DoDelNet(idcId int, innerIp, segment string) error {
	cluster, _ := models.GetClusterByIdcId(idcId)
	if cluster == nil {
		return fmt.Errorf("not found k8s cluster by idcId %d ", idcId)
	}
	ccsClient := tencent.NewSingleCcsClient(idcId)
	if ccsClient == nil {
		return fmt.Errorf("NewSingleCcsClient err")
	}
	return ccsClient.DeleteRoute(cluster, innerIp, segment)
}

func (h *TenXunHandler) NewCheckAll(cluster string, idcId int) (NewCheckAllResp, error) {
	var resp NewCheckAllResp
	k8sCluster, _ := client.Cluster(cluster)
	if k8sCluster == nil {
		return resp, fmt.Errorf("k8s cluster %s not found", cluster)
	}

	segments := make(map[string]SegmentsInfo)
	ccsClient := tencent.NewSingleCcsClient(idcId)
	if ccsClient == nil {
		return resp, fmt.Errorf("NewSingleCcsClient err")
	}
	if resp, err := ccsClient.ListRoute(k8sCluster); err == nil {
		for _, routeInfo := range resp.Response.RouteSet {
			segment := SegmentsInfo{
				NextHop:  *routeInfo.GatewayIp,
				Segments: make([]string, 0),
			}
			if _, ok := segments[*routeInfo.GatewayIp]; ok {
				segment = segments[*routeInfo.GatewayIp]
			}
			segment.Segments = append(segment.Segments, *routeInfo.DestinationCidrBlock)
			segments[*routeInfo.GatewayIp] = segment
		}
	}
	resp.Success = true
	for _, segment := range segments {
		resp.SegmentsInfo = append(resp.SegmentsInfo, segment)
	}

	return resp, nil
}
