package tencent

import (
	"aquaman/src/backend/models"
	"errors"
	"fmt"
	tke "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tke/v20180525"
)

type CcsClient struct {
	SecretId  string
	SecretKey string
}

var ccsClients = make(map[int]*CcsClient)

func NewSingleCcsClient(idcId int) *CcsClient {
	if _, ok := ccsClients[idcId]; ok {
		return ccsClients[idcId]
	}
	accessKeyId, accessKeySecret, err := getTxAkSk(idcId)
	if err != nil {
		return nil
	}
	ccsClient := &CcsClient{
		SecretId:  accessKeyId,
		SecretKey: accessKeySecret,
	}
	ccsClients[idcId] = ccsClient
	return ccsClient
}

func getTxAkSk(idcId int) (ak, sk string, err error) {
	sName := ""
	if idcId == 0 {
		sName = "tencent-yy"
	}
	sMt, _ := models.GetSecretMgtByName(sName, idcId)
	if sMt == nil {
		return "", "", fmt.Errorf("get secret from db by %s  %d err", sName, idcId)
	}
	return sMt.GetAkSkPlainText()
}

func (c *CcsClient) CreateRouteTable(cluster *models.Cluster) error {
	if cluster.IsEdge == 1 {
		return nil
	}
	regionId := cluster.MetaData["regionId"]
	vpcId, _ := cluster.MetaData["vpcId"].(string)
	cidr, _ := cluster.MetaData["cidr"].(string)
	if regionId == nil || vpcId == "" || cidr == "" {
		return fmt.Errorf("k8s cluster %s metaData err %+v", cluster.ClusterName, cluster.MetaData)
	}

	client, err := tke.NewClientWithSecretId(c.SecretId, c.SecretKey, regionId.(string))
	if err != nil {
		return err
	}

	var ignoreConflictOpt int64 = 1
	_, err = client.CreateClusterRouteTable(&tke.CreateClusterRouteTableRequest{
		RouteTableName:            &cluster.ClusterName,
		RouteTableCidrBlock:       &(cidr),
		VpcId:                     &(vpcId),
		IgnoreClusterCidrConflict: &ignoreConflictOpt,
	})
	return err
}

func (c *CcsClient) DeleteRouteTable(cluster *models.Cluster) error {
	regionId := cluster.MetaData["regionId"]
	if regionId == nil {
		return fmt.Errorf("k8s cluster %s metaData err %+v", cluster.ClusterName, cluster.MetaData)
	}

	client, err := tke.NewClientWithSecretId(c.SecretId, c.SecretKey, regionId.(string))
	if err != nil {
		return err
	}

	_, err = client.DeleteClusterRouteTable(&tke.DeleteClusterRouteTableRequest{
		RouteTableName: &cluster.ClusterName,
	})
	return err
}

func (c *CcsClient) CreateRoute(cluster *models.Cluster, nodeIp, nodeCidr string) error {
	regionId := cluster.MetaData["regionId"]
	if regionId == nil {
		return fmt.Errorf("k8s cluster %s metaData err %+v", cluster.ClusterName, cluster.MetaData)
	}

	client, err := tke.NewClientWithSecretId(c.SecretId, c.SecretKey, regionId.(string))
	if err != nil {
		return err
	}

	rtbResponse, err := client.DescribeClusterRouteTables(&tke.DescribeClusterRouteTablesRequest{})
	if err != nil {
		return err
	}

	var routeTable *tke.RouteTableInfo

	for _, rtb := range rtbResponse.Response.RouteTableSet {
		if *rtb.RouteTableName == cluster.ClusterName {
			routeTable = rtb
			break
		}
	}

	if routeTable == nil {
		if err = c.CreateRouteTable(cluster); err != nil {
			return errors.New(fmt.Sprintf("create route table %s err.%v", cluster.ClusterName, err))
		}
	}

	_, err = client.CreateClusterRoute(&tke.CreateClusterRouteRequest{
		RouteTableName:       &cluster.ClusterName,
		DestinationCidrBlock: &nodeCidr,
		GatewayIp:            &nodeIp,
	})
	return err
}

func (c *CcsClient) ListRoute(cluster *models.Cluster) (*tke.DescribeClusterRoutesResponse, error) {
	var response *tke.DescribeClusterRoutesResponse
	regionId := cluster.MetaData["regionId"]
	if regionId == nil {
		return response, fmt.Errorf("k8s cluster %s metaData err %+v", cluster.ClusterName, cluster.MetaData)
	}

	client, err := tke.NewClientWithSecretId(c.SecretId, c.SecretKey, regionId.(string))
	if err != nil {
		return response, err
	}
	response, err = client.DescribeClusterRoutes(&tke.DescribeClusterRoutesRequest{
		RouteTableName: &cluster.ClusterName,
	})
	return response, err
}

func (c *CcsClient) DeleteRoute(cluster *models.Cluster, nodeIp, nodeCidr string) error {
	regionId := cluster.MetaData["regionId"]
	if regionId == nil {
		return fmt.Errorf("k8s cluster %s metaData err %+v", cluster.ClusterName, cluster.MetaData)
	}

	client, err := tke.NewClientWithSecretId(c.SecretId, c.SecretKey, regionId.(string))
	if err != nil {
		return err
	}

	_, err = client.DeleteClusterRoute(&tke.DeleteClusterRouteRequest{
		RouteTableName:       &cluster.ClusterName,
		DestinationCidrBlock: &nodeCidr,
		GatewayIp:            &nodeIp,
	})
	return err
}
