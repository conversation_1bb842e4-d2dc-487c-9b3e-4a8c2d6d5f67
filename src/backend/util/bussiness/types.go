package bussiness

const (
	GetAllBussinessUri     = "/common/getAllBussiness.do"
	CmdbGetAllBussinessUri = "/webservice/business/listAllBusiness.do"
)

type Bussiness struct {
	Name               string `json:"name"`
	BusinessId         string `json:"businessId"`
	ParentId           string `json:"parentId"`
	Sequence           int    `json:"sequence"`
	Status             int    `json:"status"`
	SysopAdmin         string `json:"sysopAdmin"`
	DevOrgCode         string `json:"devOrgCode"`
	Level              int    `json:"level"`
	Fullname           string `json:"fullname"`
	SysopAdminNickName string `json:"sysopAdminNickName"`
	DevAdminNickName   string `json:"devAdminNickName"`
	CoreBiz            int    `json:"coreBiz"`
	ConfigCoreBiz      int    `json:"configCoreBiz"`
	ProductAdmin       string `json:"productAdmin"`
	ApproveAdmin       string `json:"approveAdmin"`
	Yygroup            string `json:"yygroup"`
	Yycgroup           string `json:"yycgroup"`
}

type GetResp struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Object  []Bussiness `json:"object"`
}

type BussResp struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Object  []Buss `json:"object"`
}

type Buss struct {
	Name     string `json:"name"`
	Value    string `json:"value"`
	PId      string `json:"pId"`
	Data     string `json:"data"`
	Children []Buss `json:"children"`
}

type BussData struct {
	BusinessId    string `json:"businessId"`
	ParentId      string `json:"parentId"`
	Status        int    `json:"status"`
	SysopAdmin    string `json:"sysopAdmin"`
	DevAdmin      string `json:"devAdmin"`
	ProductAdmin  string `json:"productAdmin"`
	Name          string `json:"name"`
	CoreBiz       int    `json:"core_biz"`
	ConfigCoreBiz int    `json:"config_core_biz"`
}
