/* 更新日志：
* 2023-02-21 liuyang16
* a.容器资源使用报表发送函数SendBussinessResourceUsageInfo中增加负责人

* 2023-02-13  liuyang16
* a. 日志优化，写文件方式改成写syslog,日志会自动定向到/data/yy/log目录并自动轮转;aquaman->aquaman_web_d,aquaman_api->aquman_api_d
* b. 生成业务报表SendBussinessResourceUsageInfo函数中增加警告日志方便问题定位
* c. 发布模板解析函数中ParseTemplate增加redis cluster地址变量识别和获取redis cluster地址函数map
* d. 注释掉dbms获取账号FetchMysqlAccount调用，和dba方确认，配置文件中账号变量替换功能未开发，建议去掉
*/