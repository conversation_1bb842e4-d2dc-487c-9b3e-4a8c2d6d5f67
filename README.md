# Boce Client - 重构后的项目说明

## 概述

这个项目已经成功重构，将pusher功能从main.go中分离到独立的pusher包中。现在项目具有更好的代码组织结构和可维护性。

## 重构内容

### 1. 代码结构重组

**之前的结构：**
- 所有代码都在main.go中
- pusher功能与主程序逻辑混合

**重构后的结构：**
```
.
├── main.go              # 主程序入口，只包含命令行处理和调用逻辑
├── wsclient/            # WebSocket客户端包
│   └── wsclient.go
├── pusher/              # Pusher功能包（新增）
│   ├── pusher.go        # 核心pusher功能
│   └── pusher_test.go   # 单元测试
├── go.mod               # Go模块文件
└── 各种配置和说明文件
```

### 2. 功能分离

**main.go 现在只负责：**
- 命令行参数解析
- 子命令路由
- 调用相应的功能模块

**pusher/pusher.go 包含：**
- `BlackboxPusher` 结构体
- 所有pusher相关的方法
- 完整的错误处理
- 配置管理功能

### 3. API 改进

**公开的方法（首字母大写）：**
- `NewBlackboxPusher()` - 创建pusher实例
- `PushOnce()` - 执行一次推送
- `StartPeriodicPush()` - 开始周期性推送
- `DeleteMetrics()` - 删除指标
- `SetTimeout()` - 设置超时
- `GetConfig()` - 获取配置信息

**私有的方法（首字母小写）：**
- `fetchAndPushMetrics()` - 内部实现细节

## 使用方法

### 编译项目

```bash
go build -o boce-client main.go
```

### 运行测试

```bash
# 运行pusher包的单元测试
go test ./pusher

# 运行所有测试
go test ./...

# 运行测试并显示详细输出
go test -v ./pusher
```

### 使用示例

#### 1. WebSocket客户端（默认功能）

```bash
# 使用默认参数
./boce-client

# 或显式指定ws子命令
./boce-client ws --server=ws://localhost:8081 --client=127.0.0.1
```

#### 2. Pusher功能

```bash
# 一次性推送
./boce-client pusher \
  --blackbox.url=http://127.0.0.1:8080/metrics \
  --pushgateway.url=http://localhost:9091 \
  --job=blackbox_exporter \
  --instance=127.0.0.1:8080 \
  --once

# 周期性推送（每30秒）
./boce-client pusher \
  --blackbox.url=http://127.0.0.1:8080/metrics \
  --pushgateway.url=http://localhost:9091 \
  --job=blackbox_exporter \
  --instance=127.0.0.1:8080 \
  --interval=30s
```

## 重构的优势

### 1. 代码组织更清晰
- 功能模块化，职责分离
- 更容易理解和维护
- 便于团队协作开发

### 2. 可测试性提升
- pusher功能有独立的单元测试
- 可以模拟外部依赖进行测试
- 测试覆盖率更高

### 3. 可扩展性更好
- 新功能可以作为独立包添加
- 不会影响现有功能
- 便于功能的独立演进

### 4. 代码复用
- pusher包可以被其他项目引用
- API设计更加通用
- 配置管理更加灵活

## 测试覆盖

pusher包包含以下测试用例：

1. **基础功能测试**
   - `TestNewBlackboxPusher` - 测试实例创建
   - `TestSetTimeout` - 测试超时设置

2. **集成测试**
   - `TestPushOnceWithMockServers` - 测试正常推送流程
   - `TestPushOnceWithBlackboxError` - 测试错误处理
   - `TestDeleteMetrics` - 测试指标删除

3. **使用模拟服务器**
   - 模拟blackbox exporter响应
   - 模拟push gateway响应
   - 测试各种错误场景

## 性能和可靠性

### 1. 错误处理
- 完整的错误传播机制
- 详细的错误信息
- 优雅的失败处理

### 2. 配置管理
- 灵活的超时设置
- 配置信息查询接口
- 运行时配置调整

### 3. 日志记录
- 详细的操作日志
- 性能统计信息
- 错误追踪支持

## 未来扩展

基于当前的架构，可以轻松添加：

1. **新的数据源支持**
   - 其他exporter类型
   - 多数据源聚合
   - 数据转换功能

2. **高级推送功能**
   - 批量推送
   - 压缩传输
   - 重试机制

3. **监控和告警**
   - 推送状态监控
   - 失败告警
   - 性能指标收集

## 迁移指南

如果你之前使用的是未重构的版本：

1. **命令行接口保持不变**
   - 所有原有的命令行参数都继续支持
   - 行为和输出保持一致

2. **如果你直接使用了代码**
   - 导入路径需要更新：`import "boce-client/pusher"`
   - 方法名称有变化：`pushOnce()` → `PushOnce()`
   - 需要使用 `pusher.NewBlackboxPusher()` 创建实例

3. **配置文件**
   - 无需更改，所有配置方式保持兼容

## 总结

这次重构显著提升了项目的代码质量和可维护性，同时保持了向后兼容性。新的架构为未来的功能扩展奠定了良好的基础。
